import { RedisService } from 'src/util/database/redis/redis.service';
export declare class DistributedLock {
    private readonly redisService;
    private readonly logger;
    private readonly lockPrefix;
    constructor(redisService: RedisService);
    acquire(key: string, ttl: number): Promise<boolean>;
    release(key: string): Promise<boolean>;
    withLock<T>(key: string, callback: () => Promise<T>, ttl?: number): Promise<T>;
    tryWithLock<T>(key: string, callback: () => Promise<T>, defaultValue: T, ttl?: number): Promise<T>;
    isLockFree(key: string): Promise<boolean>;
    extendLock(key: string, ttl: number): Promise<boolean>;
    getLockTTL(key: string): Promise<number>;
    private formatKey;
}
