"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationStatsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class LocationStatsResponseDto {
    userId;
    statisticsPeriod;
    commonLocations;
    summary;
}
exports.LocationStatsResponseDto = LocationStatsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 12345 }),
    __metadata("design:type", Number)
], LocationStatsResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '统计周期',
        type: 'object',
        properties: {
            days: { type: 'number', example: 30 },
            startDate: { type: 'string', example: '2024-12-23T00:00:00Z' },
            endDate: { type: 'string', example: '2025-01-22T23:59:59Z' }
        }
    }),
    __metadata("design:type", Object)
], LocationStatsResponseDto.prototype, "statisticsPeriod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '常用登录地列表',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                province: { type: 'string', example: '广东省' },
                city: { type: 'string', example: '深圳市' },
                loginCount: { type: 'number', example: 45 },
                isTrusted: { type: 'boolean', example: true },
                trustScore: { type: 'number', example: 95.5 },
                firstLoginAt: { type: 'string', example: '2024-11-15T09:20:00Z' },
                lastLoginAt: { type: 'string', example: '2025-01-22T10:30:00Z' }
            }
        }
    }),
    __metadata("design:type", Array)
], LocationStatsResponseDto.prototype, "commonLocations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '统计摘要',
        type: 'object',
        properties: {
            totalLocations: { type: 'number', example: 3 },
            trustedLocations: { type: 'number', example: 2 },
            riskLoginCount: { type: 'number', example: 2 },
            totalLoginCount: { type: 'number', example: 47 },
            riskRate: { type: 'number', example: 4.26 }
        }
    }),
    __metadata("design:type", Object)
], LocationStatsResponseDto.prototype, "summary", void 0);
//# sourceMappingURL=location-stats.response.dto.js.map