"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClosePaymentDto = exports.QueryPaymentDto = exports.CreatePaymentDto = exports.PaymentMode = exports.PaymentChannel = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var PaymentChannel;
(function (PaymentChannel) {
    PaymentChannel["ALIPAY"] = "alipay";
    PaymentChannel["WECHATPAY"] = "wechatpay";
})(PaymentChannel || (exports.PaymentChannel = PaymentChannel = {}));
var PaymentMode;
(function (PaymentMode) {
    PaymentMode["REDIRECT"] = "redirect";
    PaymentMode["QR_CODE"] = "qrcode";
})(PaymentMode || (exports.PaymentMode = PaymentMode = {}));
class CreatePaymentDto {
    userId;
    amount;
    subject;
    description;
    channel;
    paymentMode = PaymentMode.REDIRECT;
    clientIp;
    returnUrl;
    extraData;
}
exports.CreatePaymentDto = CreatePaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 'user123456' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额(元)', example: 99.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '支付金额必须为数字' }),
    (0, class_validator_1.Min)(0.01, { message: '支付金额必须大于0.01元' }),
    __metadata("design:type", Number)
], CreatePaymentDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品标题', example: 'iPhone 15手机壳' }),
    (0, class_validator_1.IsNotEmpty)({ message: '商品标题不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述', required: false, example: '透明防摔手机保护壳' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '支付渠道',
        enum: PaymentChannel,
        example: PaymentChannel.ALIPAY,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付渠道不能为空' }),
    (0, class_validator_1.IsEnum)(PaymentChannel, { message: '无效的支付渠道' }),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '支付方式',
        enum: PaymentMode,
        example: PaymentMode.REDIRECT,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PaymentMode, { message: '无效的支付方式' }),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "paymentMode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '客户端IP', required: false, example: '***********' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "clientIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '支付完成后的跳转URL',
        required: false,
        example: 'https://example.com/pay/success',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentDto.prototype, "returnUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '扩展参数',
        required: false,
        example: { storeId: 'S001' },
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreatePaymentDto.prototype, "extraData", void 0);
class QueryPaymentDto {
    orderNo;
}
exports.QueryPaymentDto = QueryPaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单编号', example: 'P202307250001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '订单编号不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryPaymentDto.prototype, "orderNo", void 0);
class ClosePaymentDto {
    orderNo;
    reason;
}
exports.ClosePaymentDto = ClosePaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单编号', example: 'P202307250001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '订单编号不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ClosePaymentDto.prototype, "orderNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关闭原因', required: false, example: '用户取消支付' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ClosePaymentDto.prototype, "reason", void 0);
//# sourceMappingURL=payment-request.dto.js.map