"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherAuditService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const teacher_audit_entity_1 = require("./entities/teacher_audit.entity");
const user_info_entity_1 = require("../../util/database/mysql/user_info/entities/user_info.entity");
let TeacherAuditService = class TeacherAuditService {
    teacherAuditRepository;
    userInfoRepository;
    constructor(teacherAuditRepository, userInfoRepository) {
        this.teacherAuditRepository = teacherAuditRepository;
        this.userInfoRepository = userInfoRepository;
    }
    async findByTeacherName(teacherName) {
        return this.teacherAuditRepository.find({
            where: {
                teacherName: teacherName,
                isDelete: 0
            }
        });
    }
    async findByUserId(userId) {
        console.log("zww：根据用户ID查询教师认证记录的用户ID为", userId);
        if (userId === undefined || userId === null) {
            throw new Error('userId 不能为空');
        }
        return this.teacherAuditRepository.find({
            where: {
                teacherId: userId,
                isDelete: 0
            },
            relations: ['teacher', 'auditor'],
            order: {
                createTime: 'DESC'
            }
        });
    }
    async findByTeacherNameLike(namePattern) {
        return this.teacherAuditRepository.createQueryBuilder('teacherAudit')
            .where('teacherAudit.teacherName LIKE :namePattern', { namePattern: `%${namePattern}%` })
            .andWhere('teacherAudit.isDelete = :isDelete', { isDelete: 0 })
            .getMany();
    }
    async findByTeacherNameAndStatus(teacherName, status) {
        return this.teacherAuditRepository.find({
            where: {
                teacherName: teacherName,
                result: status,
                isDelete: 0
            }
        });
    }
    async create(createTeacherAuditDto) {
        const audit = this.teacherAuditRepository.create(createTeacherAuditDto);
        return this.teacherAuditRepository.save(audit);
    }
    async findAll() {
        return this.teacherAuditRepository.find({
            relations: ['teacher', 'auditor'],
            where: { isDelete: 0 }
        });
    }
    async findPending() {
        return this.teacherAuditRepository.find({
            where: {
                result: teacher_audit_entity_1.AuditResult.PENDING,
                isDelete: 0
            },
            relations: ['teacher', 'auditor'],
        });
    }
    async findByTeacherId(teacherId) {
        console.log("zww：查询教师认证的ID为", teacherId);
        const audits = await this.findByUserId(teacherId);
        console.log("zww：根据教师id找到的认证记录为", audits);
        if (!audits.length) {
            console.log("zww：未找到教师id为", teacherId, "的认证记录");
            return null;
        }
        return audits[0];
    }
    async findOne(id) {
        const audit = await this.teacherAuditRepository.findOne({
            where: {
                id,
                isDelete: 0
            },
            relations: ['teacher', 'auditor'],
        });
        if (!audit) {
            throw new common_1.NotFoundException(`未找到ID为 ${id} 的教师认证记录`);
        }
        return audit;
    }
    async update(id, updateTeacherAuditDto) {
        const audit = await this.findOne(id);
        console.log("zww：根据教师认证记录id找到的认证记录为", audit);
        if (updateTeacherAuditDto.result !== undefined &&
            audit.result === teacher_audit_entity_1.AuditResult.PENDING) {
            updateTeacherAuditDto.beforeStatus = audit.result;
            updateTeacherAuditDto.afterStatus = updateTeacherAuditDto.result;
        }
        const updated = Object.assign(audit, updateTeacherAuditDto);
        console.log("zww：最后保存到数据库的记录信息为", updated);
        return this.teacherAuditRepository.save(updated);
    }
    async remove(id) {
        const audit = await this.findOne(id);
        audit.isDelete = 1;
        await this.teacherAuditRepository.save(audit);
    }
    async getTeacherAuditDetail(id) {
        const audit = await this.teacherAuditRepository.findOne({
            where: {
                id,
                isDelete: 0
            },
            relations: ['teacher', 'auditor'],
        });
        if (!audit) {
            throw new common_1.NotFoundException(`未找到ID为 ${id} 的教师认证记录`);
        }
        return {
            ...audit,
            teacher: audit.teacher ? {
                id: audit.teacher.id,
                nickName: audit.teacher.nickName,
                avatarUrl: audit.teacher.avatarUrl,
                phone: audit.teacher.phone,
            } : null,
            auditor: audit.auditor ? {
                id: audit.auditor.id,
                nickName: audit.auditor.nickName,
                avatarUrl: audit.auditor.avatarUrl,
                phone: audit.auditor.phone,
            } : null
        };
    }
};
exports.TeacherAuditService = TeacherAuditService;
exports.TeacherAuditService = TeacherAuditService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(teacher_audit_entity_1.TeacherAudit)),
    __param(1, (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfo)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], TeacherAuditService);
//# sourceMappingURL=teacher_audit.service.js.map