/**
 * IP地理位置功能安装验证脚本
 * 用于验证ip2region库是否正确安装和工作
 */

console.log('🔍 开始验证IP地理位置功能安装...\n');

// 1. 检查ip2region库是否安装
try {
  const IP2Region = require('ip2region').default;
  console.log('✅ ip2region库导入成功');
  
  // 2. 创建ip2region实例
  const query = new IP2Region();
  console.log('✅ ip2region实例创建成功');
  
  // 3. 测试一些常见IP地址
  const testIPs = [
    '*******',           // Google DNS
    '***************',   // 114 DNS
    '**************',    // 百度
    '*********',         // 阿里DNS
    '*******',           // Cloudflare DNS
    '127.0.0.1',         // 本地回环
    '***********'        // 私有IP
  ];
  
  console.log('\n🧪 开始测试IP地址解析...\n');
  
  testIPs.forEach((ip, index) => {
    try {
      const result = query.search(ip);
      console.log(`${index + 1}. IP: ${ip}`);
      console.log(`   国家: ${result.country || '未知'}`);
      console.log(`   省份: ${result.province || '未知'}`);
      console.log(`   城市: ${result.city || '未知'}`);
      console.log(`   运营商: ${result.isp || '未知'}`);
      console.log(`   原始数据: ${JSON.stringify(result)}`);
      console.log('');
    } catch (error) {
      console.log(`❌ IP ${ip} 解析失败: ${error.message}`);
    }
  });
  
  // 4. 测试IPv6地址（如果支持）
  console.log('🔍 测试IPv6地址...\n');
  const ipv6Tests = [
    '::1',                                    // IPv6 localhost
    '2001:db8::1',                           // IPv6 test address
    '2001:4860:4860::8888'                   // Google IPv6 DNS
  ];
  
  ipv6Tests.forEach((ip, index) => {
    try {
      const result = query.search(ip);
      console.log(`IPv6 ${index + 1}. IP: ${ip}`);
      console.log(`   结果: ${JSON.stringify(result)}`);
      console.log('');
    } catch (error) {
      console.log(`❌ IPv6 ${ip} 解析失败: ${error.message}`);
    }
  });
  
  // 5. 性能测试
  console.log('⚡ 进行性能测试...\n');
  const performanceTestIP = '*******';
  const testCount = 1000;
  
  const startTime = Date.now();
  for (let i = 0; i < testCount; i++) {
    query.search(performanceTestIP);
  }
  const endTime = Date.now();
  
  const totalTime = endTime - startTime;
  const avgTime = totalTime / testCount;
  
  console.log(`📊 性能测试结果:`);
  console.log(`   测试次数: ${testCount}`);
  console.log(`   总耗时: ${totalTime}ms`);
  console.log(`   平均耗时: ${avgTime.toFixed(2)}ms`);
  console.log(`   QPS: ${(testCount / (totalTime / 1000)).toFixed(0)}`);
  
  // 6. 数据质量分析
  console.log('\n📈 数据质量分析...\n');
  
  const qualityTestIPs = [
    '**************',  // 百度
    '*************',   // 腾讯
    '***************', // 百度
    '**************'   // 百度
  ];
  
  qualityTestIPs.forEach((ip, index) => {
    try {
      const result = query.search(ip);
      const emptyFields = [result.country, result.province, result.city, result.isp]
        .filter(field => field === '0' || field === '' || !field).length;
      const confidence = Math.max(100 - (emptyFields * 25), 0);
      
      console.log(`${index + 1}. IP: ${ip}`);
      console.log(`   完整性: ${4 - emptyFields}/4 字段有效`);
      console.log(`   置信度: ${confidence}%`);
      console.log(`   位置: ${result.country} ${result.province} ${result.city}`);
      console.log('');
    } catch (error) {
      console.log(`❌ IP ${ip} 质量分析失败: ${error.message}`);
    }
  });
  
  console.log('🎉 IP地理位置功能验证完成！');
  console.log('\n📋 验证总结:');
  console.log('✅ ip2region库安装正常');
  console.log('✅ IP地址解析功能正常');
  console.log('✅ 性能表现良好');
  console.log('✅ 数据质量可接受');
  
  console.log('\n🚀 可以开始使用IP地理位置功能了！');
  
} catch (error) {
  console.log('❌ ip2region库验证失败:');
  console.log(`   错误信息: ${error.message}`);
  console.log(`   错误堆栈: ${error.stack}`);
  
  console.log('\n🔧 可能的解决方案:');
  console.log('1. 确认ip2region包已正确安装: npm install ip2region --save');
  console.log('2. 检查Node.js版本是否兼容');
  console.log('3. 尝试重新安装依赖: npm install');
  console.log('4. 检查网络连接是否正常');
  
  process.exit(1);
}

// 7. 输出使用建议
console.log('\n💡 使用建议:');
console.log('1. IP地址解析结果中的"0"表示数据缺失');
console.log('2. 建议对解析结果进行数据质量检查');
console.log('3. 可以使用Redis缓存提高查询性能');
console.log('4. 私有IP地址（如192.168.x.x）通常无法解析出有效位置');
console.log('5. IPv6地址的城市信息可能经常为空，这是正常现象');

console.log('\n📚 更多信息请查看:');
console.log('- 使用指南: src/util/ip_location/docs/usage-guide.md');
console.log('- 测试页面: src/util/ip_location/test/ip-location-test.html');
console.log('- API文档: 启动服务后访问 /api-docs');
