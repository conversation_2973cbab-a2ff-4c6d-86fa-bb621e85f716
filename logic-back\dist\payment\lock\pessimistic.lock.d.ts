import { Connection, EntityManager, QueryRunner, ObjectLiteral } from 'typeorm';
export declare class PessimisticLock {
    private readonly connection;
    private readonly logger;
    constructor(connection: Connection);
    withRowLock<T>(tableName: string, condition: Record<string, any>, callback: (manager: EntityManager) => Promise<T>): Promise<T>;
    withTableLock<T>(tableName: string, lockMode: 'READ' | 'WRITE', callback: (manager: EntityManager) => Promise<T>): Promise<T>;
    withEntityLock<Entity extends ObjectLiteral, T>(entityClass: {
        new (): Entity;
    } & any, findOptions: any, callback: (entities: Entity[], manager: EntityManager) => Promise<T>): Promise<T>;
    withTransaction<T>(callback: (queryRunner: QueryRunner) => Promise<T>): Promise<T>;
}
