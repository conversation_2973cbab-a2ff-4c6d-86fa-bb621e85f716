export declare class IpAddress {
    private readonly _value;
    private readonly _type;
    private readonly _isPrivate;
    private readonly _isLoopback;
    constructor(value: string);
    get value(): string;
    get type(): 'IPv4' | 'IPv6';
    get isPrivate(): boolean;
    get isLoopback(): boolean;
    get isPublic(): boolean;
    get canGeolocate(): boolean;
    get masked(): string;
    private cleanIpAddress;
    private isValidIpAddress;
    private determineIpType;
    private isPrivateIp;
    private isLoopbackIp;
    static create(value: string): IpAddress;
    static tryCreate(value: string): IpAddress | null;
    equals(other: IpAddress): boolean;
    toString(): string;
    toJSON(): object;
}
