"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRecord = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let PaymentRecord = class PaymentRecord {
    id;
    orderId;
    orderNo;
    paymentChannel;
    paymentId;
    amount;
    status;
    userId;
    clientIp;
    paymentTime;
    rawRequest;
    rawResponse;
    version;
    createdAt;
    updatedAt;
};
exports.PaymentRecord = PaymentRecord;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '订单ID', length: 36 }),
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "orderId", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '订单编号', length: 64 }),
    (0, swagger_1.ApiProperty)({ description: '订单编号' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "orderNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付渠道', length: 32 }),
    (0, swagger_1.ApiProperty)({ description: '支付渠道' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "paymentChannel", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '支付平台交易号', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '支付平台交易号' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "paymentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付金额', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '支付金额' }),
    __metadata("design:type", Number)
], PaymentRecord.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '支付状态', length: 32, default: 'pending' }),
    (0, swagger_1.ApiProperty)({ description: '支付状态' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '用户ID', length: 36 }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '客户端IP', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '客户端IP' }),
    __metadata("design:type", String)
], PaymentRecord.prototype, "clientIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付完成时间', type: 'timestamp', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '支付完成时间' }),
    __metadata("design:type", Date)
], PaymentRecord.prototype, "paymentTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '原始请求数据', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '原始请求数据' }),
    __metadata("design:type", Object)
], PaymentRecord.prototype, "rawRequest", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '原始响应数据', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '原始响应数据' }),
    __metadata("design:type", Object)
], PaymentRecord.prototype, "rawResponse", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '版本号（乐观锁）', type: 'int', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '版本号（乐观锁）' }),
    __metadata("design:type", Number)
], PaymentRecord.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentRecord.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], PaymentRecord.prototype, "updatedAt", void 0);
exports.PaymentRecord = PaymentRecord = __decorate([
    (0, typeorm_1.Entity)('payment_record')
], PaymentRecord);
//# sourceMappingURL=payment-record.entity.js.map