{"version": 3, "file": "login-logger-extension.util.js", "sourceRoot": "", "sources": ["../../../../src/util/ip_location/utils/login-logger-extension.util.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6GAAuG;AACvG,qFAAgF;AAChF,8GAA4G;AAC5G,0EAAsE;AA0B/D,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAEhB;IACA;IAFnB,YACmB,iBAA+C,EAC/C,aAA4B;QAD5B,sBAAiB,GAAjB,iBAAiB,CAA8B;QAC/C,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAMJ,KAAK,CAAC,2BAA2B,CAAC,IAA0B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,IAAI,cAAc,GAAG,IAAI,CAAC;YAG1B,IAAI,IAAI,CAAC,wBAAwB,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7D,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;wBAClE,EAAE,EAAE,IAAI,CAAC,QAAQ;wBACjB,WAAW,EAAE,KAAK;qBACnB,CAAC,CAAC;oBAEH,YAAY,GAAG;wBACb,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,IAAI,EAAE,cAAc,CAAC,IAAI;wBACzB,GAAG,EAAE,cAAc,CAAC,GAAG;wBACvB,cAAc,EAAE,cAAc,CAAC,UAAU;wBACzC,WAAW,EAAE,cAAc,CAAC,UAAU;qBACvC,CAAC;oBAGF,MAAM,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,EAAE;wBACjE,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,IAAI,EAAE,cAAc,CAAC,IAAI;wBACzB,GAAG,EAAE,cAAc,CAAC,GAAG;wBACvB,UAAU,EAAE,cAAc,CAAC,UAAU;wBACrC,cAAc,EAAE,CAAC,cAAc,CAAC,aAAa;wBAC7C,UAAU,EAAE,cAAc,CAAC,UAAU;wBACrC,WAAW,EAAE,cAAc,CAAC,WAAW;qBACxC,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC;oBACvB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,EAAE;wBAC5E,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAC/B,EAAE,aAAa,CAAC,CAAC;oBAGlB,YAAY,GAAG;wBACb,OAAO,EAAE,IAAI;wBACb,QAAQ,EAAE,IAAI;wBACd,IAAI,EAAE,IAAI;wBACV,GAAG,EAAE,IAAI;wBACT,cAAc,EAAE,UAAU;wBAC1B,WAAW,EAAE,CAAC;qBACf,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,QAAQ,IAAI,YAAY,EAAE,CAAC;gBAC/D,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;wBAC7D,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,SAAS,EAAE,IAAI,CAAC,QAAQ;wBACxB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;qBAC1B,CAAC,CAAC;oBAEH,cAAc,GAAG;wBACf,SAAS,EAAE,UAAU,CAAC,cAAc,CAAC,KAAK;wBAC1C,UAAU,EAAE,UAAU,CAAC,cAAc,CAAC,MAAM;qBAC7C,CAAC;oBAGF,IAAI,UAAU,CAAC,cAAc,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;wBAC/C,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,eAAe,EAAE;4BAC1E,MAAM,EAAE,IAAI,CAAC,MAAM;4BACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;4BAC9B,QAAQ,EAAE,GAAG,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,EAAE;4BACzD,SAAS,EAAE,UAAU,CAAC,cAAc,CAAC,KAAK;4BAC1C,SAAS,EAAE,UAAU,CAAC,cAAc,CAAC,KAAK;4BAC1C,OAAO,EAAE,UAAU,CAAC,cAAc,CAAC,OAAO;yBAC3C,CAAC,CAAC;oBACL,CAAC;gBAEH,CAAC;gBAAC,OAAO,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,YAAY,EAAE;wBACvE,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAC/B,EAAE,SAAS,CAAC,CAAC;oBAGd,cAAc,GAAG;wBACf,SAAS,EAAE,KAAK;wBAChB,UAAU,EAAE,QAAQ;qBACrB,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,MAAM,eAAe,GAAG;gBACtB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,UAAU,EAAE,IAAI,CAAC,UAAU;gBAE3B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;gBAEpF,GAAG,CAAC,YAAY,IAAI;oBAClB,OAAO,EAAE,YAAY,CAAC,OAAO;oBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,IAAI,EAAE,YAAY,CAAC,IAAI;oBACvB,GAAG,EAAE,YAAY,CAAC,GAAG;oBACrB,cAAc,EAAE,YAAY,CAAC,cAAc;oBAC3C,WAAW,EAAE,YAAY,CAAC,WAAW;iBACtC,CAAC;gBACF,GAAG,CAAC,cAAc,IAAI;oBACpB,SAAS,EAAE,cAAc,CAAC,SAAS;oBACnC,UAAU,EAAE,cAAc,CAAC,UAAU;iBACtC,CAAC;aACH,CAAC;YAGF,MAAM,mCAAe,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAEvD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,6BAA6B,EAAE;gBACxF,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC9B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI;gBAC/E,SAAS,EAAE,cAAc,EAAE,SAAS,IAAI,SAAS;gBACjD,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACrC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,6BAA6B,EAAE;gBACxF,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC9B,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACrC,EAAE,KAAK,CAAC,CAAC;YAGV,IAAI,CAAC;gBACH,MAAM,mCAAe,CAAC,eAAe,CAAC;oBACpC,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,eAAe,EAAE,IAAI,CAAC,eAAe;oBACrC,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,EAAE;oBAC5E,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB,EAAE,aAAa,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAMD,KAAK,CAAC,0BAA0B,CAAC,IAA0B;QACzD,IAAI,CAAC;YACH,IAAI,YAAY,GAAG,IAAI,CAAC;YAGxB,IAAI,IAAI,CAAC,wBAAwB,KAAK,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7D,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;wBAClE,EAAE,EAAE,IAAI,CAAC,QAAQ;wBACjB,WAAW,EAAE,KAAK;qBACnB,CAAC,CAAC;oBAEH,YAAY,GAAG;wBACb,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,IAAI,EAAE,cAAc,CAAC,IAAI;wBACzB,GAAG,EAAE,cAAc,CAAC,GAAG;qBACxB,CAAC;gBAEJ,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC;oBAEvB,YAAY,GAAG,IAAI,CAAC;gBACtB,CAAC;YACH,CAAC;YAGD,MAAM,WAAW,GAAG;gBAClB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,mCAAW,CAAC,MAAM;gBAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;aACrF,CAAC;YAGF,MAAM,mCAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAGlD,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBACzF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,uBAAuB,EAAE;oBAClF,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC9B,QAAQ,EAAE,GAAG,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,EAAE;oBACzD,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,4BAA4B,EAAE;gBACvF,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC/B,EAAE,KAAK,CAAC,CAAC;YAGV,IAAI,CAAC;gBACH,MAAM,mCAAe,CAAC,cAAc,CAAC;oBACnC,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,WAAW,EAAE,mCAAW,CAAC,MAAM;oBAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,uBAAuB,EAAE;oBAClF,MAAM,EAAE,IAAI,CAAC,MAAM;iBACpB,EAAE,aAAa,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,+BAA+B,CAAC,MAAc,EAAE,QAAgB;QAKpE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC7D,MAAM;gBACN,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YAEH,OAAO;gBACL,gBAAgB,EAAE,UAAU,CAAC,cAAc,CAAC,gBAAgB;gBAC5D,MAAM,EAAE,UAAU,CAAC,cAAc,CAAC,MAAM;gBACxC,kBAAkB,EAAE,UAAU,CAAC,cAAc,CAAC,kBAAkB;aACjE,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,iCAAiC,EAAE;gBAC5F,MAAM;gBACN,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;aAC1B,EAAE,KAAK,CAAC,CAAC;YAGV,OAAO;gBACL,gBAAgB,EAAE,KAAK;gBACvB,MAAM,EAAE,aAAa;gBACrB,kBAAkB,EAAE,EAAE;aACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAOO,MAAM,CAAC,EAAU;QACvB,IAAI,CAAC,EAAE;YAAE,OAAO,MAAM,CAAC;QAEvB,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAErB,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAC/C,CAAC;YACD,OAAO,qBAAqB,CAAC;QAC/B,CAAC;aAAM,CAAC;YAEN,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;YACnD,CAAC;YACD,OAAO,gBAAgB,CAAC;QAC1B,CAAC;IACH,CAAC;CACF,CAAA;AAzTY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAG2B,8DAA4B;QAChC,8BAAa;GAHpC,wBAAwB,CAyTpC"}