"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LockManager_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LockManager = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const distributed_lock_1 = require("./distributed.lock");
const optimistic_lock_1 = require("./optimistic.lock");
const pessimistic_lock_1 = require("./pessimistic.lock");
let LockManager = LockManager_1 = class LockManager {
    distributedLock;
    optimisticLock;
    pessimisticLock;
    configService;
    logger = new common_1.Logger(LockManager_1.name);
    constructor(distributedLock, optimisticLock, pessimisticLock, configService) {
        this.distributedLock = distributedLock;
        this.optimisticLock = optimisticLock;
        this.pessimisticLock = pessimisticLock;
        this.configService = configService;
    }
    getDistributedLock() {
        return this.distributedLock;
    }
    getOptimisticLock() {
        return this.optimisticLock;
    }
    getPessimisticLock() {
        return this.pessimisticLock;
    }
    async withDistributedLock(key, callback, ttl = 10000) {
        this.logger.log(`尝试获取分布式锁: ${key}, 超时时间: ${ttl}ms`);
        const startTime = Date.now();
        try {
            const result = await this.distributedLock.withLock(key, async () => {
                this.logger.log(`成功获取分布式锁: ${key}, 耗时: ${Date.now() - startTime}ms`);
                try {
                    const callbackResult = await callback();
                    this.logger.log(`锁内操作执行完成: ${key}, 总耗时: ${Date.now() - startTime}ms`);
                    return callbackResult;
                }
                catch (error) {
                    this.logger.error(`锁内操作执行失败: ${key}, 错误: ${error.message}`, error.stack);
                    throw error;
                }
            }, ttl);
            this.logger.log(`分布式锁已释放: ${key}, 总时间: ${Date.now() - startTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`分布式锁操作失败: ${key}, 错误: ${error.message}, 总时间: ${Date.now() - startTime}ms`, error.stack);
            throw error;
        }
    }
    async withPessimisticRowLock(tableName, condition, callback) {
        return this.pessimisticLock.withRowLock(tableName, condition, callback);
    }
    async updateWithOptimisticLock(entityClass, id, updateFn, maxRetries = 3) {
        return this.optimisticLock.updateWithOptimisticLock(entityClass, id, updateFn, maxRetries);
    }
    async withSmartLock(key, callback, options = {}) {
        const lockType = options.preferredLockType || this.configService.get('payment.defaultLockType', 'distributed');
        this.logger.debug(`使用 ${lockType} 锁执行操作: ${key}`);
        switch (lockType) {
            case 'distributed':
                return this.distributedLock.withLock(key, callback, options.ttl || 10000);
            case 'pessimistic':
                if (!options.tableName || !options.condition) {
                    throw new Error('使用悲观锁需要提供表名和条件');
                }
                return this.pessimisticLock.withRowLock(options.tableName, options.condition, async (manager) => {
                    return callback();
                });
            case 'optimistic':
                if (!options.entity || !options.entityId) {
                    throw new Error('使用乐观锁需要提供实体类和ID');
                }
                return this.optimisticLock.updateWithOptimisticLock(options.entity, options.entityId, async (entity) => {
                    await callback();
                    return entity;
                }, options.maxRetries || 3);
            default:
                throw new Error(`不支持的锁类型: ${lockType}`);
        }
    }
    async executeWithConcurrencyControl(operations) {
        const results = [];
        for (const operation of operations) {
            const result = await this.withSmartLock(operation.key, operation.callback, {
                preferredLockType: operation.lockType,
                ...operation.options
            });
            results.push(result);
        }
        return results;
    }
};
exports.LockManager = LockManager;
exports.LockManager = LockManager = LockManager_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [distributed_lock_1.DistributedLock,
        optimistic_lock_1.OptimisticLock,
        pessimistic_lock_1.PessimisticLock,
        config_1.ConfigService])
], LockManager);
//# sourceMappingURL=lock.manager.js.map