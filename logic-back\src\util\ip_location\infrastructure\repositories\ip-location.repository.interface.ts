import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';

/**
 * IP地理位置仓储接口
 * 定义数据访问的抽象层，支持读写分离
 */
export interface IIpLocationRepository {
  /**
   * 查找用户的常用位置
   * @param userId 用户ID
   * @param location 地理位置
   * @returns 用户常用位置实体
   */
  findUserCommonLocation(
    userId: number,
    location: GeographicLocation
  ): Promise<UserCommonLocation | null>;

  /**
   * 获取用户所有常用位置
   * @param userId 用户ID
   * @param options 查询选项
   * @returns 用户常用位置列表
   */
  findUserCommonLocations(
    userId: number,
    options?: {
      limit?: number;
      onlyTrusted?: boolean;
      minLoginCount?: number;
      orderBy?: 'loginCount' | 'lastLoginAt' | 'trustScore';
      orderDirection?: 'ASC' | 'DESC';
    }
  ): Promise<UserCommonLocation[]>;

  /**
   * 获取用户在指定时间范围内的位置记录
   * @param userId 用户ID
   * @param startDate 开始时间
   * @param endDate 结束时间
   * @param options 查询选项
   * @returns 位置记录列表
   */
  findUserLocationsByDateRange(
    userId: number,
    startDate: Date,
    endDate: Date,
    options?: {
      minLoginCount?: number;
      includeTrusted?: boolean;
    }
  ): Promise<UserCommonLocation[]>;

  /**
   * 保存或更新用户常用位置
   * @param userLocation 用户位置实体
   * @returns 保存后的实体
   */
  saveUserCommonLocation(userLocation: UserCommonLocation): Promise<UserCommonLocation>;

  /**
   * 批量更新可信位置
   * @param userId 用户ID
   * @param province 省份
   * @param city 城市
   * @param isTrusted 是否可信
   * @param trustReason 信任原因
   * @returns 更新的记录数
   */
  updateTrustedLocations(
    userId: number,
    province: string,
    city: string,
    isTrusted: boolean,
    trustReason?: string
  ): Promise<number>;

  /**
   * 获取用户位置统计信息
   * @param userId 用户ID
   * @param days 统计天数
   * @returns 统计信息
   */
  getUserLocationStats(
    userId: number,
    days: number
  ): Promise<{
    totalLocations: number;
    trustedLocations: number;
    totalLogins: number;
    riskLogins: number;
    uniqueProvinces: number;
    uniqueCities: number;
  }>;

  /**
   * 检查位置是否存在于用户历史中
   * @param userId 用户ID
   * @param location 地理位置
   * @param level 检查级别
   * @returns 是否存在
   */
  hasLocationInHistory(
    userId: number,
    location: GeographicLocation,
    level: 'country' | 'province' | 'city'
  ): Promise<boolean>;
}

/**
 * 命令仓储接口（写操作）
 */
export interface IIpLocationCommandRepository extends IIpLocationRepository {
  /**
   * 创建新的用户常用位置
   * @param userId 用户ID
   * @param location 地理位置
   * @param initialData 初始数据
   * @returns 创建的实体
   */
  createUserCommonLocation(
    userId: number,
    location: GeographicLocation,
    initialData?: Partial<UserCommonLocation>
  ): Promise<UserCommonLocation>;

  /**
   * 增加位置登录次数
   * @param userLocationId 用户位置ID
   * @param incrementBy 增加数量
   * @returns 更新后的实体
   */
  incrementLoginCount(
    userLocationId: number,
    incrementBy: number
  ): Promise<UserCommonLocation>;

  /**
   * 更新位置信任分数
   * @param userLocationId 用户位置ID
   * @param newTrustScore 新的信任分数
   * @returns 更新后的实体
   */
  updateTrustScore(
    userLocationId: number,
    newTrustScore: number
  ): Promise<UserCommonLocation>;

  /**
   * 删除用户位置记录
   * @param userLocationId 用户位置ID
   * @returns 是否删除成功
   */
  deleteUserCommonLocation(userLocationId: number): Promise<boolean>;
}

/**
 * 查询仓储接口（读操作）
 */
export interface IIpLocationQueryRepository extends IIpLocationRepository {
  /**
   * 获取用户最近的登录位置
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 最近的位置列表
   */
  getRecentLoginLocations(
    userId: number,
    limit: number
  ): Promise<UserCommonLocation[]>;

  /**
   * 搜索相似位置
   * @param userId 用户ID
   * @param location 目标位置
   * @param similarityThreshold 相似度阈值
   * @returns 相似位置列表
   */
  findSimilarLocations(
    userId: number,
    location: GeographicLocation,
    similarityThreshold: number
  ): Promise<UserCommonLocation[]>;

  /**
   * 获取用户的风险位置
   * @param userId 用户ID
   * @param riskThreshold 风险阈值
   * @returns 风险位置列表
   */
  getRiskLocations(
    userId: number,
    riskThreshold: number
  ): Promise<UserCommonLocation[]>;

  /**
   * 获取位置的登录趋势
   * @param userId 用户ID
   * @param location 地理位置
   * @param days 统计天数
   * @returns 登录趋势数据
   */
  getLocationLoginTrend(
    userId: number,
    location: GeographicLocation,
    days: number
  ): Promise<{
    date: string;
    loginCount: number;
  }[]>;
}
