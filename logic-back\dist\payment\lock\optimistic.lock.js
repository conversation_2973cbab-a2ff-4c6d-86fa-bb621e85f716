"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OptimisticLock_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimisticLock = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let OptimisticLock = OptimisticLock_1 = class OptimisticLock {
    connection;
    logger = new common_1.Logger(OptimisticLock_1.name);
    constructor(connection) {
        this.connection = connection;
    }
    async updateWithOptimisticLock(entityClass, id, updateFn, maxRetries = 3) {
        this.logger.log(`尝试使用乐观锁更新实体: ${entityClass.name}, ID=${id}, 最大重试次数=${maxRetries}`);
        let retries = 0;
        const startTime = Date.now();
        while (retries <= maxRetries) {
            try {
                return await this.connection.transaction(async (manager) => {
                    const repository = manager.getRepository(entityClass);
                    const entity = await repository.findOne({ where: { id } });
                    if (!entity) {
                        this.logger.warn(`乐观锁更新失败: 未找到实体 ${entityClass.name} 的记录, ID=${id}`);
                        throw new Error(`乐观锁更新失败: 未找到实体 ${entityClass.name} 的记录, ID=${id}`);
                    }
                    const currentVersion = entity['version'] !== undefined ? entity['version'] : 0;
                    this.logger.debug(`乐观锁获取实体: ${entityClass.name}, ID=${id}, 当前版本=${currentVersion}`);
                    const updatedEntity = await updateFn(entity);
                    this.logger.debug(`乐观锁准备保存更新: ${entityClass.name}, ID=${id}`);
                    const result = await repository.save(updatedEntity);
                    const newVersion = result['version'] !== undefined ? result['version'] : 0;
                    this.logger.log(`乐观锁更新成功: ${entityClass.name}, ID=${id}, 新版本=${newVersion}, 耗时=${Date.now() - startTime}ms`);
                    return result;
                });
            }
            catch (error) {
                if (error.name === 'OptimisticLockVersionMismatchError' && retries < maxRetries) {
                    retries++;
                    this.logger.warn(`乐观锁冲突，进行第${retries}次重试: ${entityClass.name}, ID=${id}`);
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 100));
                    continue;
                }
                this.logger.error(`乐观锁更新失败: ${entityClass.name}, ID=${id}, 错误: ${error.message}, 总耗时=${Date.now() - startTime}ms`, error.stack);
                throw error;
            }
        }
        throw new Error(`乐观锁更新失败，超过最大重试次数: ${maxRetries}`);
    }
    async withOptimisticLock(callback) {
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const result = await callback(queryRunner.manager);
            await queryRunner.commitTransaction();
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`乐观锁操作失败: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async bulkUpdateWithOptimisticLock(entities, entityClass) {
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const repository = queryRunner.manager.getRepository(entityClass);
            const result = await repository.save(entities);
            await queryRunner.commitTransaction();
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`批量乐观锁操作失败: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async createVersionedEntity(entityClass, data) {
        const repository = this.connection.getRepository(entityClass);
        const entity = repository.create({
            ...data,
            version: 1
        });
        return repository.save(entity);
    }
    async checkVersionConflict(entityClass, id, version) {
        const repository = this.connection.getRepository(entityClass);
        const entity = await repository.findOne(id);
        if (!entity) {
            return false;
        }
        return entity.version !== version;
    }
};
exports.OptimisticLock = OptimisticLock;
exports.OptimisticLock = OptimisticLock = OptimisticLock_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.Connection])
], OptimisticLock);
//# sourceMappingURL=optimistic.lock.js.map