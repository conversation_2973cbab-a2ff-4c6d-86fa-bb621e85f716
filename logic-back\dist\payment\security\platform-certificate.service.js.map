{"version": 3, "file": "platform-certificate.service.js", "sourceRoot": "", "sources": ["../../../src/payment/security/platform-certificate.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AASA,2CAAsF;AACtF,6EAAwE;AACxE,2EAAsE;AACtE,yBAAyB;AACzB,6BAA6B;AAC7B,iCAAiC;AACjC,iCAA0B;AAUnB,IAAM,0BAA0B,kCAAhC,MAAM,0BAA0B;IAMlB;IAEA;IAPF,MAAM,GAAG,IAAI,eAAM,CAAC,4BAA0B,CAAC,IAAI,CAAC,CAAC;IAC9D,YAAY,GAAqC,IAAI,GAAG,EAAE,CAAC;IAC3D,mBAAmB,CAAS;IAEpC,YACmB,aAAmC,EAEnC,gBAAyC;QAFzC,kBAAa,GAAb,aAAa,CAAsB;QAEnC,qBAAgB,GAAhB,gBAAgB,CAAyB;QAG1D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,iCAAiC,CAAC,CAAC;QAC1F,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,YAAY;QAEhB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAGpC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,wBAAwB,EAAE,EAAE,CAAC;gBACpE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;gBACnC,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAC1C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAKO,oBAAoB;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC7C,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC;YAEH,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAG1B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC7C,OAAO;YACT,CAAC;YAGD,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAGvD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;oBAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;oBAGzE,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,IAAI,CAAC;4BACH,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BACtD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;4BAElE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE;gCAC9B,QAAQ;gCACR,WAAW,EAAE,WAAW;gCACxB,aAAa,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;gCAClD,UAAU,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;6BAC7C,CAAC,CAAC;4BAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,QAAQ,WAAW,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC;wBAC/E,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,QAAQ,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC7D,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAKO,wBAAwB;QAE9B,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAE/D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,KAAK,CAAC,0BAA0B;QAC9B,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YAG7D,MAAM,MAAM,GAAG,KAAK,CAAC;YACrB,MAAM,GAAG,GAAG,kBAAkB,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAGrD,MAAM,OAAO,GAAG,GAAG,MAAM,KAAK,GAAG,KAAK,SAAS,KAAK,KAAK,MAAM,CAAC;YAGhE,MAAM,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;YAC7F,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAGlD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,+CAA+C,EAAE;gBAChF,OAAO,EAAE;oBACP,QAAQ,EAAE,kBAAkB;oBAC5B,eAAe,EAAE,oCAAoC,YAAY,CAAC,KAAK,gBAAgB,KAAK,gBAAgB,SAAS,gBAAgB,SAAS,gBAAgB,YAAY,CAAC,QAAQ,GAAG;iBACvL;aACF,CAAC,CAAC;YAGH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAE3B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC;YAGD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAChC,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC;gBAG7C,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CACtC,WAAW,CAAC,UAAU,EACtB,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,eAAe,CAC5B,CAAC;gBAGF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,QAAQ,MAAM,CAAC,CAAC;gBACxE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,QAAQ,OAAO,CAAC,CAAC;gBAEzE,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACrC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;oBACxC,QAAQ,EAAE,QAAQ;oBAClB,aAAa,EAAE,IAAI,CAAC,cAAc;oBAClC,UAAU,EAAE,IAAI,CAAC,WAAW;iBAC7B,CAAC,CAAC,CAAC;gBAGJ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE;oBAC9B,QAAQ;oBACR,WAAW,EAAE,QAAQ;oBACrB,aAAa,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;oBAC5C,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;iBACvC,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,QAAQ,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,YAAY,CAAC,CAAC;YACtD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,UAAkB,EAAE,KAAa,EAAE,cAAsB;QAClF,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YAE7D,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAGD,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YACrE,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CACtC,aAAa,EACb,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CACnB,CAAC;YAEF,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC7B,IAAI,cAAc,EAAE,CAAC;gBACnB,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAE/B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAMD,cAAc,CAAC,QAAgB;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,QAAQ,YAAY,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKD,oBAAoB;QAClB,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,UAAU,GAA+B,IAAI,CAAC;QAElD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC3D,UAAU,GAAG,IAAI,CAAC;YACpB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,WAAW,EAAE,UAAU,CAAC,WAAW;SACpC,CAAC;IACJ,CAAC;CACF,CAAA;AAlRY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAQR,WAAA,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,mDAAuB,CAAC,CAAC,CAAA;qCADlB,6CAAoB;QAEjB,mDAAuB;GARjD,0BAA0B,CAkRtC"}