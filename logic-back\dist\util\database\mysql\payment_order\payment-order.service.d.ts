import { Repository } from 'typeorm';
import { PaymentOrder } from './entities/payment-order.entity';
import { CreatePaymentOrderDto, PaymentStatus } from './dto/create-payment-order.dto';
import { UpdatePaymentOrderDto } from './dto/update-payment-order.dto';
export declare class PaymentOrderService {
    private readonly paymentOrderRepository;
    constructor(paymentOrderRepository: Repository<PaymentOrder>);
    create(createPaymentOrderDto: CreatePaymentOrderDto): Promise<PaymentOrder>;
    findAll(): Promise<PaymentOrder[]>;
    findOne(id: string): Promise<PaymentOrder>;
    findByBusinessOrderId(businessOrderId: string): Promise<PaymentOrder>;
    findByChannelOrderId(channelOrderId: string): Promise<PaymentOrder>;
    findByUserId(userId: string): Promise<PaymentOrder[]>;
    findByStatus(status: PaymentStatus): Promise<PaymentOrder[]>;
    update(id: string, updatePaymentOrderDto: UpdatePaymentOrderDto): Promise<PaymentOrder>;
    updateStatus(id: string, status: PaymentStatus, result?: any): Promise<PaymentOrder>;
    updateNotifyData(id: string, notifyData: any): Promise<PaymentOrder>;
    remove(id: string): Promise<void>;
}
