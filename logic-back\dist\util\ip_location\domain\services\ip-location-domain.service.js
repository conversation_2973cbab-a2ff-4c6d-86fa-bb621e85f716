"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationDomainService = void 0;
const common_1 = require("@nestjs/common");
const ip_address_vo_1 = require("../value-objects/ip-address.vo");
const geographic_location_vo_1 = require("../value-objects/geographic-location.vo");
const location_not_found_exception_1 = require("../exceptions/location-not-found.exception");
const IP2Region = require('ip2region').default;
let IpLocationDomainService = class IpLocationDomainService {
    ip2region;
    constructor() {
        this.initializeIp2Region();
    }
    async resolveLocation(ipAddress) {
        this.validateIpForGeolocation(ipAddress);
        if (ipAddress.isLoopback && this.isDevelopmentEnvironment()) {
            return this.createDefaultLocationForDevelopment(ipAddress.value);
        }
        try {
            const rawResult = this.ip2region.search(ipAddress.value);
            if (!rawResult) {
                throw location_not_found_exception_1.LocationNotFoundException.ipResolutionFailed(ipAddress.value, 'ip2region返回空结果');
            }
            const location = geographic_location_vo_1.GeographicLocation.fromIp2RegionData(rawResult);
            this.validateLocationQuality(location, ipAddress);
            return location;
        }
        catch (error) {
            if (error instanceof location_not_found_exception_1.LocationNotFoundException) {
                throw error;
            }
            throw location_not_found_exception_1.LocationNotFoundException.ipResolutionFailed(ipAddress.value, error.message);
        }
    }
    async resolveMultipleLocations(ipAddresses) {
        const results = [];
        for (const ipAddress of ipAddresses) {
            try {
                const location = await this.resolveLocation(ipAddress);
                results.push(location);
            }
            catch (error) {
                results.push(geographic_location_vo_1.GeographicLocation.createUnknown());
            }
        }
        return results;
    }
    canResolveLocation(ipAddress) {
        return ipAddress.canGeolocate;
    }
    getIpBasicInfo(ipAddress) {
        return {
            value: ipAddress.value,
            type: ipAddress.type,
            isPrivate: ipAddress.isPrivate,
            isLoopback: ipAddress.isLoopback,
            isPublic: ipAddress.isPublic,
            canGeolocate: ipAddress.canGeolocate,
            masked: ipAddress.masked
        };
    }
    async compareLocations(ipAddress1, ipAddress2) {
        const location1 = await this.resolveLocation(ipAddress1);
        const location2 = await this.resolveLocation(ipAddress2);
        return {
            location1,
            location2,
            similarity: location1.calculateSimilarity(location2),
            isSameCountry: location1.country === location2.country,
            isSameProvince: location1.isSameProvince(location2),
            isSameCity: location1.isSameCity(location2),
            isSameISP: location1.isSameISP(location2)
        };
    }
    initializeIp2Region() {
        try {
            this.ip2region = new IP2Region();
        }
        catch (error) {
            throw new Error(`ip2region初始化失败: ${error.message}`);
        }
    }
    validateIpForGeolocation(ipAddress) {
        if (ipAddress.isPrivate) {
            throw location_not_found_exception_1.LocationNotFoundException.privateIpAddress(ipAddress.value);
        }
        if (ipAddress.isLoopback) {
            throw location_not_found_exception_1.LocationNotFoundException.loopbackIpAddress(ipAddress.value);
        }
        if (!ipAddress.canGeolocate) {
            if (ipAddress.isLoopback && this.isDevelopmentEnvironment()) {
                return;
            }
            throw location_not_found_exception_1.LocationNotFoundException.ipResolutionFailed(ipAddress.value, 'IP地址不支持地理位置解析');
        }
    }
    validateLocationQuality(location, ipAddress) {
        if (location.confidence < 20) {
            throw location_not_found_exception_1.LocationNotFoundException.lowDataQuality(ipAddress.value, location.confidence);
        }
        if (location.hasEmptyFields && location.confidence === 0) {
            throw location_not_found_exception_1.LocationNotFoundException.ipResolutionFailed(ipAddress.value, '解析结果为空');
        }
    }
    getSupportedIpTypes() {
        return ['IPv4', 'IPv6'];
    }
    getServiceStatus() {
        return {
            isAvailable: !!this.ip2region,
            version: 'ip2region-v2.0',
            supportedTypes: this.getSupportedIpTypes()
        };
    }
    async testResolution(testIp = '*******') {
        const startTime = Date.now();
        try {
            const ipAddress = ip_address_vo_1.IpAddress.create(testIp);
            const location = await this.resolveLocation(ipAddress);
            return {
                success: true,
                ipAddress,
                location,
                performanceMs: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                performanceMs: Date.now() - startTime
            };
        }
    }
    isDevelopmentEnvironment() {
        return process.env.NODE_ENV === 'development' ||
            process.env.NODE_ENV === 'dev' ||
            !process.env.NODE_ENV;
    }
    createDefaultLocationForDevelopment(_ip) {
        return geographic_location_vo_1.GeographicLocation.create('中国', '广东省', '广州市', '本地开发环境');
    }
};
exports.IpLocationDomainService = IpLocationDomainService;
exports.IpLocationDomainService = IpLocationDomainService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], IpLocationDomainService);
//# sourceMappingURL=ip-location-domain.service.js.map