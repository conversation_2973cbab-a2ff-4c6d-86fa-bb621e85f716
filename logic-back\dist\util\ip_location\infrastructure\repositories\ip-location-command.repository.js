"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationCommandRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_common_location_entity_1 = require("../../domain/entities/user-common-location.entity");
let IpLocationCommandRepository = class IpLocationCommandRepository {
    repository;
    constructor(repository) {
        this.repository = repository;
    }
    async findUserCommonLocation(userId, location) {
        return await this.repository.findOne({
            where: {
                userId,
                country: location.country,
                province: location.province,
                city: location.city
            }
        });
    }
    async findUserCommonLocations(userId, options = {}) {
        const queryBuilder = this.repository.createQueryBuilder('location')
            .where('location.userId = :userId', { userId });
        if (options.onlyTrusted) {
            queryBuilder.andWhere('location.isTrusted = :isTrusted', { isTrusted: true });
        }
        if (options.minLoginCount) {
            queryBuilder.andWhere('location.loginCount >= :minLoginCount', {
                minLoginCount: options.minLoginCount
            });
        }
        if (options.orderBy) {
            queryBuilder.orderBy(`location.${options.orderBy}`, options.orderDirection || 'DESC');
        }
        if (options.limit) {
            queryBuilder.limit(options.limit);
        }
        return await queryBuilder.getMany();
    }
    async findUserLocationsByDateRange(userId, startDate, endDate, options = {}) {
        const queryBuilder = this.repository.createQueryBuilder('location')
            .where('location.userId = :userId', { userId })
            .andWhere('location.lastLoginAt >= :startDate', { startDate })
            .andWhere('location.lastLoginAt <= :endDate', { endDate });
        if (options.minLoginCount) {
            queryBuilder.andWhere('location.loginCount >= :minLoginCount', {
                minLoginCount: options.minLoginCount
            });
        }
        if (options.includeTrusted === false) {
            queryBuilder.andWhere('location.isTrusted = :isTrusted', { isTrusted: false });
        }
        return await queryBuilder.getMany();
    }
    async saveUserCommonLocation(userLocation) {
        return await this.repository.save(userLocation);
    }
    async createUserCommonLocation(userId, location, initialData = {}) {
        const now = new Date();
        const userLocation = this.repository.create({
            userId,
            country: location.country,
            province: location.province,
            city: location.city,
            isp: location.isp,
            loginCount: 1,
            lastLoginAt: now,
            firstLoginAt: now,
            isTrusted: false,
            trustScore: this.calculateInitialTrustScore(location),
            ...initialData
        });
        const saved = await this.repository.save(userLocation);
        return saved;
    }
    async incrementLoginCount(userLocationId, incrementBy = 1) {
        await this.repository.increment({ id: userLocationId }, 'loginCount', incrementBy);
        await this.repository.update(userLocationId, {
            lastLoginAt: new Date()
        });
        const updated = await this.repository.findOne({
            where: { id: userLocationId }
        });
        if (!updated) {
            throw new Error(`用户位置记录不存在: ${userLocationId}`);
        }
        return updated;
    }
    async updateTrustedLocations(userId, province, city, isTrusted, trustReason) {
        const updateData = {
            isTrusted,
            trustedAt: new Date()
        };
        if (trustReason) {
            updateData.trustReason = trustReason;
        }
        if (isTrusted) {
            updateData.trustScore = () => 'LEAST(trust_score + 20, 100)';
        }
        const result = await this.repository.update({
            userId,
            province,
            city
        }, updateData);
        return result.affected || 0;
    }
    async updateTrustScore(userLocationId, newTrustScore) {
        await this.repository.update(userLocationId, {
            trustScore: Math.max(0, Math.min(100, newTrustScore))
        });
        const updated = await this.repository.findOne({
            where: { id: userLocationId }
        });
        if (!updated) {
            throw new Error(`用户位置记录不存在: ${userLocationId}`);
        }
        return updated;
    }
    async deleteUserCommonLocation(userLocationId) {
        const result = await this.repository.delete(userLocationId);
        return (result.affected || 0) > 0;
    }
    async getUserLocationStats(userId, days) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const locations = await this.findUserLocationsByDateRange(userId, startDate, new Date());
        return {
            totalLocations: locations.length,
            trustedLocations: locations.filter(l => l.isTrusted).length,
            totalLogins: locations.reduce((sum, l) => sum + l.loginCount, 0),
            riskLogins: locations.filter(l => l.trustScore < 50).reduce((sum, l) => sum + l.loginCount, 0),
            uniqueProvinces: new Set(locations.map(l => l.province)).size,
            uniqueCities: new Set(locations.map(l => l.city)).size
        };
    }
    async hasLocationInHistory(userId, location, level) {
        const queryBuilder = this.repository.createQueryBuilder('location')
            .where('location.userId = :userId', { userId });
        switch (level) {
            case 'country':
                queryBuilder.andWhere('location.country = :country', {
                    country: location.country
                });
                break;
            case 'province':
                queryBuilder.andWhere('location.country = :country', {
                    country: location.country
                })
                    .andWhere('location.province = :province', {
                    province: location.province
                });
                break;
            case 'city':
                queryBuilder.andWhere('location.country = :country', {
                    country: location.country
                })
                    .andWhere('location.province = :province', {
                    province: location.province
                })
                    .andWhere('location.city = :city', {
                    city: location.city
                });
                break;
        }
        const count = await queryBuilder.getCount();
        return count > 0;
    }
    calculateInitialTrustScore(location) {
        let score = 50;
        if (location.isHighQuality) {
            score += 20;
        }
        else if (location.confidence > 50) {
            score += 10;
        }
        if (location.isDomestic) {
            score += 10;
        }
        return Math.min(score, 100);
    }
};
exports.IpLocationCommandRepository = IpLocationCommandRepository;
exports.IpLocationCommandRepository = IpLocationCommandRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_common_location_entity_1.UserCommonLocation)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], IpLocationCommandRepository);
//# sourceMappingURL=ip-location-command.repository.js.map