import { OnModuleInit } from '@nestjs/common';
import { LoggerService } from '../../../../common/logger/logger.service';
import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
export declare class Ip2RegionService implements OnModuleInit {
    private readonly logger;
    private ip2region;
    private isInitialized;
    constructor(logger: LoggerService);
    onModuleInit(): Promise<void>;
    private initialize;
    resolveLocation(ipAddress: IpAddress): Promise<GeographicLocation>;
    resolveMultipleLocations(ipAddresses: IpAddress[]): Promise<GeographicLocation[]>;
    testResolution(testIp?: string): Promise<{
        success: boolean;
        location?: GeographicLocation;
        error?: string;
        performanceMs: number;
    }>;
    getServiceStatus(): {
        isAvailable: boolean;
        isInitialized: boolean;
        version: string;
        supportedTypes: string[];
    };
    getRawInstance(): any;
    private convertRawDataToLocation;
    private normalizeField;
    reinitialize(): Promise<void>;
    canResolve(ipAddress: IpAddress): boolean;
    getPerformanceStats(): Promise<{
        averageResponseTime: number;
        successRate: number;
        totalRequests: number;
    }>;
}
