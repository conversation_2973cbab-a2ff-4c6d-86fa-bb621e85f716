import { LocationInfo, LocationRisk } from './ip-location.util';
import { LoggerService } from '../../../common/logger/logger.service';
export interface UserLocationStats {
    commonLocations: Array<{
        province: string;
        city: string;
        loginCount: number;
        isTrusted: boolean;
        lastLoginAt: Date;
    }>;
    riskLoginCount: number;
    totalLoginCount: number;
}
interface RiskConfig {
    lowThreshold: number;
    mediumThreshold: number;
    highThreshold: number;
    foreignLoginWeight: number;
    crossProvinceWeight: number;
    newLocationWeight: number;
    ispChangeWeight: number;
    dataQualityWeight: number;
    newUserProtectionDays: number;
    newUserRiskReduction: number;
}
export declare class RiskAssessmentUtil {
    private readonly loggerService;
    private readonly riskConfig;
    constructor(loggerService: LoggerService);
    assessLoginRisk(userId: number, currentLocation: LocationInfo, userHistory: UserLocationStats): Promise<LocationRisk>;
    private calculateRiskScore;
    private determineRiskLevel;
    private generateRiskReason;
    needAdditionalVerification(risk: LocationRisk, userHistory: UserLocationStats): boolean;
    getRecommendedVerificationMethods(risk: LocationRisk): string[];
    updateRiskConfig(newConfig: Partial<RiskConfig>): void;
    getRiskConfig(): RiskConfig;
}
export {};
