export declare class GetUserLocationStatsQuery {
    readonly userId: number;
    readonly days: number;
    readonly includeTrusted: boolean;
    readonly includeRiskAnalysis: boolean;
    readonly minLoginCount: number;
    readonly timestamp: Date;
    constructor(userId: number, days?: number, includeTrusted?: boolean, includeRiskAnalysis?: boolean, minLoginCount?: number);
    static createBasic(userId: number, days?: number): GetUserLocationStatsQuery;
    static createWithRiskAnalysis(userId: number, days?: number): GetUserLocationStatsQuery;
    static createTrustedOnly(userId: number, days?: number): GetUserLocationStatsQuery;
    static createActiveLocations(userId: number, days?: number, minLoginCount?: number): GetUserLocationStatsQuery;
    validate(): {
        isValid: boolean;
        errors: string[];
    };
    getStartDate(): Date;
    getEndDate(): Date;
    getCacheKey(): string;
    getSummary(): string;
    get isLongTermQuery(): boolean;
    get isShortTermQuery(): boolean;
    getQueryType(): string;
    getExpectedDataVolume(): 'LOW' | 'MEDIUM' | 'HIGH';
    toJSON(): object;
}
