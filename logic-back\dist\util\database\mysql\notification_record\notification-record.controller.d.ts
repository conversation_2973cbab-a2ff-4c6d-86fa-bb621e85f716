import { NotificationRecordService } from './notification-record.service';
import { NotificationRecord } from './entities/notification-record.entity';
import { CreateNotificationRecordDto, UpdateNotificationRecordDto, QueryNotificationRecordDto, NotificationStatus } from './dto/notification-record.dto';
export declare class NotificationRecordController {
    private readonly notificationRecordService;
    private readonly logger;
    constructor(notificationRecordService: NotificationRecordService);
    create(createDto: CreateNotificationRecordDto): Promise<{
        code: number;
        message: string;
        data: NotificationRecord;
    }>;
    findAll(queryDto: QueryNotificationRecordDto, page?: number, limit?: number): Promise<{
        code: number;
        message: string;
        data: {
            records: NotificationRecord[];
            total: number;
            page: number;
            limit: number;
            pages: number;
        };
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        data: NotificationRecord | null;
    }>;
    update(id: string, updateDto: UpdateNotificationRecordDto): Promise<{
        code: number;
        message: string;
        data: NotificationRecord;
    }>;
    remove(id: string): Promise<{
        code: number;
        message: string;
    }>;
    markForRetry(id: string): Promise<{
        code: number;
        message: string;
        data?: undefined;
    } | {
        code: number;
        message: string;
        data: NotificationRecord;
    }>;
    batchUpdateStatus(payload: {
        ids: string[];
        status: NotificationStatus;
    }): Promise<{
        code: number;
        message: string;
    }>;
}
