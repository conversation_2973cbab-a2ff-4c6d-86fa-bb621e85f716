"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrderService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const package_order_entity_1 = require("./entities/package-order.entity");
const dto_1 = require("./dto");
let PackageOrderService = class PackageOrderService {
    packageOrderRepository;
    constructor(packageOrderRepository) {
        this.packageOrderRepository = packageOrderRepository;
    }
    async create(createPackageOrderDto) {
        const packageOrder = this.packageOrderRepository.create({
            ...createPackageOrderDto,
            status: createPackageOrderDto.status || dto_1.PackageOrderStatus.PENDING,
        });
        return await this.packageOrderRepository.save(packageOrder);
    }
    async findAll() {
        return await this.packageOrderRepository.find({
            order: { createTime: 'DESC' }
        });
    }
    async findOne(id) {
        const packageOrder = await this.packageOrderRepository.findOne({ where: { id } });
        if (!packageOrder) {
            throw new common_1.NotFoundException(`订单ID为${id}的信息不存在`);
        }
        return packageOrder;
    }
    async findByOrderNo(orderNo) {
        const packageOrder = await this.packageOrderRepository.findOne({ where: { orderNo } });
        if (!packageOrder) {
            throw new common_1.NotFoundException(`订单编号为${orderNo}的信息不存在`);
        }
        return packageOrder;
    }
    async update(id, updatePackageOrderDto) {
        const packageOrder = await this.findOne(id);
        Object.assign(packageOrder, updatePackageOrderDto);
        return await this.packageOrderRepository.save(packageOrder);
    }
    async remove(id) {
        const packageOrder = await this.findOne(id);
        await this.packageOrderRepository.remove(packageOrder);
    }
    async findByUserId(userId) {
        return await this.packageOrderRepository.find({
            where: { userId },
            order: { createTime: 'DESC' }
        });
    }
    async findWithPagination(queryDto) {
        const { orderNo, userId, packageId, packageName, status, paymentId, startTime, endTime, page = 1, limit = 10, sortBy = 'createTime', sortOrder = 'DESC' } = queryDto;
        const where = {};
        if (orderNo) {
            where.orderNo = (0, typeorm_2.Like)(`%${orderNo}%`);
        }
        if (userId) {
            where.userId = userId;
        }
        if (packageId) {
            where.packageId = packageId;
        }
        if (packageName) {
            where.packageName = (0, typeorm_2.Like)(`%${packageName}%`);
        }
        if (status) {
            where.status = status;
        }
        if (paymentId) {
            where.paymentId = paymentId;
        }
        if (startTime && endTime) {
            where.createTime = (0, typeorm_2.Between)(new Date(startTime), new Date(endTime));
        }
        else if (startTime) {
            where.createTime = (0, typeorm_2.Between)(new Date(startTime), new Date());
        }
        else if (endTime) {
            where.createTime = (0, typeorm_2.Between)(new Date('1970-01-01'), new Date(endTime));
        }
        const options = {
            where,
            order: { [sortBy]: sortOrder },
            skip: (page - 1) * limit,
            take: limit,
        };
        const [data, total] = await this.packageOrderRepository.findAndCount(options);
        return {
            data,
            total,
            page,
            limit,
        };
    }
    async updatePaymentStatus(orderNo, paymentId, status = dto_1.PackageOrderStatus.PAID) {
        const packageOrder = await this.findByOrderNo(orderNo);
        packageOrder.status = status;
        packageOrder.paymentId = paymentId;
        packageOrder.paidTime = status === dto_1.PackageOrderStatus.PAID ? new Date() : undefined;
        return await this.packageOrderRepository.save(packageOrder);
    }
    async cancelOrder(orderNo) {
        const packageOrder = await this.findByOrderNo(orderNo);
        if (packageOrder.status === dto_1.PackageOrderStatus.PAID) {
            throw new Error('已支付的订单无法取消');
        }
        packageOrder.status = dto_1.PackageOrderStatus.CANCELLED;
        return await this.packageOrderRepository.save(packageOrder);
    }
    async getUserOrderStats(userId) {
        const [total, pending, paid, cancelled] = await Promise.all([
            this.packageOrderRepository.count({ where: { userId } }),
            this.packageOrderRepository.count({ where: { userId, status: dto_1.PackageOrderStatus.PENDING } }),
            this.packageOrderRepository.count({ where: { userId, status: dto_1.PackageOrderStatus.PAID } }),
            this.packageOrderRepository.count({ where: { userId, status: dto_1.PackageOrderStatus.CANCELLED } }),
        ]);
        return { total, pending, paid, cancelled };
    }
};
exports.PackageOrderService = PackageOrderService;
exports.PackageOrderService = PackageOrderService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(package_order_entity_1.PackageOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PackageOrderService);
//# sourceMappingURL=package-order.service.js.map