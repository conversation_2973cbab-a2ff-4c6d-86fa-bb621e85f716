"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryPackageOrderDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const create_package_order_dto_1 = require("./create-package-order.dto");
class QueryPackageOrderDto {
    orderNo;
    userId;
    packageId;
    packageName;
    status;
    paymentId;
    startTime;
    endTime;
    page = 1;
    limit = 10;
    sortBy = 'createTime';
    sortOrder = 'DESC';
}
exports.QueryPackageOrderDto = QueryPackageOrderDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单编号必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '订单编号', required: false, example: 'PKG_ORDER_20231010001' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "orderNo", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户ID必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false, example: 'user_123456' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '套餐ID必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '套餐ID', required: false, example: 1 }),
    __metadata("design:type", Number)
], QueryPackageOrderDto.prototype, "packageId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '套餐名称必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '套餐名称', required: false, example: '基础套餐' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "packageName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(create_package_order_dto_1.PackageOrderStatus, { message: '无效的订单状态' }),
    (0, swagger_1.ApiProperty)({
        description: '订单状态',
        enum: create_package_order_dto_1.PackageOrderStatus,
        required: false
    }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '支付平台交易号必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '支付平台交易号', required: false, example: 'PAY_123456789' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "paymentId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '开始时间格式不正确' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间开始', required: false, example: '2023-10-01T00:00:00.000Z' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "startTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '结束时间格式不正确' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间结束', required: false, example: '2023-10-31T23:59:59.999Z' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "endTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '页码', required: false, default: 1, example: 1 }),
    __metadata("design:type", Number)
], QueryPackageOrderDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '每页数量', required: false, default: 10, example: 10 }),
    __metadata("design:type", Number)
], QueryPackageOrderDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '排序字段必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '排序字段', required: false, default: 'createTime', example: 'createTime' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "sortBy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '排序方向必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '排序方向', required: false, default: 'DESC', example: 'DESC' }),
    __metadata("design:type", String)
], QueryPackageOrderDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=query-package-order.dto.js.map