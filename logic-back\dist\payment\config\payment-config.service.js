"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentConfigService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const defaultAlipayConfig = {
    appId: '',
    privateKey: '',
    publicKey: '',
    aliPublicKey: '',
    gateway: 'https://openapi.alipay.com/gateway.do',
    notifyUrl: '/v1/payment/notify/alipay',
    returnUrl: '/v1/payment/return/alipay',
    signType: 'RSA2',
};
const defaultWechatPayConfig = {
    appId: '',
    mchId: '',
    mchKey: '',
    privateKeyPath: './keys/wechatpay/apiclient_key.pem',
    certificatePath: './keys/wechatpay/apiclient_cert.pem',
    notifyUrl: '/v1/payment/notify/wechat',
    refundNotifyUrl: '/v1/payment/notify/wechat/refund',
    signType: 'HMAC-SHA256',
    serialNo: '',
    apiV3Key: '',
    wechatPayPublicKeyPath: './keys/wechatpay/pub_key.pem',
    returnUrl: '/v1/payment/return/wechat',
};
const defaultCommonConfig = {
    notifyDomain: 'http://7uz4301cb969.vicp.fun',
    orderExpireTime: 7200,
    retryInterval: 60,
    maxRetryCount: 5,
    defaultLockType: 'distributed',
};
let PaymentConfigService = class PaymentConfigService {
    configService;
    constructor(configService) {
        this.configService = configService;
        this.checkWechatPayConfig();
    }
    checkWechatPayConfig() {
        try {
            const config = this.getWechatPayConfig();
            if (!config.wechatPayPublicKeyPath) {
                console.warn('未配置微信支付平台证书路径(wechatPayPublicKeyPath)，这将导致无法验证回调通知');
            }
            if (!config.apiV3Key) {
                console.warn('未配置微信支付APIv3密钥(apiV3Key)，这将导致无法解密回调通知数据');
            }
            console.log('微信支付配置检查完成');
        }
        catch (error) {
            console.error(`微信支付配置检查失败: ${error.message}`);
        }
    }
    getAlipayConfig() {
        return this.configService.get('payment.alipay') || defaultAlipayConfig;
    }
    getWechatPayConfig() {
        return this.configService.get('payment.wechatpay') || defaultWechatPayConfig;
    }
    getCommonConfig() {
        return this.configService.get('payment.common') || defaultCommonConfig;
    }
    getNotifyUrl(channel) {
        const domain = this.getCommonConfig().notifyDomain;
        if (channel === 'alipay') {
            return `${domain}${this.getAlipayConfig().notifyUrl}`;
        }
        else if (channel === 'wechatpay') {
            return `${domain}${this.getWechatPayConfig().notifyUrl}`;
        }
        else if (channel === 'alipay_refund') {
            return `${domain}/v1/payment/notify/alipay/refund`;
        }
        else if (channel === 'wechatpay_refund') {
            return `${domain}${this.getWechatPayConfig().refundNotifyUrl}`;
        }
        throw new Error(`不支持的支付渠道: ${channel}`);
    }
    getReturnUrl(channel) {
        const domain = this.getCommonConfig().notifyDomain;
        if (channel === 'alipay') {
            return `${domain}${this.getAlipayConfig().returnUrl}`;
        }
        else if (channel === 'wechatpay') {
            return `${domain}${this.getWechatPayConfig().returnUrl}`;
        }
        throw new Error(`不支持的支付渠道或渠道不支持回调URL: ${channel}`);
    }
};
exports.PaymentConfigService = PaymentConfigService;
exports.PaymentConfigService = PaymentConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PaymentConfigService);
//# sourceMappingURL=payment-config.service.js.map