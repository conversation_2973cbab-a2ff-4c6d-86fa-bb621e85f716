export declare enum PaymentChannel {
    ALIPAY = "alipay",
    WECHAT = "wechat",
    UNIONPAY = "unionpay"
}
export declare enum PaymentStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SUCCESS = "success",
    FAILED = "failed",
    CLOSED = "closed"
}
export declare class CreatePaymentOrderDto {
    businessOrderId: string;
    amount: number;
    description: string;
    channel: PaymentChannel;
    channelOrderId?: string;
    status?: PaymentStatus;
    parameters?: Record<string, any>;
    userId?: string;
    clientIp?: string;
    notifyUrl?: string;
    returnUrl?: string;
    cancelUrl?: string;
    expiredAt?: Date;
}
