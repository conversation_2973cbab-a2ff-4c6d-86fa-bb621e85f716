import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
export declare class SetTrustedLocationCommand {
    readonly userId: number;
    readonly province: string;
    readonly city: string;
    readonly reason: string;
    readonly setBy: 'USER' | 'SYSTEM' | 'ADMIN';
    readonly timestamp: Date;
    constructor(userId: number, province: string, city: string, reason?: string, setBy?: 'USER' | 'SYSTEM' | 'ADMIN');
    static createByUser(userId: number, province: string, city: string, reason?: string): SetTrustedLocationCommand;
    static createBySystem(userId: number, province: string, city: string, reason: string): SetTrustedLocationCommand;
    static createByAdmin(userId: number, province: string, city: string, reason: string): SetTrustedLocationCommand;
    static fromLocation(userId: number, location: GeographicLocation, reason: string, setBy?: 'USER' | 'SYSTEM' | 'ADMIN'): SetTrustedLocationCommand;
    validate(): {
        isValid: boolean;
        errors: string[];
    };
    getLocationDescription(): string;
    getSummary(): string;
    isDomesticLocation(): boolean;
    toJSON(): object;
}
