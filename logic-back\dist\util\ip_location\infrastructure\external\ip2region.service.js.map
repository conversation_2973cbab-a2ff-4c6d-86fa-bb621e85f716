{"version": 3, "file": "ip2region.service.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/infrastructure/external/ip2region.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA0D;AAC1D,6EAAyE;AACzE,4EAAqE;AACrE,8FAAuF;AACvF,uGAAiG;AAGjG,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;AAOxC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAKR;IAJX,SAAS,CAAM;IACf,aAAa,GAAG,KAAK,CAAC;IAE9B,YACmB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IACrC,CAAC;IAKJ,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAKO,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,eAAe,CAAC,SAAoB;QACxC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEzD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,wDAAyB,CAAC,kBAAkB,CAChD,SAAS,CAAC,KAAK,EACf,gBAAgB,CACjB,CAAC;YACJ,CAAC;YAGD,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAElD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,wDAAyB,EAAE,CAAC;gBAC/C,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,aAAa,SAAS,CAAC,KAAK,EAAE,EAC9B,KAAK,EACL,kBAAkB,CACnB,CAAC;YAEF,MAAM,wDAAyB,CAAC,kBAAkB,CAChD,SAAS,CAAC,KAAK,EACf,KAAK,CAAC,OAAO,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,wBAAwB,CAAC,WAAwB;QACrD,MAAM,OAAO,GAAyB,EAAE,CAAC;QAEzC,KAAK,MAAM,SAAS,IAAI,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,kBAAkB,SAAS,CAAC,KAAK,EAAE,EACnC,kBAAkB,CACnB,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,2CAAkB,CAAC,aAAa,EAAE,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAOD,KAAK,CAAC,cAAc,CAAC,SAAiB,SAAS;QAM7C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,yBAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEvD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,QAAQ;gBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,gBAAgB;QAMd,OAAO;YACL,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,OAAO,EAAE,gBAAgB;YACzB,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;SACjC,CAAC;IACJ,CAAC;IAKD,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAKO,wBAAwB,CAAC,OAAY;QAG3C,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,GAAG,GAAG,IAAI,CAAC;QAEf,IAAI,OAAO,EAAE,CAAC;YAEZ,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACjC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACtB,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAChE,CAAC;YACH,CAAC;iBAEI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBACrC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;gBAClE,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACrE,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;gBACzD,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAGD,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC;aAC/C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;QAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,OAAO,2CAAkB,CAAC,MAAM,CAC9B,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,GAAG,EACH,WAAW,EACX,UAAU,CACX,CAAC;IACJ,CAAC;IAKO,cAAc,CAAC,KAAU;QAC/B,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACrF,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IAC1B,CAAC;IAKD,UAAU,CAAC,SAAoB;QAC7B,OAAO,SAAS,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;IACtD,CAAC;IAKD,KAAK,CAAC,mBAAmB;QAOvB,OAAO;YACL,mBAAmB,EAAE,EAAE;YACvB,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,IAAI;SACpB,CAAC;IACJ,CAAC;CACF,CAAA;AAjPY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAMgB,8BAAa;GAL7B,gBAAgB,CAiP5B"}