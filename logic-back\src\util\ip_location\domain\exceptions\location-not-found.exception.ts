import { IpLocationDomainException } from './ip-location-domain.exception';

/**
 * 位置信息未找到异常
 * 当无法解析IP地址的地理位置信息时抛出
 */
export class LocationNotFoundException extends IpLocationDomainException {
  constructor(
    message: string,
    ipAddress?: string,
    context?: Record<string, any>
  ) {
    super(
      message,
      'LOCATION_NOT_FOUND',
      {
        ipAddress,
        ...context
      }
    );
  }

  /**
   * 创建IP解析失败异常
   */
  static ipResolutionFailed(ipAddress: string, reason?: string): LocationNotFoundException {
    return new LocationNotFoundException(
      `无法解析IP地址的地理位置: ${ipAddress}`,
      ipAddress,
      { 
        reason: reason || 'resolution_failed',
        service: 'ip2region'
      }
    );
  }

  /**
   * 创建数据库查询失败异常
   */
  static databaseQueryFailed(ipAddress: string, error?: Error): LocationNotFoundException {
    return new LocationNotFoundException(
      `地理位置数据库查询失败: ${ipAddress}`,
      ipAddress,
      { 
        reason: 'database_query_failed',
        originalError: error?.message
      }
    );
  }

  /**
   * 创建私有IP异常
   */
  static privateIpAddress(ipAddress: string): LocationNotFoundException {
    return new LocationNotFoundException(
      `私有IP地址无法进行地理位置解析: ${ipAddress}`,
      ipAddress,
      { 
        reason: 'private_ip_address',
        isPrivate: true
      }
    );
  }

  /**
   * 创建回环IP异常
   */
  static loopbackIpAddress(ipAddress: string): LocationNotFoundException {
    return new LocationNotFoundException(
      `回环IP地址无法进行地理位置解析: ${ipAddress}`,
      ipAddress,
      { 
        reason: 'loopback_ip_address',
        isLoopback: true
      }
    );
  }

  /**
   * 创建服务不可用异常
   */
  static serviceUnavailable(ipAddress: string, service: string): LocationNotFoundException {
    return new LocationNotFoundException(
      `地理位置解析服务不可用: ${service}`,
      ipAddress,
      { 
        reason: 'service_unavailable',
        service
      }
    );
  }

  /**
   * 创建数据质量过低异常
   */
  static lowDataQuality(ipAddress: string, confidence: number): LocationNotFoundException {
    return new LocationNotFoundException(
      `地理位置数据质量过低: ${confidence}%`,
      ipAddress,
      { 
        reason: 'low_data_quality',
        confidence
      }
    );
  }
}
