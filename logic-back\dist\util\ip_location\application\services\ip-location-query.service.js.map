{"version": 3, "file": "ip-location-query.service.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/application/services/ip-location-query.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AAQrC,mFAAuI;AAGvI,iGAA2F;AAC3F,yGAAwH;AACxH,mGAA8F;AAG9F,8FAAuF;AAIvF,mGAAuF;AAGvF,uGAAiG;AAuG1F,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAGd;IACA;IACA;IACA;IALnB,YAEmB,sBAAsD,EACtD,uBAAgD,EAChD,qBAAkD,EAClD,yBAAoD;QAHpD,2BAAsB,GAAtB,sBAAsB,CAAgC;QACtD,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,0BAAqB,GAArB,qBAAqB,CAA6B;QAClD,8BAAyB,GAAzB,yBAAyB,CAA2B;IACpE,CAAC;IAKJ,KAAK,CAAC,qBAAqB,CACzB,KAA2B;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAA,kDAAwB,EAC7B,UAAU,CAAC,MAAM,EACjB,QAAQ,EACR,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAGD,IAAI,SAAS,GAAG,KAAK,CAAC;YAGtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAGrF,MAAM,MAAM,GAAwB;gBAClC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;gBACzB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,GAAG,EAAE,QAAQ,CAAC,GAAG;gBACjB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC,CAAC;YAGF,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAE9B,MAAM,gBAAgB,GAAwB;oBAC5C,eAAe,EAAE,EAAE;oBACnB,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,UAAU,EAAE,CAAC;iBACd,CAAC;gBAEF,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAC1D,QAAQ,EACR,gBAAgB,EAChB,KAAK,CAAC,SAAS,CAChB,CAAC;gBAEF,MAAM,CAAC,cAAc,GAAG;oBACtB,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,OAAO,EAAE,SAAS,CAAC,OAAO;oBAC1B,gBAAgB,EAAE,SAAS,CAAC,iBAAiB;oBAC7C,kBAAkB,EAAE,SAAS,CAAC,8BAA8B;iBAC7D,CAAC;YACJ,CAAC;YAID,OAAO,IAAA,kDAAwB,EAC7B,MAAM,EACN,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,SAAS,EACT,KAAK,CAAC,WAAW,EAAE,CACpB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,wDAAyB,EAAE,CAAC;gBAC/C,OAAO,IAAA,kDAAwB,EAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,EACf,QAAQ,EACR,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAED,OAAO,IAAA,kDAAwB,EAC7B,CAAC,eAAe,CAAC,EACjB,KAAK,CAAC,OAAO,EACb,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,0BAA0B,CAC9B,KAAgC;QAEhC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAA,kDAAwB,EAC7B,UAAU,CAAC,MAAM,EACjB,QAAQ,EACR,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAKD,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,UAAU,CAAC;iBAC5E,KAAK,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;iBAC5D,QAAQ,CAAC,oCAAoC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC;iBACnF,QAAQ,CAAC,kCAAkC,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAEjF,IAAI,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE;oBAC7D,aAAa,EAAE,KAAK,CAAC,aAAa;iBACnC,CAAC,CAAC;YACL,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAG/C,MAAM,MAAM,GAA4B;gBACtC,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,WAAW,EAAE;oBACX,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,SAAS,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE;oBAC7C,OAAO,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE;iBAC1C;gBACD,OAAO,EAAE;oBACP,cAAc,EAAE,SAAS,CAAC,MAAM;oBAChC,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM;oBAC3D,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;oBAChE,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;oBAC9F,eAAe,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;oBAC7D,YAAY,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;iBACvD;gBACD,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;oBACpC,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,GAAG,EAAE,QAAQ,CAAC,GAAG,IAAI,IAAI;oBACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE;oBACtD,YAAY,EAAE,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE;iBACzD,CAAC,CAAC;aACJ,CAAC;YAGF,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,MAAM,WAAW,GAAwB;oBACvC,eAAe,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBACnC,QAAQ,EAAE,2CAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC;wBACjF,UAAU,EAAE,CAAC,CAAC,UAAU;wBACxB,SAAS,EAAE,CAAC,CAAC,SAAS;wBACtB,WAAW,EAAE,CAAC,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE;wBACxC,YAAY,EAAE,CAAC,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE;qBAC3C,CAAC,CAAC;oBACH,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,WAAW;oBAC3C,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU;oBACzC,UAAU,EAAE,GAAG;iBAChB,CAAC;gBAEF,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;gBAErF,MAAM,CAAC,YAAY,GAAG;oBACpB,gBAAgB,EAAE,WAAW,CAAC,SAAS;oBACvC,WAAW,EAAE,WAAW,CAAC,eAAe;oBACxC,eAAe,EAAE,WAAW,CAAC,eAAe;iBAC7C,CAAC;YACJ,CAAC;YAID,OAAO,IAAA,kDAAwB,EAC7B,MAAM,EACN,YAAY,EACZ,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,KAAK,EACL,KAAK,CAAC,WAAW,EAAE,CACpB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,wDAAyB,EAAE,CAAC;gBAC/C,OAAO,IAAA,kDAAwB,EAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,EACf,QAAQ,EACR,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAED,OAAO,IAAA,kDAAwB,EAC7B,CAAC,iBAAiB,CAAC,EACnB,KAAK,CAAC,OAAO,EACb,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,KAA2B;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAA,kDAAwB,EAC7B,UAAU,CAAC,MAAM,EACjB,QAAQ,EACR,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAGD,IAAI,SAAS,GAAG,KAAK,CAAC;YAGtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAG5F,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;gBAC3D,KAAK,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;gBAC/B,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;gBAC9B,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;YAEH,MAAM,WAAW,GAAwB;gBACvC,eAAe,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACvC,QAAQ,EAAE,2CAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC;oBACjF,UAAU,EAAE,CAAC,CAAC,UAAU;oBACxB,SAAS,EAAE,CAAC,CAAC,SAAS;oBACtB,WAAW,EAAE,CAAC,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE;oBACxC,YAAY,EAAE,CAAC,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE;iBAC3C,CAAC,CAAC;gBACH,eAAe,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;gBACxE,cAAc,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;gBACtG,UAAU,EAAE,GAAG;aAChB,CAAC;YAGF,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAC1D,eAAe,EACf,WAAW,EACX,KAAK,CAAC,SAAS,CAChB,CAAC;YAGF,MAAM,MAAM,GAAyB;gBACnC,cAAc,EAAE;oBACd,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,OAAO,EAAE,SAAS,CAAC,OAAO;oBAC1B,gBAAgB,EAAE,SAAS,CAAC,iBAAiB;oBAC7C,kBAAkB,EAAE,SAAS,CAAC,8BAA8B;iBAC7D;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE,eAAe,CAAC,OAAO;oBAChC,QAAQ,EAAE,eAAe,CAAC,QAAQ;oBAClC,IAAI,EAAE,eAAe,CAAC,IAAI;oBAC1B,GAAG,EAAE,eAAe,CAAC,GAAG;oBACxB,WAAW,EAAE,eAAe,CAAC,WAAW;iBACzC;gBACD,WAAW,EAAE;oBACX,iBAAiB,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,SAAS;oBAC3F,mBAAmB,EAAE,aAAa,CAAC,MAAM;oBACzC,aAAa,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACrC,CAAC,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,CAC3E;iBACF;aACF,CAAC;YAGF,IAAI,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAC3E,SAAS,EACT,WAAW,CACZ,CAAC;gBAEF,MAAM,CAAC,eAAe,GAAG;oBACvB,mBAAmB,EAAE,iBAAiB,CAAC,mBAAmB;oBAC1D,OAAO,EAAE,iBAAiB,CAAC,OAAO;oBAClC,MAAM,EAAE,iBAAiB,CAAC,MAAM;iBACjC,CAAC;YACJ,CAAC;YAGD,IAAI,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;gBACrF,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;YACnC,CAAC;YAID,OAAO,IAAA,kDAAwB,EAC7B,MAAM,EACN,UAAU,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EACtB,SAAS,EACT,KAAK,CAAC,WAAW,EAAE,CACpB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,wDAAyB,EAAE,CAAC;gBAC/C,OAAO,IAAA,kDAAwB,EAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,EACf,QAAQ,EACR,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAED,OAAO,IAAA,kDAAwB,EAC7B,CAAC,eAAe,CAAC,EACjB,KAAK,CAAC,OAAO,EACb,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAnVY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,gDAAkB,CAAC,CAAA;qCACI,oBAAU;QACT,oDAAuB;QACzB,4DAA2B;QACvB,uDAAyB;GAN5D,sBAAsB,CAmVlC"}