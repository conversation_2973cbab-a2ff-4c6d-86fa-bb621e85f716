import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoggerService } from '../../../../common/logger/logger.service';

// CQRS服务
import { IpLocationCommandService } from './ip-location-command.service';
import { IpLocationQueryService } from './ip-location-query.service';

// 命令和查询对象
import { UpdateCommonLocationCommand } from '../commands/update-common-location.command';
import { SetTrustedLocationCommand } from '../commands/set-trusted-location.command';
import { RecordLoginLocationCommand } from '../commands/record-login-location.command';
import { GetLocationByIpQuery } from '../queries/get-location-by-ip.query';
import { GetUserLocationStatsQuery } from '../queries/get-user-location-stats.query';
import { AssessLoginRiskQuery } from '../queries/assess-login-risk.query';

// 领域服务
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService } from '../../domain/services/risk-assessment-domain.service';

// 值对象
import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';

// 请求和响应DTO
import { IpQueryRequestDto } from '../dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../dto/responses/location-stats.response.dto';

// 向后兼容 - 保留原有工具类
import { IpLocationUtil, LocationInfoWithQuality } from '../../utils/ip-location.util';
import { RiskAssessmentUtil, UserLocationStats } from '../../utils/risk-assessment.util';

// 实体
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';

// 异常处理
import { IpLocationDomainException } from '../../domain/exceptions/ip-location-domain.exception';
import { InvalidIpException } from '../../domain/exceptions/invalid-ip.exception';
import { LocationNotFoundException } from '../../domain/exceptions/location-not-found.exception';

/**
 * IP地理位置应用服务
 * 基于DDD架构协调各层服务，提供完整的业务功能
 * 同时保持向后兼容性
 */
@Injectable()
export class IpLocationApplicationService {
  constructor(
    // 新的DDD架构服务
    private readonly commandService: IpLocationCommandService,
    private readonly queryService: IpLocationQueryService,
    private readonly domainService: IpLocationDomainService,
    private readonly riskAssessmentDomainService: RiskAssessmentDomainService,

    // 向后兼容 - 保留原有工具类
    private readonly ipLocationUtil: IpLocationUtil,
    private readonly riskAssessmentUtil: RiskAssessmentUtil,

    // 数据库仓储
    @InjectRepository(UserCommonLocation)
    private readonly userCommonLocationRepository: Repository<UserCommonLocation>,

    // 基础服务
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * 查询IP地理位置信息
   * @param request 查询请求
   * @returns 位置信息响应
   */
  async queryIpLocation(request: IpQueryRequestDto): Promise<LocationInfoResponseDto> {
    const startTime = Date.now();

    try {
      // 使用新的DDD架构查询服务
      const query = GetLocationByIpQuery.create(request.ip, request.includeRisk);
      const queryResult = await this.queryService.handleGetLocationByIp(query);

      if (!queryResult.success) {
        throw new Error(queryResult.errors?.join(', ') || '查询失败');
      }

      const locationData = queryResult.data!;

      const response: LocationInfoResponseDto = {
        ip: request.ip,
        country: locationData.country,
        province: locationData.province,
        city: locationData.city,
        isp: locationData.isp,
        dataSource: locationData.dataSource,
        confidence: locationData.confidence,
        isHighQuality: locationData.isHighQuality,
        displayName: locationData.displayName
      };

      // 如果需要包含风险评估
      if (request.includeRisk && locationData.riskAssessment) {
        response.risk = {
          level: locationData.riskAssessment.level,
          score: locationData.riskAssessment.score,
          reason: locationData.riskAssessment.reason,
          needVerification: locationData.riskAssessment.needVerification
        };
      }

      this.loggerService.logBusiness('IpLocationApplicationService', 'queryIpLocation', {
        ip: IpLocationUtil.maskIP(request.ip),
        location: response.displayName,
        confidence: response.confidence,
        responseTime: Date.now() - startTime
      });

      return response;
    } catch (error) {
      this.loggerService.logBusiness('IpLocationApplicationService', 'queryIpLocation', {
        ip: IpLocationUtil.maskIP(request.ip),
        responseTime: Date.now() - startTime
      }, error);
      throw error;
    }
  }

  /**
   * 检查登录风险
   * @param request 风险检查请求
   * @returns 风险评估响应
   */
  async checkLoginRisk(request: RiskCheckRequestDto): Promise<RiskAssessmentResponseDto> {
    const startTime = Date.now();
    
    try {
      // 获取IP地理位置信息
      const locationInfo = await this.ipLocationUtil.getBasicLocationByIP(request.ipAddress);
      
      // 获取用户历史位置统计
      const userStats = await this.getUserLocationStats(request.userId);
      
      // 进行风险评估
      const riskAssessment = await this.riskAssessmentUtil.assessLoginRisk(
        request.userId,
        locationInfo,
        userStats
      );

      // 获取推荐的验证方式
      const recommendedActions = this.riskAssessmentUtil.getRecommendedVerificationMethods(riskAssessment);

      // 构建响应
      const response: RiskAssessmentResponseDto = {
        riskAssessment: {
          level: riskAssessment.level,
          score: riskAssessment.score,
          reason: riskAssessment.reason,
          factors: riskAssessment.factors,
          needVerification: riskAssessment.needVerification,
          recommendedActions
        },
        location: {
          country: locationInfo.country,
          province: locationInfo.province,
          city: locationInfo.city,
          isp: locationInfo.isp,
          displayName: `${locationInfo.country} ${locationInfo.province} ${locationInfo.city}`
        },
        userHistory: {
          lastLoginLocation: this.getLastLoginLocationDisplay(userStats),
          commonLocationCount: userStats.commonLocations.length,
          isNewLocation: !this.isLocationInHistory(locationInfo, userStats)
        }
      };

      this.loggerService.logBusiness('IpLocationApplicationService', 'checkLoginRisk', {
        userId: request.userId,
        ip: IpLocationUtil.maskIP(request.ipAddress),
        riskLevel: response.riskAssessment.level,
        riskScore: response.riskAssessment.score,
        responseTime: Date.now() - startTime
      });

      return response;
    } catch (error) {
      this.loggerService.logBusiness('IpLocationApplicationService', 'checkLoginRisk', {
        userId: request.userId,
        ip: IpLocationUtil.maskIP(request.ipAddress),
        responseTime: Date.now() - startTime
      }, error);
      throw error;
    }
  }

  /**
   * 获取用户位置统计
   * @param userId 用户ID
   * @param days 统计天数
   * @returns 位置统计响应
   */
  async getUserLocationStatistics(userId: number, days: number = 30): Promise<LocationStatsResponseDto> {
    const startTime = Date.now();

    try {
      // 暂时使用原有的实现方式，避免编译错误
      // 计算时间范围
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - days);

      // 直接查询数据库获取统计数据
      const commonLocations = await this.userCommonLocationRepository.find({
        where: { userId },
        order: { loginCount: 'DESC' }
      });

      // 计算统计数据
      const totalLoginCount = commonLocations.reduce((sum, loc) => sum + loc.loginCount, 0);
      const trustedLocations = commonLocations.filter(loc => loc.isTrusted).length;
      const riskLoginCount = commonLocations.filter(loc => loc.trustScore < 50).reduce((sum, loc) => sum + loc.loginCount, 0);

      const response: LocationStatsResponseDto = {
        userId,
        statisticsPeriod: {
          days,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        },
        commonLocations: commonLocations.map(loc => ({
          province: loc.province,
          city: loc.city,
          loginCount: loc.loginCount,
          isTrusted: loc.isTrusted,
          trustScore: loc.trustScore,
          firstLoginAt: loc.firstLoginAt?.toISOString() || '',
          lastLoginAt: loc.lastLoginAt?.toISOString() || ''
        })),
        summary: {
          totalLocations: commonLocations.length,
          trustedLocations,
          totalLoginCount,
          riskLoginCount,
          riskRate: totalLoginCount > 0 ? (riskLoginCount / totalLoginCount) * 100 : 0
        }
      };

      this.loggerService.logBusiness('IpLocationApplicationService', 'getUserLocationStatistics', {
        userId,
        totalLocations: response.summary.totalLocations,
        totalLoginCount: response.summary.totalLoginCount,
        responseTime: Date.now() - startTime
      });

      return response;
    } catch (error) {
      this.loggerService.logBusiness('IpLocationApplicationService', 'getUserLocationStatistics', {
        userId,
        responseTime: Date.now() - startTime
      }, error);
      throw error;
    }
  }

  /**
   * 设置可信登录地
   * @param userId 用户ID
   * @param request 设置请求
   */
  async setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<void> {
    const startTime = Date.now();
    
    try {
      // 查找或创建用户常用位置记录
      let commonLocation = await this.userCommonLocationRepository.findOne({
        where: {
          userId,
          province: request.province,
          city: request.city
        }
      });

      if (!commonLocation) {
        // 创建新的常用位置记录
        commonLocation = this.userCommonLocationRepository.create({
          userId,
          country: '中国', // 默认中国
          province: request.province,
          city: request.city,
          loginCount: 0,
          isTrusted: true,
          trustScore: 80 // 手动设置的可信位置给予较高初始评分
        });
      } else {
        // 更新现有记录
        commonLocation.setAsTrusted(request.reason);
      }

      await this.userCommonLocationRepository.save(commonLocation);

      this.loggerService.logBusiness('IpLocationApplicationService', 'setTrustedLocation', {
        userId,
        location: `${request.province} ${request.city}`,
        reason: request.reason,
        responseTime: Date.now() - startTime
      });
    } catch (error) {
      this.loggerService.logBusiness('IpLocationApplicationService', 'setTrustedLocation', {
        userId,
        location: `${request.province} ${request.city}`,
        responseTime: Date.now() - startTime
      }, error);
      throw error;
    }
  }

  /**
   * 更新用户常用登录地统计
   * @param userId 用户ID
   * @param locationInfo 位置信息
   */
  async updateUserCommonLocation(userId: number, locationInfo: LocationInfoWithQuality): Promise<void> {
    try {
      // 查找现有记录
      let commonLocation = await this.userCommonLocationRepository.findOne({
        where: {
          userId,
          province: locationInfo.province,
          city: locationInfo.city
        }
      });

      if (!commonLocation) {
        // 创建新记录
        commonLocation = this.userCommonLocationRepository.create({
          userId,
          country: locationInfo.country,
          province: locationInfo.province,
          city: locationInfo.city,
          isp: locationInfo.isp,
          loginCount: 1,
          firstLoginAt: new Date(),
          lastLoginAt: new Date(),
          isTrusted: false,
          trustScore: 10 // 新位置初始评分较低
        });
      } else {
        // 更新现有记录
        commonLocation.updateLoginStats();
        if (locationInfo.isp && locationInfo.isp !== '未知') {
          commonLocation.isp = locationInfo.isp;
        }
      }

      await this.userCommonLocationRepository.save(commonLocation);
    } catch (error) {
      this.loggerService.logBusiness('IpLocationApplicationService', 'updateUserCommonLocation', {
        userId,
        location: locationInfo.displayName
      }, error);
      // 不抛出异常，避免影响主要业务流程
    }
  }

  /**
   * 获取用户位置统计（内部使用）
   * @param userId 用户ID
   * @returns 用户位置统计
   */
  private async getUserLocationStats(userId: number): Promise<UserLocationStats> {
    const commonLocations = await this.userCommonLocationRepository.find({
      where: { userId },
      order: { loginCount: 'DESC' }
    });

    const totalLoginCount = commonLocations.reduce((sum, loc) => sum + loc.loginCount, 0);

    return {
      commonLocations: commonLocations.map(loc => ({
        province: loc.province,
        city: loc.city,
        loginCount: loc.loginCount,
        isTrusted: loc.isTrusted,
        lastLoginAt: loc.lastLoginAt || new Date()
      })),
      riskLoginCount: 0, // 需要从登录日志中统计
      totalLoginCount
    };
  }

  /**
   * 获取最后登录位置显示名称
   * @param userStats 用户统计
   * @returns 最后登录位置显示名称
   */
  private getLastLoginLocationDisplay(userStats: UserLocationStats): string {
    if (userStats.commonLocations.length === 0) {
      return '无历史记录';
    }

    const lastLocation = userStats.commonLocations
      .sort((a, b) => b.lastLoginAt.getTime() - a.lastLoginAt.getTime())[0];
    
    return `${lastLocation.province} ${lastLocation.city}`;
  }

  /**
   * 检查位置是否在用户历史中
   * @param location 当前位置
   * @param userStats 用户统计
   * @returns 是否在历史中
   */
  private isLocationInHistory(location: any, userStats: UserLocationStats): boolean {
    return userStats.commonLocations.some(loc =>
      loc.province === location.province && loc.city === location.city
    );
  }
}
