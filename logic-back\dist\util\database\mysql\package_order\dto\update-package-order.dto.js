"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePackageOrderDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const create_package_order_dto_1 = require("./create-package-order.dto");
class UpdatePackageOrderDto {
    id;
    orderNo;
    userId;
    packageId;
    packageName;
    points;
    validityDays;
    price;
    originalPrice;
    discountRate;
    status;
    paymentId;
    paidTime;
}
exports.UpdatePackageOrderDto = UpdatePackageOrderDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '订单ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '订单ID必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '订单ID', example: 1 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '订单编号必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '订单编号', required: false, example: 'PKG_ORDER_20231010001' }),
    __metadata("design:type", String)
], UpdatePackageOrderDto.prototype, "orderNo", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户ID必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false, example: 'user_123456' }),
    __metadata("design:type", String)
], UpdatePackageOrderDto.prototype, "userId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '套餐ID必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '套餐ID', required: false, example: 1 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "packageId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '套餐名称必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '套餐名称', required: false, example: '基础套餐' }),
    __metadata("design:type", String)
], UpdatePackageOrderDto.prototype, "packageName", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '点数必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '点数不能小于0' }),
    (0, swagger_1.ApiProperty)({ description: '点数', required: false, example: 100.00 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "points", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '有效期必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '有效期必须大于0天' }),
    (0, swagger_1.ApiProperty)({ description: '有效期(天)', required: false, example: 30 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "validityDays", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '实付金额必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '实付金额不能小于0' }),
    (0, swagger_1.ApiProperty)({ description: '实付金额', required: false, example: 99.99 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "price", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '原价必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '原价不能小于0' }),
    (0, swagger_1.ApiProperty)({ description: '原价', required: false, example: 129.99 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "originalPrice", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '折扣率必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '折扣率不能小于0' }),
    (0, class_validator_1.Max)(1, { message: '折扣率不能大于1' }),
    (0, swagger_1.ApiProperty)({ description: '折扣率', required: false, example: 0.77 }),
    __metadata("design:type", Number)
], UpdatePackageOrderDto.prototype, "discountRate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(create_package_order_dto_1.PackageOrderStatus, { message: '无效的订单状态' }),
    (0, swagger_1.ApiProperty)({
        description: '订单状态',
        enum: create_package_order_dto_1.PackageOrderStatus,
        required: false
    }),
    __metadata("design:type", String)
], UpdatePackageOrderDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '支付平台交易号必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '支付平台交易号', required: false, example: 'PAY_123456789' }),
    __metadata("design:type", String)
], UpdatePackageOrderDto.prototype, "paymentId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false }),
    __metadata("design:type", Date)
], UpdatePackageOrderDto.prototype, "paidTime", void 0);
//# sourceMappingURL=update-package-order.dto.js.map