import { UserLoginLogService } from './user_login_log.service';
import { CreateUserLoginLogDto } from './dto/create-user-login-log.dto';
import { QueryUserLoginLogDto } from './dto/query-user-login-log.dto';
import { UserLoginLog } from './entities/user_login_log.entity';
export declare class UserLoginLogController {
    private readonly userLoginLogService;
    constructor(userLoginLogService: UserLoginLogService);
    create(createDto: CreateUserLoginLogDto): Promise<UserLoginLog>;
    findAll(query: QueryUserLoginLogDto): Promise<{
        data: UserLoginLog[];
        total: number;
        page: number;
        limit: number;
    }>;
    getUserHistory(userId: number, limit?: number): Promise<UserLoginLog[]>;
    getLastLogin(userId: number): Promise<UserLoginLog | null>;
    getLoginStats(userId: number, days?: number): Promise<any>;
    checkAbnormalLogin(userId: number, clientIp: string, userAgent: string): Promise<{
        isAbnormal: boolean;
    }>;
    findOne(id: number): Promise<UserLoginLog | null>;
    remove(id: number): Promise<{
        message: string;
    }>;
    recordLogout(userId: number, sessionId?: string): Promise<{
        message: string;
    }>;
    getUserRecentLogs(userId: number): Promise<any>;
}
