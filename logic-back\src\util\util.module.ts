import { AliyunStaticGestureRecognitionModule } from './ai_providers/aliyun_static_gesture_recognition/aliyun_static_gesture_recognition.module';
import { Module } from '@nestjs/common';
import { YamlModule } from './yaml/yaml.module';
import { AliOssModule } from './ali_service/ali_oss/ali_oss.module';
import { MysqlModule } from './database/mysql/mysql.module';
import { RedisModule } from './database/redis/redis.module';
import { QueueModule } from './queue/queue.module';
import { WebSocketModule } from './web_socket/web_socket.module';
import { AliyunExpressionModule } from './ai_providers/aliyun_expression/aliyun_expression.module';
import { AliyunFaceRecognitionModule } from './ai_providers/aliyun_face_recognition/aliyun_face_recognition.module';
import { IpLocationModule } from './ip_location/ip-location.module';
import { MinimaxImageModule } from './ai_providers/minimax-image/minimax-image.module';
import { MinimaxTtsModule } from './ai_providers/minimax-tts/minimax-tts.module';
import { AliQwenVisionModule } from './ai_providers/ali-qwen-vision/ali-qwen-vision.module';
import { AliQwenTurboModule } from './ai_providers/ali-qwen-turbo/ali-qwen-turbo.module';
import { ZhipuLlmModule } from './ai_providers/zhipu-llm/zhipu-llm.module';
import { AliyunSegmentImageModule } from './ai_providers/aliyun_segment_image/aliyun_segment_image.module';
import { AliyunImageScoreModule } from './ai_providers/aliyun_image_score/aliyun_image_score.module';
import { BaiduImageEnhanceModule } from './ai_providers/baidu_image_enhance/baidu_image_enhance.module';
import { AliSmsModule } from './ali_service/ali_sms/ali_sms.module';
import { AliyunFaceCompareModule } from './ai_providers/aliyun_face_compare/aliyun_face_compare.module';
import { XunfeiSpeechRecognitionModule } from './ai_providers/xunfei_speech_recognition/xunfei_speech_recognition.module';
import { AliyunObjectDetectionModule } from './ai_providers/aliyun_object_detection/aliyun_object_detection.module';
import { EncryptModule } from './encrypt/encrypt.module';

@Module({
  imports: [
    MysqlModule,
    YamlModule,
    AliOssModule,
    RedisModule,
    QueueModule,
    WebSocketModule,
    AliyunExpressionModule,
    MinimaxImageModule,
    MinimaxTtsModule,
    AliQwenVisionModule,
    AliQwenTurboModule,
    ZhipuLlmModule,
    AliyunSegmentImageModule,
    AliyunImageScoreModule,
    BaiduImageEnhanceModule,
    AliSmsModule,
    AliyunFaceRecognitionModule,
    AliyunFaceCompareModule,
    XunfeiSpeechRecognitionModule,
    AliyunObjectDetectionModule,
    AliyunStaticGestureRecognitionModule,
    EncryptModule,
    IpLocationModule
  ],
})
export class UtilModule { }
