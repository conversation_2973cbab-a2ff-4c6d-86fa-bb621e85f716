import { IpLocationDomainException } from './ip-location-domain.exception';

/**
 * 无效IP地址异常
 * 当提供的IP地址格式不正确或无效时抛出
 */
export class InvalidIpException extends IpLocationDomainException {
  constructor(
    message: string,
    ipAddress?: string,
    context?: Record<string, any>
  ) {
    super(
      message,
      'INVALID_IP_ADDRESS',
      {
        ipAddress,
        ...context
      }
    );
  }

  /**
   * 创建无效格式异常
   */
  static invalidFormat(ipAddress: string): InvalidIpException {
    return new InvalidIpException(
      `IP地址格式无效: ${ipAddress}`,
      ipAddress,
      { reason: 'invalid_format' }
    );
  }

  /**
   * 创建空IP异常
   */
  static empty(): InvalidIpException {
    return new InvalidIpException(
      'IP地址不能为空',
      '',
      { reason: 'empty_ip' }
    );
  }

  /**
   * 创建IP长度异常
   */
  static invalidLength(ipAddress: string): InvalidIpException {
    return new InvalidIpException(
      `IP地址长度无效: ${ipAddress}`,
      ipAddress,
      { 
        reason: 'invalid_length',
        length: ipAddress.length
      }
    );
  }

  /**
   * 创建不支持的IP类型异常
   */
  static unsupportedType(ipAddress: string, type: string): InvalidIpException {
    return new InvalidIpException(
      `不支持的IP地址类型: ${type}`,
      ipAddress,
      { 
        reason: 'unsupported_type',
        type
      }
    );
  }
}
