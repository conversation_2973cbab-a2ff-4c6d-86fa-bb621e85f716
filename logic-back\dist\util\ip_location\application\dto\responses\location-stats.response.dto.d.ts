export declare class LocationStatsResponseDto {
    userId: number;
    statisticsPeriod: {
        days: number;
        startDate: string;
        endDate: string;
    };
    commonLocations: Array<{
        province: string;
        city: string;
        loginCount: number;
        isTrusted: boolean;
        trustScore: number;
        firstLoginAt: string;
        lastLoginAt: string;
    }>;
    summary: {
        totalLocations: number;
        trustedLocations: number;
        riskLoginCount: number;
        totalLoginCount: number;
        riskRate: number;
    };
}
