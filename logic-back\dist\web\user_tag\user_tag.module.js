"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTagModule = void 0;
const common_1 = require("@nestjs/common");
const user_tag_controller_1 = require("./user_tag.controller");
const user_tag_service_1 = require("./user_tag.service");
const tag_module_1 = require("../../util/database/mysql/tag/tag.module");
const http_response_result_module_1 = require("../http_response_result/http_response_result.module");
let UserTagModule = class UserTagModule {
};
exports.UserTagModule = UserTagModule;
exports.UserTagModule = UserTagModule = __decorate([
    (0, common_1.Module)({
        imports: [
            tag_module_1.TagModule,
            http_response_result_module_1.HttpResponseResultModule,
        ],
        controllers: [user_tag_controller_1.UserTagController],
        providers: [user_tag_service_1.UserTagService],
        exports: [user_tag_service_1.UserTagService],
    })
], UserTagModule);
//# sourceMappingURL=user_tag.module.js.map