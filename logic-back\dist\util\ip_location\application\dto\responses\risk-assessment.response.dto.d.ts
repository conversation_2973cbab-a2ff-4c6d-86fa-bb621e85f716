export declare class RiskAssessmentResponseDto {
    riskAssessment: {
        level: 'LOW' | 'MEDIUM' | 'HIGH';
        score: number;
        reason: string;
        factors: string[];
        needVerification: boolean;
        recommendedActions: string[];
    };
    location: {
        country: string;
        province: string;
        city: string;
        isp: string;
        displayName: string;
    };
    userHistory: {
        lastLoginLocation: string;
        commonLocationCount: number;
        isNewLocation: boolean;
    };
}
