"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisCacheService = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
const cache_manager_2 = require("cache-manager");
const redis_service_1 = require("../../../database/redis/redis.service");
const logger_service_1 = require("../../../../common/logger/logger.service");
let RedisCacheService = class RedisCacheService {
    cacheManager;
    redisService;
    logger;
    CACHE_PREFIX = 'ip_location:';
    DEFAULT_TTL = 24 * 60 * 60 * 1000;
    CACHE_VERSION = 'v1.0';
    stats = {
        hits: 0,
        misses: 0,
        hitRate: 0,
        totalRequests: 0
    };
    constructor(cacheManager, redisService, logger) {
        this.cacheManager = cacheManager;
        this.redisService = redisService;
        this.logger = logger;
    }
    async cacheLocation(ip, location, ttl = this.DEFAULT_TTL) {
        const key = this.buildLocationKey(ip);
        const cacheItem = {
            data: location,
            timestamp: Date.now(),
            ttl,
            version: this.CACHE_VERSION
        };
        try {
            await this.cacheManager.set(key, cacheItem, ttl);
            this.logger.debug(`缓存IP位置信息: ${ip}`, 'RedisCacheService');
        }
        catch (error) {
            this.logger.error(`缓存IP位置信息失败: ${ip}`, error, 'RedisCacheService');
        }
    }
    async getLocation(ip) {
        const key = this.buildLocationKey(ip);
        this.stats.totalRequests++;
        try {
            const cacheItem = await this.cacheManager.get(key);
            if (cacheItem && this.isValidCacheItem(cacheItem)) {
                this.stats.hits++;
                this.updateHitRate();
                this.logger.debug(`缓存命中IP位置信息: ${ip}`, 'RedisCacheService');
                return cacheItem.data;
            }
            this.stats.misses++;
            this.updateHitRate();
            return null;
        }
        catch (error) {
            this.logger.error(`获取缓存IP位置信息失败: ${ip}`, error, 'RedisCacheService');
            this.stats.misses++;
            this.updateHitRate();
            return null;
        }
    }
    async cacheRiskAssessment(userId, ip, riskScore, ttl = 5 * 60 * 1000) {
        const key = this.buildRiskKey(userId, ip);
        const cacheItem = {
            data: riskScore,
            timestamp: Date.now(),
            ttl,
            version: this.CACHE_VERSION
        };
        try {
            await this.cacheManager.set(key, cacheItem, ttl);
            this.logger.debug(`缓存风险评估结果: ${userId}-${ip}`, 'RedisCacheService');
        }
        catch (error) {
            this.logger.error(`缓存风险评估结果失败: ${userId}-${ip}`, error, 'RedisCacheService');
        }
    }
    async getRiskAssessment(userId, ip) {
        const key = this.buildRiskKey(userId, ip);
        this.stats.totalRequests++;
        try {
            const cacheItem = await this.cacheManager.get(key);
            if (cacheItem && this.isValidCacheItem(cacheItem)) {
                this.stats.hits++;
                this.updateHitRate();
                this.logger.debug(`缓存命中风险评估结果: ${userId}-${ip}`, 'RedisCacheService');
                return cacheItem.data;
            }
            this.stats.misses++;
            this.updateHitRate();
            return null;
        }
        catch (error) {
            this.logger.error(`获取缓存风险评估结果失败: ${userId}-${ip}`, error, 'RedisCacheService');
            this.stats.misses++;
            this.updateHitRate();
            return null;
        }
    }
    async cacheUserStats(userId, days, stats, ttl = 60 * 60 * 1000) {
        const key = this.buildUserStatsKey(userId, days);
        const cacheItem = {
            data: stats,
            timestamp: Date.now(),
            ttl,
            version: this.CACHE_VERSION
        };
        try {
            await this.cacheManager.set(key, cacheItem, ttl);
            this.logger.debug(`缓存用户统计数据: ${userId}-${days}天`, 'RedisCacheService');
        }
        catch (error) {
            this.logger.error(`缓存用户统计数据失败: ${userId}-${days}天`, error, 'RedisCacheService');
        }
    }
    async getUserStats(userId, days) {
        const key = this.buildUserStatsKey(userId, days);
        this.stats.totalRequests++;
        try {
            const cacheItem = await this.cacheManager.get(key);
            if (cacheItem && this.isValidCacheItem(cacheItem)) {
                this.stats.hits++;
                this.updateHitRate();
                this.logger.debug(`缓存命中用户统计数据: ${userId}-${days}天`, 'RedisCacheService');
                return cacheItem.data;
            }
            this.stats.misses++;
            this.updateHitRate();
            return null;
        }
        catch (error) {
            this.logger.error(`获取缓存用户统计数据失败: ${userId}-${days}天`, error, 'RedisCacheService');
            this.stats.misses++;
            this.updateHitRate();
            return null;
        }
    }
    async deleteLocationCache(ip) {
        const key = this.buildLocationKey(ip);
        try {
            await this.cacheManager.del(key);
            this.logger.debug(`删除IP位置缓存: ${ip}`, 'RedisCacheService');
        }
        catch (error) {
            this.logger.error(`删除IP位置缓存失败: ${ip}`, error, 'RedisCacheService');
        }
    }
    async deleteUserCache(userId) {
        try {
            const pattern = `${this.CACHE_PREFIX}*:${userId}:*`;
            await this.redisService.deleteByPattern(pattern);
            this.logger.debug(`删除用户相关缓存: ${userId}`, 'RedisCacheService');
        }
        catch (error) {
            this.logger.error(`删除用户相关缓存失败: ${userId}`, error, 'RedisCacheService');
        }
    }
    async clearAllCache() {
        try {
            const pattern = `${this.CACHE_PREFIX}*`;
            await this.redisService.deleteByPattern(pattern);
            this.logger.log('清空所有IP地理位置缓存', 'RedisCacheService');
        }
        catch (error) {
            this.logger.error('清空所有IP地理位置缓存失败', error, 'RedisCacheService');
        }
    }
    getCacheStats() {
        return { ...this.stats };
    }
    resetStats() {
        this.stats = {
            hits: 0,
            misses: 0,
            hitRate: 0,
            totalRequests: 0
        };
    }
    buildLocationKey(ip) {
        return `${this.CACHE_PREFIX}location:${ip}`;
    }
    buildRiskKey(userId, ip) {
        return `${this.CACHE_PREFIX}risk:${userId}:${ip}`;
    }
    buildUserStatsKey(userId, days) {
        return `${this.CACHE_PREFIX}stats:${userId}:${days}d`;
    }
    isValidCacheItem(cacheItem) {
        if (!cacheItem || !cacheItem.data) {
            return false;
        }
        if (cacheItem.version !== this.CACHE_VERSION) {
            return false;
        }
        const now = Date.now();
        if (now - cacheItem.timestamp > cacheItem.ttl) {
            return false;
        }
        return true;
    }
    updateHitRate() {
        if (this.stats.totalRequests > 0) {
            this.stats.hitRate = (this.stats.hits / this.stats.totalRequests) * 100;
        }
    }
};
exports.RedisCacheService = RedisCacheService;
exports.RedisCacheService = RedisCacheService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [typeof (_a = typeof cache_manager_2.Cache !== "undefined" && cache_manager_2.Cache) === "function" ? _a : Object, redis_service_1.RedisService,
        logger_service_1.LoggerService])
], RedisCacheService);
//# sourceMappingURL=redis-cache.service.js.map