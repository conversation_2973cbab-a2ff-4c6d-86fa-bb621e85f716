import { Repository } from 'typeorm';
import { PackageOrder } from './entities/package-order.entity';
import { CreatePackageOrderDto, UpdatePackageOrderDto, QueryPackageOrderDto, PackageOrderStatus } from './dto';
export declare class PackageOrderService {
    private packageOrderRepository;
    constructor(packageOrderRepository: Repository<PackageOrder>);
    create(createPackageOrderDto: CreatePackageOrderDto): Promise<PackageOrder>;
    findAll(): Promise<PackageOrder[]>;
    findOne(id: number): Promise<PackageOrder>;
    findByOrderNo(orderNo: string): Promise<PackageOrder>;
    update(id: number, updatePackageOrderDto: UpdatePackageOrderDto): Promise<PackageOrder>;
    remove(id: number): Promise<void>;
    findByUserId(userId: string): Promise<PackageOrder[]>;
    findWithPagination(queryDto: QueryPackageOrderDto): Promise<{
        data: PackageOrder[];
        total: number;
        page: number;
        limit: number;
    }>;
    updatePaymentStatus(orderNo: string, paymentId: string, status?: PackageOrderStatus): Promise<PackageOrder>;
    cancelOrder(orderNo: string): Promise<PackageOrder>;
    getUserOrderStats(userId: string): Promise<{
        total: number;
        pending: number;
        paid: number;
        cancelled: number;
    }>;
}
