import { Repository } from 'typeorm';
import { TeacherAuditAttachment } from './entities/teacher_audit_attachment.entity';
import { CreateTeacherAuditAttachmentDto } from './dto/create-teacher_audit_attachment.dto';
import { Attachment } from '../attachment/entities/attachment.entity';
export declare class TeacherAuditAttachmentService {
    private teacherAuditAttachmentRepository;
    private attachmentRepository;
    constructor(teacherAuditAttachmentRepository: Repository<TeacherAuditAttachment>, attachmentRepository: Repository<Attachment>);
    create(createTeacherAuditAttachmentDto: CreateTeacherAuditAttachmentDto): Promise<TeacherAuditAttachment>;
    createMany(dtos: CreateTeacherAuditAttachmentDto[]): Promise<TeacherAuditAttachment[]>;
    findAttachmentsByAuditId(teacherAuditId: number): Promise<Attachment[]>;
    findAll(): Promise<TeacherAuditAttachment[]>;
    findByTeacherAuditId(teacherAuditId: number): Promise<TeacherAuditAttachment[]>;
    remove(id: number): Promise<void>;
    removeByTeacherAuditId(teacherAuditId: number): Promise<void>;
}
