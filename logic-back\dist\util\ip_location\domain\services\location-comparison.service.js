"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationComparisonService = void 0;
const common_1 = require("@nestjs/common");
let LocationComparisonService = class LocationComparisonService {
    compareLocations(location1, location2) {
        const differences = [];
        const commonalities = [];
        const riskFactors = [];
        if (location1.country !== location2.country) {
            differences.push(`国家: ${location1.country} → ${location2.country}`);
            if (location1.isDomestic !== location2.isDomestic) {
                riskFactors.push('跨境变化');
            }
        }
        else if (location1.country !== '未知') {
            commonalities.push(`相同国家: ${location1.country}`);
        }
        if (!location1.isSameProvince(location2)) {
            differences.push(`省份: ${location1.province} → ${location2.province}`);
            if (location1.isDomestic && location2.isDomestic) {
                riskFactors.push('跨省变化');
            }
        }
        else if (location1.province !== '未知') {
            commonalities.push(`相同省份: ${location1.province}`);
        }
        if (!location1.isSameCity(location2)) {
            differences.push(`城市: ${location1.city} → ${location2.city}`);
            if (location1.isSameProvince(location2)) {
                riskFactors.push('省内城市变化');
            }
        }
        else if (location1.city !== '未知') {
            commonalities.push(`相同城市: ${location1.city}`);
        }
        if (!location1.isSameISP(location2)) {
            differences.push(`运营商: ${location1.isp} → ${location2.isp}`);
            if (location1.isSameCity(location2)) {
                riskFactors.push('运营商变化');
            }
        }
        else if (location1.isp !== '未知') {
            commonalities.push(`相同运营商: ${location1.isp}`);
        }
        const similarity = location1.calculateSimilarity(location2);
        const changeLevel = this.determineChangeLevel(similarity, riskFactors);
        const isSignificantChange = changeLevel === 'MAJOR' || changeLevel === 'EXTREME';
        return {
            similarity,
            differences,
            commonalities,
            riskFactors,
            isSignificantChange,
            changeLevel
        };
    }
    compareMultipleLocations(locations) {
        const matrix = [];
        for (let i = 0; i < locations.length; i++) {
            matrix[i] = [];
            for (let j = 0; j < locations.length; j++) {
                if (i === j) {
                    matrix[i][j] = 100;
                }
                else {
                    matrix[i][j] = locations[i].calculateSimilarity(locations[j]);
                }
            }
        }
        return matrix;
    }
    findMostSimilarLocation(targetLocation, candidateLocations) {
        if (candidateLocations.length === 0) {
            return null;
        }
        let mostSimilar = candidateLocations[0];
        let highestSimilarity = targetLocation.calculateSimilarity(mostSimilar);
        for (let i = 1; i < candidateLocations.length; i++) {
            const similarity = targetLocation.calculateSimilarity(candidateLocations[i]);
            if (similarity > highestSimilarity) {
                highestSimilarity = similarity;
                mostSimilar = candidateLocations[i];
            }
        }
        return {
            location: mostSimilar,
            similarity: highestSimilarity
        };
    }
    clusterLocations(locations, similarityThreshold = 70) {
        const clusters = [];
        const processed = new Set();
        for (let i = 0; i < locations.length; i++) {
            if (processed.has(i))
                continue;
            const cluster = [locations[i]];
            processed.add(i);
            for (let j = i + 1; j < locations.length; j++) {
                if (processed.has(j))
                    continue;
                const similarity = locations[i].calculateSimilarity(locations[j]);
                if (similarity >= similarityThreshold) {
                    cluster.push(locations[j]);
                    processed.add(j);
                }
            }
            const centroid = this.calculateCentroid(cluster);
            const radius = this.calculateClusterRadius(cluster, centroid);
            const confidence = this.calculateClusterConfidence(cluster);
            clusters.push({
                centroid,
                locations: cluster,
                radius,
                confidence
            });
        }
        return clusters.sort((a, b) => b.locations.length - a.locations.length);
    }
    analyzeLocationTrend(locationHistory) {
        if (locationHistory.length < 2) {
            return {
                stability: 100,
                mainLocations: locationHistory,
                changeFrequency: 0,
                trendDirection: 'STABLE'
            };
        }
        let totalSimilarity = 0;
        let changeCount = 0;
        for (let i = 1; i < locationHistory.length; i++) {
            const similarity = locationHistory[i - 1].calculateSimilarity(locationHistory[i]);
            totalSimilarity += similarity;
            if (similarity < 80) {
                changeCount++;
            }
        }
        const stability = totalSimilarity / (locationHistory.length - 1);
        const changeFrequency = (changeCount / (locationHistory.length - 1)) * 100;
        const clusters = this.clusterLocations(locationHistory, 60);
        const mainLocations = clusters.slice(0, 3).map(cluster => cluster.centroid);
        let trendDirection;
        if (stability > 80) {
            trendDirection = 'STABLE';
        }
        else if (changeFrequency > 50) {
            trendDirection = 'CHAOTIC';
        }
        else if (clusters.length > locationHistory.length * 0.7) {
            trendDirection = 'EXPANDING';
        }
        else {
            trendDirection = 'CONTRACTING';
        }
        return {
            stability,
            mainLocations,
            changeFrequency,
            trendDirection
        };
    }
    determineChangeLevel(similarity, riskFactors) {
        if (similarity >= 95)
            return 'NONE';
        if (similarity >= 80)
            return 'MINOR';
        if (similarity >= 60)
            return 'MODERATE';
        if (similarity >= 30)
            return 'MAJOR';
        if (riskFactors.includes('跨境变化'))
            return 'EXTREME';
        return 'EXTREME';
    }
    calculateCentroid(locations) {
        if (locations.length === 1) {
            return locations[0];
        }
        const locationCounts = new Map();
        locations.forEach(location => {
            const key = `${location.country}-${location.province}-${location.city}`;
            const existing = locationCounts.get(key);
            if (existing) {
                existing.count++;
            }
            else {
                locationCounts.set(key, { location, count: 1 });
            }
        });
        let maxCount = 0;
        let centroid = locations[0];
        locationCounts.forEach(({ location, count }) => {
            if (count > maxCount) {
                maxCount = count;
                centroid = location;
            }
        });
        return centroid;
    }
    calculateClusterRadius(locations, centroid) {
        if (locations.length === 1)
            return 0;
        const similarities = locations.map(location => centroid.calculateSimilarity(location));
        return 100 - Math.min(...similarities);
    }
    calculateClusterConfidence(locations) {
        if (locations.length === 1)
            return locations[0].confidence;
        const avgConfidence = locations.reduce((sum, location) => sum + location.confidence, 0) / locations.length;
        const sizeBonus = Math.min(locations.length * 5, 20);
        return Math.min(avgConfidence + sizeBonus, 100);
    }
};
exports.LocationComparisonService = LocationComparisonService;
exports.LocationComparisonService = LocationComparisonService = __decorate([
    (0, common_1.Injectable)()
], LocationComparisonService);
//# sourceMappingURL=location-comparison.service.js.map