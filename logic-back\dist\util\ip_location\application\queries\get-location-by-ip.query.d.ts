import { IpAddress } from '../../domain/value-objects/ip-address.vo';
export declare class GetLocationByIpQuery {
    readonly ipAddress: IpAddress;
    readonly includeRisk: boolean;
    readonly cacheEnabled: boolean;
    readonly timestamp: Date;
    constructor(ipAddress: IpAddress, includeRisk?: boolean, cacheEnabled?: boolean);
    static create(ipAddressString: string, includeRisk?: boolean, cacheEnabled?: boolean): GetLocationByIpQuery;
    static createWithRisk(ipAddressString: string, cacheEnabled?: boolean): GetLocationByIpQuery;
    static createWithoutCache(ipAddressString: string, includeRisk?: boolean): GetLocationByIpQuery;
    validate(): {
        isValid: boolean;
        errors: string[];
    };
    getCacheKey(): string;
    getSummary(): string;
    get needsRiskAssessment(): boolean;
    get canUseCache(): boolean;
    toJSON(): object;
}
