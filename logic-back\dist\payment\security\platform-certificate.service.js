"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PlatformCertificateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlatformCertificateService = void 0;
const common_1 = require("@nestjs/common");
const payment_config_service_1 = require("../config/payment-config.service");
const payment_signature_service_1 = require("./payment-signature.service");
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const axios_1 = require("axios");
let PlatformCertificateService = PlatformCertificateService_1 = class PlatformCertificateService {
    configService;
    signatureService;
    logger = new common_1.Logger(PlatformCertificateService_1.name);
    certificates = new Map();
    certificateCacheDir;
    constructor(configService, signatureService) {
        this.configService = configService;
        this.signatureService = signatureService;
        this.certificateCacheDir = path.resolve(process.cwd(), './keys/wechatpay/platform_certs');
        this.ensureCacheDirExists();
    }
    async onModuleInit() {
        try {
            await this.loadCachedCertificates();
            if (this.certificates.size === 0 || this.shouldUpdateCertificates()) {
                this.logger.log('正在获取微信支付平台证书...');
                await this.fetchAndUpdateCertificates();
            }
        }
        catch (error) {
            this.logger.error(`初始化微信支付平台证书失败: ${error.message}`, error.stack);
        }
    }
    ensureCacheDirExists() {
        try {
            if (!fs.existsSync(this.certificateCacheDir)) {
                fs.mkdirSync(this.certificateCacheDir, { recursive: true });
                this.logger.log(`创建证书缓存目录: ${this.certificateCacheDir}`);
            }
        }
        catch (error) {
            this.logger.error(`创建证书缓存目录失败: ${error.message}`, error.stack);
        }
    }
    async loadCachedCertificates() {
        try {
            this.certificates.clear();
            if (!fs.existsSync(this.certificateCacheDir)) {
                return;
            }
            const files = fs.readdirSync(this.certificateCacheDir);
            for (const file of files) {
                if (file.endsWith('.pem')) {
                    const serialNo = file.replace('.pem', '');
                    const certPath = path.join(this.certificateCacheDir, file);
                    const metaPath = path.join(this.certificateCacheDir, `${serialNo}.json`);
                    if (fs.existsSync(metaPath)) {
                        try {
                            const certContent = fs.readFileSync(certPath, 'utf8');
                            const metaContent = JSON.parse(fs.readFileSync(metaPath, 'utf8'));
                            this.certificates.set(serialNo, {
                                serialNo,
                                certificate: certContent,
                                effectiveTime: new Date(metaContent.effectiveTime),
                                expireTime: new Date(metaContent.expireTime)
                            });
                            this.logger.log(`已加载微信支付平台证书: ${serialNo}, 过期时间: ${metaContent.expireTime}`);
                        }
                        catch (error) {
                            this.logger.error(`读取证书 ${serialNo} 失败: ${error.message}`);
                        }
                    }
                }
            }
            this.logger.log(`已加载 ${this.certificates.size} 个微信支付平台证书`);
        }
        catch (error) {
            this.logger.error(`加载缓存证书失败: ${error.message}`, error.stack);
        }
    }
    shouldUpdateCertificates() {
        if (this.certificates.size === 0) {
            return true;
        }
        const now = new Date();
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        for (const cert of this.certificates.values()) {
            if (cert.expireTime < tomorrow) {
                return true;
            }
        }
        return false;
    }
    async fetchAndUpdateCertificates() {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const method = 'GET';
            const url = '/v3/certificates';
            const timestamp = Math.floor(Date.now() / 1000);
            const nonce = crypto.randomBytes(16).toString('hex');
            const message = `${method}\n${url}\n${timestamp}\n${nonce}\n\n`;
            const privateKey = fs.readFileSync(path.resolve(process.cwd(), wechatConfig.privateKeyPath));
            const sign = crypto.createSign('RSA-SHA256');
            sign.update(message);
            const signature = sign.sign(privateKey, 'base64');
            const response = await axios_1.default.get('https://api.mch.weixin.qq.com/v3/certificates', {
                headers: {
                    'Accept': 'application/json',
                    'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${wechatConfig.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${wechatConfig.serialNo}"`
                }
            });
            const data = response.data;
            if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
                throw new Error('未获取到微信支付平台证书');
            }
            for (const item of data.data) {
                const serialNo = item.serial_no;
                const encryptCert = item.encrypt_certificate;
                const certText = this.decryptCertificate(encryptCert.ciphertext, encryptCert.nonce, encryptCert.associated_data);
                const certPath = path.join(this.certificateCacheDir, `${serialNo}.pem`);
                const metaPath = path.join(this.certificateCacheDir, `${serialNo}.json`);
                fs.writeFileSync(certPath, certText);
                fs.writeFileSync(metaPath, JSON.stringify({
                    serialNo: serialNo,
                    effectiveTime: item.effective_time,
                    expireTime: item.expire_time
                }));
                this.certificates.set(serialNo, {
                    serialNo,
                    certificate: certText,
                    effectiveTime: new Date(item.effective_time),
                    expireTime: new Date(item.expire_time)
                });
                this.logger.log(`已更新微信支付平台证书: ${serialNo}, 过期时间: ${item.expire_time}`);
            }
            this.logger.log(`成功获取 ${data.data.length} 个微信支付平台证书`);
            return true;
        }
        catch (error) {
            this.logger.error(`获取微信支付平台证书失败: ${error.message}`, error.stack);
            if (error.response) {
                this.logger.error(`错误响应: ${JSON.stringify(error.response.data)}`);
            }
            return false;
        }
    }
    decryptCertificate(ciphertext, nonce, associatedData) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            if (!wechatConfig.apiV3Key) {
                throw new Error('未配置微信支付APIv3密钥，无法解密证书');
            }
            const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
            const authTag = ciphertextBuffer.slice(ciphertextBuffer.length - 16);
            const data = ciphertextBuffer.slice(0, ciphertextBuffer.length - 16);
            const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(wechatConfig.apiV3Key), Buffer.from(nonce));
            decipher.setAuthTag(authTag);
            if (associatedData) {
                decipher.setAAD(Buffer.from(associatedData));
            }
            const decoded = decipher.update(data);
            const final = decipher.final();
            return Buffer.concat([decoded, final]).toString('utf8');
        }
        catch (error) {
            this.logger.error(`解密证书数据失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    getCertificate(serialNo) {
        const cert = this.certificates.get(serialNo);
        if (!cert) {
            this.logger.warn(`未找到序列号为 ${serialNo} 的微信支付平台证书`);
            return null;
        }
        return cert.certificate;
    }
    getLatestCertificate() {
        if (this.certificates.size === 0) {
            return null;
        }
        let latestCert = null;
        for (const cert of this.certificates.values()) {
            if (!latestCert || cert.expireTime > latestCert.expireTime) {
                latestCert = cert;
            }
        }
        if (!latestCert) {
            return null;
        }
        return {
            serialNo: latestCert.serialNo,
            certificate: latestCert.certificate
        };
    }
};
exports.PlatformCertificateService = PlatformCertificateService;
exports.PlatformCertificateService = PlatformCertificateService = PlatformCertificateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => payment_signature_service_1.PaymentSignatureService))),
    __metadata("design:paramtypes", [payment_config_service_1.PaymentConfigService,
        payment_signature_service_1.PaymentSignatureService])
], PlatformCertificateService);
//# sourceMappingURL=platform-certificate.service.js.map