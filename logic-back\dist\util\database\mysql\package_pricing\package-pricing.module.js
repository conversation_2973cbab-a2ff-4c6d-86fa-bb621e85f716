"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePricingModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const package_pricing_service_1 = require("./package-pricing.service");
const package_pricing_controller_1 = require("./package-pricing.controller");
const package_pricing_entity_1 = require("./entities/package-pricing.entity");
let PackagePricingModule = class PackagePricingModule {
};
exports.PackagePricingModule = PackagePricingModule;
exports.PackagePricingModule = PackagePricingModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([package_pricing_entity_1.PackagePricing])],
        controllers: [package_pricing_controller_1.PackagePricingController],
        providers: [package_pricing_service_1.PackagePricingService],
        exports: [package_pricing_service_1.PackagePricingService],
    })
], PackagePricingModule);
//# sourceMappingURL=package-pricing.module.js.map