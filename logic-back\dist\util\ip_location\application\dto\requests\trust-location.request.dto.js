"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrustLocationRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class TrustLocationRequestDto {
    province;
    city;
    reason;
}
exports.TrustLocationRequestDto = TrustLocationRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份', example: '广东省' }),
    (0, class_validator_1.IsNotEmpty)({ message: '省份不能为空' }),
    (0, class_validator_1.IsString)({ message: '省份必须是字符串' }),
    __metadata("design:type", String)
], TrustLocationRequestDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市', example: '深圳市' }),
    (0, class_validator_1.IsNotEmpty)({ message: '城市不能为空' }),
    (0, class_validator_1.IsString)({ message: '城市必须是字符串' }),
    __metadata("design:type", String)
], TrustLocationRequestDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '设置原因',
        required: false,
        example: '用户主动设置'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '设置原因必须是字符串' }),
    __metadata("design:type", String)
], TrustLocationRequestDto.prototype, "reason", void 0);
//# sourceMappingURL=trust-location.request.dto.js.map