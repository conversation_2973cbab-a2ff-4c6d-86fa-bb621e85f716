"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRefundModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const payment_refund_service_1 = require("./payment-refund.service");
const payment_refund_controller_1 = require("./payment-refund.controller");
const payment_refund_entity_1 = require("./entities/payment-refund.entity");
let PaymentRefundModule = class PaymentRefundModule {
};
exports.PaymentRefundModule = PaymentRefundModule;
exports.PaymentRefundModule = PaymentRefundModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([payment_refund_entity_1.PaymentRefund])],
        controllers: [payment_refund_controller_1.PaymentRefundController],
        providers: [payment_refund_service_1.PaymentRefundService],
        exports: [payment_refund_service_1.PaymentRefundService]
    })
], PaymentRefundModule);
//# sourceMappingURL=payment-refund.module.js.map