export declare enum LogType {
    PAYMENT = "payment",
    REFUND = "refund",
    NOTIFICATION = "notification",
    SYSTEM = "system"
}
export declare enum LogStatus {
    SUCCESS = "success",
    FAIL = "fail"
}
export declare enum OperationType {
    CREATE = "create",
    QUERY = "query",
    UPDATE = "update",
    DELETE = "delete",
    NOTIFY = "notify",
    CALLBACK = "callback"
}
export declare class CreatePaymentLogDto {
    logType: string;
    orderNo?: string;
    refundNo?: string;
    paymentChannel?: string;
    operation: string;
    operatorId?: string;
    clientIp?: string;
    requestData?: Record<string, any>;
    responseData?: Record<string, any>;
    status?: string;
    errorMessage?: string;
    executionTime?: number;
}
export declare class QueryPaymentLogDto {
    logType?: string;
    orderNo?: string;
    refundNo?: string;
    paymentChannel?: string;
    operation?: string;
    operatorId?: string;
    status?: string;
    startTime?: string;
    endTime?: string;
}
