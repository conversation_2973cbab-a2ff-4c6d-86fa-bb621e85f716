import { PaymentRecordService } from './payment-record.service';
import { CreatePaymentRecordDto } from './dto/create-payment-record.dto';
import { UpdatePaymentRecordDto } from './dto/update-payment-record.dto';
import { QueryPaymentRecordDto } from './dto/query-payment-record.dto';
import { PaymentRecord } from './entities/payment-record.entity';
export declare class PaymentRecordController {
    private readonly paymentRecordService;
    constructor(paymentRecordService: PaymentRecordService);
    create(createPaymentRecordDto: CreatePaymentRecordDto): Promise<{
        code: number;
        message: string;
        data: PaymentRecord;
    }>;
    findAll(queryPaymentRecordDto: QueryPaymentRecordDto, page?: number, limit?: number): Promise<{
        code: number;
        message: string;
        data: {
            items: PaymentRecord[];
            total: number;
        };
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        data: PaymentRecord;
    }>;
    findByOrderNo(orderNo: string): Promise<{
        code: number;
        message: string;
        data: PaymentRecord[];
    }>;
    update(id: string, updatePaymentRecordDto: UpdatePaymentRecordDto): Promise<{
        code: number;
        message: string;
        data: PaymentRecord;
    }>;
    updateStatus(id: string, body: {
        status: string;
        paymentId?: string;
        paymentTime?: Date;
        rawResponse?: Record<string, any>;
    }): Promise<{
        code: number;
        message: string;
        data: PaymentRecord;
    }>;
}
