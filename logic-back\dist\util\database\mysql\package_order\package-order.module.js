"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrderModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const package_order_service_1 = require("./package-order.service");
const package_order_controller_1 = require("./package-order.controller");
const package_order_entity_1 = require("./entities/package-order.entity");
let PackageOrderModule = class PackageOrderModule {
};
exports.PackageOrderModule = PackageOrderModule;
exports.PackageOrderModule = PackageOrderModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([package_order_entity_1.PackageOrder])],
        controllers: [package_order_controller_1.PackageOrderController],
        providers: [package_order_service_1.PackageOrderService],
        exports: [package_order_service_1.PackageOrderService],
    })
], PackageOrderModule);
//# sourceMappingURL=package-order.module.js.map