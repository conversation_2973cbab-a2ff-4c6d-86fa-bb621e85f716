import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 用户常用登录地统计表
 */
@Entity('user_common_locations')
@Index(['userId'])
@Index(['userId', 'isTrusted'])
@Index(['loginCount'])
@Index(['trustScore'])
export class UserCommonLocation {
  @PrimaryGeneratedColumn({ type: 'bigint', comment: '主键ID' })
  @ApiProperty({ description: '主键ID' })
  id: number;

  @Column({ type: 'bigint', comment: '用户ID' })
  @ApiProperty({ description: '用户ID' })
  userId: number;

  @Column({ 
    type: 'varchar', 
    length: 20, 
    default: '中国', 
    comment: '国家' 
  })
  @ApiProperty({ description: '国家', default: '中国' })
  country: string;

  @Column({ 
    type: 'varchar', 
    length: 30, 
    comment: '省份' 
  })
  @ApiProperty({ description: '省份' })
  province: string;

  @Column({ 
    type: 'varchar', 
    length: 30, 
    comment: '城市' 
  })
  @ApiProperty({ description: '城市' })
  city: string;

  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: true, 
    comment: '主要运营商' 
  })
  @ApiProperty({ description: '主要运营商', required: false })
  isp?: string;

  @Column({ 
    type: 'int', 
    default: 1, 
    comment: '登录次数' 
  })
  @ApiProperty({ description: '登录次数', default: 1 })
  loginCount: number;

  @Column({ 
    type: 'datetime', 
    nullable: true, 
    comment: '首次登录时间' 
  })
  @ApiProperty({ description: '首次登录时间', required: false })
  firstLoginAt?: Date;

  @Column({ 
    type: 'datetime', 
    nullable: true, 
    comment: '最后登录时间' 
  })
  @ApiProperty({ description: '最后登录时间', required: false })
  lastLoginAt?: Date;

  @Column({ 
    type: 'boolean', 
    default: false, 
    comment: '是否为可信地区' 
  })
  @ApiProperty({ description: '是否为可信地区', default: false })
  isTrusted: boolean;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    default: 0.00, 
    comment: '信任评分(0-100)' 
  })
  @ApiProperty({ description: '信任评分(0-100)', default: 0.00 })
  trustScore: number;

  @CreateDateColumn({ 
    type: 'datetime', 
    comment: '创建时间' 
  })
  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ 
    type: 'datetime', 
    comment: '更新时间' 
  })
  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  /**
   * 获取位置显示名称
   */
  getDisplayName(): string {
    const parts: string[] = [];
    if (this.country !== '中国') parts.push(this.country);
    if (this.province) parts.push(this.province);
    if (this.city && this.city !== this.province) parts.push(this.city);
    return parts.join(' ') || '未知位置';
  }

  /**
   * 检查是否为新位置（首次登录时间在指定天数内）
   */
  isNewLocation(days: number = 7): boolean {
    if (!this.firstLoginAt) return true;
    
    const daysDiff = (Date.now() - this.firstLoginAt.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff <= days;
  }

  /**
   * 检查是否为活跃位置（最后登录时间在指定天数内）
   */
  isActiveLocation(days: number = 30): boolean {
    if (!this.lastLoginAt) return false;
    
    const daysDiff = (Date.now() - this.lastLoginAt.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff <= days;
  }

  /**
   * 更新登录统计
   */
  updateLoginStats(loginTime: Date = new Date()): void {
    this.loginCount += 1;
    this.lastLoginAt = loginTime;
    
    if (!this.firstLoginAt) {
      this.firstLoginAt = loginTime;
    }

    // 根据登录次数自动调整信任评分
    this.updateTrustScore();
  }

  /**
   * 更新信任评分
   */
  private updateTrustScore(): void {
    // 基础评分：基于登录次数
    let baseScore = Math.min(this.loginCount * 2, 60);
    
    // 时间因子：考虑使用时长
    if (this.firstLoginAt) {
      const daysSinceFirst = (Date.now() - this.firstLoginAt.getTime()) / (1000 * 60 * 60 * 24);
      const timeBonus = Math.min(daysSinceFirst / 30 * 20, 20); // 最多20分时间奖励
      baseScore += timeBonus;
    }
    
    // 活跃度因子：考虑最近登录
    if (this.lastLoginAt) {
      const daysSinceLast = (Date.now() - this.lastLoginAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceLast <= 7) {
        baseScore += 10; // 最近活跃奖励
      } else if (daysSinceLast > 90) {
        baseScore -= 10; // 长期未使用惩罚
      }
    }
    
    // 手动可信位置额外加分
    if (this.isTrusted) {
      baseScore += 20;
    }
    
    this.trustScore = Math.min(Math.max(baseScore, 0), 100);
  }

  /**
   * 设置为可信位置
   */
  setAsTrusted(reason?: string): void {
    this.isTrusted = true;
    this.updateTrustScore();
  }

  /**
   * 取消可信状态
   */
  removeTrustedStatus(): void {
    this.isTrusted = false;
    this.updateTrustScore();
  }
}
