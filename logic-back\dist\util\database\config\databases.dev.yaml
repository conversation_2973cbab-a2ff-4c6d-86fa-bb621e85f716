mysql:
  type: mysql
  host: ************
  port: 13306
  username: root
  password: '123456'
  database: logicleaptest
  charset: utf8mb4
  entities:
    - 'dist/**/*.entity{.ts,.js}'
    # - "src/**/*.entity{.ts,.js}"
  synchronize: false # 生产环境中设置为false
  # 添加连接池配置
  extra:
    connectionLimit: 50        # 增加到50个连接
    waitForConnections: true
    queueLimit: 100           # 增加队列限制
    acquireTimeout: 60000     # 获取连接超时时间
    timeout: 60000            # 查询超时时间
    reconnect: true           # 自动重连
    maxReconnects: 3          # 最大重连次数
  # 添加重试机制
  retryAttempts: 3
  retryDelay: 3000
  keepConnectionAlive: true
  connectTimeout: 60000

redis:
  host: 127.0.0.1
  port: 6379
  password: ''
  db: 0
