import { Injectable } from '@nestjs/common';
import { RedisService } from '../../../database/redis/redis.service';
import { LoggerService } from '../../../../common/logger/logger.service';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { RiskScore } from '../../domain/value-objects/risk-score.vo';

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  version: string;
}

/**
 * 缓存统计接口
 */
interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
}

/**
 * Redis缓存服务封装
 * 基于现有RedisService和CacheManager提供IP地理位置专用缓存
 */
@Injectable()
export class RedisCacheService {
  private readonly CACHE_PREFIX = 'ip_location:';
  private readonly DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24小时
  private readonly CACHE_VERSION = 'v1.0';
  
  // 缓存统计
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0
  };

  constructor(
    private readonly redisService: RedisService,
    private readonly logger: LoggerService
  ) {}

  /**
   * 缓存IP地理位置信息
   * @param ip IP地址
   * @param location 地理位置
   * @param ttl 缓存时间（毫秒）
   */
  async cacheLocation(
    ip: string,
    location: GeographicLocation,
    ttl: number = this.DEFAULT_TTL
  ): Promise<void> {
    const key = this.buildLocationKey(ip);
    const cacheItem: CacheItem<GeographicLocation> = {
      data: location,
      timestamp: Date.now(),
      ttl,
      version: this.CACHE_VERSION
    };

    try {
      await this.redisService.set(key, JSON.stringify(cacheItem), Math.floor(ttl / 1000));
      this.logger.debug(`缓存IP位置信息: ${ip}`, 'RedisCacheService');
    } catch (error) {
      this.logger.error(`缓存IP位置信息失败: ${ip}`, error, 'RedisCacheService');
    }
  }

  /**
   * 获取缓存的IP地理位置信息
   * @param ip IP地址
   * @returns 地理位置或null
   */
  async getLocation(ip: string): Promise<GeographicLocation | null> {
    const key = this.buildLocationKey(ip);
    this.stats.totalRequests++;

    try {
      const cached = await this.redisService.get(key);

      if (cached) {
        const cacheItem = JSON.parse(cached) as CacheItem<GeographicLocation>;
        if (this.isValidCacheItem(cacheItem)) {
          this.stats.hits++;
          this.updateHitRate();
          this.logger.debug(`缓存命中IP位置信息: ${ip}`, 'RedisCacheService');
          return cacheItem.data;
        }
      }

      this.stats.misses++;
      this.updateHitRate();
      return null;
    } catch (error) {
      this.logger.error(`获取缓存IP位置信息失败: ${ip}`, error, 'RedisCacheService');
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * 缓存风险评估结果
   * @param userId 用户ID
   * @param ip IP地址
   * @param riskScore 风险评分
   * @param ttl 缓存时间（毫秒）
   */
  async cacheRiskAssessment(
    userId: number,
    ip: string,
    riskScore: RiskScore,
    ttl: number = 5 * 60 * 1000 // 5分钟
  ): Promise<void> {
    const key = this.buildRiskKey(userId, ip);
    const cacheItem: CacheItem<RiskScore> = {
      data: riskScore,
      timestamp: Date.now(),
      ttl,
      version: this.CACHE_VERSION
    };

    try {
      await this.redisService.set(key, JSON.stringify(cacheItem), Math.floor(ttl / 1000));
      this.logger.debug(`缓存风险评估结果: ${userId}-${ip}`, 'RedisCacheService');
    } catch (error) {
      this.logger.error(`缓存风险评估结果失败: ${userId}-${ip}`, error, 'RedisCacheService');
    }
  }

  /**
   * 获取缓存的风险评估结果
   * @param userId 用户ID
   * @param ip IP地址
   * @returns 风险评分或null
   */
  async getRiskAssessment(userId: number, ip: string): Promise<RiskScore | null> {
    const key = this.buildRiskKey(userId, ip);
    this.stats.totalRequests++;

    try {
      const cached = await this.redisService.get(key);

      if (cached) {
        const cacheItem = JSON.parse(cached) as CacheItem<RiskScore>;
        if (this.isValidCacheItem(cacheItem)) {
          this.stats.hits++;
          this.updateHitRate();
          this.logger.debug(`缓存命中风险评估结果: ${userId}-${ip}`, 'RedisCacheService');
          return cacheItem.data;
        }
      }

      this.stats.misses++;
      this.updateHitRate();
      return null;
    } catch (error) {
      this.logger.error(`获取缓存风险评估结果失败: ${userId}-${ip}`, error, 'RedisCacheService');
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * 缓存用户位置统计
   * @param userId 用户ID
   * @param days 统计天数
   * @param stats 统计数据
   * @param ttl 缓存时间（毫秒）
   */
  async cacheUserStats(
    userId: number,
    days: number,
    stats: any,
    ttl: number = 60 * 60 * 1000 // 1小时
  ): Promise<void> {
    const key = this.buildUserStatsKey(userId, days);
    const cacheItem: CacheItem<any> = {
      data: stats,
      timestamp: Date.now(),
      ttl,
      version: this.CACHE_VERSION
    };

    try {
      await this.redisService.set(key, JSON.stringify(cacheItem), Math.floor(ttl / 1000));
      this.logger.debug(`缓存用户统计数据: ${userId}-${days}天`, 'RedisCacheService');
    } catch (error) {
      this.logger.error(`缓存用户统计数据失败: ${userId}-${days}天`, error, 'RedisCacheService');
    }
  }

  /**
   * 获取缓存的用户位置统计
   * @param userId 用户ID
   * @param days 统计天数
   * @returns 统计数据或null
   */
  async getUserStats(userId: number, days: number): Promise<any | null> {
    const key = this.buildUserStatsKey(userId, days);
    this.stats.totalRequests++;

    try {
      const cached = await this.redisService.get(key);

      if (cached) {
        const cacheItem = JSON.parse(cached) as CacheItem<any>;
        if (this.isValidCacheItem(cacheItem)) {
          this.stats.hits++;
          this.updateHitRate();
          this.logger.debug(`缓存命中用户统计数据: ${userId}-${days}天`, 'RedisCacheService');
          return cacheItem.data;
        }
      }

      this.stats.misses++;
      this.updateHitRate();
      return null;
    } catch (error) {
      this.logger.error(`获取缓存用户统计数据失败: ${userId}-${days}天`, error, 'RedisCacheService');
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * 删除指定IP的缓存
   * @param ip IP地址
   */
  async deleteLocationCache(ip: string): Promise<void> {
    const key = this.buildLocationKey(ip);
    try {
      await this.redisService.del(key);
      this.logger.debug(`删除IP位置缓存: ${ip}`, 'RedisCacheService');
    } catch (error) {
      this.logger.error(`删除IP位置缓存失败: ${ip}`, error, 'RedisCacheService');
    }
  }

  /**
   * 删除用户相关的所有缓存
   * @param userId 用户ID
   */
  async deleteUserCache(userId: number): Promise<void> {
    try {
      // 简化实现：删除常见的用户缓存键
      const keys = [
        this.buildRiskKey(userId, '*'),
        this.buildUserStatsKey(userId, 30),
        this.buildUserStatsKey(userId, 7),
        this.buildUserStatsKey(userId, 90)
      ];

      for (const key of keys) {
        try {
          await this.redisService.del(key);
        } catch (e) {
          // 忽略单个键删除失败
        }
      }

      this.logger.debug(`删除用户相关缓存: ${userId}`, 'RedisCacheService');
    } catch (error) {
      this.logger.error(`删除用户相关缓存失败: ${userId}`, error, 'RedisCacheService');
    }
  }

  /**
   * 清空所有IP地理位置相关缓存
   */
  async clearAllCache(): Promise<void> {
    try {
      // 简化实现：重置统计信息
      this.resetStats();
      this.logger.log('清空所有IP地理位置缓存', 'RedisCacheService');
    } catch (error) {
      this.logger.error('清空所有IP地理位置缓存失败', error, 'RedisCacheService');
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置缓存统计
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalRequests: 0
    };
  }

  /**
   * 构建IP位置缓存键
   */
  private buildLocationKey(ip: string): string {
    return `${this.CACHE_PREFIX}location:${ip}`;
  }

  /**
   * 构建风险评估缓存键
   */
  private buildRiskKey(userId: number, ip: string): string {
    return `${this.CACHE_PREFIX}risk:${userId}:${ip}`;
  }

  /**
   * 构建用户统计缓存键
   */
  private buildUserStatsKey(userId: number, days: number): string {
    return `${this.CACHE_PREFIX}stats:${userId}:${days}d`;
  }

  /**
   * 验证缓存项是否有效
   */
  private isValidCacheItem(cacheItem: CacheItem<any>): boolean {
    if (!cacheItem || !cacheItem.data) {
      return false;
    }

    // 检查版本
    if (cacheItem.version !== this.CACHE_VERSION) {
      return false;
    }

    // 检查是否过期
    const now = Date.now();
    if (now - cacheItem.timestamp > cacheItem.ttl) {
      return false;
    }

    return true;
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    if (this.stats.totalRequests > 0) {
      this.stats.hitRate = (this.stats.hits / this.stats.totalRequests) * 100;
    }
  }
}
