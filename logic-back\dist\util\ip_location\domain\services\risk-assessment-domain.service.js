"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessmentDomainService = void 0;
const common_1 = require("@nestjs/common");
const risk_score_vo_1 = require("../value-objects/risk-score.vo");
let RiskAssessmentDomainService = class RiskAssessmentDomainService {
    defaultConfig = {
        lowThreshold: 30,
        mediumThreshold: 70,
        highThreshold: 100,
        foreignLoginWeight: 60,
        crossProvinceWeight: 40,
        newLocationWeight: 15,
        ispChangeWeight: 5,
        dataQualityWeight: 20,
        newUserProtectionDays: 7,
        newUserRiskReduction: 15,
        minDataQuality: 20
    };
    config;
    constructor(config) {
        this.config = { ...this.defaultConfig, ...config };
    }
    assessLoginRisk(currentLocation, userHistory, ipAddress) {
        const riskFactors = [];
        let riskScore = 0;
        if (currentLocation.isForeign) {
            riskScore += this.config.foreignLoginWeight;
            riskFactors.push('境外登录');
        }
        if (currentLocation.confidence < this.config.minDataQuality) {
            riskScore += this.config.dataQualityWeight;
            riskFactors.push('位置信息不完整');
        }
        const hasProvinceHistory = this.hasLocationInHistory(currentLocation, userHistory, 'province');
        if (!hasProvinceHistory && currentLocation.isDomestic && currentLocation.province !== '未知') {
            riskScore += this.config.crossProvinceWeight;
            riskFactors.push(`跨省登录至${currentLocation.province}`);
        }
        if (hasProvinceHistory) {
            const hasCityHistory = this.hasLocationInHistory(currentLocation, userHistory, 'city');
            if (!hasCityHistory && currentLocation.city !== '未知') {
                riskScore += this.config.newLocationWeight;
                riskFactors.push(`省内异地登录至${currentLocation.city}`);
            }
        }
        if (hasProvinceHistory && !this.hasISPInHistory(currentLocation, userHistory)) {
            riskScore += this.config.ispChangeWeight;
            riskFactors.push('运营商变化');
        }
        if (userHistory.riskLoginCount > 5) {
            riskScore += 10;
            riskFactors.push('频繁异地登录');
        }
        if (this.isNewUser(userHistory)) {
            riskScore = Math.max(riskScore - this.config.newUserRiskReduction, 0);
            riskFactors.push('新用户保护');
        }
        if (ipAddress.type === 'IPv6' && !hasProvinceHistory) {
            riskScore += 5;
            riskFactors.push('IPv6新位置');
        }
        const finalScore = Math.min(Math.max(riskScore, 0), 100);
        return risk_score_vo_1.RiskScore.create(finalScore, riskFactors);
    }
    assessLocationChangeRisk(previousLocation, currentLocation) {
        const riskFactors = [];
        let riskScore = 0;
        const similarity = currentLocation.calculateSimilarity(previousLocation);
        if (similarity < 20) {
            riskScore += 50;
            riskFactors.push('位置完全不同');
        }
        else if (similarity < 50) {
            riskScore += 30;
            riskFactors.push('位置差异较大');
        }
        else if (similarity < 80) {
            riskScore += 10;
            riskFactors.push('位置有所变化');
        }
        if (previousLocation.country !== currentLocation.country) {
            riskScore += 40;
            riskFactors.push('跨国登录');
        }
        if (!currentLocation.isSameProvince(previousLocation)) {
            riskScore += 25;
            riskFactors.push('跨省登录');
        }
        if (!currentLocation.isSameCity(previousLocation)) {
            riskScore += 10;
            riskFactors.push('跨城市登录');
        }
        return risk_score_vo_1.RiskScore.create(riskScore, riskFactors);
    }
    analyzeVerificationNeeds(riskScore, userHistory) {
        let needsVerification = riskScore.needsVerification;
        let urgency = 'LOW';
        if (riskScore.isHighRisk) {
            needsVerification = true;
            urgency = 'HIGH';
        }
        if (riskScore.isMediumRisk && this.isNewUser(userHistory)) {
            needsVerification = true;
            urgency = 'MEDIUM';
        }
        if (riskScore.factors.includes('境外登录')) {
            needsVerification = true;
            urgency = 'HIGH';
        }
        return {
            needsVerification,
            verificationMethods: riskScore.recommendedVerificationMethods,
            reason: riskScore.reason,
            urgency
        };
    }
    calculateUserRiskProfile(userHistory) {
        const characteristics = [];
        const recommendations = [];
        let trustScore = 50;
        if (userHistory.accountAge > 365) {
            trustScore += 20;
            characteristics.push('老用户');
        }
        else if (userHistory.accountAge < 30) {
            trustScore -= 10;
            characteristics.push('新用户');
            recommendations.push('建议增强验证');
        }
        const riskRate = userHistory.totalLoginCount > 0
            ? (userHistory.riskLoginCount / userHistory.totalLoginCount) * 100
            : 0;
        if (riskRate > 20) {
            trustScore -= 15;
            characteristics.push('频繁异地登录');
            recommendations.push('建议设置常用登录地');
        }
        else if (riskRate < 5) {
            trustScore += 10;
            characteristics.push('登录位置稳定');
        }
        const trustedLocationCount = userHistory.commonLocations
            .filter(loc => loc.isTrusted).length;
        if (trustedLocationCount > 0) {
            trustScore += trustedLocationCount * 5;
            characteristics.push(`${trustedLocationCount}个可信位置`);
        }
        else {
            recommendations.push('建议设置可信登录地');
        }
        let riskLevel;
        if (trustScore >= 70) {
            riskLevel = 'LOW';
        }
        else if (trustScore >= 40) {
            riskLevel = 'MEDIUM';
        }
        else {
            riskLevel = 'HIGH';
            recommendations.push('建议完善账户安全设置');
        }
        return {
            riskLevel,
            trustScore: Math.max(0, Math.min(100, trustScore)),
            characteristics,
            recommendations
        };
    }
    hasLocationInHistory(location, userHistory, level) {
        return userHistory.commonLocations.some(historyItem => {
            switch (level) {
                case 'country':
                    return historyItem.location.country === location.country &&
                        location.country !== '未知';
                case 'province':
                    return historyItem.location.isSameProvince(location);
                case 'city':
                    return historyItem.location.isSameCity(location);
                default:
                    return false;
            }
        });
    }
    hasISPInHistory(location, userHistory) {
        return userHistory.commonLocations.some(historyItem => historyItem.location.isSameProvince(location) &&
            historyItem.location.isSameISP(location));
    }
    isNewUser(userHistory) {
        return userHistory.totalLoginCount < 3 ||
            userHistory.accountAge < this.config.newUserProtectionDays;
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
    getConfig() {
        return { ...this.config };
    }
};
exports.RiskAssessmentDomainService = RiskAssessmentDomainService;
exports.RiskAssessmentDomainService = RiskAssessmentDomainService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object])
], RiskAssessmentDomainService);
//# sourceMappingURL=risk-assessment-domain.service.js.map