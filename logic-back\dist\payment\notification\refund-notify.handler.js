"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RefundNotifyHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundNotifyHandler = void 0;
const common_1 = require("@nestjs/common");
const payment_service_1 = require("../services/payment.service");
const notify_service_1 = require("./notify.service");
const payment_record_service_1 = require("../../util/database/mysql/payment_record/payment-record.service");
const lock_manager_1 = require("../lock/lock.manager");
let RefundNotifyHandler = RefundNotifyHandler_1 = class RefundNotifyHandler {
    paymentService;
    notifyService;
    paymentRecordService;
    lockManager;
    logger = new common_1.Logger(RefundNotifyHandler_1.name);
    constructor(paymentService, notifyService, paymentRecordService, lockManager) {
        this.paymentService = paymentService;
        this.notifyService = notifyService;
        this.paymentRecordService = paymentRecordService;
        this.lockManager = lockManager;
    }
    async handleRefundNotify(channel, data) {
        const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        this.logger.log(`[${requestId}] 开始处理${channel}退款通知`);
        try {
            this.logger.log(`[${requestId}] 尝试提取退款订单号和验证通知签名`);
            let outRefundNo;
            let outTradeNo;
            let refundId;
            let refundAmount = 0;
            let status;
            if (channel === 'alipay') {
                outRefundNo = data.out_request_no;
                outTradeNo = data.out_trade_no;
                refundId = data.trade_no;
                refundAmount = parseFloat(data.refund_amount || '0');
                status = data.refund_status === 'REFUND_SUCCESS' ? 'success' : 'failed';
            }
            else if (channel === 'wechatpay') {
                try {
                    let decryptedData;
                    if (data.body && data.body.resource_decoded) {
                        decryptedData = data.body.resource_decoded;
                    }
                    else {
                        const strategy = this.paymentService.getPaymentStrategy('wechatpay');
                        const verifyResult = await strategy.verifyNotify(data);
                        if (!verifyResult.verified) {
                            this.logger.error(`[${requestId}] 微信支付退款通知验签失败`);
                            return { success: false, message: '验签失败' };
                        }
                        if (verifyResult.rawNotify) {
                            decryptedData = verifyResult.rawNotify;
                        }
                        else {
                            throw new Error('无法解密通知数据');
                        }
                    }
                    this.logger.debug(`[${requestId}] 微信支付退款通知解密数据: ${JSON.stringify(decryptedData)}`);
                    outRefundNo = decryptedData.out_refund_no;
                    outTradeNo = decryptedData.out_trade_no;
                    refundId = decryptedData.refund_id;
                    if (decryptedData.amount && typeof decryptedData.amount.refund === 'number') {
                        refundAmount = decryptedData.amount.refund / 100;
                    }
                    status = decryptedData.refund_status === 'SUCCESS' ? 'success' : 'failed';
                    this.logger.debug(`[${requestId}] 微信支付退款通知解析结果: outRefundNo=${outRefundNo}, outTradeNo=${outTradeNo}, refundId=${refundId}, refundAmount=${refundAmount}, status=${status}`);
                }
                catch (error) {
                    this.logger.error(`[${requestId}] 微信支付退款通知解析失败: ${error.message}`, error.stack);
                    return { success: false, message: `通知解析失败: ${error.message}` };
                }
            }
            if (!outRefundNo || !outTradeNo) {
                this.logger.warn(`[${requestId}] ${channel}退款通知缺少必要的订单号信息`);
                return { success: false, message: '缺少订单号信息' };
            }
            this.logger.log(`[${requestId}] 提取退款信息成功: 退款单号=${outRefundNo}, 原订单号=${outTradeNo}, 退款金额=${refundAmount}, 状态=${status}`);
            return this.lockManager.withDistributedLock(`refund:notify:${outRefundNo}`, async () => {
                this.logger.log(`[${requestId}] 获取退款通知分布式锁成功: ${outRefundNo}, 准备处理退款通知`);
                try {
                    if (status === 'success') {
                        const result = await this.notifyService.handleRefundSuccess(outRefundNo, refundId || '', refundAmount, channel, {
                            outTradeNo,
                            refundId,
                            rawNotify: data,
                            decryptedData: data.body?.resource_decoded
                        });
                        if (result) {
                            this.logger.log(`[${requestId}] ${channel}退款成功通知处理成功: ${outRefundNo}`);
                            return { success: true, message: '处理成功' };
                        }
                        else {
                            this.logger.warn(`[${requestId}] ${channel}退款成功通知处理失败: ${outRefundNo}`);
                            return { success: false, message: '处理失败' };
                        }
                    }
                    else {
                        const failReason = data.body?.resource_decoded?.refund_status || '未知原因';
                        const result = await this.notifyService.handleRefundFail(outRefundNo, `退款失败: ${failReason}`, channel, {
                            outTradeNo,
                            refundId,
                            rawNotify: data,
                            decryptedData: data.body?.resource_decoded
                        });
                        if (result) {
                            this.logger.log(`[${requestId}] ${channel}退款失败通知处理成功: ${outRefundNo}`);
                            return { success: true, message: '处理成功' };
                        }
                        else {
                            this.logger.warn(`[${requestId}] ${channel}退款失败通知处理失败: ${outRefundNo}`);
                            return { success: false, message: '处理失败' };
                        }
                    }
                }
                catch (error) {
                    this.logger.error(`[${requestId}] 处理${channel}退款通知异常: ${error.message}`, error.stack);
                    return { success: false, message: `处理异常: ${error.message}` };
                }
                finally {
                    this.logger.log(`[${requestId}] 退款通知处理完成，分布式锁即将释放: ${outRefundNo}`);
                }
            }, 10000);
        }
        catch (error) {
            this.logger.error(`[${requestId}] 处理${channel}退款通知异常: ${error.message}`, error.stack);
            return { success: false, message: `处理异常: ${error.message}` };
        }
    }
    async manualTriggerRefundSuccess(outRefundNo, outTradeNo, channel) {
        return this.lockManager.withDistributedLock(`refund:manual:${outRefundNo}`, async () => {
            try {
                this.logger.log(`手动触发退款成功处理: ${outRefundNo}, 订单号: ${outTradeNo}, 渠道: ${channel}`);
                const paymentRecords = await this.paymentRecordService.findByOrderNo(outTradeNo);
                if (!paymentRecords || paymentRecords.length === 0) {
                    this.logger.error(`未找到支付记录: ${outTradeNo}`);
                    return false;
                }
                const paymentRecord = paymentRecords[0];
                const queryResult = {
                    success: true,
                    refunded: true,
                    refundId: `refund_${Date.now()}`,
                    amount: paymentRecord.amount,
                    status: 'success',
                };
                if (!queryResult.success) {
                    this.logger.error(`查询退款状态失败: ${outRefundNo}`);
                    return false;
                }
                if (!queryResult.refunded) {
                    this.logger.warn(`退款未完成，无法手动触发: ${outRefundNo}, 状态: ${queryResult.status}`);
                    return false;
                }
                const result = await this.notifyService.handleRefundSuccess(outRefundNo, queryResult.refundId, queryResult.amount, channel, {
                    outTradeNo,
                    rawQuery: queryResult
                });
                if (!result) {
                    this.logger.error(`手动触发退款成功处理失败: ${outRefundNo}`);
                    return false;
                }
                this.logger.log(`手动触发退款成功处理完成: ${outRefundNo}`);
                return true;
            }
            catch (error) {
                this.logger.error(`手动触发退款成功处理异常: ${error.message}`, error.stack);
                return false;
            }
        }, 10000);
    }
};
exports.RefundNotifyHandler = RefundNotifyHandler;
exports.RefundNotifyHandler = RefundNotifyHandler = RefundNotifyHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_service_1.PaymentService,
        notify_service_1.NotifyService,
        payment_record_service_1.PaymentRecordService,
        lock_manager_1.LockManager])
], RefundNotifyHandler);
//# sourceMappingURL=refund-notify.handler.js.map