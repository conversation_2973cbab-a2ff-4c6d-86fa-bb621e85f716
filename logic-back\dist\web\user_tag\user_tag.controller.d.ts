import { UserTagService } from './user_tag.service';
import { Tag } from 'src/util/database/mysql/tag/entities/tag.entity';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class UserTagController {
    private readonly userTagService;
    private readonly httpResponseResultService;
    constructor(userTagService: UserTagService, httpResponseResultService: HttpResponseResultService);
    createTag(data: {
        name: string;
        description?: string;
        color?: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<Tag>>;
    updateTag(id: number, data: {
        name?: string;
        description?: string;
        color?: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<Tag>>;
    deleteTag(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<boolean>>;
    getTagInfo(id: number): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<Tag>>;
    getTagList(query: {
        page?: number;
        size?: number;
        keyword?: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null> | import("../http_response_result/http-response.interface").HttpResponse<{
        list: Tag[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>>;
}
