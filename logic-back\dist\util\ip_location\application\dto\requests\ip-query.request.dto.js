"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpQueryRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class IpQueryRequestDto {
    ip;
    includeRisk = false;
}
exports.IpQueryRequestDto = IpQueryRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'IP地址', example: '**************' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'IP地址不能为空' }),
    (0, class_validator_1.IsString)({ message: 'IP地址必须是字符串' }),
    __metadata("design:type", String)
], IpQueryRequestDto.prototype, "ip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含风险评估',
        required: false,
        default: false,
        type: Boolean
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return value;
    }),
    (0, class_validator_1.IsBoolean)({ message: '包含风险评估必须是布尔值' }),
    __metadata("design:type", Boolean)
], IpQueryRequestDto.prototype, "includeRisk", void 0);
//# sourceMappingURL=ip-query.request.dto.js.map