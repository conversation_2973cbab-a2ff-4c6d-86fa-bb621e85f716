import { RefundService } from '../services/refund.service';
import { RefundRequestDto, RefundQueryDto, RefundResultDto } from '../dto/refund-dto';
import { Request } from 'express';
interface RequestWithUser extends Request {
    user: any;
}
export declare class RefundController {
    private readonly refundService;
    constructor(refundService: RefundService);
    create(refundRequestDto: RefundRequestDto, req: RequestWithUser): Promise<RefundResultDto>;
    queryRefund(refundQueryDto: RefundQueryDto): Promise<RefundResultDto>;
    getRefundDetail(refundNo: string): Promise<RefundResultDto>;
    checkRefundStatus(refundNo: string): Promise<RefundResultDto>;
}
export {};
