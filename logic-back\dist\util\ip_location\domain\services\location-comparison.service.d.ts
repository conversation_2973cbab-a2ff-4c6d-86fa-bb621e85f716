import { GeographicLocation } from '../value-objects/geographic-location.vo';
export interface LocationComparisonResult {
    similarity: number;
    differences: string[];
    commonalities: string[];
    riskFactors: string[];
    isSignificantChange: boolean;
    changeLevel: 'NONE' | 'MINOR' | 'MODERATE' | 'MAJOR' | 'EXTREME';
}
export interface LocationCluster {
    centroid: GeographicLocation;
    locations: GeographicLocation[];
    radius: number;
    confidence: number;
}
export declare class LocationComparisonService {
    compareLocations(location1: GeographicLocation, location2: GeographicLocation): LocationComparisonResult;
    compareMultipleLocations(locations: GeographicLocation[]): number[][];
    findMostSimilarLocation(targetLocation: GeographicLocation, candidateLocations: GeographicLocation[]): {
        location: GeographicLocation;
        similarity: number;
    } | null;
    clusterLocations(locations: GeographicLocation[], similarityThreshold?: number): LocationCluster[];
    analyzeLocationTrend(locationHistory: GeographicLocation[]): {
        stability: number;
        mainLocations: GeographicLocation[];
        changeFrequency: number;
        trendDirection: 'STABLE' | 'EXPANDING' | 'CONTRACTING' | 'CHAOTIC';
    };
    private determineChangeLevel;
    private calculateCentroid;
    private calculateClusterRadius;
    private calculateClusterConfidence;
}
