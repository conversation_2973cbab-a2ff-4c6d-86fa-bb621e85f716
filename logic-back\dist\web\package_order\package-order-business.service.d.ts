import { Repository, DataSource } from 'typeorm';
import { PackageOrderService } from '../../util/database/mysql/package_order/package-order.service';
import { PackageInfoService } from '../../util/database/mysql/package_info/package_info.service';
import { UserInfoService } from '../user_info/user_info.service';
import { UserPackageService } from '../../util/database/mysql/user_package/user_package.service';
import { UserPointsService } from '../../util/database/mysql/user_points/user_points.service';
import { PaymentService } from '../../payment/services/payment.service';
import { PackagePricingService } from '../../util/database/mysql/package_pricing/package-pricing.service';
import { PaymentOrderService } from '../../util/database/mysql/payment_order/payment-order.service';
import { PackageOrder } from '../../util/database/mysql/package_order/entities/package-order.entity';
import { UserPackage } from '../../util/database/mysql/user_package/entities/user_package.entity';
import { UserInfo } from '../../util/database/mysql/user_info/entities/user_info.entity';
import { PurchasePackageDto, PaymentCallbackDto } from './dto';
export declare class PackageOrderBusinessService {
    private readonly dataSource;
    private readonly packageOrderService;
    private readonly packageInfoService;
    private readonly userInfoService;
    private readonly userPackageService;
    private readonly userPointsService;
    private readonly paymentService;
    private readonly packagePricingService;
    private readonly paymentOrderService;
    private readonly packageOrderRepository;
    private readonly userPackageRepository;
    private readonly userInfoRepository;
    private readonly logger;
    constructor(dataSource: DataSource, packageOrderService: PackageOrderService, packageInfoService: PackageInfoService, userInfoService: UserInfoService, userPackageService: UserPackageService, userPointsService: UserPointsService, paymentService: PaymentService, packagePricingService: PackagePricingService, paymentOrderService: PaymentOrderService, packageOrderRepository: Repository<PackageOrder>, userPackageRepository: Repository<UserPackage>, userInfoRepository: Repository<UserInfo>);
    getAvailablePackages(): Promise<Array<{
        packageId: number;
        packageName: string;
        packageDescription: string;
        points: number;
        validityDays: number;
        originalPrice: number;
        currentPrice: number;
        discountRate: number;
        savings: number;
        currency: string;
        promotion?: Record<string, any>;
    }>>;
    purchasePackage(userId: string, purchaseDto: PurchasePackageDto): Promise<{
        orderNo: string;
        paymentUrl?: string;
        qrCode?: string;
        amount: number;
        packageName: string;
        expireTime?: Date;
    }>;
    handlePaymentSuccess(callbackDto: PaymentCallbackDto): Promise<void>;
    private assignPackageToUser;
    getUserOrders(userId: string, page?: number, limit?: number): Promise<{
        data: PackageOrder[];
        total: number;
        page: number;
        limit: number;
    }>;
    cancelOrder(userId: string, orderNo: string): Promise<PackageOrder>;
    getOrderDetail(userId: string, orderNo: string): Promise<PackageOrder>;
    getOrderStatus(orderNo: string): Promise<{
        orderNo: string;
        userId: string;
        status: string;
        packageName?: string;
        points?: number;
        amount?: number;
        paidTime?: Date;
        createTime?: Date;
    }>;
    private mapPaymentChannel;
    private mapPaymentMode;
    private generateOrderNo;
}
