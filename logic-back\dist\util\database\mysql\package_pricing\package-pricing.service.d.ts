import { Repository } from 'typeorm';
import { PackagePricing } from './entities/package-pricing.entity';
import { CreatePackagePricingDto, UpdatePackagePricingDto, PriceType } from './dto';
export declare class PackagePricingService {
    private packagePricingRepository;
    constructor(packagePricingRepository: Repository<PackagePricing>);
    create(createPackagePricingDto: CreatePackagePricingDto): Promise<PackagePricing>;
    findAll(): Promise<PackagePricing[]>;
    findOne(id: number): Promise<PackagePricing>;
    getCurrentPricing(packageId: number, priceType?: PriceType): Promise<PackagePricing | null>;
    getPackagePricings(packageId: number): Promise<PackagePricing[]>;
    update(id: number, updatePackagePricingDto: UpdatePackagePricingDto): Promise<PackagePricing>;
    remove(id: number): Promise<void>;
    getAllCurrentPricings(): Promise<Array<{
        packageId: number;
        packageName: string;
        packageDescription: string;
        points: number;
        validityDays: number;
        originalPrice: number;
        currentPrice: number;
        discountRate: number;
        savings: number;
        priceType: string;
        currency: string;
        promotion?: Record<string, any>;
    }>>;
}
