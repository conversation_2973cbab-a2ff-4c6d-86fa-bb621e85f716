import { Repository } from 'typeorm';
import { PaymentRecord } from './entities/payment-record.entity';
import { CreatePaymentRecordDto } from './dto/create-payment-record.dto';
import { UpdatePaymentRecordDto } from './dto/update-payment-record.dto';
import { QueryPaymentRecordDto } from './dto/query-payment-record.dto';
export declare class PaymentRecordService {
    private readonly paymentRecordRepository;
    private readonly logger;
    constructor(paymentRecordRepository: Repository<PaymentRecord>);
    create(createPaymentRecordDto: CreatePaymentRecordDto): Promise<PaymentRecord>;
    update(id: string, updatePaymentRecordDto: UpdatePaymentRecordDto): Promise<PaymentRecord>;
    findById(id: string): Promise<PaymentRecord>;
    findByOrderNo(orderNo: string): Promise<PaymentRecord[]>;
    findByPaymentId(paymentId: string, paymentChannel?: string): Promise<PaymentRecord>;
    findAll(queryPaymentRecordDto: QueryPaymentRecordDto, page?: number, limit?: number): Promise<{
        items: PaymentRecord[];
        total: number;
    }>;
    updateStatus(id: string, status: string, paymentId?: string, paymentTime?: Date, rawResponse?: Record<string, any>): Promise<PaymentRecord>;
}
