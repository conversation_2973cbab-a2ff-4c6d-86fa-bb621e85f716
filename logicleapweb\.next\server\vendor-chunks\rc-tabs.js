"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-tabs";
exports.ids = ["vendor-chunks/rc-tabs"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-tabs/es/TabContext.js":
/*!***********************************************!*\
  !*** ./node_modules/rc-tabs/es/TabContext.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUN0Qyw4RUFBNEIsb0RBQWEsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10YWJzL2VzL1RhYkNvbnRleHQuanM/ZjBkYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL2NyZWF0ZUNvbnRleHQobnVsbCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/AddButton.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar AddButton = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AddButton);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L0FkZEJ1dHRvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFDL0IsNkJBQTZCLDZDQUFnQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixnREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0QsaUVBQWUsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10YWJzL2VzL1RhYk5hdkxpc3QvQWRkQnV0dG9uLmpzPzU4ZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xudmFyIEFkZEJ1dHRvbiA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgZWRpdGFibGUgPSBwcm9wcy5lZGl0YWJsZSxcbiAgICBsb2NhbGUgPSBwcm9wcy5sb2NhbGUsXG4gICAgc3R5bGUgPSBwcm9wcy5zdHlsZTtcbiAgaWYgKCFlZGl0YWJsZSB8fCBlZGl0YWJsZS5zaG93QWRkID09PSBmYWxzZSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImJ1dHRvblwiLCB7XG4gICAgcmVmOiByZWYsXG4gICAgdHlwZTogXCJidXR0b25cIixcbiAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItbmF2LWFkZFwiKSxcbiAgICBzdHlsZTogc3R5bGUsXG4gICAgXCJhcmlhLWxhYmVsXCI6IChsb2NhbGUgPT09IG51bGwgfHwgbG9jYWxlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBsb2NhbGUuYWRkQXJpYUxhYmVsKSB8fCAnQWRkIHRhYicsXG4gICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhldmVudCkge1xuICAgICAgZWRpdGFibGUub25FZGl0KCdhZGQnLCB7XG4gICAgICAgIGV2ZW50OiBldmVudFxuICAgICAgfSk7XG4gICAgfVxuICB9LCBlZGl0YWJsZS5hZGRJY29uIHx8ICcrJyk7XG59KTtcbmV4cG9ydCBkZWZhdWx0IEFkZEJ1dHRvbjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/ExtraContent.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar ExtraContent = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(extra) === 'object' && ! /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (true) {\n  ExtraContent.displayName = 'ExtraContent';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExtraContent);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L0V4dHJhQ29udGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ3pCO0FBQy9CLGdDQUFnQyw2Q0FBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU0sNkVBQU8sdUNBQXVDLGlEQUFvQjtBQUN4RTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGdEQUFtQjtBQUNuRDtBQUNBO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRCxJQUFJLElBQXFDO0FBQ3pDO0FBQ0E7QUFDQSxpRUFBZSxZQUFZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLXRhYnMvZXMvVGFiTmF2TGlzdC9FeHRyYUNvbnRlbnQuanM/YzNhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX3R5cGVvZiBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdHlwZW9mXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgRXh0cmFDb250ZW50ID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgdmFyIHBvc2l0aW9uID0gcHJvcHMucG9zaXRpb24sXG4gICAgcHJlZml4Q2xzID0gcHJvcHMucHJlZml4Q2xzLFxuICAgIGV4dHJhID0gcHJvcHMuZXh0cmE7XG4gIGlmICghZXh0cmEpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICB2YXIgY29udGVudDtcblxuICAvLyBQYXJzZSBleHRyYVxuICB2YXIgYXNzZXJ0RXh0cmEgPSB7fTtcbiAgaWYgKF90eXBlb2YoZXh0cmEpID09PSAnb2JqZWN0JyAmJiAhIC8qI19fUFVSRV9fKi9SZWFjdC5pc1ZhbGlkRWxlbWVudChleHRyYSkpIHtcbiAgICBhc3NlcnRFeHRyYSA9IGV4dHJhO1xuICB9IGVsc2Uge1xuICAgIGFzc2VydEV4dHJhLnJpZ2h0ID0gZXh0cmE7XG4gIH1cbiAgaWYgKHBvc2l0aW9uID09PSAncmlnaHQnKSB7XG4gICAgY29udGVudCA9IGFzc2VydEV4dHJhLnJpZ2h0O1xuICB9XG4gIGlmIChwb3NpdGlvbiA9PT0gJ2xlZnQnKSB7XG4gICAgY29udGVudCA9IGFzc2VydEV4dHJhLmxlZnQ7XG4gIH1cbiAgcmV0dXJuIGNvbnRlbnQgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWV4dHJhLWNvbnRlbnRcIiksXG4gICAgcmVmOiByZWZcbiAgfSwgY29udGVudCkgOiBudWxsO1xufSk7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBFeHRyYUNvbnRlbnQuZGlzcGxheU5hbWUgPSAnRXh0cmFDb250ZW50Jztcbn1cbmV4cG9ydCBkZWZhdWx0IEV4dHJhQ29udGVudDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/OperationNode.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/OperationNode.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var rc_dropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-dropdown */ \"(ssr)/./node_modules/rc-dropdown/es/index.js\");\n/* harmony import */ var rc_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-menu */ \"(ssr)/./node_modules/rc-menu/es/index.js\");\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/rc-tabs/es/util.js\");\n/* harmony import */ var _AddButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./AddButton */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar OperationNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    tabs = props.tabs,\n    locale = props.locale,\n    mobile = props.mobile,\n    _props$more = props.more,\n    moreProps = _props$more === void 0 ? {} : _props$more,\n    style = props.style,\n    className = props.className,\n    editable = props.editable,\n    tabBarGutter = props.tabBarGutter,\n    rtl = props.rtl,\n    removeAriaLabel = props.removeAriaLabel,\n    onTabClick = props.onTabClick,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var _moreProps$icon = moreProps.icon,\n    moreIcon = _moreProps$icon === void 0 ? 'More' : _moreProps$icon;\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n    onClick: function onClick(_ref) {\n      var key = _ref.key,\n        domEvent = _ref.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = (0,_util__WEBPACK_IMPORTED_MODULE_8__.getRemovable)(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_menu__WEBPACK_IMPORTED_MODULE_5__.MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"span\", null, label), removable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].DOWN, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].SPACE, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].ESC:\n        setOpen(false);\n        break;\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].SPACE:\n      case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_6__[\"default\"].ENTER:\n        if (selectedKey !== null) {\n          onTabClick(selectedKey, e);\n        }\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classnames__WEBPACK_IMPORTED_MODULE_3___default()((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_dropdown__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    visible: tabs.length ? open : false,\n    onVisibleChange: setOpen,\n    overlayClassName: classnames__WEBPACK_IMPORTED_MODULE_3___default()(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, moreProps), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\",\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_AddButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.memo(OperationNode, function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L09wZXJhdGlvbk5vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUEwRDtBQUNjO0FBQ0Y7QUFDbEM7QUFDRDtBQUNNO0FBQ0E7QUFDVjtBQUNhO0FBQ0w7QUFDSDtBQUNwQyxpQ0FBaUMsNkNBQWdCO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwrQ0FBUTtBQUMxQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDBCQUEwQixnREFBbUIsQ0FBQywrQ0FBSTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbURBQVk7QUFDaEMsd0JBQXdCLGdEQUFtQixDQUFDLDZDQUFRO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLGVBQWUsZ0RBQW1CLGlEQUFpRCxnREFBbUI7QUFDM0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxvQkFBb0IsU0FBUztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywwREFBTyxPQUFPLDBEQUFPLFFBQVEsMERBQU87QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywwREFBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLDBEQUFPO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMERBQU87QUFDbEI7QUFDQTtBQUNBLFdBQVcsMERBQU87QUFDbEIsV0FBVywwREFBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLGtCQUFrQixxRkFBZSxHQUFHO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGlEQUFVLENBQUMscUZBQWUsR0FBRztBQUN0RCw4Q0FBOEMsZ0RBQW1CLENBQUMsbURBQVEsRUFBRSw4RUFBUTtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixpREFBVTtBQUNoQztBQUNBO0FBQ0E7QUFDQSxHQUFHLDJCQUEyQixnREFBbUI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLGdEQUFtQjtBQUN6QyxlQUFlLGlEQUFVO0FBQ3pCO0FBQ0E7QUFDQSxHQUFHLHlCQUF5QixnREFBbUIsQ0FBQyxrREFBUztBQUN6RDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNELDhFQUE0Qix1Q0FBVTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLXRhYnMvZXMvVGFiTmF2TGlzdC9PcGVyYXRpb25Ob2RlLmpzPzdjNjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eVwiO1xuaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgY2xhc3NOYW1lcyBmcm9tICdjbGFzc25hbWVzJztcbmltcG9ydCBEcm9wZG93biBmcm9tICdyYy1kcm9wZG93bic7XG5pbXBvcnQgTWVudSwgeyBNZW51SXRlbSB9IGZyb20gJ3JjLW1lbnUnO1xuaW1wb3J0IEtleUNvZGUgZnJvbSBcInJjLXV0aWwvZXMvS2V5Q29kZVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGdldFJlbW92YWJsZSB9IGZyb20gXCIuLi91dGlsXCI7XG5pbXBvcnQgQWRkQnV0dG9uIGZyb20gXCIuL0FkZEJ1dHRvblwiO1xudmFyIE9wZXJhdGlvbk5vZGUgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikge1xuICB2YXIgcHJlZml4Q2xzID0gcHJvcHMucHJlZml4Q2xzLFxuICAgIGlkID0gcHJvcHMuaWQsXG4gICAgdGFicyA9IHByb3BzLnRhYnMsXG4gICAgbG9jYWxlID0gcHJvcHMubG9jYWxlLFxuICAgIG1vYmlsZSA9IHByb3BzLm1vYmlsZSxcbiAgICBfcHJvcHMkbW9yZSA9IHByb3BzLm1vcmUsXG4gICAgbW9yZVByb3BzID0gX3Byb3BzJG1vcmUgPT09IHZvaWQgMCA/IHt9IDogX3Byb3BzJG1vcmUsXG4gICAgc3R5bGUgPSBwcm9wcy5zdHlsZSxcbiAgICBjbGFzc05hbWUgPSBwcm9wcy5jbGFzc05hbWUsXG4gICAgZWRpdGFibGUgPSBwcm9wcy5lZGl0YWJsZSxcbiAgICB0YWJCYXJHdXR0ZXIgPSBwcm9wcy50YWJCYXJHdXR0ZXIsXG4gICAgcnRsID0gcHJvcHMucnRsLFxuICAgIHJlbW92ZUFyaWFMYWJlbCA9IHByb3BzLnJlbW92ZUFyaWFMYWJlbCxcbiAgICBvblRhYkNsaWNrID0gcHJvcHMub25UYWJDbGljayxcbiAgICBnZXRQb3B1cENvbnRhaW5lciA9IHByb3BzLmdldFBvcHVwQ29udGFpbmVyLFxuICAgIHBvcHVwQ2xhc3NOYW1lID0gcHJvcHMucG9wdXBDbGFzc05hbWU7XG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PSBEcm9wZG93biA9PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKGZhbHNlKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBvcGVuID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRPcGVuID0gX3VzZVN0YXRlMlsxXTtcbiAgdmFyIF91c2VTdGF0ZTMgPSB1c2VTdGF0ZShudWxsKSxcbiAgICBfdXNlU3RhdGU0ID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlMywgMiksXG4gICAgc2VsZWN0ZWRLZXkgPSBfdXNlU3RhdGU0WzBdLFxuICAgIHNldFNlbGVjdGVkS2V5ID0gX3VzZVN0YXRlNFsxXTtcbiAgdmFyIF9tb3JlUHJvcHMkaWNvbiA9IG1vcmVQcm9wcy5pY29uLFxuICAgIG1vcmVJY29uID0gX21vcmVQcm9wcyRpY29uID09PSB2b2lkIDAgPyAnTW9yZScgOiBfbW9yZVByb3BzJGljb247XG4gIHZhciBwb3B1cElkID0gXCJcIi5jb25jYXQoaWQsIFwiLW1vcmUtcG9wdXBcIik7XG4gIHZhciBkcm9wZG93blByZWZpeCA9IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItZHJvcGRvd25cIik7XG4gIHZhciBzZWxlY3RlZEl0ZW1JZCA9IHNlbGVjdGVkS2V5ICE9PSBudWxsID8gXCJcIi5jb25jYXQocG9wdXBJZCwgXCItXCIpLmNvbmNhdChzZWxlY3RlZEtleSkgOiBudWxsO1xuICB2YXIgZHJvcGRvd25BcmlhTGFiZWwgPSBsb2NhbGUgPT09IG51bGwgfHwgbG9jYWxlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBsb2NhbGUuZHJvcGRvd25BcmlhTGFiZWw7XG4gIGZ1bmN0aW9uIG9uUmVtb3ZlVGFiKGV2ZW50LCBrZXkpIHtcbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIGVkaXRhYmxlLm9uRWRpdCgncmVtb3ZlJywge1xuICAgICAga2V5OiBrZXksXG4gICAgICBldmVudDogZXZlbnRcbiAgICB9KTtcbiAgfVxuICB2YXIgbWVudSA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE1lbnUsIHtcbiAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKF9yZWYpIHtcbiAgICAgIHZhciBrZXkgPSBfcmVmLmtleSxcbiAgICAgICAgZG9tRXZlbnQgPSBfcmVmLmRvbUV2ZW50O1xuICAgICAgb25UYWJDbGljayhrZXksIGRvbUV2ZW50KTtcbiAgICAgIHNldE9wZW4oZmFsc2UpO1xuICAgIH0sXG4gICAgcHJlZml4Q2xzOiBcIlwiLmNvbmNhdChkcm9wZG93blByZWZpeCwgXCItbWVudVwiKSxcbiAgICBpZDogcG9wdXBJZCxcbiAgICB0YWJJbmRleDogLTEsXG4gICAgcm9sZTogXCJsaXN0Ym94XCIsXG4gICAgXCJhcmlhLWFjdGl2ZWRlc2NlbmRhbnRcIjogc2VsZWN0ZWRJdGVtSWQsXG4gICAgc2VsZWN0ZWRLZXlzOiBbc2VsZWN0ZWRLZXldLFxuICAgIFwiYXJpYS1sYWJlbFwiOiBkcm9wZG93bkFyaWFMYWJlbCAhPT0gdW5kZWZpbmVkID8gZHJvcGRvd25BcmlhTGFiZWwgOiAnZXhwYW5kZWQgZHJvcGRvd24nXG4gIH0sIHRhYnMubWFwKGZ1bmN0aW9uICh0YWIpIHtcbiAgICB2YXIgY2xvc2FibGUgPSB0YWIuY2xvc2FibGUsXG4gICAgICBkaXNhYmxlZCA9IHRhYi5kaXNhYmxlZCxcbiAgICAgIGNsb3NlSWNvbiA9IHRhYi5jbG9zZUljb24sXG4gICAgICBrZXkgPSB0YWIua2V5LFxuICAgICAgbGFiZWwgPSB0YWIubGFiZWw7XG4gICAgdmFyIHJlbW92YWJsZSA9IGdldFJlbW92YWJsZShjbG9zYWJsZSwgY2xvc2VJY29uLCBlZGl0YWJsZSwgZGlzYWJsZWQpO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChNZW51SXRlbSwge1xuICAgICAga2V5OiBrZXksXG4gICAgICBpZDogXCJcIi5jb25jYXQocG9wdXBJZCwgXCItXCIpLmNvbmNhdChrZXkpLFxuICAgICAgcm9sZTogXCJvcHRpb25cIixcbiAgICAgIFwiYXJpYS1jb250cm9sc1wiOiBpZCAmJiBcIlwiLmNvbmNhdChpZCwgXCItcGFuZWwtXCIpLmNvbmNhdChrZXkpLFxuICAgICAgZGlzYWJsZWQ6IGRpc2FibGVkXG4gICAgfSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIsIG51bGwsIGxhYmVsKSwgcmVtb3ZhYmxlICYmIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiYnV0dG9uXCIsIHtcbiAgICAgIHR5cGU6IFwiYnV0dG9uXCIsXG4gICAgICBcImFyaWEtbGFiZWxcIjogcmVtb3ZlQXJpYUxhYmVsIHx8ICdyZW1vdmUnLFxuICAgICAgdGFiSW5kZXg6IDAsXG4gICAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KGRyb3Bkb3duUHJlZml4LCBcIi1tZW51LWl0ZW0tcmVtb3ZlXCIpLFxuICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhlKSB7XG4gICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgIG9uUmVtb3ZlVGFiKGUsIGtleSk7XG4gICAgICB9XG4gICAgfSwgY2xvc2VJY29uIHx8IGVkaXRhYmxlLnJlbW92ZUljb24gfHwgJ8OXJykpO1xuICB9KSk7XG4gIGZ1bmN0aW9uIHNlbGVjdE9mZnNldChvZmZzZXQpIHtcbiAgICB2YXIgZW5hYmxlZFRhYnMgPSB0YWJzLmZpbHRlcihmdW5jdGlvbiAodGFiKSB7XG4gICAgICByZXR1cm4gIXRhYi5kaXNhYmxlZDtcbiAgICB9KTtcbiAgICB2YXIgc2VsZWN0ZWRJbmRleCA9IGVuYWJsZWRUYWJzLmZpbmRJbmRleChmdW5jdGlvbiAodGFiKSB7XG4gICAgICByZXR1cm4gdGFiLmtleSA9PT0gc2VsZWN0ZWRLZXk7XG4gICAgfSkgfHwgMDtcbiAgICB2YXIgbGVuID0gZW5hYmxlZFRhYnMubGVuZ3RoO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuOyBpICs9IDEpIHtcbiAgICAgIHNlbGVjdGVkSW5kZXggPSAoc2VsZWN0ZWRJbmRleCArIG9mZnNldCArIGxlbikgJSBsZW47XG4gICAgICB2YXIgdGFiID0gZW5hYmxlZFRhYnNbc2VsZWN0ZWRJbmRleF07XG4gICAgICBpZiAoIXRhYi5kaXNhYmxlZCkge1xuICAgICAgICBzZXRTZWxlY3RlZEtleSh0YWIua2V5KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBvbktleURvd24oZSkge1xuICAgIHZhciB3aGljaCA9IGUud2hpY2g7XG4gICAgaWYgKCFvcGVuKSB7XG4gICAgICBpZiAoW0tleUNvZGUuRE9XTiwgS2V5Q29kZS5TUEFDRSwgS2V5Q29kZS5FTlRFUl0uaW5jbHVkZXMod2hpY2gpKSB7XG4gICAgICAgIHNldE9wZW4odHJ1ZSk7XG4gICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIH1cbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgc3dpdGNoICh3aGljaCkge1xuICAgICAgY2FzZSBLZXlDb2RlLlVQOlxuICAgICAgICBzZWxlY3RPZmZzZXQoLTEpO1xuICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSBLZXlDb2RlLkRPV046XG4gICAgICAgIHNlbGVjdE9mZnNldCgxKTtcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgS2V5Q29kZS5FU0M6XG4gICAgICAgIHNldE9wZW4oZmFsc2UpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgS2V5Q29kZS5TUEFDRTpcbiAgICAgIGNhc2UgS2V5Q29kZS5FTlRFUjpcbiAgICAgICAgaWYgKHNlbGVjdGVkS2V5ICE9PSBudWxsKSB7XG4gICAgICAgICAgb25UYWJDbGljayhzZWxlY3RlZEtleSwgZSk7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICB9XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PSBFZmZlY3QgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIC8vIFdlIHVzZSBxdWVyeSBlbGVtZW50IGhlcmUgdG8gYXZvaWQgUmVhY3Qgc3RyaWN0IHdhcm5pbmdcbiAgICB2YXIgZWxlID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoc2VsZWN0ZWRJdGVtSWQpO1xuICAgIGlmIChlbGUgJiYgZWxlLnNjcm9sbEludG9WaWV3KSB7XG4gICAgICBlbGUuc2Nyb2xsSW50b1ZpZXcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkS2V5XSk7XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKCFvcGVuKSB7XG4gICAgICBzZXRTZWxlY3RlZEtleShudWxsKTtcbiAgICB9XG4gIH0sIFtvcGVuXSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PSBSZW5kZXIgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgbW9yZVN0eWxlID0gX2RlZmluZVByb3BlcnR5KHt9LCBydGwgPyAnbWFyZ2luUmlnaHQnIDogJ21hcmdpbkxlZnQnLCB0YWJCYXJHdXR0ZXIpO1xuICBpZiAoIXRhYnMubGVuZ3RoKSB7XG4gICAgbW9yZVN0eWxlLnZpc2liaWxpdHkgPSAnaGlkZGVuJztcbiAgICBtb3JlU3R5bGUub3JkZXIgPSAxO1xuICB9XG4gIHZhciBvdmVybGF5Q2xhc3NOYW1lID0gY2xhc3NOYW1lcyhfZGVmaW5lUHJvcGVydHkoe30sIFwiXCIuY29uY2F0KGRyb3Bkb3duUHJlZml4LCBcIi1ydGxcIiksIHJ0bCkpO1xuICB2YXIgbW9yZU5vZGUgPSBtb2JpbGUgPyBudWxsIDogLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoRHJvcGRvd24sIF9leHRlbmRzKHtcbiAgICBwcmVmaXhDbHM6IGRyb3Bkb3duUHJlZml4LFxuICAgIG92ZXJsYXk6IG1lbnUsXG4gICAgdmlzaWJsZTogdGFicy5sZW5ndGggPyBvcGVuIDogZmFsc2UsXG4gICAgb25WaXNpYmxlQ2hhbmdlOiBzZXRPcGVuLFxuICAgIG92ZXJsYXlDbGFzc05hbWU6IGNsYXNzTmFtZXMob3ZlcmxheUNsYXNzTmFtZSwgcG9wdXBDbGFzc05hbWUpLFxuICAgIG1vdXNlRW50ZXJEZWxheTogMC4xLFxuICAgIG1vdXNlTGVhdmVEZWxheTogMC4xLFxuICAgIGdldFBvcHVwQ29udGFpbmVyOiBnZXRQb3B1cENvbnRhaW5lclxuICB9LCBtb3JlUHJvcHMpLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImJ1dHRvblwiLCB7XG4gICAgdHlwZTogXCJidXR0b25cIixcbiAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItbmF2LW1vcmVcIiksXG4gICAgc3R5bGU6IG1vcmVTdHlsZSxcbiAgICB0YWJJbmRleDogLTEsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImFyaWEtaGFzcG9wdXBcIjogXCJsaXN0Ym94XCIsXG4gICAgXCJhcmlhLWNvbnRyb2xzXCI6IHBvcHVwSWQsXG4gICAgaWQ6IFwiXCIuY29uY2F0KGlkLCBcIi1tb3JlXCIpLFxuICAgIFwiYXJpYS1leHBhbmRlZFwiOiBvcGVuLFxuICAgIG9uS2V5RG93bjogb25LZXlEb3duXG4gIH0sIG1vcmVJY29uKSk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKFwiXCIuY29uY2F0KHByZWZpeENscywgXCItbmF2LW9wZXJhdGlvbnNcIiksIGNsYXNzTmFtZSksXG4gICAgc3R5bGU6IHN0eWxlLFxuICAgIHJlZjogcmVmXG4gIH0sIG1vcmVOb2RlLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBZGRCdXR0b24sIHtcbiAgICBwcmVmaXhDbHM6IHByZWZpeENscyxcbiAgICBsb2NhbGU6IGxvY2FsZSxcbiAgICBlZGl0YWJsZTogZWRpdGFibGVcbiAgfSkpO1xufSk7XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QubWVtbyhPcGVyYXRpb25Ob2RlLCBmdW5jdGlvbiAoXywgbmV4dCkge1xuICByZXR1cm4gKFxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9hbnQtZGVzaWduL2FudC1kZXNpZ24vaXNzdWVzLzMyNTQ0XG4gICAgLy8gV2UnZCBiZXR0ZXIgcmVtb3ZlIHN5bnRhY3RpYyBzdWdhciBpbiBgcmMtbWVudWAgc2luY2UgdGhpcyBoYXMgcGVyZiBpc3N1ZVxuICAgIG5leHQudGFiTW92aW5nXG4gICk7XG59KTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/OperationNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/TabNode.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/TabNode.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/rc-tabs/es/util.js\");\n\n\n\n\n\nvar TabNode = function TabNode(props) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    active = props.active,\n    _props$tab = props.tab,\n    key = _props$tab.key,\n    label = _props$tab.label,\n    disabled = _props$tab.disabled,\n    closeIcon = _props$tab.closeIcon,\n    icon = _props$tab.icon,\n    closable = props.closable,\n    renderWrapper = props.renderWrapper,\n    removeAriaLabel = props.removeAriaLabel,\n    editable = props.editable,\n    onClick = props.onClick,\n    onFocus = props.onFocus,\n    style = props.style;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = (0,_util__WEBPACK_IMPORTED_MODULE_4__.getRemovable)(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var labelNode = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {\n    return icon && typeof label === 'string' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(\"span\", null, label) : label;\n  }, [label, icon]);\n  var node = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(\"div\", {\n    key: key\n    // ref={ref}\n    ,\n    \"data-node-key\": (0,_util__WEBPACK_IMPORTED_MODULE_4__.genDataNodeKey)(key),\n    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(tabPrefix, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, \"\".concat(tabPrefix, \"-with-remove\"), removable), \"\".concat(tabPrefix, \"-active\"), active), \"\".concat(tabPrefix, \"-disabled\"), disabled)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(\"div\", {\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : 0,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: function onKeyDown(e) {\n      if ([rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__[\"default\"].SPACE, rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_2__[\"default\"].ENTER].includes(e.which)) {\n        e.preventDefault();\n        onInternalClick(e);\n      }\n    },\n    onFocus: onFocus\n  }, icon && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(\"span\", {\n    className: \"\".concat(tabPrefix, \"-icon\")\n  }, icon), label && labelNode), removable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(\"button\", {\n    type: \"button\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: 0,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabNode);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/TabNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/Wrapper.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/Wrapper.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! . */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/index.js\");\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _TabPanelList_TabPane__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../TabPanelList/TabPane */ \"(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js\");\n\n\n\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\n// zombieJ: To compatible with `renderTabBar` usage.\n\n\n\n\n\n// We have to create a TabNavList components.\nvar TabNavListWrapper = function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref, _excluded);\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_3__.useContext(_TabContext__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n    tabs = _React$useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_ref2, _excluded2);\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_TabPanelList_TabPane__WEBPACK_IMPORTED_MODULE_6__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, ___WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(___WEBPACK_IMPORTED_MODULE_4__[\"default\"], restProps);\n};\nif (true) {\n  TabNavListWrapper.displayName = 'TabNavListWrapper';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabNavListWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/Wrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabNavList/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabNavList/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _hooks_useIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/useIndicator */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useIndicator.js\");\n/* harmony import */ var _hooks_useOffsets__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../hooks/useOffsets */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useOffsets.js\");\n/* harmony import */ var _hooks_useSyncState__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../hooks/useSyncState */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useSyncState.js\");\n/* harmony import */ var _hooks_useTouchMove__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../hooks/useTouchMove */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useTouchMove.js\");\n/* harmony import */ var _hooks_useUpdate__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../hooks/useUpdate */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useUpdate.js\");\n/* harmony import */ var _hooks_useVisibleRange__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../hooks/useVisibleRange */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useVisibleRange.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/rc-tabs/es/util.js\");\n/* harmony import */ var _AddButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./AddButton */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/AddButton.js\");\n/* harmony import */ var _ExtraContent__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./ExtraContent */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/ExtraContent.js\");\n/* harmony import */ var _OperationNode__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./OperationNode */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/OperationNode.js\");\n/* harmony import */ var _TabNode__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./TabNode */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/TabNode.js\");\n\n\n\n\n\n/* eslint-disable react-hooks/exhaustive-deps */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar getTabSize = function getTabSize(tab, containerRect) {\n  // tabListRef\n  var offsetWidth = tab.offsetWidth,\n    offsetHeight = tab.offsetHeight,\n    offsetTop = tab.offsetTop,\n    offsetLeft = tab.offsetLeft;\n  var _tab$getBoundingClien = tab.getBoundingClientRect(),\n    width = _tab$getBoundingClien.width,\n    height = _tab$getBoundingClien.height,\n    left = _tab$getBoundingClien.left,\n    top = _tab$getBoundingClien.top;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (Math.abs(width - offsetWidth) < 1) {\n    return [width, height, left - containerRect.left, top - containerRect.top];\n  }\n  return [offsetWidth, offsetHeight, offsetLeft, offsetTop];\n};\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (refObj.current) {\n    var _refObj$current$getBo = refObj.current.getBoundingClientRect(),\n      width = _refObj$current$getBo.width,\n      height = _refObj$current$getBo.height;\n    if (Math.abs(width - offsetWidth) < 1) {\n      return [width, height];\n    }\n  }\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nvar TabNavList = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.forwardRef(function (props, ref) {\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    indicator = props.indicator;\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_9__.useContext(_TabContext__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var extraLeftRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var extraRightRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var tabsWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var tabListRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var operationsRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var innerAddButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = (0,_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = (0,_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([0, 0]),\n    _useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = (0,_hooks_useUpdate__WEBPACK_IMPORTED_MODULE_15__.useUpdateState)(new Map()),\n    _useUpdateState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = (0,_hooks_useOffsets__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = Math.floor(containerExcludeExtraSizeValue) < Math.floor(tabContentSizeValue + addSizeValue);\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);\n  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),\n    _useState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    if (touchMovingRef.current) {\n      clearTimeout(touchMovingRef.current);\n    }\n  }\n  (0,_hooks_useTouchMove__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = (0,_hooks_useVisibleRange__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPosition === 'top' || tabPosition === 'bottom') {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabNode__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onFocus: function onFocus() {\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var _tabListRef$current;\n      var newSizes = new Map();\n      var listRect = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.getBoundingClientRect();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current2;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.querySelector(\"[data-node-key=\\\"\".concat((0,_util__WEBPACK_IMPORTED_MODULE_17__.genDataNodeKey)(key), \"\\\"]\"));\n        if (btnNode) {\n          var _getTabSize = getTabSize(btnNode, listRect),\n            _getTabSize2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_getTabSize, 4),\n            width = _getTabSize2[0],\n            height = _getTabSize2[1],\n            left = _getTabSize2[2],\n            top = _getTabSize2[3];\n          newSizes.set(key, {\n            width: width,\n            height: height,\n            left: left,\n            top: top\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = (0,_hooks_useUpdate__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(startHiddenTabs), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var activeTabOffset = tabOffsets.get(activeKey);\n  var _useIndicator = (0,_hooks_useIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n      activeTabOffset: activeTabOffset,\n      horizontal: tabPositionTopOrBottom,\n      indicator: indicator,\n      rtl: rtl\n    }),\n    indicatorStyle = _useIndicator.style;\n\n  // ========================= Effect ========================\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    scrollToTab();\n  }, [activeKey, transformMin, transformMax, (0,_util__WEBPACK_IMPORTED_MODULE_17__.stringify)(activeTabOffset), (0,_util__WEBPACK_IMPORTED_MODULE_17__.stringify)(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_8__.useComposeRef)(ref, containerRef),\n    role: \"tablist\",\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ExtraContent__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(wrapPrefix, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_AddButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(\"\".concat(prefixCls, \"-ink-bar\"), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: indicatorStyle\n  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_OperationNode__WEBPACK_IMPORTED_MODULE_20__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_ExtraContent__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabNavList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJOYXZMaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDYztBQUNNO0FBQ1Q7QUFDQztBQUN0RTtBQUNvQztBQUNZO0FBQ0M7QUFDRjtBQUNoQjtBQUNxQjtBQUNiO0FBQ1U7QUFDSjtBQUNJO0FBQ0E7QUFDYztBQUNSO0FBQ0g7QUFDaEI7QUFDTTtBQUNFO0FBQ1o7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4Qiw2Q0FBZ0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZDQUFnQixDQUFDLG9EQUFVO0FBQ3JEO0FBQ0E7QUFDQSxxQkFBcUIsNkNBQU07QUFDM0IscUJBQXFCLDZDQUFNO0FBQzNCLHNCQUFzQiw2Q0FBTTtBQUM1Qix1QkFBdUIsNkNBQU07QUFDN0IsbUJBQW1CLDZDQUFNO0FBQ3pCLHNCQUFzQiw2Q0FBTTtBQUM1QiwwQkFBMEIsNkNBQU07QUFDaEM7QUFDQSxzQkFBc0IsZ0VBQVk7QUFDbEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMLHFCQUFxQixvRkFBYztBQUNuQztBQUNBO0FBQ0EsdUJBQXVCLGdFQUFZO0FBQ25DO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTCxxQkFBcUIsb0ZBQWM7QUFDbkM7QUFDQTtBQUNBLGtCQUFrQiwrQ0FBUTtBQUMxQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLG1CQUFtQiwrQ0FBUTtBQUMzQixpQkFBaUIsb0ZBQWM7QUFDL0I7QUFDQTtBQUNBLHdCQUF3QixpRUFBYztBQUN0Qyx1QkFBdUIsb0ZBQWM7QUFDckM7QUFDQTtBQUNBLG1CQUFtQiw4REFBVTs7QUFFN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx1QkFBdUIsNkNBQU07QUFDN0IsbUJBQW1CLCtDQUFRO0FBQzNCLGtCQUFrQixvRkFBYztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsZ0VBQVk7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBLHlCQUF5QixtRUFBZTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLFlBQVk7QUFDbEU7QUFDQSxLQUFLO0FBQ0wsd0JBQXdCLG9GQUFjO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IscUVBQVE7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0RBQW1CLENBQUMsaURBQU87QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUxBQXVMLHNEQUFjO0FBQ3JNO0FBQ0E7QUFDQSwyQkFBMkIsb0ZBQWM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMO0FBQ0EsRUFBRSxnREFBUztBQUNYO0FBQ0EsR0FBRztBQUNIO0FBQ0EsR0FBRztBQUNILDJCQUEyQiw2REFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qix3RkFBa0IsbUJBQW1CLHdGQUFrQjs7QUFFcEY7QUFDQTtBQUNBLHNCQUFzQixnRUFBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBLEVBQUUsZ0RBQVM7QUFDWDtBQUNBLEdBQUcsMENBQTBDLGlEQUFTLG1CQUFtQixpREFBUzs7QUFFbEY7QUFDQSxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLDBEQUFjO0FBQ3hEO0FBQ0EsR0FBRyxlQUFlLGdEQUFtQjtBQUNyQyxTQUFTLDZEQUFhO0FBQ3RCO0FBQ0EsZUFBZSxpREFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxlQUFlLGdEQUFtQixDQUFDLHNEQUFZO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxnQkFBZ0IsZ0RBQW1CLENBQUMsMERBQWM7QUFDckQ7QUFDQSxHQUFHLGVBQWUsZ0RBQW1CO0FBQ3JDLGVBQWUsaURBQVUsYUFBYSxxRkFBZSxDQUFDLHFGQUFlLENBQUMscUZBQWUsQ0FBQyxxRkFBZSxHQUFHO0FBQ3hHO0FBQ0EsR0FBRyxlQUFlLGdEQUFtQixDQUFDLDBEQUFjO0FBQ3BEO0FBQ0EsR0FBRyxlQUFlLGdEQUFtQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLHlCQUF5QixnREFBbUIsQ0FBQyxtREFBUztBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0ZBQWEsQ0FBQyxvRkFBYSxHQUFHLHVEQUF1RDtBQUNoRztBQUNBLEtBQUs7QUFDTCxHQUFHLGdCQUFnQixnREFBbUI7QUFDdEMsZUFBZSxpREFBVSxtQ0FBbUMscUZBQWUsR0FBRztBQUM5RTtBQUNBLEdBQUcsb0JBQW9CLGdEQUFtQixDQUFDLHVEQUFhLEVBQUUsOEVBQVEsR0FBRztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLGlCQUFpQixnREFBbUIsQ0FBQyxzREFBWTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUM7QUFDRCxpRUFBZSxVQUFVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLXRhYnMvZXMvVGFiTmF2TGlzdC9pbmRleC5qcz9iYjlmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHlcIjtcbmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5XCI7XG5pbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG4vKiBlc2xpbnQtZGlzYWJsZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHMgKi9cbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IFJlc2l6ZU9ic2VydmVyIGZyb20gJ3JjLXJlc2l6ZS1vYnNlcnZlcic7XG5pbXBvcnQgdXNlRXZlbnQgZnJvbSBcInJjLXV0aWwvZXMvaG9va3MvdXNlRXZlbnRcIjtcbmltcG9ydCB7IHVzZUNvbXBvc2VSZWYgfSBmcm9tIFwicmMtdXRpbC9lcy9yZWZcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBUYWJDb250ZXh0IGZyb20gXCIuLi9UYWJDb250ZXh0XCI7XG5pbXBvcnQgdXNlSW5kaWNhdG9yIGZyb20gXCIuLi9ob29rcy91c2VJbmRpY2F0b3JcIjtcbmltcG9ydCB1c2VPZmZzZXRzIGZyb20gXCIuLi9ob29rcy91c2VPZmZzZXRzXCI7XG5pbXBvcnQgdXNlU3luY1N0YXRlIGZyb20gXCIuLi9ob29rcy91c2VTeW5jU3RhdGVcIjtcbmltcG9ydCB1c2VUb3VjaE1vdmUgZnJvbSBcIi4uL2hvb2tzL3VzZVRvdWNoTW92ZVwiO1xuaW1wb3J0IHVzZVVwZGF0ZSwgeyB1c2VVcGRhdGVTdGF0ZSB9IGZyb20gXCIuLi9ob29rcy91c2VVcGRhdGVcIjtcbmltcG9ydCB1c2VWaXNpYmxlUmFuZ2UgZnJvbSBcIi4uL2hvb2tzL3VzZVZpc2libGVSYW5nZVwiO1xuaW1wb3J0IHsgZ2VuRGF0YU5vZGVLZXksIHN0cmluZ2lmeSB9IGZyb20gXCIuLi91dGlsXCI7XG5pbXBvcnQgQWRkQnV0dG9uIGZyb20gXCIuL0FkZEJ1dHRvblwiO1xuaW1wb3J0IEV4dHJhQ29udGVudCBmcm9tIFwiLi9FeHRyYUNvbnRlbnRcIjtcbmltcG9ydCBPcGVyYXRpb25Ob2RlIGZyb20gXCIuL09wZXJhdGlvbk5vZGVcIjtcbmltcG9ydCBUYWJOb2RlIGZyb20gXCIuL1RhYk5vZGVcIjtcbnZhciBnZXRUYWJTaXplID0gZnVuY3Rpb24gZ2V0VGFiU2l6ZSh0YWIsIGNvbnRhaW5lclJlY3QpIHtcbiAgLy8gdGFiTGlzdFJlZlxuICB2YXIgb2Zmc2V0V2lkdGggPSB0YWIub2Zmc2V0V2lkdGgsXG4gICAgb2Zmc2V0SGVpZ2h0ID0gdGFiLm9mZnNldEhlaWdodCxcbiAgICBvZmZzZXRUb3AgPSB0YWIub2Zmc2V0VG9wLFxuICAgIG9mZnNldExlZnQgPSB0YWIub2Zmc2V0TGVmdDtcbiAgdmFyIF90YWIkZ2V0Qm91bmRpbmdDbGllbiA9IHRhYi5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSxcbiAgICB3aWR0aCA9IF90YWIkZ2V0Qm91bmRpbmdDbGllbi53aWR0aCxcbiAgICBoZWlnaHQgPSBfdGFiJGdldEJvdW5kaW5nQ2xpZW4uaGVpZ2h0LFxuICAgIGxlZnQgPSBfdGFiJGdldEJvdW5kaW5nQ2xpZW4ubGVmdCxcbiAgICB0b3AgPSBfdGFiJGdldEJvdW5kaW5nQ2xpZW4udG9wO1xuXG4gIC8vIFVzZSBnZXRCb3VuZGluZ0NsaWVudFJlY3QgdG8gYXZvaWQgZGVjaW1hbCBpbmFjY3VyYWN5XG4gIGlmIChNYXRoLmFicyh3aWR0aCAtIG9mZnNldFdpZHRoKSA8IDEpIHtcbiAgICByZXR1cm4gW3dpZHRoLCBoZWlnaHQsIGxlZnQgLSBjb250YWluZXJSZWN0LmxlZnQsIHRvcCAtIGNvbnRhaW5lclJlY3QudG9wXTtcbiAgfVxuICByZXR1cm4gW29mZnNldFdpZHRoLCBvZmZzZXRIZWlnaHQsIG9mZnNldExlZnQsIG9mZnNldFRvcF07XG59O1xudmFyIGdldFNpemUgPSBmdW5jdGlvbiBnZXRTaXplKHJlZk9iaikge1xuICB2YXIgX3JlZiA9IHJlZk9iai5jdXJyZW50IHx8IHt9LFxuICAgIF9yZWYkb2Zmc2V0V2lkdGggPSBfcmVmLm9mZnNldFdpZHRoLFxuICAgIG9mZnNldFdpZHRoID0gX3JlZiRvZmZzZXRXaWR0aCA9PT0gdm9pZCAwID8gMCA6IF9yZWYkb2Zmc2V0V2lkdGgsXG4gICAgX3JlZiRvZmZzZXRIZWlnaHQgPSBfcmVmLm9mZnNldEhlaWdodCxcbiAgICBvZmZzZXRIZWlnaHQgPSBfcmVmJG9mZnNldEhlaWdodCA9PT0gdm9pZCAwID8gMCA6IF9yZWYkb2Zmc2V0SGVpZ2h0O1xuXG4gIC8vIFVzZSBnZXRCb3VuZGluZ0NsaWVudFJlY3QgdG8gYXZvaWQgZGVjaW1hbCBpbmFjY3VyYWN5XG4gIGlmIChyZWZPYmouY3VycmVudCkge1xuICAgIHZhciBfcmVmT2JqJGN1cnJlbnQkZ2V0Qm8gPSByZWZPYmouY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSxcbiAgICAgIHdpZHRoID0gX3JlZk9iaiRjdXJyZW50JGdldEJvLndpZHRoLFxuICAgICAgaGVpZ2h0ID0gX3JlZk9iaiRjdXJyZW50JGdldEJvLmhlaWdodDtcbiAgICBpZiAoTWF0aC5hYnMod2lkdGggLSBvZmZzZXRXaWR0aCkgPCAxKSB7XG4gICAgICByZXR1cm4gW3dpZHRoLCBoZWlnaHRdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gW29mZnNldFdpZHRoLCBvZmZzZXRIZWlnaHRdO1xufTtcblxuLyoqXG4gKiBDb252ZXJ0IGBTaXplSW5mb2AgdG8gdW5pdCB2YWx1ZS4gU3VjaCBhcyBbMTIzLCA0NTZdIHdpdGggYHRvcGAgcG9zaXRpb24gZ2V0IGAxMjNgXG4gKi9cbnZhciBnZXRVbml0VmFsdWUgPSBmdW5jdGlvbiBnZXRVbml0VmFsdWUoc2l6ZSwgdGFiUG9zaXRpb25Ub3BPckJvdHRvbSkge1xuICByZXR1cm4gc2l6ZVt0YWJQb3NpdGlvblRvcE9yQm90dG9tID8gMCA6IDFdO1xufTtcbnZhciBUYWJOYXZMaXN0ID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgdmFyIGNsYXNzTmFtZSA9IHByb3BzLmNsYXNzTmFtZSxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIGlkID0gcHJvcHMuaWQsXG4gICAgYW5pbWF0ZWQgPSBwcm9wcy5hbmltYXRlZCxcbiAgICBhY3RpdmVLZXkgPSBwcm9wcy5hY3RpdmVLZXksXG4gICAgcnRsID0gcHJvcHMucnRsLFxuICAgIGV4dHJhID0gcHJvcHMuZXh0cmEsXG4gICAgZWRpdGFibGUgPSBwcm9wcy5lZGl0YWJsZSxcbiAgICBsb2NhbGUgPSBwcm9wcy5sb2NhbGUsXG4gICAgdGFiUG9zaXRpb24gPSBwcm9wcy50YWJQb3NpdGlvbixcbiAgICB0YWJCYXJHdXR0ZXIgPSBwcm9wcy50YWJCYXJHdXR0ZXIsXG4gICAgY2hpbGRyZW4gPSBwcm9wcy5jaGlsZHJlbixcbiAgICBvblRhYkNsaWNrID0gcHJvcHMub25UYWJDbGljayxcbiAgICBvblRhYlNjcm9sbCA9IHByb3BzLm9uVGFiU2Nyb2xsLFxuICAgIGluZGljYXRvciA9IHByb3BzLmluZGljYXRvcjtcbiAgdmFyIF9SZWFjdCR1c2VDb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChUYWJDb250ZXh0KSxcbiAgICBwcmVmaXhDbHMgPSBfUmVhY3QkdXNlQ29udGV4dC5wcmVmaXhDbHMsXG4gICAgdGFicyA9IF9SZWFjdCR1c2VDb250ZXh0LnRhYnM7XG4gIHZhciBjb250YWluZXJSZWYgPSB1c2VSZWYobnVsbCk7XG4gIHZhciBleHRyYUxlZnRSZWYgPSB1c2VSZWYobnVsbCk7XG4gIHZhciBleHRyYVJpZ2h0UmVmID0gdXNlUmVmKG51bGwpO1xuICB2YXIgdGFic1dyYXBwZXJSZWYgPSB1c2VSZWYobnVsbCk7XG4gIHZhciB0YWJMaXN0UmVmID0gdXNlUmVmKG51bGwpO1xuICB2YXIgb3BlcmF0aW9uc1JlZiA9IHVzZVJlZihudWxsKTtcbiAgdmFyIGlubmVyQWRkQnV0dG9uUmVmID0gdXNlUmVmKG51bGwpO1xuICB2YXIgdGFiUG9zaXRpb25Ub3BPckJvdHRvbSA9IHRhYlBvc2l0aW9uID09PSAndG9wJyB8fCB0YWJQb3NpdGlvbiA9PT0gJ2JvdHRvbSc7XG4gIHZhciBfdXNlU3luY1N0YXRlID0gdXNlU3luY1N0YXRlKDAsIGZ1bmN0aW9uIChuZXh0LCBwcmV2KSB7XG4gICAgICBpZiAodGFiUG9zaXRpb25Ub3BPckJvdHRvbSAmJiBvblRhYlNjcm9sbCkge1xuICAgICAgICBvblRhYlNjcm9sbCh7XG4gICAgICAgICAgZGlyZWN0aW9uOiBuZXh0ID4gcHJldiA/ICdsZWZ0JyA6ICdyaWdodCdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSksXG4gICAgX3VzZVN5bmNTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3luY1N0YXRlLCAyKSxcbiAgICB0cmFuc2Zvcm1MZWZ0ID0gX3VzZVN5bmNTdGF0ZTJbMF0sXG4gICAgc2V0VHJhbnNmb3JtTGVmdCA9IF91c2VTeW5jU3RhdGUyWzFdO1xuICB2YXIgX3VzZVN5bmNTdGF0ZTMgPSB1c2VTeW5jU3RhdGUoMCwgZnVuY3Rpb24gKG5leHQsIHByZXYpIHtcbiAgICAgIGlmICghdGFiUG9zaXRpb25Ub3BPckJvdHRvbSAmJiBvblRhYlNjcm9sbCkge1xuICAgICAgICBvblRhYlNjcm9sbCh7XG4gICAgICAgICAgZGlyZWN0aW9uOiBuZXh0ID4gcHJldiA/ICd0b3AnIDogJ2JvdHRvbSdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSksXG4gICAgX3VzZVN5bmNTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3luY1N0YXRlMywgMiksXG4gICAgdHJhbnNmb3JtVG9wID0gX3VzZVN5bmNTdGF0ZTRbMF0sXG4gICAgc2V0VHJhbnNmb3JtVG9wID0gX3VzZVN5bmNTdGF0ZTRbMV07XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZShbMCwgMF0pLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIGNvbnRhaW5lckV4Y2x1ZGVFeHRyYVNpemUgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldENvbnRhaW5lckV4Y2x1ZGVFeHRyYVNpemUgPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgX3VzZVN0YXRlMyA9IHVzZVN0YXRlKFswLCAwXSksXG4gICAgX3VzZVN0YXRlNCA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZTMsIDIpLFxuICAgIHRhYkNvbnRlbnRTaXplID0gX3VzZVN0YXRlNFswXSxcbiAgICBzZXRUYWJDb250ZW50U2l6ZSA9IF91c2VTdGF0ZTRbMV07XG4gIHZhciBfdXNlU3RhdGU1ID0gdXNlU3RhdGUoWzAsIDBdKSxcbiAgICBfdXNlU3RhdGU2ID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlNSwgMiksXG4gICAgYWRkU2l6ZSA9IF91c2VTdGF0ZTZbMF0sXG4gICAgc2V0QWRkU2l6ZSA9IF91c2VTdGF0ZTZbMV07XG4gIHZhciBfdXNlU3RhdGU3ID0gdXNlU3RhdGUoWzAsIDBdKSxcbiAgICBfdXNlU3RhdGU4ID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlNywgMiksXG4gICAgb3BlcmF0aW9uU2l6ZSA9IF91c2VTdGF0ZThbMF0sXG4gICAgc2V0T3BlcmF0aW9uU2l6ZSA9IF91c2VTdGF0ZThbMV07XG4gIHZhciBfdXNlVXBkYXRlU3RhdGUgPSB1c2VVcGRhdGVTdGF0ZShuZXcgTWFwKCkpLFxuICAgIF91c2VVcGRhdGVTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlVXBkYXRlU3RhdGUsIDIpLFxuICAgIHRhYlNpemVzID0gX3VzZVVwZGF0ZVN0YXRlMlswXSxcbiAgICBzZXRUYWJTaXplcyA9IF91c2VVcGRhdGVTdGF0ZTJbMV07XG4gIHZhciB0YWJPZmZzZXRzID0gdXNlT2Zmc2V0cyh0YWJzLCB0YWJTaXplcywgdGFiQ29udGVudFNpemVbMF0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09IFVuaXQgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgY29udGFpbmVyRXhjbHVkZUV4dHJhU2l6ZVZhbHVlID0gZ2V0VW5pdFZhbHVlKGNvbnRhaW5lckV4Y2x1ZGVFeHRyYVNpemUsIHRhYlBvc2l0aW9uVG9wT3JCb3R0b20pO1xuICB2YXIgdGFiQ29udGVudFNpemVWYWx1ZSA9IGdldFVuaXRWYWx1ZSh0YWJDb250ZW50U2l6ZSwgdGFiUG9zaXRpb25Ub3BPckJvdHRvbSk7XG4gIHZhciBhZGRTaXplVmFsdWUgPSBnZXRVbml0VmFsdWUoYWRkU2l6ZSwgdGFiUG9zaXRpb25Ub3BPckJvdHRvbSk7XG4gIHZhciBvcGVyYXRpb25TaXplVmFsdWUgPSBnZXRVbml0VmFsdWUob3BlcmF0aW9uU2l6ZSwgdGFiUG9zaXRpb25Ub3BPckJvdHRvbSk7XG4gIHZhciBuZWVkU2Nyb2xsID0gTWF0aC5mbG9vcihjb250YWluZXJFeGNsdWRlRXh0cmFTaXplVmFsdWUpIDwgTWF0aC5mbG9vcih0YWJDb250ZW50U2l6ZVZhbHVlICsgYWRkU2l6ZVZhbHVlKTtcbiAgdmFyIHZpc2libGVUYWJDb250ZW50VmFsdWUgPSBuZWVkU2Nyb2xsID8gY29udGFpbmVyRXhjbHVkZUV4dHJhU2l6ZVZhbHVlIC0gb3BlcmF0aW9uU2l6ZVZhbHVlIDogY29udGFpbmVyRXhjbHVkZUV4dHJhU2l6ZVZhbHVlIC0gYWRkU2l6ZVZhbHVlO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09IFV0aWwgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgb3BlcmF0aW9uc0hpZGRlbkNsYXNzTmFtZSA9IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItbmF2LW9wZXJhdGlvbnMtaGlkZGVuXCIpO1xuICB2YXIgdHJhbnNmb3JtTWluID0gMDtcbiAgdmFyIHRyYW5zZm9ybU1heCA9IDA7XG4gIGlmICghdGFiUG9zaXRpb25Ub3BPckJvdHRvbSkge1xuICAgIHRyYW5zZm9ybU1pbiA9IE1hdGgubWluKDAsIHZpc2libGVUYWJDb250ZW50VmFsdWUgLSB0YWJDb250ZW50U2l6ZVZhbHVlKTtcbiAgICB0cmFuc2Zvcm1NYXggPSAwO1xuICB9IGVsc2UgaWYgKHJ0bCkge1xuICAgIHRyYW5zZm9ybU1pbiA9IDA7XG4gICAgdHJhbnNmb3JtTWF4ID0gTWF0aC5tYXgoMCwgdGFiQ29udGVudFNpemVWYWx1ZSAtIHZpc2libGVUYWJDb250ZW50VmFsdWUpO1xuICB9IGVsc2Uge1xuICAgIHRyYW5zZm9ybU1pbiA9IE1hdGgubWluKDAsIHZpc2libGVUYWJDb250ZW50VmFsdWUgLSB0YWJDb250ZW50U2l6ZVZhbHVlKTtcbiAgICB0cmFuc2Zvcm1NYXggPSAwO1xuICB9XG4gIGZ1bmN0aW9uIGFsaWduSW5SYW5nZSh2YWx1ZSkge1xuICAgIGlmICh2YWx1ZSA8IHRyYW5zZm9ybU1pbikge1xuICAgICAgcmV0dXJuIHRyYW5zZm9ybU1pbjtcbiAgICB9XG4gICAgaWYgKHZhbHVlID4gdHJhbnNmb3JtTWF4KSB7XG4gICAgICByZXR1cm4gdHJhbnNmb3JtTWF4O1xuICAgIH1cbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09IE1vYmlsZSA9PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIHRvdWNoTW92aW5nUmVmID0gdXNlUmVmKG51bGwpO1xuICB2YXIgX3VzZVN0YXRlOSA9IHVzZVN0YXRlKCksXG4gICAgX3VzZVN0YXRlMTAgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGU5LCAyKSxcbiAgICBsb2NrQW5pbWF0aW9uID0gX3VzZVN0YXRlMTBbMF0sXG4gICAgc2V0TG9ja0FuaW1hdGlvbiA9IF91c2VTdGF0ZTEwWzFdO1xuICBmdW5jdGlvbiBkb0xvY2tBbmltYXRpb24oKSB7XG4gICAgc2V0TG9ja0FuaW1hdGlvbihEYXRlLm5vdygpKTtcbiAgfVxuICBmdW5jdGlvbiBjbGVhclRvdWNoTW92aW5nKCkge1xuICAgIGlmICh0b3VjaE1vdmluZ1JlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQodG91Y2hNb3ZpbmdSZWYuY3VycmVudCk7XG4gICAgfVxuICB9XG4gIHVzZVRvdWNoTW92ZSh0YWJzV3JhcHBlclJlZiwgZnVuY3Rpb24gKG9mZnNldFgsIG9mZnNldFkpIHtcbiAgICBmdW5jdGlvbiBkb01vdmUoc2V0U3RhdGUsIG9mZnNldCkge1xuICAgICAgc2V0U3RhdGUoZnVuY3Rpb24gKHZhbHVlKSB7XG4gICAgICAgIHZhciBuZXdWYWx1ZSA9IGFsaWduSW5SYW5nZSh2YWx1ZSArIG9mZnNldCk7XG4gICAgICAgIHJldHVybiBuZXdWYWx1ZTtcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIFNraXAgc2Nyb2xsIGlmIHBsYWNlIGlzIGVub3VnaFxuICAgIGlmICghbmVlZFNjcm9sbCkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAodGFiUG9zaXRpb25Ub3BPckJvdHRvbSkge1xuICAgICAgZG9Nb3ZlKHNldFRyYW5zZm9ybUxlZnQsIG9mZnNldFgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBkb01vdmUoc2V0VHJhbnNmb3JtVG9wLCBvZmZzZXRZKTtcbiAgICB9XG4gICAgY2xlYXJUb3VjaE1vdmluZygpO1xuICAgIGRvTG9ja0FuaW1hdGlvbigpO1xuICAgIHJldHVybiB0cnVlO1xuICB9KTtcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBjbGVhclRvdWNoTW92aW5nKCk7XG4gICAgaWYgKGxvY2tBbmltYXRpb24pIHtcbiAgICAgIHRvdWNoTW92aW5nUmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgc2V0TG9ja0FuaW1hdGlvbigwKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfVxuICAgIHJldHVybiBjbGVhclRvdWNoTW92aW5nO1xuICB9LCBbbG9ja0FuaW1hdGlvbl0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PSBWaXNpYmxlIFJhbmdlID09PT09PT09PT09PT09PT09PT09PVxuICAvLyBSZW5kZXIgdGFiIG5vZGUgJiBjb2xsZWN0IHRhYiBvZmZzZXRcbiAgdmFyIF91c2VWaXNpYmxlUmFuZ2UgPSB1c2VWaXNpYmxlUmFuZ2UodGFiT2Zmc2V0cyxcbiAgICAvLyBDb250YWluZXJcbiAgICB2aXNpYmxlVGFiQ29udGVudFZhbHVlLFxuICAgIC8vIFRyYW5zZm9ybVxuICAgIHRhYlBvc2l0aW9uVG9wT3JCb3R0b20gPyB0cmFuc2Zvcm1MZWZ0IDogdHJhbnNmb3JtVG9wLFxuICAgIC8vIFRhYnNcbiAgICB0YWJDb250ZW50U2l6ZVZhbHVlLFxuICAgIC8vIEFkZFxuICAgIGFkZFNpemVWYWx1ZSxcbiAgICAvLyBPcGVyYXRpb25cbiAgICBvcGVyYXRpb25TaXplVmFsdWUsIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgICAgdGFiczogdGFic1xuICAgIH0pKSxcbiAgICBfdXNlVmlzaWJsZVJhbmdlMiA9IF9zbGljZWRUb0FycmF5KF91c2VWaXNpYmxlUmFuZ2UsIDIpLFxuICAgIHZpc2libGVTdGFydCA9IF91c2VWaXNpYmxlUmFuZ2UyWzBdLFxuICAgIHZpc2libGVFbmQgPSBfdXNlVmlzaWJsZVJhbmdlMlsxXTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09IFNjcm9sbCA9PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIHNjcm9sbFRvVGFiID0gdXNlRXZlbnQoZnVuY3Rpb24gKCkge1xuICAgIHZhciBrZXkgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IGFjdGl2ZUtleTtcbiAgICB2YXIgdGFiT2Zmc2V0ID0gdGFiT2Zmc2V0cy5nZXQoa2V5KSB8fCB7XG4gICAgICB3aWR0aDogMCxcbiAgICAgIGhlaWdodDogMCxcbiAgICAgIGxlZnQ6IDAsXG4gICAgICByaWdodDogMCxcbiAgICAgIHRvcDogMFxuICAgIH07XG4gICAgaWYgKHRhYlBvc2l0aW9uVG9wT3JCb3R0b20pIHtcbiAgICAgIC8vID09PT09PT09PT09PSBBbGlnbiB3aXRoIHRvcCAmIGJvdHRvbSA9PT09PT09PT09PT1cbiAgICAgIHZhciBuZXdUcmFuc2Zvcm0gPSB0cmFuc2Zvcm1MZWZ0O1xuXG4gICAgICAvLyBSVExcbiAgICAgIGlmIChydGwpIHtcbiAgICAgICAgaWYgKHRhYk9mZnNldC5yaWdodCA8IHRyYW5zZm9ybUxlZnQpIHtcbiAgICAgICAgICBuZXdUcmFuc2Zvcm0gPSB0YWJPZmZzZXQucmlnaHQ7XG4gICAgICAgIH0gZWxzZSBpZiAodGFiT2Zmc2V0LnJpZ2h0ICsgdGFiT2Zmc2V0LndpZHRoID4gdHJhbnNmb3JtTGVmdCArIHZpc2libGVUYWJDb250ZW50VmFsdWUpIHtcbiAgICAgICAgICBuZXdUcmFuc2Zvcm0gPSB0YWJPZmZzZXQucmlnaHQgKyB0YWJPZmZzZXQud2lkdGggLSB2aXNpYmxlVGFiQ29udGVudFZhbHVlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICAvLyBMVFJcbiAgICAgIGVsc2UgaWYgKHRhYk9mZnNldC5sZWZ0IDwgLXRyYW5zZm9ybUxlZnQpIHtcbiAgICAgICAgbmV3VHJhbnNmb3JtID0gLXRhYk9mZnNldC5sZWZ0O1xuICAgICAgfSBlbHNlIGlmICh0YWJPZmZzZXQubGVmdCArIHRhYk9mZnNldC53aWR0aCA+IC10cmFuc2Zvcm1MZWZ0ICsgdmlzaWJsZVRhYkNvbnRlbnRWYWx1ZSkge1xuICAgICAgICBuZXdUcmFuc2Zvcm0gPSAtKHRhYk9mZnNldC5sZWZ0ICsgdGFiT2Zmc2V0LndpZHRoIC0gdmlzaWJsZVRhYkNvbnRlbnRWYWx1ZSk7XG4gICAgICB9XG4gICAgICBzZXRUcmFuc2Zvcm1Ub3AoMCk7XG4gICAgICBzZXRUcmFuc2Zvcm1MZWZ0KGFsaWduSW5SYW5nZShuZXdUcmFuc2Zvcm0pKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gPT09PT09PT09PT09IEFsaWduIHdpdGggbGVmdCAmIHJpZ2h0ID09PT09PT09PT09PVxuICAgICAgdmFyIF9uZXdUcmFuc2Zvcm0gPSB0cmFuc2Zvcm1Ub3A7XG4gICAgICBpZiAodGFiT2Zmc2V0LnRvcCA8IC10cmFuc2Zvcm1Ub3ApIHtcbiAgICAgICAgX25ld1RyYW5zZm9ybSA9IC10YWJPZmZzZXQudG9wO1xuICAgICAgfSBlbHNlIGlmICh0YWJPZmZzZXQudG9wICsgdGFiT2Zmc2V0LmhlaWdodCA+IC10cmFuc2Zvcm1Ub3AgKyB2aXNpYmxlVGFiQ29udGVudFZhbHVlKSB7XG4gICAgICAgIF9uZXdUcmFuc2Zvcm0gPSAtKHRhYk9mZnNldC50b3AgKyB0YWJPZmZzZXQuaGVpZ2h0IC0gdmlzaWJsZVRhYkNvbnRlbnRWYWx1ZSk7XG4gICAgICB9XG4gICAgICBzZXRUcmFuc2Zvcm1MZWZ0KDApO1xuICAgICAgc2V0VHJhbnNmb3JtVG9wKGFsaWduSW5SYW5nZShfbmV3VHJhbnNmb3JtKSk7XG4gICAgfVxuICB9KTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PSBUYWIgPT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIHRhYk5vZGVTdHlsZSA9IHt9O1xuICBpZiAodGFiUG9zaXRpb24gPT09ICd0b3AnIHx8IHRhYlBvc2l0aW9uID09PSAnYm90dG9tJykge1xuICAgIHRhYk5vZGVTdHlsZVtydGwgPyAnbWFyZ2luUmlnaHQnIDogJ21hcmdpbkxlZnQnXSA9IHRhYkJhckd1dHRlcjtcbiAgfSBlbHNlIHtcbiAgICB0YWJOb2RlU3R5bGUubWFyZ2luVG9wID0gdGFiQmFyR3V0dGVyO1xuICB9XG4gIHZhciB0YWJOb2RlcyA9IHRhYnMubWFwKGZ1bmN0aW9uICh0YWIsIGkpIHtcbiAgICB2YXIga2V5ID0gdGFiLmtleTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoVGFiTm9kZSwge1xuICAgICAgaWQ6IGlkLFxuICAgICAgcHJlZml4Q2xzOiBwcmVmaXhDbHMsXG4gICAgICBrZXk6IGtleSxcbiAgICAgIHRhYjogdGFiXG4gICAgICAvKiBmaXJzdCBub2RlIHNob3VsZCBub3QgaGF2ZSBtYXJnaW4gbGVmdCAqLyxcbiAgICAgIHN0eWxlOiBpID09PSAwID8gdW5kZWZpbmVkIDogdGFiTm9kZVN0eWxlLFxuICAgICAgY2xvc2FibGU6IHRhYi5jbG9zYWJsZSxcbiAgICAgIGVkaXRhYmxlOiBlZGl0YWJsZSxcbiAgICAgIGFjdGl2ZToga2V5ID09PSBhY3RpdmVLZXksXG4gICAgICByZW5kZXJXcmFwcGVyOiBjaGlsZHJlbixcbiAgICAgIHJlbW92ZUFyaWFMYWJlbDogbG9jYWxlID09PSBudWxsIHx8IGxvY2FsZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogbG9jYWxlLnJlbW92ZUFyaWFMYWJlbCxcbiAgICAgIG9uQ2xpY2s6IGZ1bmN0aW9uIG9uQ2xpY2soZSkge1xuICAgICAgICBvblRhYkNsaWNrKGtleSwgZSk7XG4gICAgICB9LFxuICAgICAgb25Gb2N1czogZnVuY3Rpb24gb25Gb2N1cygpIHtcbiAgICAgICAgc2Nyb2xsVG9UYWIoa2V5KTtcbiAgICAgICAgZG9Mb2NrQW5pbWF0aW9uKCk7XG4gICAgICAgIGlmICghdGFic1dyYXBwZXJSZWYuY3VycmVudCkge1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICAvLyBGb2N1cyBlbGVtZW50IHdpbGwgbWFrZSBzY3JvbGxMZWZ0IGNoYW5nZSB3aGljaCB3ZSBzaG91bGQgcmVzZXQgYmFja1xuICAgICAgICBpZiAoIXJ0bCkge1xuICAgICAgICAgIHRhYnNXcmFwcGVyUmVmLmN1cnJlbnQuc2Nyb2xsTGVmdCA9IDA7XG4gICAgICAgIH1cbiAgICAgICAgdGFic1dyYXBwZXJSZWYuY3VycmVudC5zY3JvbGxUb3AgPSAwO1xuICAgICAgfVxuICAgIH0pO1xuICB9KTtcblxuICAvLyBVcGRhdGUgYnV0dG9ucyByZWNvcmRzXG4gIHZhciB1cGRhdGVUYWJTaXplcyA9IGZ1bmN0aW9uIHVwZGF0ZVRhYlNpemVzKCkge1xuICAgIHJldHVybiBzZXRUYWJTaXplcyhmdW5jdGlvbiAoKSB7XG4gICAgICB2YXIgX3RhYkxpc3RSZWYkY3VycmVudDtcbiAgICAgIHZhciBuZXdTaXplcyA9IG5ldyBNYXAoKTtcbiAgICAgIHZhciBsaXN0UmVjdCA9IChfdGFiTGlzdFJlZiRjdXJyZW50ID0gdGFiTGlzdFJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfdGFiTGlzdFJlZiRjdXJyZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfdGFiTGlzdFJlZiRjdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgICAgdGFicy5mb3JFYWNoKGZ1bmN0aW9uIChfcmVmMikge1xuICAgICAgICB2YXIgX3RhYkxpc3RSZWYkY3VycmVudDI7XG4gICAgICAgIHZhciBrZXkgPSBfcmVmMi5rZXk7XG4gICAgICAgIHZhciBidG5Ob2RlID0gKF90YWJMaXN0UmVmJGN1cnJlbnQyID0gdGFiTGlzdFJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfdGFiTGlzdFJlZiRjdXJyZW50MiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3RhYkxpc3RSZWYkY3VycmVudDIucXVlcnlTZWxlY3RvcihcIltkYXRhLW5vZGUta2V5PVxcXCJcIi5jb25jYXQoZ2VuRGF0YU5vZGVLZXkoa2V5KSwgXCJcXFwiXVwiKSk7XG4gICAgICAgIGlmIChidG5Ob2RlKSB7XG4gICAgICAgICAgdmFyIF9nZXRUYWJTaXplID0gZ2V0VGFiU2l6ZShidG5Ob2RlLCBsaXN0UmVjdCksXG4gICAgICAgICAgICBfZ2V0VGFiU2l6ZTIgPSBfc2xpY2VkVG9BcnJheShfZ2V0VGFiU2l6ZSwgNCksXG4gICAgICAgICAgICB3aWR0aCA9IF9nZXRUYWJTaXplMlswXSxcbiAgICAgICAgICAgIGhlaWdodCA9IF9nZXRUYWJTaXplMlsxXSxcbiAgICAgICAgICAgIGxlZnQgPSBfZ2V0VGFiU2l6ZTJbMl0sXG4gICAgICAgICAgICB0b3AgPSBfZ2V0VGFiU2l6ZTJbM107XG4gICAgICAgICAgbmV3U2l6ZXMuc2V0KGtleSwge1xuICAgICAgICAgICAgd2lkdGg6IHdpZHRoLFxuICAgICAgICAgICAgaGVpZ2h0OiBoZWlnaHQsXG4gICAgICAgICAgICBsZWZ0OiBsZWZ0LFxuICAgICAgICAgICAgdG9wOiB0b3BcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICByZXR1cm4gbmV3U2l6ZXM7XG4gICAgfSk7XG4gIH07XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgdXBkYXRlVGFiU2l6ZXMoKTtcbiAgfSwgW3RhYnMubWFwKGZ1bmN0aW9uICh0YWIpIHtcbiAgICByZXR1cm4gdGFiLmtleTtcbiAgfSkuam9pbignXycpXSk7XG4gIHZhciBvbkxpc3RIb2xkZXJSZXNpemUgPSB1c2VVcGRhdGUoZnVuY3Rpb24gKCkge1xuICAgIC8vIFVwZGF0ZSB3cmFwcGVyIHJlY29yZHNcbiAgICB2YXIgY29udGFpbmVyU2l6ZSA9IGdldFNpemUoY29udGFpbmVyUmVmKTtcbiAgICB2YXIgZXh0cmFMZWZ0U2l6ZSA9IGdldFNpemUoZXh0cmFMZWZ0UmVmKTtcbiAgICB2YXIgZXh0cmFSaWdodFNpemUgPSBnZXRTaXplKGV4dHJhUmlnaHRSZWYpO1xuICAgIHNldENvbnRhaW5lckV4Y2x1ZGVFeHRyYVNpemUoW2NvbnRhaW5lclNpemVbMF0gLSBleHRyYUxlZnRTaXplWzBdIC0gZXh0cmFSaWdodFNpemVbMF0sIGNvbnRhaW5lclNpemVbMV0gLSBleHRyYUxlZnRTaXplWzFdIC0gZXh0cmFSaWdodFNpemVbMV1dKTtcbiAgICB2YXIgbmV3QWRkU2l6ZSA9IGdldFNpemUoaW5uZXJBZGRCdXR0b25SZWYpO1xuICAgIHNldEFkZFNpemUobmV3QWRkU2l6ZSk7XG4gICAgdmFyIG5ld09wZXJhdGlvblNpemUgPSBnZXRTaXplKG9wZXJhdGlvbnNSZWYpO1xuICAgIHNldE9wZXJhdGlvblNpemUobmV3T3BlcmF0aW9uU2l6ZSk7XG5cbiAgICAvLyBXaGljaCBpbmNsdWRlcyBhZGQgYnV0dG9uIHNpemVcbiAgICB2YXIgdGFiQ29udGVudEZ1bGxTaXplID0gZ2V0U2l6ZSh0YWJMaXN0UmVmKTtcbiAgICBzZXRUYWJDb250ZW50U2l6ZShbdGFiQ29udGVudEZ1bGxTaXplWzBdIC0gbmV3QWRkU2l6ZVswXSwgdGFiQ29udGVudEZ1bGxTaXplWzFdIC0gbmV3QWRkU2l6ZVsxXV0pO1xuXG4gICAgLy8gVXBkYXRlIGJ1dHRvbnMgcmVjb3Jkc1xuICAgIHVwZGF0ZVRhYlNpemVzKCk7XG4gIH0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PSBEcm9wZG93biA9PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgc3RhcnRIaWRkZW5UYWJzID0gdGFicy5zbGljZSgwLCB2aXNpYmxlU3RhcnQpO1xuICB2YXIgZW5kSGlkZGVuVGFicyA9IHRhYnMuc2xpY2UodmlzaWJsZUVuZCArIDEpO1xuICB2YXIgaGlkZGVuVGFicyA9IFtdLmNvbmNhdChfdG9Db25zdW1hYmxlQXJyYXkoc3RhcnRIaWRkZW5UYWJzKSwgX3RvQ29uc3VtYWJsZUFycmF5KGVuZEhpZGRlblRhYnMpKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09IExpbmsgJiBPcGVyYXRpb25zID09PT09PT09PT09PT09PT09PT1cbiAgdmFyIGFjdGl2ZVRhYk9mZnNldCA9IHRhYk9mZnNldHMuZ2V0KGFjdGl2ZUtleSk7XG4gIHZhciBfdXNlSW5kaWNhdG9yID0gdXNlSW5kaWNhdG9yKHtcbiAgICAgIGFjdGl2ZVRhYk9mZnNldDogYWN0aXZlVGFiT2Zmc2V0LFxuICAgICAgaG9yaXpvbnRhbDogdGFiUG9zaXRpb25Ub3BPckJvdHRvbSxcbiAgICAgIGluZGljYXRvcjogaW5kaWNhdG9yLFxuICAgICAgcnRsOiBydGxcbiAgICB9KSxcbiAgICBpbmRpY2F0b3JTdHlsZSA9IF91c2VJbmRpY2F0b3Iuc3R5bGU7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PSBFZmZlY3QgPT09PT09PT09PT09PT09PT09PT09PT09XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgc2Nyb2xsVG9UYWIoKTtcbiAgfSwgW2FjdGl2ZUtleSwgdHJhbnNmb3JtTWluLCB0cmFuc2Zvcm1NYXgsIHN0cmluZ2lmeShhY3RpdmVUYWJPZmZzZXQpLCBzdHJpbmdpZnkodGFiT2Zmc2V0cyksIHRhYlBvc2l0aW9uVG9wT3JCb3R0b21dKTtcblxuICAvLyBTaG91bGQgcmVjYWxjdWxhdGUgd2hlbiBydGwgY2hhbmdlZFxuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIG9uTGlzdEhvbGRlclJlc2l6ZSgpO1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZVxuICB9LCBbcnRsXSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PSBSZW5kZXIgPT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBoYXNEcm9wZG93biA9ICEhaGlkZGVuVGFicy5sZW5ndGg7XG4gIHZhciB3cmFwUHJlZml4ID0gXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1uYXYtd3JhcFwiKTtcbiAgdmFyIHBpbmdMZWZ0O1xuICB2YXIgcGluZ1JpZ2h0O1xuICB2YXIgcGluZ1RvcDtcbiAgdmFyIHBpbmdCb3R0b207XG4gIGlmICh0YWJQb3NpdGlvblRvcE9yQm90dG9tKSB7XG4gICAgaWYgKHJ0bCkge1xuICAgICAgcGluZ1JpZ2h0ID0gdHJhbnNmb3JtTGVmdCA+IDA7XG4gICAgICBwaW5nTGVmdCA9IHRyYW5zZm9ybUxlZnQgIT09IHRyYW5zZm9ybU1heDtcbiAgICB9IGVsc2Uge1xuICAgICAgcGluZ0xlZnQgPSB0cmFuc2Zvcm1MZWZ0IDwgMDtcbiAgICAgIHBpbmdSaWdodCA9IHRyYW5zZm9ybUxlZnQgIT09IHRyYW5zZm9ybU1pbjtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgcGluZ1RvcCA9IHRyYW5zZm9ybVRvcCA8IDA7XG4gICAgcGluZ0JvdHRvbSA9IHRyYW5zZm9ybVRvcCAhPT0gdHJhbnNmb3JtTWluO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChSZXNpemVPYnNlcnZlciwge1xuICAgIG9uUmVzaXplOiBvbkxpc3RIb2xkZXJSZXNpemVcbiAgfSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgIHJlZjogdXNlQ29tcG9zZVJlZihyZWYsIGNvbnRhaW5lclJlZiksXG4gICAgcm9sZTogXCJ0YWJsaXN0XCIsXG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKFwiXCIuY29uY2F0KHByZWZpeENscywgXCItbmF2XCIpLCBjbGFzc05hbWUpLFxuICAgIHN0eWxlOiBzdHlsZSxcbiAgICBvbktleURvd246IGZ1bmN0aW9uIG9uS2V5RG93bigpIHtcbiAgICAgIC8vIE5vIG5lZWQgYW5pbWF0aW9uIHdoZW4gdXNlIGtleWJvYXJkXG4gICAgICBkb0xvY2tBbmltYXRpb24oKTtcbiAgICB9XG4gIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEV4dHJhQ29udGVudCwge1xuICAgIHJlZjogZXh0cmFMZWZ0UmVmLFxuICAgIHBvc2l0aW9uOiBcImxlZnRcIixcbiAgICBleHRyYTogZXh0cmEsXG4gICAgcHJlZml4Q2xzOiBwcmVmaXhDbHNcbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlc2l6ZU9ic2VydmVyLCB7XG4gICAgb25SZXNpemU6IG9uTGlzdEhvbGRlclJlc2l6ZVxuICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKHdyYXBQcmVmaXgsIF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eSh7fSwgXCJcIi5jb25jYXQod3JhcFByZWZpeCwgXCItcGluZy1sZWZ0XCIpLCBwaW5nTGVmdCksIFwiXCIuY29uY2F0KHdyYXBQcmVmaXgsIFwiLXBpbmctcmlnaHRcIiksIHBpbmdSaWdodCksIFwiXCIuY29uY2F0KHdyYXBQcmVmaXgsIFwiLXBpbmctdG9wXCIpLCBwaW5nVG9wKSwgXCJcIi5jb25jYXQod3JhcFByZWZpeCwgXCItcGluZy1ib3R0b21cIiksIHBpbmdCb3R0b20pKSxcbiAgICByZWY6IHRhYnNXcmFwcGVyUmVmXG4gIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlc2l6ZU9ic2VydmVyLCB7XG4gICAgb25SZXNpemU6IG9uTGlzdEhvbGRlclJlc2l6ZVxuICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgcmVmOiB0YWJMaXN0UmVmLFxuICAgIGNsYXNzTmFtZTogXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1uYXYtbGlzdFwiKSxcbiAgICBzdHlsZToge1xuICAgICAgdHJhbnNmb3JtOiBcInRyYW5zbGF0ZShcIi5jb25jYXQodHJhbnNmb3JtTGVmdCwgXCJweCwgXCIpLmNvbmNhdCh0cmFuc2Zvcm1Ub3AsIFwicHgpXCIpLFxuICAgICAgdHJhbnNpdGlvbjogbG9ja0FuaW1hdGlvbiA/ICdub25lJyA6IHVuZGVmaW5lZFxuICAgIH1cbiAgfSwgdGFiTm9kZXMsIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFkZEJ1dHRvbiwge1xuICAgIHJlZjogaW5uZXJBZGRCdXR0b25SZWYsXG4gICAgcHJlZml4Q2xzOiBwcmVmaXhDbHMsXG4gICAgbG9jYWxlOiBsb2NhbGUsXG4gICAgZWRpdGFibGU6IGVkaXRhYmxlLFxuICAgIHN0eWxlOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHRhYk5vZGVzLmxlbmd0aCA9PT0gMCA/IHVuZGVmaW5lZCA6IHRhYk5vZGVTdHlsZSksIHt9LCB7XG4gICAgICB2aXNpYmlsaXR5OiBoYXNEcm9wZG93biA/ICdoaWRkZW4nIDogbnVsbFxuICAgIH0pXG4gIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKFwiXCIuY29uY2F0KHByZWZpeENscywgXCItaW5rLWJhclwiKSwgX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWluay1iYXItYW5pbWF0ZWRcIiksIGFuaW1hdGVkLmlua0JhcikpLFxuICAgIHN0eWxlOiBpbmRpY2F0b3JTdHlsZVxuICB9KSkpKSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE9wZXJhdGlvbk5vZGUsIF9leHRlbmRzKHt9LCBwcm9wcywge1xuICAgIHJlbW92ZUFyaWFMYWJlbDogbG9jYWxlID09PSBudWxsIHx8IGxvY2FsZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogbG9jYWxlLnJlbW92ZUFyaWFMYWJlbCxcbiAgICByZWY6IG9wZXJhdGlvbnNSZWYsXG4gICAgcHJlZml4Q2xzOiBwcmVmaXhDbHMsXG4gICAgdGFiczogaGlkZGVuVGFicyxcbiAgICBjbGFzc05hbWU6ICFoYXNEcm9wZG93biAmJiBvcGVyYXRpb25zSGlkZGVuQ2xhc3NOYW1lLFxuICAgIHRhYk1vdmluZzogISFsb2NrQW5pbWF0aW9uXG4gIH0pKSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoRXh0cmFDb250ZW50LCB7XG4gICAgcmVmOiBleHRyYVJpZ2h0UmVmLFxuICAgIHBvc2l0aW9uOiBcInJpZ2h0XCIsXG4gICAgZXh0cmE6IGV4dHJhLFxuICAgIHByZWZpeENsczogcHJlZml4Q2xzXG4gIH0pKSk7XG4gIC8qIGVzbGludC1lbmFibGUgKi9cbn0pO1xuZXhwb3J0IGRlZmF1bHQgVGFiTmF2TGlzdDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabNavList/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabPanelList/TabPane.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar TabPane = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    id = props.id,\n    active = props.active,\n    tabKey = props.tabKey,\n    children = props.children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classnames__WEBPACK_IMPORTED_MODULE_0___default()(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (true) {\n  TabPane.displayName = 'TabPane';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabPane);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJQYW5lbExpc3QvVGFiUGFuZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQztBQUNMO0FBQy9CLDJCQUEyQiw2Q0FBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsaURBQVU7QUFDekI7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNELElBQUksSUFBcUM7QUFDekM7QUFDQTtBQUNBLGlFQUFlLE9BQU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9UYWJQYW5lbExpc3QvVGFiUGFuZS5qcz81Nzg0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xudmFyIFRhYlBhbmUgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikge1xuICB2YXIgcHJlZml4Q2xzID0gcHJvcHMucHJlZml4Q2xzLFxuICAgIGNsYXNzTmFtZSA9IHByb3BzLmNsYXNzTmFtZSxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIGlkID0gcHJvcHMuaWQsXG4gICAgYWN0aXZlID0gcHJvcHMuYWN0aXZlLFxuICAgIHRhYktleSA9IHByb3BzLnRhYktleSxcbiAgICBjaGlsZHJlbiA9IHByb3BzLmNoaWxkcmVuO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgIGlkOiBpZCAmJiBcIlwiLmNvbmNhdChpZCwgXCItcGFuZWwtXCIpLmNvbmNhdCh0YWJLZXkpLFxuICAgIHJvbGU6IFwidGFicGFuZWxcIixcbiAgICB0YWJJbmRleDogYWN0aXZlID8gMCA6IC0xLFxuICAgIFwiYXJpYS1sYWJlbGxlZGJ5XCI6IGlkICYmIFwiXCIuY29uY2F0KGlkLCBcIi10YWItXCIpLmNvbmNhdCh0YWJLZXkpLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogIWFjdGl2ZSxcbiAgICBzdHlsZTogc3R5bGUsXG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKHByZWZpeENscywgYWN0aXZlICYmIFwiXCIuY29uY2F0KHByZWZpeENscywgXCItYWN0aXZlXCIpLCBjbGFzc05hbWUpLFxuICAgIHJlZjogcmVmXG4gIH0sIGNoaWxkcmVuKTtcbn0pO1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgVGFiUGFuZS5kaXNwbGF5TmFtZSA9ICdUYWJQYW5lJztcbn1cbmV4cG9ydCBkZWZhdWx0IFRhYlBhbmU7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/TabPanelList/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/TabPanelList/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _TabPane__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TabPane */ \"(ssr)/./node_modules/rc-tabs/es/TabPanelList/TabPane.js\");\n\n\n\n\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\", \"destroyInactiveTabPane\"];\n\n\n\n\n\nvar TabPanelList = function TabPanelList(props) {\n  var id = props.id,\n    activeKey = props.activeKey,\n    animated = props.animated,\n    tabPosition = props.tabPosition,\n    destroyInactiveTabPane = props.destroyInactiveTabPane;\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_6__.useContext(_TabContext__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (item) {\n    var key = item.key,\n      forceRender = item.forceRender,\n      paneStyle = item.style,\n      paneClassName = item.className,\n      itemDestroyInactiveTabPane = item.destroyInactiveTabPane,\n      restTabProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(item, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_5__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!(destroyInactiveTabPane || itemDestroyInactiveTabPane),\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref, ref) {\n      var motionStyle = _ref.style,\n        motionClassName = _ref.className;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_TabPane__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, paneStyle), motionStyle),\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabPanelList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/TabPanelList/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/Tabs.js":
/*!*****************************************!*\
  !*** ./node_modules/rc-tabs/es/Tabs.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/isMobile */ \"(ssr)/./node_modules/rc-util/es/isMobile.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _TabContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./TabContext */ \"(ssr)/./node_modules/rc-tabs/es/TabContext.js\");\n/* harmony import */ var _TabNavList_Wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./TabNavList/Wrapper */ \"(ssr)/./node_modules/rc-tabs/es/TabNavList/Wrapper.js\");\n/* harmony import */ var _TabPanelList__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TabPanelList */ \"(ssr)/./node_modules/rc-tabs/es/TabPanelList/index.js\");\n/* harmony import */ var _hooks_useAnimateConfig__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useAnimateConfig */ \"(ssr)/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js\");\n\n\n\n\n\n\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"items\", \"direction\", \"activeKey\", \"defaultActiveKey\", \"editable\", \"animated\", \"tabPosition\", \"tabBarGutter\", \"tabBarStyle\", \"tabBarExtraContent\", \"locale\", \"more\", \"destroyInactiveTabPane\", \"renderTabBar\", \"onChange\", \"onTabClick\", \"onTabScroll\", \"getPopupContainer\", \"popupClassName\", \"indicator\"];\n// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role\n\n\n\n\n\n\n\n\n\n/**\n * Should added antd:\n * - type\n *\n * Removed:\n * - onNextClick\n * - onPrevClick\n * - keyboard\n */\n\n// Used for accessibility\nvar uuid = 0;\nvar Tabs = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,\n    className = props.className,\n    items = props.items,\n    direction = props.direction,\n    activeKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    editable = props.editable,\n    animated = props.animated,\n    _props$tabPosition = props.tabPosition,\n    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    tabBarStyle = props.tabBarStyle,\n    tabBarExtraContent = props.tabBarExtraContent,\n    locale = props.locale,\n    more = props.more,\n    destroyInactiveTabPane = props.destroyInactiveTabPane,\n    renderTabBar = props.renderTabBar,\n    onChange = props.onChange,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName,\n    indicator = props.indicator,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(props, _excluded);\n  var tabs = react__WEBPACK_IMPORTED_MODULE_9__.useMemo(function () {\n    return (items || []).filter(function (item) {\n      return item && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(item) === 'object' && 'key' in item;\n    });\n  }, [items]);\n  var rtl = direction === 'rtl';\n  var mergedAnimated = (0,_hooks_useAnimateConfig__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(animated);\n\n  // ======================== Mobile ========================\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    // Only update on the client side\n    setMobile((0,rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_8__[\"default\"])());\n  }, []);\n\n  // ====================== Active Key ======================\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n      var _tabs$;\n      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;\n    }, {\n      value: activeKey,\n      defaultValue: defaultActiveKey\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useMergedState, 2),\n    mergedActiveKey = _useMergedState2[0],\n    setMergedActiveKey = _useMergedState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(function () {\n      return tabs.findIndex(function (tab) {\n        return tab.key === mergedActiveKey;\n      });\n    }),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useState3, 2),\n    activeIndex = _useState4[0],\n    setActiveIndex = _useState4[1];\n\n  // Reset active key if not exist anymore\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    var newActiveIndex = tabs.findIndex(function (tab) {\n      return tab.key === mergedActiveKey;\n    });\n    if (newActiveIndex === -1) {\n      var _tabs$newActiveIndex;\n      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));\n      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);\n    }\n    setActiveIndex(newActiveIndex);\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), mergedActiveKey, activeIndex]);\n\n  // ===================== Accessibility ====================\n  var _useMergedState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(null, {\n      value: id\n    }),\n    _useMergedState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_useMergedState3, 2),\n    mergedId = _useMergedState4[0],\n    setMergedId = _useMergedState4[1];\n\n  // Async generate id to avoid ssr mapping failed\n  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {\n    if (!id) {\n      setMergedId(\"rc-tabs-\".concat( false ? 0 : uuid));\n      uuid += 1;\n    }\n  }, []);\n\n  // ======================== Events ========================\n  function onInternalTabClick(key, e) {\n    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);\n    var isActiveChanged = key !== mergedActiveKey;\n    setMergedActiveKey(key);\n    if (isActiveChanged) {\n      onChange === null || onChange === void 0 || onChange(key);\n    }\n  }\n\n  // ======================== Render ========================\n  var sharedProps = {\n    id: mergedId,\n    activeKey: mergedActiveKey,\n    animated: mergedAnimated,\n    tabPosition: tabPosition,\n    rtl: rtl,\n    mobile: mobile\n  };\n  var tabNavBarProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, sharedProps), {}, {\n    editable: editable,\n    locale: locale,\n    more: more,\n    tabBarGutter: tabBarGutter,\n    onTabClick: onInternalTabClick,\n    onTabScroll: onTabScroll,\n    extra: tabBarExtraContent,\n    style: tabBarStyle,\n    panes: null,\n    getPopupContainer: getPopupContainer,\n    popupClassName: popupClassName,\n    indicator: indicator\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabContext__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Provider, {\n    value: {\n      tabs: tabs,\n      prefixCls: prefixCls\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    id: id,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, \"\".concat(prefixCls, \"-\").concat(tabPosition), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-mobile\"), mobile), \"\".concat(prefixCls, \"-editable\"), editable), \"\".concat(prefixCls, \"-rtl\"), rtl), className)\n  }, restProps), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabNavList_Wrapper__WEBPACK_IMPORTED_MODULE_11__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, tabNavBarProps, {\n    renderTabBar: renderTabBar\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(_TabPanelList__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    destroyInactiveTabPane: destroyInactiveTabPane\n  }, sharedProps, {\n    animated: mergedAnimated\n  }))));\n});\nif (true) {\n  Tabs.displayName = 'Tabs';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Tabs);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/Tabs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useAnimateConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAnimateConfig)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n\n\n\nfunction useAnimateConfig() {\n  var animated = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    inkBar: true,\n    tabPane: false\n  };\n  var mergedAnimated;\n  if (animated === false) {\n    mergedAnimated = {\n      inkBar: false,\n      tabPane: false\n    };\n  } else if (animated === true) {\n    mergedAnimated = {\n      inkBar: true,\n      tabPane: false\n    };\n  } else {\n    mergedAnimated = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n      inkBar: true\n    }, (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(animated) === 'object' ? animated : {});\n  }\n\n  // Enable tabPane animation if provide motion\n  if (mergedAnimated.tabPaneMotion && mergedAnimated.tabPane === undefined) {\n    mergedAnimated.tabPane = true;\n  }\n  if (!mergedAnimated.tabPaneMotion && mergedAnimated.tabPane) {\n    if (true) {\n      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(false, '`animated.tabPane` is true but `animated.tabPaneMotion` is not provided. Motion will not work.');\n    }\n    mergedAnimated.tabPane = false;\n  }\n  return mergedAnimated;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VBbmltYXRlQ29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDYTtBQUM1QjtBQUMxQjtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLHFCQUFxQixvRkFBYTtBQUNsQztBQUNBLEtBQUssRUFBRSw2RUFBTyx1Q0FBdUM7QUFDckQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsSUFBcUM7QUFDN0MsTUFBTSw4REFBTztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VBbmltYXRlQ29uZmlnLmpzPzI5M2YiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF90eXBlb2YgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3R5cGVvZlwiO1xuaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCB3YXJuaW5nIGZyb20gXCJyYy11dGlsL2VzL3dhcm5pbmdcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUFuaW1hdGVDb25maWcoKSB7XG4gIHZhciBhbmltYXRlZCA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDoge1xuICAgIGlua0JhcjogdHJ1ZSxcbiAgICB0YWJQYW5lOiBmYWxzZVxuICB9O1xuICB2YXIgbWVyZ2VkQW5pbWF0ZWQ7XG4gIGlmIChhbmltYXRlZCA9PT0gZmFsc2UpIHtcbiAgICBtZXJnZWRBbmltYXRlZCA9IHtcbiAgICAgIGlua0JhcjogZmFsc2UsXG4gICAgICB0YWJQYW5lOiBmYWxzZVxuICAgIH07XG4gIH0gZWxzZSBpZiAoYW5pbWF0ZWQgPT09IHRydWUpIHtcbiAgICBtZXJnZWRBbmltYXRlZCA9IHtcbiAgICAgIGlua0JhcjogdHJ1ZSxcbiAgICAgIHRhYlBhbmU6IGZhbHNlXG4gICAgfTtcbiAgfSBlbHNlIHtcbiAgICBtZXJnZWRBbmltYXRlZCA9IF9vYmplY3RTcHJlYWQoe1xuICAgICAgaW5rQmFyOiB0cnVlXG4gICAgfSwgX3R5cGVvZihhbmltYXRlZCkgPT09ICdvYmplY3QnID8gYW5pbWF0ZWQgOiB7fSk7XG4gIH1cblxuICAvLyBFbmFibGUgdGFiUGFuZSBhbmltYXRpb24gaWYgcHJvdmlkZSBtb3Rpb25cbiAgaWYgKG1lcmdlZEFuaW1hdGVkLnRhYlBhbmVNb3Rpb24gJiYgbWVyZ2VkQW5pbWF0ZWQudGFiUGFuZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgbWVyZ2VkQW5pbWF0ZWQudGFiUGFuZSA9IHRydWU7XG4gIH1cbiAgaWYgKCFtZXJnZWRBbmltYXRlZC50YWJQYW5lTW90aW9uICYmIG1lcmdlZEFuaW1hdGVkLnRhYlBhbmUpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgd2FybmluZyhmYWxzZSwgJ2BhbmltYXRlZC50YWJQYW5lYCBpcyB0cnVlIGJ1dCBgYW5pbWF0ZWQudGFiUGFuZU1vdGlvbmAgaXMgbm90IHByb3ZpZGVkLiBNb3Rpb24gd2lsbCBub3Qgd29yay4nKTtcbiAgICB9XG4gICAgbWVyZ2VkQW5pbWF0ZWQudGFiUGFuZSA9IGZhbHNlO1xuICB9XG4gIHJldHVybiBtZXJnZWRBbmltYXRlZDtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useAnimateConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useIndicator.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useIndicator.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar useIndicator = function useIndicator(options) {\n  var activeTabOffset = options.activeTabOffset,\n    horizontal = options.horizontal,\n    rtl = options.rtl,\n    _options$indicator = options.indicator,\n    indicator = _options$indicator === void 0 ? {} : _options$indicator;\n  var size = indicator.size,\n    _indicator$align = indicator.align,\n    align = _indicator$align === void 0 ? 'center' : _indicator$align;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    inkStyle = _useState2[0],\n    setInkStyle = _useState2[1];\n  var inkBarRafRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  var getLength = react__WEBPACK_IMPORTED_MODULE_2___default().useCallback(function (origin) {\n    if (typeof size === 'function') {\n      return size(origin);\n    }\n    if (typeof size === 'number') {\n      return size;\n    }\n    return origin;\n  }, [size]);\n\n  // Delay set ink style to avoid remove tab blink\n  function cleanInkBarRaf() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__[\"default\"].cancel(inkBarRafRef.current);\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (horizontal) {\n        newInkStyle.width = getLength(activeTabOffset.width);\n        var key = rtl ? 'right' : 'left';\n        if (align === 'start') {\n          newInkStyle[key] = activeTabOffset[key];\n        }\n        if (align === 'center') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width / 2;\n          newInkStyle.transform = rtl ? 'translateX(50%)' : 'translateX(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width;\n          newInkStyle.transform = 'translateX(-100%)';\n        }\n      } else {\n        newInkStyle.height = getLength(activeTabOffset.height);\n        if (align === 'start') {\n          newInkStyle.top = activeTabOffset.top;\n        }\n        if (align === 'center') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height / 2;\n          newInkStyle.transform = 'translateY(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height;\n          newInkStyle.transform = 'translateY(-100%)';\n        }\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n      setInkStyle(newInkStyle);\n    });\n    return cleanInkBarRaf;\n  }, [activeTabOffset, horizontal, rtl, align, getLength]);\n  return {\n    style: inkStyle\n  };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIndicator);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useIndicator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useOffsets.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useOffsets.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useOffsets)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nfunction useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useOffsets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useSyncState.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useSyncState.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSyncState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useSyncState(defaultState, onChange) {\n  var stateRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(defaultState);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState({}),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  function setState(updater) {\n    var newValue = typeof updater === 'function' ? updater(stateRef.current) : updater;\n    if (newValue !== stateRef.current) {\n      onChange(newValue, stateRef.current);\n    }\n    stateRef.current = newValue;\n    forceUpdate({});\n  }\n  return [stateRef.current, setState];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VTeW5jU3RhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzRTtBQUN2QztBQUNoQjtBQUNmLGlCQUFpQix5Q0FBWTtBQUM3Qix3QkFBd0IsMkNBQWMsR0FBRztBQUN6Qyx1QkFBdUIsb0ZBQWM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLXRhYnMvZXMvaG9va3MvdXNlU3luY1N0YXRlLmpzPzE1MWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VTeW5jU3RhdGUoZGVmYXVsdFN0YXRlLCBvbkNoYW5nZSkge1xuICB2YXIgc3RhdGVSZWYgPSBSZWFjdC51c2VSZWYoZGVmYXVsdFN0YXRlKTtcbiAgdmFyIF9SZWFjdCR1c2VTdGF0ZSA9IFJlYWN0LnVzZVN0YXRlKHt9KSxcbiAgICBfUmVhY3QkdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX1JlYWN0JHVzZVN0YXRlLCAyKSxcbiAgICBmb3JjZVVwZGF0ZSA9IF9SZWFjdCR1c2VTdGF0ZTJbMV07XG4gIGZ1bmN0aW9uIHNldFN0YXRlKHVwZGF0ZXIpIHtcbiAgICB2YXIgbmV3VmFsdWUgPSB0eXBlb2YgdXBkYXRlciA9PT0gJ2Z1bmN0aW9uJyA/IHVwZGF0ZXIoc3RhdGVSZWYuY3VycmVudCkgOiB1cGRhdGVyO1xuICAgIGlmIChuZXdWYWx1ZSAhPT0gc3RhdGVSZWYuY3VycmVudCkge1xuICAgICAgb25DaGFuZ2UobmV3VmFsdWUsIHN0YXRlUmVmLmN1cnJlbnQpO1xuICAgIH1cbiAgICBzdGF0ZVJlZi5jdXJyZW50ID0gbmV3VmFsdWU7XG4gICAgZm9yY2VVcGRhdGUoe30pO1xuICB9XG4gIHJldHVybiBbc3RhdGVSZWYuY3VycmVudCwgc2V0U3RhdGVdO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useSyncState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useTouchMove.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useTouchMove.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useTouchMove)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nvar MIN_SWIPE_DISTANCE = 0.1;\nvar STOP_SWIPE_DISTANCE = 0.01;\nvar REFRESH_INTERVAL = 20;\nvar SPEED_OFF_MULTIPLE = Math.pow(0.995, REFRESH_INTERVAL);\n\n// ================================= Hook =================================\nfunction useTouchMove(ref, onOffset) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    touchPosition = _useState2[0],\n    setTouchPosition = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState3, 2),\n    lastTimestamp = _useState4[0],\n    setLastTimestamp = _useState4[1];\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState5, 2),\n    lastTimeDiff = _useState6[0],\n    setLastTimeDiff = _useState6[1];\n  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    _useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState7, 2),\n    lastOffset = _useState8[0],\n    setLastOffset = _useState8[1];\n  var motionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n\n  // ========================= Events =========================\n  // >>> Touch events\n  function onTouchStart(e) {\n    var _e$touches$ = e.touches[0],\n      screenX = _e$touches$.screenX,\n      screenY = _e$touches$.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    window.clearInterval(motionRef.current);\n  }\n  function onTouchMove(e) {\n    if (!touchPosition) return;\n\n    // e.preventDefault();\n    var _e$touches$2 = e.touches[0],\n      screenX = _e$touches$2.screenX,\n      screenY = _e$touches$2.screenY;\n    setTouchPosition({\n      x: screenX,\n      y: screenY\n    });\n    var offsetX = screenX - touchPosition.x;\n    var offsetY = screenY - touchPosition.y;\n    onOffset(offsetX, offsetY);\n    var now = Date.now();\n    setLastTimestamp(now);\n    setLastTimeDiff(now - lastTimestamp);\n    setLastOffset({\n      x: offsetX,\n      y: offsetY\n    });\n  }\n  function onTouchEnd() {\n    if (!touchPosition) return;\n    setTouchPosition(null);\n    setLastOffset(null);\n\n    // Swipe if needed\n    if (lastOffset) {\n      var distanceX = lastOffset.x / lastTimeDiff;\n      var distanceY = lastOffset.y / lastTimeDiff;\n      var absX = Math.abs(distanceX);\n      var absY = Math.abs(distanceY);\n\n      // Skip swipe if low distance\n      if (Math.max(absX, absY) < MIN_SWIPE_DISTANCE) return;\n      var currentX = distanceX;\n      var currentY = distanceY;\n      motionRef.current = window.setInterval(function () {\n        if (Math.abs(currentX) < STOP_SWIPE_DISTANCE && Math.abs(currentY) < STOP_SWIPE_DISTANCE) {\n          window.clearInterval(motionRef.current);\n          return;\n        }\n        currentX *= SPEED_OFF_MULTIPLE;\n        currentY *= SPEED_OFF_MULTIPLE;\n        onOffset(currentX * REFRESH_INTERVAL, currentY * REFRESH_INTERVAL);\n      }, REFRESH_INTERVAL);\n    }\n  }\n\n  // >>> Wheel event\n  var lastWheelDirectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  function onWheel(e) {\n    var deltaX = e.deltaX,\n      deltaY = e.deltaY;\n\n    // Convert both to x & y since wheel only happened on PC\n    var mixed = 0;\n    var absX = Math.abs(deltaX);\n    var absY = Math.abs(deltaY);\n    if (absX === absY) {\n      mixed = lastWheelDirectionRef.current === 'x' ? deltaX : deltaY;\n    } else if (absX > absY) {\n      mixed = deltaX;\n      lastWheelDirectionRef.current = 'x';\n    } else {\n      mixed = deltaY;\n      lastWheelDirectionRef.current = 'y';\n    }\n    if (onOffset(-mixed, -mixed)) {\n      e.preventDefault();\n    }\n  }\n\n  // ========================= Effect =========================\n  var touchEventsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  touchEventsRef.current = {\n    onTouchStart: onTouchStart,\n    onTouchMove: onTouchMove,\n    onTouchEnd: onTouchEnd,\n    onWheel: onWheel\n  };\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    function onProxyTouchStart(e) {\n      touchEventsRef.current.onTouchStart(e);\n    }\n    function onProxyTouchMove(e) {\n      touchEventsRef.current.onTouchMove(e);\n    }\n    function onProxyTouchEnd(e) {\n      touchEventsRef.current.onTouchEnd(e);\n    }\n    function onProxyWheel(e) {\n      touchEventsRef.current.onWheel(e);\n    }\n    document.addEventListener('touchmove', onProxyTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', onProxyTouchEnd, {\n      passive: true\n    });\n\n    // No need to clean up since element removed\n    ref.current.addEventListener('touchstart', onProxyTouchStart, {\n      passive: true\n    });\n    ref.current.addEventListener('wheel', onProxyWheel, {\n      passive: false\n    });\n    return function () {\n      document.removeEventListener('touchmove', onProxyTouchMove);\n      document.removeEventListener('touchend', onProxyTouchEnd);\n    };\n  }, []);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useTouchMove.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useUpdate.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useUpdate.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useUpdate),\n/* harmony export */   useUpdateState: () => (/* binding */ useUpdateState)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nfunction useUpdate(callback) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n  var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__.useLayoutUpdateEffect)(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nfunction useUpdateState(defaultState) {\n  var batchRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({}),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VVcGRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNFO0FBQ0c7QUFDaEM7O0FBRXpDO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZixrQkFBa0IsK0NBQVE7QUFDMUIsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0E7QUFDQSxrQkFBa0IsNkNBQU07QUFDeEIsb0JBQW9CLDZDQUFNO0FBQzFCOztBQUVBO0FBQ0EsRUFBRSx1RkFBcUI7QUFDdkI7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxpQkFBaUIsNkNBQU07QUFDdkIsbUJBQW1CLCtDQUFRLEdBQUc7QUFDOUIsaUJBQWlCLG9GQUFjO0FBQy9CO0FBQ0EsY0FBYyw2Q0FBTTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9ob29rcy91c2VVcGRhdGUuanM/MWMzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCB7IHVzZUxheW91dFVwZGF0ZUVmZmVjdCB9IGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZUxheW91dEVmZmVjdFwiO1xuaW1wb3J0IHsgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBIZWxwIHRvIG1lcmdlIGNhbGxiYWNrIHdpdGggYHVzZUxheW91dEVmZmVjdGAuXG4gKiBPbmUgdGltZSB3aWxsIG9ubHkgdHJpZ2dlciBvbmNlLlxuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VVcGRhdGUoY2FsbGJhY2spIHtcbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKDApLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIGNvdW50ID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRDb3VudCA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciBlZmZlY3RSZWYgPSB1c2VSZWYoMCk7XG4gIHZhciBjYWxsYmFja1JlZiA9IHVzZVJlZigpO1xuICBjYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG5cbiAgLy8gVHJpZ2dlciBvbiBgdXNlTGF5b3V0RWZmZWN0YFxuICB1c2VMYXlvdXRVcGRhdGVFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHZhciBfY2FsbGJhY2tSZWYkY3VycmVudDtcbiAgICAoX2NhbGxiYWNrUmVmJGN1cnJlbnQgPSBjYWxsYmFja1JlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfY2FsbGJhY2tSZWYkY3VycmVudCA9PT0gdm9pZCAwIHx8IF9jYWxsYmFja1JlZiRjdXJyZW50LmNhbGwoY2FsbGJhY2tSZWYpO1xuICB9LCBbY291bnRdKTtcblxuICAvLyBUcmlnZ2VyIHRvIHVwZGF0ZSBjb3VudFxuICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgIGlmIChlZmZlY3RSZWYuY3VycmVudCAhPT0gY291bnQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgZWZmZWN0UmVmLmN1cnJlbnQgKz0gMTtcbiAgICBzZXRDb3VudChlZmZlY3RSZWYuY3VycmVudCk7XG4gIH07XG59XG5leHBvcnQgZnVuY3Rpb24gdXNlVXBkYXRlU3RhdGUoZGVmYXVsdFN0YXRlKSB7XG4gIHZhciBiYXRjaFJlZiA9IHVzZVJlZihbXSk7XG4gIHZhciBfdXNlU3RhdGUzID0gdXNlU3RhdGUoe30pLFxuICAgIF91c2VTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUzLCAyKSxcbiAgICBmb3JjZVVwZGF0ZSA9IF91c2VTdGF0ZTRbMV07XG4gIHZhciBzdGF0ZSA9IHVzZVJlZih0eXBlb2YgZGVmYXVsdFN0YXRlID09PSAnZnVuY3Rpb24nID8gZGVmYXVsdFN0YXRlKCkgOiBkZWZhdWx0U3RhdGUpO1xuICB2YXIgZmx1c2hVcGRhdGUgPSB1c2VVcGRhdGUoZnVuY3Rpb24gKCkge1xuICAgIHZhciBjdXJyZW50ID0gc3RhdGUuY3VycmVudDtcbiAgICBiYXRjaFJlZi5jdXJyZW50LmZvckVhY2goZnVuY3Rpb24gKGNhbGxiYWNrKSB7XG4gICAgICBjdXJyZW50ID0gY2FsbGJhY2soY3VycmVudCk7XG4gICAgfSk7XG4gICAgYmF0Y2hSZWYuY3VycmVudCA9IFtdO1xuICAgIHN0YXRlLmN1cnJlbnQgPSBjdXJyZW50O1xuICAgIGZvcmNlVXBkYXRlKHt9KTtcbiAgfSk7XG4gIGZ1bmN0aW9uIHVwZGF0ZXIoY2FsbGJhY2spIHtcbiAgICBiYXRjaFJlZi5jdXJyZW50LnB1c2goY2FsbGJhY2spO1xuICAgIGZsdXNoVXBkYXRlKCk7XG4gIH1cbiAgcmV0dXJuIFtzdGF0ZS5jdXJyZW50LCB1cGRhdGVyXTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/hooks/useVisibleRange.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-tabs/es/hooks/useVisibleRange.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useVisibleRange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0,\n  right: 0\n};\nfunction useVisibleRange(tabOffsets, visibleTabContentValue, transform, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, _ref) {\n  var tabs = _ref.tabs,\n    tabPosition = _ref.tabPosition,\n    rtl = _ref.rtl;\n  var charUnit;\n  var position;\n  var transformSize;\n  if (['top', 'bottom'].includes(tabPosition)) {\n    charUnit = 'width';\n    position = rtl ? 'right' : 'left';\n    transformSize = Math.abs(transform);\n  } else {\n    charUnit = 'height';\n    position = 'top';\n    transformSize = -transform;\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    if (!tabs.length) {\n      return [0, 0];\n    }\n    var len = tabs.length;\n    var endIndex = len;\n    for (var i = 0; i < len; i += 1) {\n      var offset = tabOffsets.get(tabs[i].key) || DEFAULT_SIZE;\n      if (Math.floor(offset[position] + offset[charUnit]) > Math.floor(transformSize + visibleTabContentValue)) {\n        endIndex = i - 1;\n        break;\n      }\n    }\n    var startIndex = 0;\n    for (var _i = len - 1; _i >= 0; _i -= 1) {\n      var _offset = tabOffsets.get(tabs[_i].key) || DEFAULT_SIZE;\n      if (_offset[position] < transformSize) {\n        startIndex = _i + 1;\n        break;\n      }\n    }\n    return startIndex >= endIndex ? [0, 0] : [startIndex, endIndex];\n  }, [tabOffsets, visibleTabContentValue, tabContentSizeValue, addNodeSizeValue, operationNodeSizeValue, transformSize, tabPosition, tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), rtl]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/hooks/useVisibleRange.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/index.js":
/*!******************************************!*\
  !*** ./node_modules/rc-tabs/es/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Tabs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tabs */ \"(ssr)/./node_modules/rc-tabs/es/Tabs.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Tabs__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQjtBQUMxQixpRUFBZSw2Q0FBSSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10YWJzL2VzL2luZGV4LmpzPzEzZjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFRhYnMgZnJvbSBcIi4vVGFic1wiO1xuZXhwb3J0IGRlZmF1bHQgVGFiczsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tabs/es/util.js":
/*!*****************************************!*\
  !*** ./node_modules/rc-tabs/es/util.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genDataNodeKey: () => (/* binding */ genDataNodeKey),\n/* harmony export */   getRemovable: () => (/* binding */ getRemovable),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * We trade Map as deps which may change with same value but different ref object.\n * We should make it as hash for deps\n * */\nfunction stringify(obj) {\n  var tgt;\n  if (obj instanceof Map) {\n    tgt = {};\n    obj.forEach(function (v, k) {\n      tgt[k] = v;\n    });\n  } else {\n    tgt = obj;\n  }\n  return JSON.stringify(tgt);\n}\nvar RC_TABS_DOUBLE_QUOTE = 'TABS_DQ';\nfunction genDataNodeKey(key) {\n  return String(key).replace(/\"/g, RC_TABS_DOUBLE_QUOTE);\n}\nfunction getRemovable(closable, closeIcon, editable, disabled) {\n  if (\n  // Only editable tabs can be removed\n  !editable ||\n  // Tabs cannot be removed when disabled\n  disabled ||\n  // closable is false\n  closable === false ||\n  // If closable is undefined, the remove button should be hidden when closeIcon is null or false\n  closable === undefined && (closeIcon === false || closeIcon === null)) {\n    return false;\n  }\n  return true;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGFicy9lcy91dGlsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10YWJzL2VzL3V0aWwuanM/NTNkMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFdlIHRyYWRlIE1hcCBhcyBkZXBzIHdoaWNoIG1heSBjaGFuZ2Ugd2l0aCBzYW1lIHZhbHVlIGJ1dCBkaWZmZXJlbnQgcmVmIG9iamVjdC5cbiAqIFdlIHNob3VsZCBtYWtlIGl0IGFzIGhhc2ggZm9yIGRlcHNcbiAqICovXG5leHBvcnQgZnVuY3Rpb24gc3RyaW5naWZ5KG9iaikge1xuICB2YXIgdGd0O1xuICBpZiAob2JqIGluc3RhbmNlb2YgTWFwKSB7XG4gICAgdGd0ID0ge307XG4gICAgb2JqLmZvckVhY2goZnVuY3Rpb24gKHYsIGspIHtcbiAgICAgIHRndFtrXSA9IHY7XG4gICAgfSk7XG4gIH0gZWxzZSB7XG4gICAgdGd0ID0gb2JqO1xuICB9XG4gIHJldHVybiBKU09OLnN0cmluZ2lmeSh0Z3QpO1xufVxudmFyIFJDX1RBQlNfRE9VQkxFX1FVT1RFID0gJ1RBQlNfRFEnO1xuZXhwb3J0IGZ1bmN0aW9uIGdlbkRhdGFOb2RlS2V5KGtleSkge1xuICByZXR1cm4gU3RyaW5nKGtleSkucmVwbGFjZSgvXCIvZywgUkNfVEFCU19ET1VCTEVfUVVPVEUpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldFJlbW92YWJsZShjbG9zYWJsZSwgY2xvc2VJY29uLCBlZGl0YWJsZSwgZGlzYWJsZWQpIHtcbiAgaWYgKFxuICAvLyBPbmx5IGVkaXRhYmxlIHRhYnMgY2FuIGJlIHJlbW92ZWRcbiAgIWVkaXRhYmxlIHx8XG4gIC8vIFRhYnMgY2Fubm90IGJlIHJlbW92ZWQgd2hlbiBkaXNhYmxlZFxuICBkaXNhYmxlZCB8fFxuICAvLyBjbG9zYWJsZSBpcyBmYWxzZVxuICBjbG9zYWJsZSA9PT0gZmFsc2UgfHxcbiAgLy8gSWYgY2xvc2FibGUgaXMgdW5kZWZpbmVkLCB0aGUgcmVtb3ZlIGJ1dHRvbiBzaG91bGQgYmUgaGlkZGVuIHdoZW4gY2xvc2VJY29uIGlzIG51bGwgb3IgZmFsc2VcbiAgY2xvc2FibGUgPT09IHVuZGVmaW5lZCAmJiAoY2xvc2VJY29uID09PSBmYWxzZSB8fCBjbG9zZUljb24gPT09IG51bGwpKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHJldHVybiB0cnVlO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tabs/es/util.js\n");

/***/ })

};
;