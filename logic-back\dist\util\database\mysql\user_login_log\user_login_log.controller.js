"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLoginLogController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_login_log_service_1 = require("./user_login_log.service");
const create_user_login_log_dto_1 = require("./dto/create-user-login-log.dto");
const query_user_login_log_dto_1 = require("./dto/query-user-login-log.dto");
const user_login_log_entity_1 = require("./entities/user_login_log.entity");
let UserLoginLogController = class UserLoginLogController {
    userLoginLogService;
    constructor(userLoginLogService) {
        this.userLoginLogService = userLoginLogService;
    }
    async create(createDto) {
        return await this.userLoginLogService.create(createDto);
    }
    async findAll(query) {
        return await this.userLoginLogService.findWithPagination(query);
    }
    async getUserHistory(userId, limit = 10) {
        return await this.userLoginLogService.getUserLoginHistory(userId, limit);
    }
    async getLastLogin(userId) {
        return await this.userLoginLogService.getLastLogin(userId);
    }
    async getLoginStats(userId, days = 30) {
        return await this.userLoginLogService.getLoginStats(userId, days);
    }
    async checkAbnormalLogin(userId, clientIp, userAgent) {
        const isAbnormal = await this.userLoginLogService.checkAbnormalLogin(userId, clientIp, userAgent);
        return { isAbnormal };
    }
    async findOne(id) {
        return await this.userLoginLogService.findOne(id);
    }
    async remove(id) {
        await this.userLoginLogService.remove(id);
        return { message: '删除成功' };
    }
    async recordLogout(userId, sessionId) {
        await this.userLoginLogService.recordLogout(userId, sessionId);
        return { message: '登出记录成功' };
    }
    async getUserRecentLogs(userId) {
        try {
            const logs = await this.userLoginLogService.getUserLoginHistory(userId, 10);
            return {
                success: true,
                data: logs,
                message: `用户${userId}最近10条登录记录`
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                message: '查询失败'
            };
        }
    }
};
exports.UserLoginLogController = UserLoginLogController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建登录日志' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: user_login_log_entity_1.UserLoginLog }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_login_log_dto_1.CreateUserLoginLogDto]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '分页查询登录日志' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_user_login_log_dto_1.QueryUserLoginLogDto]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('user/:userId/history'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户登录历史' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [user_login_log_entity_1.UserLoginLog] }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "getUserHistory", null);
__decorate([
    (0, common_1.Get)('user/:userId/last'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户最近登录记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: user_login_log_entity_1.UserLoginLog }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "getLastLogin", null);
__decorate([
    (0, common_1.Get)('user/:userId/stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户登录统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "getLoginStats", null);
__decorate([
    (0, common_1.Get)('user/:userId/check-abnormal'),
    (0, swagger_1.ApiOperation)({ summary: '检查异常登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检查完成' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Query)('clientIp')),
    __param(2, (0, common_1.Query)('userAgent')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "checkAbnormalLogin", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID查询登录日志' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: user_login_log_entity_1.UserLoginLog }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "findOne", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除登录日志' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('user/:userId/logout'),
    (0, swagger_1.ApiOperation)({ summary: '记录用户登出' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '记录成功' }),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "recordLogout", null);
__decorate([
    (0, common_1.Get)('debug/user/:userId/recent'),
    (0, swagger_1.ApiOperation)({ summary: '查看用户最近的登录记录（调试用）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserLoginLogController.prototype, "getUserRecentLogs", null);
exports.UserLoginLogController = UserLoginLogController = __decorate([
    (0, swagger_1.ApiTags)('用户登录日志'),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, common_1.Controller)('api/user-login-log'),
    __metadata("design:paramtypes", [user_login_log_service_1.UserLoginLogService])
], UserLoginLogController);
//# sourceMappingURL=user_login_log.controller.js.map