"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetUserLocationStatsQuery = void 0;
class GetUserLocationStatsQuery {
    userId;
    days;
    includeTrusted;
    includeRiskAnalysis;
    minLoginCount;
    timestamp;
    constructor(userId, days = 30, includeTrusted = true, includeRiskAnalysis = false, minLoginCount = 1) {
        this.userId = userId;
        this.days = Math.max(1, Math.min(365, days));
        this.includeTrusted = includeTrusted;
        this.includeRiskAnalysis = includeRiskAnalysis;
        this.minLoginCount = Math.max(1, minLoginCount);
        this.timestamp = new Date();
    }
    static createBasic(userId, days = 30) {
        return new GetUserLocationStatsQuery(userId, days, true, false, 1);
    }
    static createWithRiskAnalysis(userId, days = 30) {
        return new GetUserLocationStatsQuery(userId, days, true, true, 1);
    }
    static createTrustedOnly(userId, days = 30) {
        return new GetUserLocationStatsQuery(userId, days, true, false, 5);
    }
    static createActiveLocations(userId, days = 30, minLoginCount = 5) {
        return new GetUserLocationStatsQuery(userId, days, true, false, minLoginCount);
    }
    validate() {
        const errors = [];
        if (this.userId <= 0) {
            errors.push('用户ID必须大于0');
        }
        if (this.days < 1 || this.days > 365) {
            errors.push('查询天数必须在1-365天之间');
        }
        if (this.minLoginCount < 1) {
            errors.push('最小登录次数必须大于0');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    getStartDate() {
        const startDate = new Date(this.timestamp);
        startDate.setDate(startDate.getDate() - this.days);
        startDate.setHours(0, 0, 0, 0);
        return startDate;
    }
    getEndDate() {
        const endDate = new Date(this.timestamp);
        endDate.setHours(23, 59, 59, 999);
        return endDate;
    }
    getCacheKey() {
        const riskSuffix = this.includeRiskAnalysis ? '_risk' : '';
        const trustedSuffix = this.includeTrusted ? '_trusted' : '';
        return `user_location_stats:${this.userId}:${this.days}d:${this.minLoginCount}${trustedSuffix}${riskSuffix}`;
    }
    getSummary() {
        const riskInfo = this.includeRiskAnalysis ? '(包含风险分析)' : '';
        const trustedInfo = this.includeTrusted ? '(包含可信位置)' : '';
        return `查询用户${this.userId}最近${this.days}天的位置统计 ${trustedInfo}${riskInfo}`;
    }
    get isLongTermQuery() {
        return this.days > 90;
    }
    get isShortTermQuery() {
        return this.days <= 7;
    }
    getQueryType() {
        if (this.isShortTermQuery) {
            return '短期查询';
        }
        else if (this.isLongTermQuery) {
            return '长期查询';
        }
        else {
            return '中期查询';
        }
    }
    getExpectedDataVolume() {
        if (this.days <= 7) {
            return 'LOW';
        }
        else if (this.days <= 30) {
            return 'MEDIUM';
        }
        else {
            return 'HIGH';
        }
    }
    toJSON() {
        return {
            userId: this.userId,
            days: this.days,
            includeTrusted: this.includeTrusted,
            includeRiskAnalysis: this.includeRiskAnalysis,
            minLoginCount: this.minLoginCount,
            timestamp: this.timestamp.toISOString(),
            startDate: this.getStartDate().toISOString(),
            endDate: this.getEndDate().toISOString(),
            cacheKey: this.getCacheKey(),
            queryType: this.getQueryType(),
            expectedDataVolume: this.getExpectedDataVolume()
        };
    }
}
exports.GetUserLocationStatsQuery = GetUserLocationStatsQuery;
//# sourceMappingURL=get-user-location-stats.query.js.map