"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrderController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const package_order_service_1 = require("./package-order.service");
const dto_1 = require("./dto");
const package_order_entity_1 = require("./entities/package-order.entity");
let PackageOrderController = class PackageOrderController {
    packageOrderService;
    constructor(packageOrderService) {
        this.packageOrderService = packageOrderService;
    }
    async create(createPackageOrderDto) {
        return await this.packageOrderService.create(createPackageOrderDto);
    }
    async findAll() {
        return await this.packageOrderService.findAll();
    }
    async search(queryDto) {
        return await this.packageOrderService.findWithPagination(queryDto);
    }
    async findByUserId(userId) {
        return await this.packageOrderService.findByUserId(userId);
    }
    async getUserStats(userId) {
        return await this.packageOrderService.getUserOrderStats(userId);
    }
    async findByOrderNo(orderNo) {
        return await this.packageOrderService.findByOrderNo(orderNo);
    }
    async findOne(id) {
        return await this.packageOrderService.findOne(+id);
    }
    async update(id, updatePackageOrderDto) {
        return await this.packageOrderService.update(+id, updatePackageOrderDto);
    }
    async remove(id) {
        return await this.packageOrderService.remove(+id);
    }
    async updatePaymentStatus(orderNo, body) {
        return await this.packageOrderService.updatePaymentStatus(orderNo, body.paymentId, body.status);
    }
    async cancelOrder(orderNo) {
        return await this.packageOrderService.cancelOrder(orderNo);
    }
};
exports.PackageOrderController = PackageOrderController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建套餐订单' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: package_order_entity_1.PackageOrder }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreatePackageOrderDto]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有套餐订单' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [package_order_entity_1.PackageOrder] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: '分页查询套餐订单' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        schema: {
            type: 'object',
            properties: {
                data: { type: 'array', items: { $ref: '#/components/schemas/PackageOrder' } },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
            }
        }
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryPackageOrderDto]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "search", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID获取订单列表' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [package_order_entity_1.PackageOrder] }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('user/:userId/stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户订单统计' }),
    (0, swagger_1.ApiParam)({ name: 'userId', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number', description: '总订单数' },
                pending: { type: 'number', description: '待支付订单数' },
                paid: { type: 'number', description: '已支付订单数' },
                cancelled: { type: 'number', description: '已取消订单数' }
            }
        }
    }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "getUserStats", null);
__decorate([
    (0, common_1.Get)('order-no/:orderNo'),
    (0, swagger_1.ApiOperation)({ summary: '根据订单编号获取订单' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: package_order_entity_1.PackageOrder }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "findByOrderNo", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取套餐订单' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: package_order_entity_1.PackageOrder }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新套餐订单' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: package_order_entity_1.PackageOrder }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdatePackageOrderDto]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除套餐订单' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '订单ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)('payment/:orderNo'),
    (0, swagger_1.ApiOperation)({ summary: '更新订单支付状态' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: package_order_entity_1.PackageOrder }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('orderNo')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "updatePaymentStatus", null);
__decorate([
    (0, common_1.Patch)('cancel/:orderNo'),
    (0, swagger_1.ApiOperation)({ summary: '取消订单' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功', type: package_order_entity_1.PackageOrder }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '订单状态不允许取消' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderController.prototype, "cancelOrder", null);
exports.PackageOrderController = PackageOrderController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/套餐订单(package_order)'),
    (0, common_1.Controller)('package-order'),
    __metadata("design:paramtypes", [package_order_service_1.PackageOrderService])
], PackageOrderController);
//# sourceMappingURL=package-order.controller.js.map