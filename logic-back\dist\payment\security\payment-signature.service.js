"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentSignatureService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentSignatureService = void 0;
const common_1 = require("@nestjs/common");
const crypto = require("crypto");
const fs = require("fs");
const path = require("path");
const payment_config_service_1 = require("../config/payment-config.service");
const platform_certificate_service_1 = require("./platform-certificate.service");
let PaymentSignatureService = PaymentSignatureService_1 = class PaymentSignatureService {
    configService;
    platformCertificateService;
    logger = new common_1.Logger(PaymentSignatureService_1.name);
    constructor(configService, platformCertificateService) {
        this.configService = configService;
        this.platformCertificateService = platformCertificateService;
    }
    generateWechatPaySignature(method, url, timestamp, nonce, body) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const parsedUrl = new URL(url.startsWith('http') ? url : `https://api.mch.weixin.qq.com${url}`);
            const signUrl = parsedUrl.pathname + parsedUrl.search;
            const message = `${method}\n${signUrl}\n${timestamp}\n${nonce}\n${body}\n`;
            this.logger.debug(`微信支付签名串: ${message}`);
            const sign = crypto.createSign('RSA-SHA256');
            sign.update(message);
            sign.end();
            const privateKey = fs.readFileSync(path.resolve(process.cwd(), wechatConfig.privateKeyPath));
            return sign.sign(privateKey, 'base64');
        }
        catch (error) {
            this.logger.error(`生成微信支付签名失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    verifyWechatPaySignature(timestamp, nonce, body, signature, serialNo) {
        try {
            const bodyStr = typeof body === 'string' ? body : JSON.stringify(body);
            const message = `${timestamp}\n${nonce}\n${bodyStr}\n`;
            this.logger.debug(`微信支付验签名串: ${message}`);
            let wechatPayPublicKey = null;
            let certPem = null;
            try {
                if (serialNo) {
                    this.logger.debug(`尝试获取序列号为 ${serialNo} 的微信支付平台证书`);
                    certPem = this.platformCertificateService.getCertificate(serialNo);
                    if (certPem) {
                        this.logger.debug(`成功从平台证书服务获取证书: ${serialNo}`);
                        wechatPayPublicKey = certPem;
                    }
                    else {
                        this.logger.warn(`平台证书服务中未找到序列号为 ${serialNo} 的证书，尝试使用配置的证书`);
                    }
                }
                if (!wechatPayPublicKey) {
                    const wechatConfig = this.configService.getWechatPayConfig();
                    if (!wechatConfig.wechatPayPublicKeyPath) {
                        this.logger.error('微信支付平台证书路径未配置，无法进行验签');
                        this.logger.error('请通过微信支付平台下载证书或使用平台证书下载工具获取证书');
                        throw new Error('微信支付平台证书路径未配置，请正确配置wechatPayPublicKeyPath或集成平台证书服务');
                    }
                    wechatPayPublicKey = fs.readFileSync(path.resolve(process.cwd(), wechatConfig.wechatPayPublicKeyPath));
                    this.logger.debug(`成功读取配置的微信支付平台证书: ${wechatConfig.wechatPayPublicKeyPath}`);
                    if (serialNo) {
                        this.logger.warn(`使用配置的证书进行验签，但通知使用的证书序列号为: ${serialNo}`);
                        this.logger.warn('请确保已下载并配置此序列号对应的微信支付平台证书');
                    }
                }
            }
            catch (err) {
                this.logger.error(`获取微信支付平台证书失败: ${err.message}`, err.stack);
                return false;
            }
            try {
                const verify = crypto.createVerify('RSA-SHA256');
                verify.update(message);
                const signVerified = verify.verify(wechatPayPublicKey, signature, 'base64');
                if (!signVerified) {
                    this.logger.warn('微信支付通知签名验证失败');
                    this.logger.debug(`签名: ${signature}`);
                    this.logger.debug(`验签串: ${message}`);
                    if (serialNo) {
                        this.logger.warn(`可能是因为使用了错误的证书，通知中的证书序列号为: ${serialNo}`);
                        this.logger.warn('请确保已下载此序列号对应的微信支付平台证书');
                    }
                    return false;
                }
                this.logger.log('微信支付通知签名验证成功');
                return true;
            }
            catch (error) {
                this.logger.error(`微信支付签名验证过程出错: ${error.message}`, error.stack);
                return false;
            }
        }
        catch (error) {
            this.logger.error(`微信支付验签失败: ${error.message}`, error.stack);
            return false;
        }
    }
    generateAlipaySignature(params) {
        try {
            const alipayConfig = this.configService.getAlipayConfig();
            const sortedKeys = Object.keys(params).sort();
            const stringToSign = sortedKeys
                .filter(key => params[key] !== '' && params[key] !== null && params[key] !== undefined && key !== 'sign')
                .map(key => `${key}=${params[key]}`)
                .join('&');
            this.logger.debug(`支付宝待签名字符串: ${stringToSign}`);
            let privateKey = alipayConfig.privateKey;
            if (!privateKey.includes('-----BEGIN')) {
                privateKey = `-----BEGIN RSA PRIVATE KEY-----\n${privateKey}\n-----END RSA PRIVATE KEY-----`;
                privateKey = privateKey.replace(/-----BEGIN RSA PRIVATE KEY-----\n(.+)\n-----END RSA PRIVATE KEY-----/s, (_, key) => {
                    const formattedKey = key.replace(/(.{64})/g, '$1\n');
                    return `-----BEGIN RSA PRIVATE KEY-----\n${formattedKey}\n-----END RSA PRIVATE KEY-----`;
                });
                this.logger.debug('已格式化支付宝私钥为PEM格式');
            }
            let sign;
            try {
                if (alipayConfig.signType === 'RSA2') {
                    const signer = crypto.createSign('RSA-SHA256');
                    signer.update(stringToSign, 'utf8');
                    sign = signer.sign(privateKey, 'base64');
                }
                else {
                    const signer = crypto.createSign('RSA-SHA1');
                    signer.update(stringToSign, 'utf8');
                    sign = signer.sign(privateKey, 'base64');
                }
                this.logger.debug(`生成的支付宝签名: ${sign}`);
                return sign;
            }
            catch (error) {
                this.logger.error(`支付宝签名生成失败: ${error.message}`, error.stack);
                throw new Error(`支付宝签名生成失败: ${error.message}`);
            }
        }
        catch (error) {
            this.logger.error(`支付宝签名生成过程出错: ${error.message}`, error.stack);
            throw error;
        }
    }
    verifyAlipaySignature(params) {
        try {
            const alipayConfig = this.configService.getAlipayConfig();
            const sign = params.sign;
            if (!sign) {
                this.logger.warn('支付宝验签失败: 未找到签名');
                return false;
            }
            const sortedKeys = Object.keys(params).sort();
            const stringToVerify = sortedKeys
                .filter(key => params[key] !== '' && params[key] !== null && params[key] !== undefined && key !== 'sign' && key !== 'sign_type')
                .map(key => `${key}=${params[key]}`)
                .join('&');
            this.logger.debug(`支付宝待验证字符串: ${stringToVerify}`);
            let publicKey = alipayConfig.aliPublicKey;
            this.logger.debug(`使用支付宝公钥(aliPublicKey)验证通知签名`);
            if (!publicKey.includes('-----BEGIN')) {
                publicKey = `-----BEGIN PUBLIC KEY-----\n${publicKey}\n-----END PUBLIC KEY-----`;
                publicKey = publicKey.replace(/-----BEGIN PUBLIC KEY-----\n(.+)\n-----END PUBLIC KEY-----/s, (_, key) => {
                    const formattedKey = key.replace(/(.{64})/g, '$1\n');
                    return `-----BEGIN PUBLIC KEY-----\n${formattedKey}\n-----END PUBLIC KEY-----`;
                });
                this.logger.debug('已格式化支付宝公钥为PEM格式');
            }
            try {
                let verifier;
                if (alipayConfig.signType === 'RSA2') {
                    verifier = crypto.createVerify('RSA-SHA256');
                }
                else {
                    verifier = crypto.createVerify('RSA-SHA1');
                }
                verifier.update(stringToVerify, 'utf8');
                const result = verifier.verify(publicKey, sign, 'base64');
                this.logger.debug(`支付宝签名验证结果: ${result}`);
                return result;
            }
            catch (error) {
                this.logger.error(`支付宝验签出错: ${error.message}`, error.stack);
                return false;
            }
        }
        catch (error) {
            this.logger.error(`支付宝验签过程出错: ${error.message}`, error.stack);
            return false;
        }
    }
    decryptWechatPayResource(resource) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const { ciphertext, nonce, associated_data } = resource;
            if (!wechatConfig.apiV3Key) {
                throw new Error('未配置微信支付APIv3密钥，无法解密数据');
            }
            const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
            const authTag = ciphertextBuffer.slice(ciphertextBuffer.length - 16);
            const data = ciphertextBuffer.slice(0, ciphertextBuffer.length - 16);
            const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(wechatConfig.apiV3Key), Buffer.from(nonce));
            decipher.setAuthTag(authTag);
            if (associated_data) {
                decipher.setAAD(Buffer.from(associated_data));
            }
            const decoded = decipher.update(data);
            const final = decipher.final();
            const decryptedData = Buffer.concat([decoded, final]).toString('utf8');
            return JSON.parse(decryptedData);
        }
        catch (error) {
            this.logger.error(`解密微信支付资源数据失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.PaymentSignatureService = PaymentSignatureService;
exports.PaymentSignatureService = PaymentSignatureService = PaymentSignatureService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => platform_certificate_service_1.PlatformCertificateService))),
    __metadata("design:paramtypes", [payment_config_service_1.PaymentConfigService,
        platform_certificate_service_1.PlatformCertificateService])
], PaymentSignatureService);
//# sourceMappingURL=payment-signature.service.js.map