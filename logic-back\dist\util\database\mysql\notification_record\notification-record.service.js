"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationRecordService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationRecordService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const notification_record_entity_1 = require("./entities/notification-record.entity");
const notification_record_dto_1 = require("./dto/notification-record.dto");
let NotificationRecordService = NotificationRecordService_1 = class NotificationRecordService {
    notificationRecordRepository;
    logger = new common_1.Logger(NotificationRecordService_1.name);
    constructor(notificationRecordRepository) {
        this.notificationRecordRepository = notificationRecordRepository;
    }
    async create(createDto) {
        try {
            const notification = this.notificationRecordRepository.create({
                ...createDto,
                status: createDto.status || notification_record_dto_1.NotificationStatus.PENDING,
                retryCount: 0,
                maxRetryCount: createDto.maxRetryCount || 3,
                nextRetryTime: new Date(Date.now() + 5 * 60 * 1000),
                version: 1,
            });
            const result = await this.notificationRecordRepository.save(notification);
            this.logger.log(`创建通知记录成功: ID=${result.id}, 类型=${result.notificationType}, 目标ID=${result.targetId}, 状态=${result.status}`);
            return result;
        }
        catch (error) {
            this.logger.error(`创建通知记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async update(id, updateDto) {
        try {
            await this.notificationRecordRepository.update(id, {
                ...updateDto,
                version: () => 'version + 1',
            });
            const updated = await this.notificationRecordRepository.findOne({ where: { id } });
            if (!updated) {
                throw new Error(`更新通知记录后未找到记录: ID=${id}`);
            }
            this.logger.log(`更新通知记录成功: ID=${id}, 状态=${updated.status}`);
            return updated;
        }
        catch (error) {
            this.logger.error(`更新通知记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findById(id) {
        return this.notificationRecordRepository.findOne({ where: { id } });
    }
    async findAll(queryDto, page = 1, limit = 10) {
        try {
            const where = {};
            if (queryDto.notificationType) {
                where.notificationType = queryDto.notificationType;
            }
            if (queryDto.targetId) {
                where.targetId = queryDto.targetId;
            }
            if (queryDto.userId) {
                where.userId = queryDto.userId;
            }
            if (queryDto.status) {
                where.status = queryDto.status;
            }
            return await this.notificationRecordRepository.findAndCount({
                where,
                order: { createdAt: 'DESC' },
                skip: (page - 1) * limit,
                take: limit,
            });
        }
        catch (error) {
            this.logger.error(`查询通知记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findPendingRetryNotifications(limit = 100) {
        try {
            return await this.notificationRecordRepository.find({
                where: {
                    status: notification_record_dto_1.NotificationStatus.PENDING,
                    nextRetryTime: (0, typeorm_2.Between)(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date()),
                },
                order: { nextRetryTime: 'ASC' },
                take: limit,
            });
        }
        catch (error) {
            this.logger.error(`查询待重试通知记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateStatus(ids, status) {
        try {
            await this.notificationRecordRepository
                .createQueryBuilder()
                .update(notification_record_entity_1.NotificationRecord)
                .set({ status, version: () => 'version + 1' })
                .whereInIds(ids)
                .execute();
            this.logger.log(`批量更新通知状态成功: 数量=${ids.length}, 新状态=${status}`);
        }
        catch (error) {
            this.logger.error(`批量更新通知状态失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async remove(id) {
        try {
            const result = await this.notificationRecordRepository.delete(id);
            if (result.affected && result.affected > 0) {
                this.logger.log(`删除通知记录成功: ID=${id}`);
            }
            else {
                this.logger.warn(`删除通知记录失败: 未找到ID=${id}的记录`);
            }
        }
        catch (error) {
            this.logger.error(`删除通知记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByTargetIdAndType(targetId, notificationType, status) {
        try {
            const where = {
                targetId,
                notificationType
            };
            if (status) {
                where.status = status;
            }
            const records = await this.notificationRecordRepository.find({
                where,
                order: { createdAt: 'DESC' }
            });
            return records;
        }
        catch (error) {
            this.logger.error(`查询通知记录失败: 目标ID=${targetId}, 类型=${notificationType}, ${error.message}`, error.stack);
            return [];
        }
    }
};
exports.NotificationRecordService = NotificationRecordService;
exports.NotificationRecordService = NotificationRecordService = NotificationRecordService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(notification_record_entity_1.NotificationRecord)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], NotificationRecordService);
//# sourceMappingURL=notification-record.service.js.map