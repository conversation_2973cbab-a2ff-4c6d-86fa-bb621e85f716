import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional } from 'class-validator';

/**
 * 登录风险检查请求DTO
 */
export class RiskCheckRequestDto {
  @ApiProperty({ description: '用户ID', example: 12345 })
  @IsNotEmpty({ message: '用户ID不能为空' })
  @IsNumber({}, { message: '用户ID必须是数字' })
  userId: number;

  @ApiProperty({ description: 'IP地址', example: '**************' })
  @IsNotEmpty({ message: 'IP地址不能为空' })
  @IsString({ message: 'IP地址必须是字符串' })
  ipAddress: string;

  @ApiProperty({ 
    description: '用户代理信息', 
    required: false,
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  })
  @IsOptional()
  @IsString({ message: '用户代理信息必须是字符串' })
  userAgent?: string;

  @ApiProperty({ 
    description: '会话ID', 
    required: false,
    example: 'sess_123456'
  })
  @IsOptional()
  @IsString({ message: '会话ID必须是字符串' })
  sessionId?: string;
}
