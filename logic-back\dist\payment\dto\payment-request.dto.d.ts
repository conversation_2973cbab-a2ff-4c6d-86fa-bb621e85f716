export declare enum PaymentChannel {
    ALIPAY = "alipay",
    WECHATPAY = "wechatpay"
}
export declare enum PaymentMode {
    REDIRECT = "redirect",
    QR_CODE = "qrcode"
}
export declare class CreatePaymentDto {
    userId: string;
    amount: number;
    subject: string;
    description?: string;
    channel: PaymentChannel;
    paymentMode?: PaymentMode;
    clientIp?: string;
    returnUrl?: string;
    extraData?: Record<string, any>;
}
export declare class QueryPaymentDto {
    orderNo: string;
}
export declare class ClosePaymentDto {
    orderNo: string;
    reason?: string;
}
