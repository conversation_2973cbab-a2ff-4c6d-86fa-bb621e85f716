"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRefund = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let PaymentRefund = class PaymentRefund {
    id;
    paymentOrderId;
    businessRefundId;
    channelRefundId;
    channel;
    amount;
    reason;
    status;
    parameters;
    result;
    notifyData;
    userId;
    operatorId;
    notifyUrl;
    refundedAt;
    version;
    createdAt;
    updatedAt;
};
exports.PaymentRefund = PaymentRefund;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '支付订单ID', length: 64 }),
    (0, swagger_1.ApiProperty)({ description: '支付订单ID' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "paymentOrderId", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '业务退款单号', length: 64 }),
    (0, swagger_1.ApiProperty)({ description: '业务退款单号' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "businessRefundId", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '渠道退款单号', length: 50, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '渠道退款单号' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "channelRefundId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付渠道', length: 20 }),
    (0, swagger_1.ApiProperty)({ description: '支付渠道' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "channel", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '退款金额', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '退款金额' }),
    __metadata("design:type", Number)
], PaymentRefund.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '退款原因', length: 255, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '退款原因' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "reason", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '退款状态', length: 20, default: 'pending' }),
    (0, swagger_1.ApiProperty)({ description: '退款状态' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '退款参数', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '退款参数' }),
    __metadata("design:type", Object)
], PaymentRefund.prototype, "parameters", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '退款结果', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '退款结果' }),
    __metadata("design:type", Object)
], PaymentRefund.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '通知数据', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '通知数据' }),
    __metadata("design:type", Object)
], PaymentRefund.prototype, "notifyData", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '用户ID', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '操作人ID', length: 32, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '操作人ID' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '退款回调URL', length: 255, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '退款回调URL' }),
    __metadata("design:type", String)
], PaymentRefund.prototype, "notifyUrl", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '退款完成时间', type: 'timestamp', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '退款完成时间' }),
    __metadata("design:type", Date)
], PaymentRefund.prototype, "refundedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '版本号（乐观锁）', type: 'int', default: 1 }),
    (0, swagger_1.ApiProperty)({ description: '版本号（乐观锁）' }),
    __metadata("design:type", Number)
], PaymentRefund.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentRefund.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], PaymentRefund.prototype, "updatedAt", void 0);
exports.PaymentRefund = PaymentRefund = __decorate([
    (0, typeorm_1.Entity)('payment_refund')
], PaymentRefund);
//# sourceMappingURL=payment-refund.entity.js.map