"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginLoggerExtensionUtil = void 0;
const common_1 = require("@nestjs/common");
const ip_location_application_service_1 = require("../application/services/ip-location-application.service");
const login_logger_util_1 = require("../../../web/user_login_log/login-logger.util");
const logger_service_1 = require("../../../common/logger/logger.service");
let LoginLoggerExtensionUtil = class LoginLoggerExtensionUtil {
    ipLocationService;
    loggerService;
    constructor(ipLocationService, loggerService) {
        this.ipLocationService = ipLocationService;
        this.loggerService = loggerService;
    }
    async logSuccessLoginWithLocation(data) {
        const startTime = Date.now();
        try {
            let locationInfo = null;
            let riskAssessment = null;
            if (data.enableLocationResolution !== false && data.clientIp) {
                try {
                    const locationResult = await this.ipLocationService.queryIpLocation({
                        ip: data.clientIp,
                        includeRisk: false
                    });
                    locationInfo = {
                        country: locationResult.country,
                        province: locationResult.province,
                        city: locationResult.city,
                        isp: locationResult.isp,
                        locationSource: locationResult.dataSource,
                        dataQuality: locationResult.confidence
                    };
                    await this.ipLocationService.updateUserCommonLocation(data.userId, {
                        country: locationResult.country,
                        province: locationResult.province,
                        city: locationResult.city,
                        isp: locationResult.isp,
                        dataSource: locationResult.dataSource,
                        hasEmptyFields: !locationResult.isHighQuality,
                        confidence: locationResult.confidence,
                        displayName: locationResult.displayName
                    });
                }
                catch (locationError) {
                    this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'resolveLocation', {
                        userId: data.userId,
                        ip: this.maskIP(data.clientIp)
                    }, locationError);
                    locationInfo = {
                        country: '未知',
                        province: '未知',
                        city: '未知',
                        isp: '未知',
                        locationSource: 'fallback',
                        dataQuality: 0
                    };
                }
            }
            if (data.enableRiskAssessment && data.clientIp && locationInfo) {
                try {
                    const riskResult = await this.ipLocationService.checkLoginRisk({
                        userId: data.userId,
                        ipAddress: data.clientIp,
                        userAgent: data.userAgent,
                        sessionId: data.sessionId
                    });
                    riskAssessment = {
                        riskLevel: riskResult.riskAssessment.level,
                        riskReason: riskResult.riskAssessment.reason
                    };
                    if (riskResult.riskAssessment.level === 'HIGH') {
                        this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'highRiskLogin', {
                            userId: data.userId,
                            ip: this.maskIP(data.clientIp),
                            location: `${locationInfo.province} ${locationInfo.city}`,
                            riskLevel: riskResult.riskAssessment.level,
                            riskScore: riskResult.riskAssessment.score,
                            factors: riskResult.riskAssessment.factors
                        });
                    }
                }
                catch (riskError) {
                    this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'assessRisk', {
                        userId: data.userId,
                        ip: this.maskIP(data.clientIp)
                    }, riskError);
                    riskAssessment = {
                        riskLevel: 'LOW',
                        riskReason: '风险评估异常'
                    };
                }
            }
            const completeLogData = {
                userId: data.userId,
                loginType: data.loginType,
                clientIp: data.clientIp,
                userAgent: data.userAgent,
                deviceInfo: data.deviceInfo,
                sessionId: data.sessionId,
                tokenExpireTime: data.tokenExpireTime,
                location: locationInfo ? `${locationInfo.province} ${locationInfo.city}` : undefined,
                ...(locationInfo && {
                    country: locationInfo.country,
                    province: locationInfo.province,
                    city: locationInfo.city,
                    isp: locationInfo.isp,
                    locationSource: locationInfo.locationSource,
                    dataQuality: locationInfo.dataQuality
                }),
                ...(riskAssessment && {
                    riskLevel: riskAssessment.riskLevel,
                    riskReason: riskAssessment.riskReason
                })
            };
            await login_logger_util_1.LoginLoggerUtil.logSuccessLogin(completeLogData);
            this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'logSuccessLoginWithLocation', {
                userId: data.userId,
                ip: this.maskIP(data.clientIp),
                location: locationInfo ? `${locationInfo.province} ${locationInfo.city}` : '未知',
                riskLevel: riskAssessment?.riskLevel || 'UNKNOWN',
                responseTime: Date.now() - startTime
            });
        }
        catch (error) {
            this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'logSuccessLoginWithLocation', {
                userId: data.userId,
                ip: this.maskIP(data.clientIp),
                responseTime: Date.now() - startTime
            }, error);
            try {
                await login_logger_util_1.LoginLoggerUtil.logSuccessLogin({
                    userId: data.userId,
                    loginType: data.loginType,
                    clientIp: data.clientIp,
                    userAgent: data.userAgent,
                    deviceInfo: data.deviceInfo,
                    sessionId: data.sessionId
                });
            }
            catch (fallbackError) {
                this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'fallbackLogging', {
                    userId: data.userId
                }, fallbackError);
            }
        }
    }
    async logFailedLoginWithLocation(data) {
        try {
            let locationInfo = null;
            if (data.enableLocationResolution !== false && data.clientIp) {
                try {
                    const locationResult = await this.ipLocationService.queryIpLocation({
                        ip: data.clientIp,
                        includeRisk: false
                    });
                    locationInfo = {
                        country: locationResult.country,
                        province: locationResult.province,
                        city: locationResult.city,
                        isp: locationResult.isp
                    };
                }
                catch (locationError) {
                    locationInfo = null;
                }
            }
            const failLogData = {
                userId: data.userId,
                loginType: data.loginType,
                clientIp: data.clientIp,
                userAgent: data.userAgent,
                failReason: data.failReason || '登录失败'
            };
            await login_logger_util_1.LoginLoggerUtil.logFailedLogin(failLogData);
            if (locationInfo && (locationInfo.country !== '中国' || data.failReason?.includes('密码错误'))) {
                this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'suspiciousFailedLogin', {
                    userId: data.userId,
                    ip: this.maskIP(data.clientIp),
                    location: `${locationInfo.province} ${locationInfo.city}`,
                    failReason: data.failReason
                });
            }
        }
        catch (error) {
            this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'logFailedLoginWithLocation', {
                userId: data.userId,
                ip: this.maskIP(data.clientIp)
            }, error);
            try {
                await login_logger_util_1.LoginLoggerUtil.logFailedLogin({
                    userId: data.userId,
                    loginType: data.loginType,
                    clientIp: data.clientIp,
                    userAgent: data.userAgent,
                    failReason: data.failReason || '登录失败'
                });
            }
            catch (fallbackError) {
                this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'fallbackFailedLogging', {
                    userId: data.userId
                }, fallbackError);
            }
        }
    }
    async checkNeedAdditionalVerification(userId, clientIp) {
        try {
            const riskResult = await this.ipLocationService.checkLoginRisk({
                userId,
                ipAddress: clientIp
            });
            return {
                needVerification: riskResult.riskAssessment.needVerification,
                reason: riskResult.riskAssessment.reason,
                recommendedMethods: riskResult.riskAssessment.recommendedActions
            };
        }
        catch (error) {
            this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'checkNeedAdditionalVerification', {
                userId,
                ip: this.maskIP(clientIp)
            }, error);
            return {
                needVerification: false,
                reason: '风险评估异常，默认通过',
                recommendedMethods: []
            };
        }
    }
    maskIP(ip) {
        if (!ip)
            return '未知IP';
        if (ip.includes(':')) {
            const parts = ip.split(':');
            if (parts.length >= 4) {
                return `${parts.slice(0, 4).join(':')}:****`;
            }
            return '****:****:****:****';
        }
        else {
            const parts = ip.split('.');
            if (parts.length === 4) {
                return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
            }
            return '***.***.***.**';
        }
    }
};
exports.LoginLoggerExtensionUtil = LoginLoggerExtensionUtil;
exports.LoginLoggerExtensionUtil = LoginLoggerExtensionUtil = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ip_location_application_service_1.IpLocationApplicationService,
        logger_service_1.LoggerService])
], LoginLoggerExtensionUtil);
//# sourceMappingURL=login-logger-extension.util.js.map