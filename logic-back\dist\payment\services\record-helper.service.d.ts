import { ModuleRef } from '@nestjs/core';
import { PaymentRecordService } from '../../util/database/mysql/payment_record/payment-record.service';
import { LockManager } from '../lock/lock.manager';
import { OptimisticLock } from '../lock/optimistic.lock';
export interface PaymentRecordParams {
    tradeNo: string;
    paymentId?: string;
    amount?: number;
    status: string;
    paymentTime?: Date;
    payMethod?: string;
    userId?: string;
    description?: string;
    remark?: string;
    extraData?: any;
}
export declare class RecordHelperService {
    private readonly moduleRef;
    private readonly lockManager?;
    private readonly optimisticLock?;
    private readonly paymentRecordService?;
    private readonly logger;
    constructor(moduleRef: ModuleRef, lockManager?: LockManager | undefined, optimisticLock?: OptimisticLock | undefined, paymentRecordService?: PaymentRecordService | undefined);
    private isValidStatusTransition;
    createOrUpdatePaymentRecord(params: PaymentRecordParams): Promise<boolean>;
    private updateWithOptimisticLock;
    private executeSafely;
}
