# IP地理位置解析功能使用指南

## 概述

本模块基于 `ip2region` 库提供离线IP地理位置解析功能，支持IPv4和IPv6地址解析，并集成了登录风险评估和用户位置统计功能。

## 快速开始

### 1. 安装依赖

```bash
npm install ip2region --save
```

### 2. 数据库迁移

执行SQL迁移脚本：

```bash
mysql -u root -p your_database < src/util/ip_location/sql/ip-location-migration.sql
```

### 3. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# IP地理位置功能配置
IP_LOCATION_ENABLED=true
IP_LOCATION_CACHE_TTL=86400
IP_LOCATION_RISK_THRESHOLD=70
IP_LOCATION_FALLBACK_ENABLED=false
IP_LOCATION_DATA_QUALITY_MIN=80
```

## API 接口使用

### 1. IP地理位置查询

```http
GET /api/v1/ip-location/query?ip=**************&includeRisk=false
```

**响应示例：**
```json
{
  "ip": "**************",
  "country": "中国",
  "province": "北京市",
  "city": "北京市",
  "isp": "联通",
  "dataSource": "ip2region",
  "confidence": 95,
  "isHighQuality": true,
  "displayName": "中国 北京市 北京市"
}
```

### 2. 登录风险检查

```http
POST /api/v1/ip-location/check-risk
Content-Type: application/json

{
  "userId": 12345,
  "ipAddress": "**************",
  "userAgent": "Mozilla/5.0...",
  "sessionId": "sess_123456"
}
```

**响应示例：**
```json
{
  "riskAssessment": {
    "level": "HIGH",
    "score": 85,
    "reason": "跨省登录",
    "factors": ["跨省登录", "新登录地", "运营商变化"],
    "needVerification": true,
    "recommendedActions": ["短信验证", "邮箱验证"]
  },
  "location": {
    "country": "中国",
    "province": "上海市",
    "city": "上海市",
    "isp": "电信",
    "displayName": "中国 上海市 上海市"
  },
  "userHistory": {
    "lastLoginLocation": "广东省 深圳市",
    "commonLocationCount": 2,
    "isNewLocation": true
  }
}
```

### 3. 用户位置统计

```http
GET /api/v1/ip-location/user/12345/stats?days=30
```

### 4. 设置可信位置

```http
POST /api/v1/ip-location/user/12345/trust
Content-Type: application/json

{
  "province": "广东省",
  "city": "深圳市",
  "reason": "用户主动设置"
}
```

## 代码集成示例

### 1. 在登录控制器中集成

```typescript
import { LoginLoggerExtensionUtil } from '../util/ip_location/utils/login-logger-extension.util';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly loginLoggerExtension: LoginLoggerExtensionUtil
  ) {}

  @Post('login')
  async login(@Body() loginDto: LoginDto, @Req() request: Request) {
    try {
      // 执行登录逻辑
      const loginResult = await this.authService.login(loginDto);
      
      if (loginResult.success) {
        // 记录成功登录日志（包含地理位置和风险评估）
        await this.loginLoggerExtension.logSuccessLoginWithLocation({
          userId: loginResult.user.id,
          loginType: LoginType.PASSWORD,
          loginStatus: LoginStatus.SUCCESS,
          clientIp: this.extractClientIP(request),
          userAgent: request.headers['user-agent'],
          deviceInfo: this.extractDeviceInfo(request),
          sessionId: loginResult.sessionId,
          tokenExpireTime: loginResult.tokenExpireTime,
          enableLocationResolution: true,
          enableRiskAssessment: true
        });

        // 检查是否需要额外验证
        const verificationCheck = await this.loginLoggerExtension
          .checkNeedAdditionalVerification(
            loginResult.user.id, 
            this.extractClientIP(request)
          );

        if (verificationCheck.needVerification) {
          return {
            success: true,
            needVerification: true,
            reason: verificationCheck.reason,
            recommendedMethods: verificationCheck.recommendedMethods,
            tempToken: loginResult.tempToken // 临时token，用于验证后换取正式token
          };
        }

        return {
          success: true,
          token: loginResult.token,
          user: loginResult.user
        };
      } else {
        // 记录失败登录日志
        await this.loginLoggerExtension.logFailedLoginWithLocation({
          userId: loginDto.userId || 0,
          loginType: LoginType.PASSWORD,
          loginStatus: LoginStatus.FAILED,
          clientIp: this.extractClientIP(request),
          userAgent: request.headers['user-agent'],
          failReason: loginResult.reason,
          enableLocationResolution: true
        });

        return {
          success: false,
          message: loginResult.reason
        };
      }
    } catch (error) {
      // 错误处理
      throw error;
    }
  }

  private extractClientIP(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIp = request.headers['x-real-ip'] as string;
    const clientIp = request.connection?.remoteAddress;

    if (forwarded) return forwarded.split(',')[0].trim();
    if (realIp) return realIp;
    return clientIp?.replace('::ffff:', '') || '127.0.0.1';
  }
}
```

### 2. 直接使用IP地理位置服务

```typescript
import { IpLocationApplicationService } from '../util/ip_location/application/services/ip-location-application.service';

@Injectable()
export class SomeService {
  constructor(
    private readonly ipLocationService: IpLocationApplicationService
  ) {}

  async handleUserAction(userId: number, clientIp: string) {
    // 查询IP地理位置
    const locationInfo = await this.ipLocationService.queryIpLocation({
      ip: clientIp,
      includeRisk: false
    });

    // 进行风险评估
    const riskAssessment = await this.ipLocationService.checkLoginRisk({
      userId,
      ipAddress: clientIp
    });

    // 根据风险等级采取不同措施
    if (riskAssessment.riskAssessment.level === 'HIGH') {
      // 高风险处理逻辑
      await this.handleHighRiskAction(userId, locationInfo, riskAssessment);
    }

    // 更新用户常用位置统计
    await this.ipLocationService.updateUserCommonLocation(userId, {
      country: locationInfo.country,
      province: locationInfo.province,
      city: locationInfo.city,
      isp: locationInfo.isp,
      dataSource: locationInfo.dataSource,
      hasEmptyFields: !locationInfo.isHighQuality,
      confidence: locationInfo.confidence,
      displayName: locationInfo.displayName
    });
  }
}
```

## 配置说明

### 风险评估配置

可以通过环境变量调整风险评估参数：

```bash
# 风险阈值配置
RISK_SCORE_LOW_THRESHOLD=30
RISK_SCORE_MEDIUM_THRESHOLD=70
RISK_SCORE_HIGH_THRESHOLD=100

# 风险因子权重
RISK_WEIGHT_FOREIGN_LOGIN=60
RISK_WEIGHT_CROSS_PROVINCE=40
RISK_WEIGHT_NEW_LOCATION=30
RISK_WEIGHT_ISP_CHANGE=10

# 用户保护配置
NEW_USER_PROTECTION_DAYS=7
NEW_USER_RISK_REDUCTION=15
```

### 缓存配置

```bash
# Redis缓存配置
REDIS_IP_LOCATION_DB=2
REDIS_IP_LOCATION_PREFIX=ip_loc:
CACHE_IP_LOCATION_MAX_SIZE=100MB
```

## 测试

### 1. 运行单元测试

```bash
npm run test src/util/ip_location/test/ip-location.test.ts
```

### 2. 使用测试页面

打开 `src/util/ip_location/test/ip-location-test.html` 在浏览器中进行功能测试。

### 3. API测试

使用Postman或curl测试API接口：

```bash
# 测试IP查询
curl -X GET "http://localhost:8003/api/v1/ip-location/query?ip=*******" \
  -H "Authorization: Bearer your-token"

# 测试风险检查
curl -X POST "http://localhost:8003/api/v1/ip-location/check-risk" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{"userId": 12345, "ipAddress": "**************"}'
```

## 注意事项

1. **数据精度限制**：ip2region只支持到城市级别，不支持区县级别定位
2. **缓存策略**：IP位置信息会缓存24小时，减少重复查询
3. **错误处理**：位置解析失败时会返回默认值，不影响主要业务流程
4. **性能考虑**：风险评估是异步进行的，不会阻塞登录流程
5. **隐私保护**：IP地址在日志中会自动脱敏处理

## 故障排除

### 常见问题

1. **ip2region初始化失败**
   - 检查npm包是否正确安装
   - 确认Node.js版本兼容性

2. **数据库连接错误**
   - 检查数据库迁移是否执行成功
   - 确认数据库连接配置正确

3. **Redis缓存问题**
   - 检查Redis服务是否正常运行
   - 确认Redis配置参数正确

4. **API权限错误**
   - 检查认证token是否有效
   - 确认用户权限配置正确
