import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * IP地址查询请求DTO
 */
export class IpQueryRequestDto {
  @ApiProperty({ description: 'IP地址', example: '**************' })
  @IsNotEmpty({ message: 'IP地址不能为空' })
  @IsString({ message: 'IP地址必须是字符串' })
  ip: string;

  @ApiProperty({
    description: '是否包含风险评估',
    required: false,
    default: false,
    type: Boolean
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return value;
  })
  @IsBoolean({ message: '包含风险评估必须是布尔值' })
  includeRisk?: boolean = false;
}
