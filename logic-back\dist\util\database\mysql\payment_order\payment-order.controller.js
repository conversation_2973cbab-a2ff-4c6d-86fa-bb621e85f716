"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentOrderController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_order_service_1 = require("./payment-order.service");
const create_payment_order_dto_1 = require("./dto/create-payment-order.dto");
const update_payment_order_dto_1 = require("./dto/update-payment-order.dto");
let PaymentOrderController = class PaymentOrderController {
    paymentOrderService;
    constructor(paymentOrderService) {
        this.paymentOrderService = paymentOrderService;
    }
    create(createPaymentOrderDto) {
        return this.paymentOrderService.create(createPaymentOrderDto);
    }
    findAll() {
        return this.paymentOrderService.findAll();
    }
    findOne(id) {
        return this.paymentOrderService.findOne(id);
    }
    findByBusinessOrderId(businessOrderId) {
        return this.paymentOrderService.findByBusinessOrderId(businessOrderId);
    }
    findByChannelOrderId(channelOrderId) {
        return this.paymentOrderService.findByChannelOrderId(channelOrderId);
    }
    findByUserId(userId) {
        return this.paymentOrderService.findByUserId(userId);
    }
    findByStatus(status) {
        return this.paymentOrderService.findByStatus(status);
    }
    update(id, updatePaymentOrderDto) {
        return this.paymentOrderService.update(id, updatePaymentOrderDto);
    }
    updateStatus(id, status, result) {
        return this.paymentOrderService.updateStatus(id, status, result);
    }
    updateNotifyData(id, notifyData) {
        return this.paymentOrderService.updateNotifyData(id, notifyData);
    }
    remove(id) {
        return this.paymentOrderService.remove(id);
    }
};
exports.PaymentOrderController = PaymentOrderController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建支付订单' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_order_dto_1.CreatePaymentOrderDto]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取所有支付订单' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据ID查询支付订单' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据业务订单号查询支付订单' }),
    (0, common_1.Get)('business-order-id/:businessOrderId'),
    __param(0, (0, common_1.Param)('businessOrderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "findByBusinessOrderId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据渠道订单号查询支付订单' }),
    (0, common_1.Get)('channel-order-id/:channelOrderId'),
    __param(0, (0, common_1.Param)('channelOrderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "findByChannelOrderId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID查询支付订单' }),
    (0, common_1.Get)('user/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "findByUserId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据支付状态查询支付订单' }),
    (0, common_1.Get)('status/:status'),
    __param(0, (0, common_1.Param)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "findByStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新支付订单信息' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_payment_order_dto_1.UpdatePaymentOrderDto]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新支付状态' }),
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Body)('result')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "updateStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新通知数据' }),
    (0, common_1.Patch)(':id/notify'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('notifyData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "updateNotifyData", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除支付订单' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentOrderController.prototype, "remove", null);
exports.PaymentOrderController = PaymentOrderController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/支付订单(payment_order)'),
    (0, common_1.Controller)('payment-order'),
    __metadata("design:paramtypes", [payment_order_service_1.PaymentOrderService])
], PaymentOrderController);
//# sourceMappingURL=payment-order.controller.js.map