"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-notification";
exports.ids = ["vendor-chunks/rc-notification"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-notification/es/Notice.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-notification/es/Notice.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/./node_modules/rc-util/es/pickAttrs.js\");\n\n\n\n\n\n\n\n\nvar Notify = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    className = props.className,\n    _props$duration = props.duration,\n    duration = _props$duration === void 0 ? 4.5 : _props$duration,\n    showProgress = props.showProgress,\n    _props$pauseOnHover = props.pauseOnHover,\n    pauseOnHover = _props$pauseOnHover === void 0 ? true : _props$pauseOnHover,\n    eventKey = props.eventKey,\n    content = props.content,\n    closable = props.closable,\n    _props$closeIcon = props.closeIcon,\n    closeIcon = _props$closeIcon === void 0 ? 'x' : _props$closeIcon,\n    divProps = props.props,\n    onClick = props.onClick,\n    onNoticeClose = props.onNoticeClose,\n    times = props.times,\n    forcedHovering = props.hovering;\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_6__.useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState, 2),\n    hovering = _React$useState2[0],\n    setHovering = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState3, 2),\n    percent = _React$useState4[0],\n    setPercent = _React$useState4[1];\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState5, 2),\n    spentTime = _React$useState6[0],\n    setSpentTime = _React$useState6[1];\n  var mergedHovering = forcedHovering || hovering;\n  var mergedShowProgress = duration > 0 && showProgress;\n\n  // ======================== Close =========================\n  var onInternalClose = function onInternalClose() {\n    onNoticeClose(eventKey);\n  };\n  var onCloseKeyDown = function onCloseKeyDown(e) {\n    if (e.key === 'Enter' || e.code === 'Enter' || e.keyCode === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__[\"default\"].ENTER) {\n      onInternalClose();\n    }\n  };\n\n  // ======================== Effect ========================\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!mergedHovering && duration > 0) {\n      var start = Date.now() - spentTime;\n      var timeout = setTimeout(function () {\n        onInternalClose();\n      }, duration * 1000 - spentTime);\n      return function () {\n        if (pauseOnHover) {\n          clearTimeout(timeout);\n        }\n        setSpentTime(Date.now() - start);\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, mergedHovering, times]);\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!mergedHovering && mergedShowProgress && (pauseOnHover || spentTime === 0)) {\n      var start = performance.now();\n      var animationFrame;\n      var calculate = function calculate() {\n        cancelAnimationFrame(animationFrame);\n        animationFrame = requestAnimationFrame(function (timestamp) {\n          var runtime = timestamp + spentTime - start;\n          var progress = Math.min(runtime / (duration * 1000), 1);\n          setPercent(progress * 100);\n          if (progress < 1) {\n            calculate();\n          }\n        });\n      };\n      calculate();\n      return function () {\n        if (pauseOnHover) {\n          cancelAnimationFrame(animationFrame);\n        }\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [duration, spentTime, mergedHovering, mergedShowProgress, times]);\n\n  // ======================== Closable ========================\n  var closableObj = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(closable) === 'object' && closable !== null) {\n      return closable;\n    }\n    if (closable) {\n      return {\n        closeIcon: closeIcon\n      };\n    }\n    return {};\n  }, [closable, closeIcon]);\n  var ariaProps = (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(closableObj, true);\n\n  // ======================== Progress ========================\n  var validPercent = 100 - (!percent || percent < 0 ? 0 : percent > 100 ? 100 : percent);\n\n  // ======================== Render ========================\n  var noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, divProps, {\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(noticePrefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(noticePrefixCls, \"-closable\"), closable)),\n    style: style,\n    onMouseEnter: function onMouseEnter(e) {\n      var _divProps$onMouseEnte;\n      setHovering(true);\n      divProps === null || divProps === void 0 || (_divProps$onMouseEnte = divProps.onMouseEnter) === null || _divProps$onMouseEnte === void 0 || _divProps$onMouseEnte.call(divProps, e);\n    },\n    onMouseLeave: function onMouseLeave(e) {\n      var _divProps$onMouseLeav;\n      setHovering(false);\n      divProps === null || divProps === void 0 || (_divProps$onMouseLeav = divProps.onMouseLeave) === null || _divProps$onMouseLeav === void 0 || _divProps$onMouseLeav.call(divProps, e);\n    },\n    onClick: onClick\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: \"\".concat(noticePrefixCls, \"-content\")\n  }, content), closable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"a\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    tabIndex: 0,\n    className: \"\".concat(noticePrefixCls, \"-close\"),\n    onKeyDown: onCloseKeyDown,\n    \"aria-label\": \"Close\"\n  }, ariaProps, {\n    onClick: function onClick(e) {\n      e.preventDefault();\n      e.stopPropagation();\n      onInternalClose();\n    }\n  }), closableObj.closeIcon), mergedShowProgress && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"progress\", {\n    className: \"\".concat(noticePrefixCls, \"-progress\"),\n    max: \"100\",\n    value: validPercent\n  }, validPercent + '%'));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NoticeList.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-notification/es/NoticeList.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n/* harmony import */ var _hooks_useStack__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStack */ \"(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\");\n\n\n\n\n\n\nvar _excluded = [\"className\", \"style\", \"classNames\", \"styles\"];\n\n\n\n\n\n\nvar NoticeList = function NoticeList(props) {\n  var configList = props.configList,\n    placement = props.placement,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    motion = props.motion,\n    onAllNoticeRemoved = props.onAllNoticeRemoved,\n    onNoticeClose = props.onNoticeClose,\n    stackConfig = props.stack;\n  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_6__.useContext)(_NotificationProvider__WEBPACK_IMPORTED_MODULE_10__.NotificationContext),\n    ctxCls = _useContext.classNames;\n  var dictRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)({});\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState, 2),\n    latestNotice = _useState2[0],\n    setLatestNotice = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState3, 2),\n    hoverKeys = _useState4[0],\n    setHoverKeys = _useState4[1];\n  var keys = configList.map(function (config) {\n    return {\n      config: config,\n      key: String(config.key)\n    };\n  });\n  var _useStack = (0,_hooks_useStack__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(stackConfig),\n    _useStack2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useStack, 2),\n    stack = _useStack2[0],\n    _useStack2$ = _useStack2[1],\n    offset = _useStack2$.offset,\n    threshold = _useStack2$.threshold,\n    gap = _useStack2$.gap;\n  var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n  var placementMotion = typeof motion === 'function' ? motion(placement) : motion;\n\n  // Clean hover key\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (stack && hoverKeys.length > 1) {\n      setHoverKeys(function (prev) {\n        return prev.filter(function (key) {\n          return keys.some(function (_ref) {\n            var dataKey = _ref.key;\n            return key === dataKey;\n          });\n        });\n      });\n    }\n  }, [hoverKeys, keys, stack]);\n\n  // Force update latest notice\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    var _keys;\n    if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n      var _keys2;\n      setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n    }\n  }, [keys, stack]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(rc_motion__WEBPACK_IMPORTED_MODULE_8__.CSSMotionList, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    key: placement,\n    className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n    style: style,\n    keys: keys,\n    motionAppear: true\n  }, placementMotion, {\n    onAllRemoved: function onAllRemoved() {\n      onAllNoticeRemoved(placement);\n    }\n  }), function (_ref2, nodeRef) {\n    var config = _ref2.config,\n      motionClassName = _ref2.className,\n      motionStyle = _ref2.style,\n      motionIndex = _ref2.index;\n    var _ref3 = config,\n      key = _ref3.key,\n      times = _ref3.times;\n    var strKey = String(key);\n    var _ref4 = config,\n      configClassName = _ref4.className,\n      configStyle = _ref4.style,\n      configClassNames = _ref4.classNames,\n      configStyles = _ref4.styles,\n      restConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref4, _excluded);\n    var dataIndex = keys.findIndex(function (item) {\n      return item.key === strKey;\n    });\n\n    // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n    // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n    var stackStyle = {};\n    if (stack) {\n      var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n      var transformX = placement === 'top' || placement === 'bottom' ? '-50%' : '0';\n      if (index > 0) {\n        var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n        stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n\n        // Transform\n        var verticalOffset = 0;\n        for (var i = 0; i < index; i++) {\n          var _dictRef$current$keys;\n          verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n        }\n        var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith('top') ? 1 : -1);\n        var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n      } else {\n        stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(\"div\", {\n      ref: nodeRef,\n      className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n      style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n      onMouseEnter: function onMouseEnter() {\n        return setHoverKeys(function (prev) {\n          return prev.includes(strKey) ? prev : [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev), [strKey]);\n        });\n      },\n      onMouseLeave: function onMouseLeave() {\n        return setHoverKeys(function (prev) {\n          return prev.filter(function (k) {\n            return k !== strKey;\n          });\n        });\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Notice__WEBPACK_IMPORTED_MODULE_9__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restConfig, {\n      ref: function ref(node) {\n        if (dataIndex > -1) {\n          dictRef.current[strKey] = node;\n        } else {\n          delete dictRef.current[strKey];\n        }\n      },\n      prefixCls: prefixCls,\n      classNames: configClassNames,\n      styles: configStyles,\n      className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n      style: configStyle,\n      times: times,\n      key: key,\n      eventKey: key,\n      onNoticeClose: onNoticeClose,\n      hovering: stack && hoverKeys.length > 0\n    })));\n  });\n};\nif (true) {\n  NoticeList.displayName = 'NoticeList';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoticeList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NoticeList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NotificationProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/rc-notification/es/NotificationProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContext: () => (/* binding */ NotificationContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar NotificationContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({});\nvar NotificationProvider = function NotificationProvider(_ref) {\n  var children = _ref.children,\n    classNames = _ref.classNames;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NotificationContext.Provider, {\n    value: {\n      classNames: classNames\n    }\n  }, children);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGlmaWNhdGlvblByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDbkIsdUNBQXVDLDBEQUFtQixHQUFHO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsaUVBQWUsb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW5vdGlmaWNhdGlvbi9lcy9Ob3RpZmljYXRpb25Qcm92aWRlci5qcz8xN2MxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIE5vdGlmaWNhdGlvbkNvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dCh7fSk7XG52YXIgTm90aWZpY2F0aW9uUHJvdmlkZXIgPSBmdW5jdGlvbiBOb3RpZmljYXRpb25Qcm92aWRlcihfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgY2xhc3NOYW1lcyA9IF9yZWYuY2xhc3NOYW1lcztcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE5vdGlmaWNhdGlvbkNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZToge1xuICAgICAgY2xhc3NOYW1lczogY2xhc3NOYW1lc1xuICAgIH1cbiAgfSwgY2hpbGRyZW4pO1xufTtcbmV4cG9ydCBkZWZhdWx0IE5vdGlmaWNhdGlvblByb3ZpZGVyOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/Notifications.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-notification/es/Notifications.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _NoticeList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoticeList */ \"(ssr)/./node_modules/rc-notification/es/NoticeList.js\");\n\n\n\n\n\n\n// ant-notification ant-notification-topRight\nvar Notifications = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-notification' : _props$prefixCls,\n    container = props.container,\n    motion = props.motion,\n    maxCount = props.maxCount,\n    className = props.className,\n    style = props.style,\n    onAllRemoved = props.onAllRemoved,\n    stack = props.stack,\n    renderNotifications = props.renderNotifications;\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState([]),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    configList = _React$useState2[0],\n    setConfigList = _React$useState2[1];\n\n  // ======================== Close =========================\n  var onNoticeClose = function onNoticeClose(key) {\n    var _config$onClose;\n    // Trigger close event\n    var config = configList.find(function (item) {\n      return item.key === key;\n    });\n    config === null || config === void 0 || (_config$onClose = config.onClose) === null || _config$onClose === void 0 || _config$onClose.call(config);\n    setConfigList(function (list) {\n      return list.filter(function (item) {\n        return item.key !== key;\n      });\n    });\n  };\n\n  // ========================= Refs =========================\n  react__WEBPACK_IMPORTED_MODULE_3__.useImperativeHandle(ref, function () {\n    return {\n      open: function open(config) {\n        setConfigList(function (list) {\n          var clone = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(list);\n\n          // Replace if exist\n          var index = clone.findIndex(function (item) {\n            return item.key === config.key;\n          });\n          var innerConfig = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config);\n          if (index >= 0) {\n            var _list$index;\n            innerConfig.times = (((_list$index = list[index]) === null || _list$index === void 0 ? void 0 : _list$index.times) || 0) + 1;\n            clone[index] = innerConfig;\n          } else {\n            innerConfig.times = 0;\n            clone.push(innerConfig);\n          }\n          if (maxCount > 0 && clone.length > maxCount) {\n            clone = clone.slice(-maxCount);\n          }\n          return clone;\n        });\n      },\n      close: function close(key) {\n        onNoticeClose(key);\n      },\n      destroy: function destroy() {\n        setConfigList([]);\n      }\n    };\n  });\n\n  // ====================== Placements ======================\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState({}),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState3, 2),\n    placements = _React$useState4[0],\n    setPlacements = _React$useState4[1];\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    var nextPlacements = {};\n    configList.forEach(function (config) {\n      var _config$placement = config.placement,\n        placement = _config$placement === void 0 ? 'topRight' : _config$placement;\n      if (placement) {\n        nextPlacements[placement] = nextPlacements[placement] || [];\n        nextPlacements[placement].push(config);\n      }\n    });\n\n    // Fill exist placements to avoid empty list causing remove without motion\n    Object.keys(placements).forEach(function (placement) {\n      nextPlacements[placement] = nextPlacements[placement] || [];\n    });\n    setPlacements(nextPlacements);\n  }, [configList]);\n\n  // Clean up container if all notices fade out\n  var onAllNoticeRemoved = function onAllNoticeRemoved(placement) {\n    setPlacements(function (originPlacements) {\n      var clone = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, originPlacements);\n      var list = clone[placement] || [];\n      if (!list.length) {\n        delete clone[placement];\n      }\n      return clone;\n    });\n  };\n\n  // Effect tell that placements is empty now\n  var emptyRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    if (Object.keys(placements).length > 0) {\n      emptyRef.current = true;\n    } else if (emptyRef.current) {\n      // Trigger only when from exist to empty\n      onAllRemoved === null || onAllRemoved === void 0 || onAllRemoved();\n      emptyRef.current = false;\n    }\n  }, [placements]);\n  // ======================== Render ========================\n  if (!container) {\n    return null;\n  }\n  var placementList = Object.keys(placements);\n  return /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, placementList.map(function (placement) {\n    var placementConfigList = placements[placement];\n    var list = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_NoticeList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      key: placement,\n      configList: placementConfigList,\n      placement: placement,\n      prefixCls: prefixCls,\n      className: className === null || className === void 0 ? void 0 : className(placement),\n      style: style === null || style === void 0 ? void 0 : style(placement),\n      motion: motion,\n      onNoticeClose: onNoticeClose,\n      onAllNoticeRemoved: onAllNoticeRemoved,\n      stack: stack\n    });\n    return renderNotifications ? renderNotifications(list, {\n      prefixCls: prefixCls,\n      key: placement\n    }) : list;\n  })), container);\n});\nif (true) {\n  Notifications.displayName = 'Notifications';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notifications);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useNotification.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Notifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Notifications */ \"(ssr)/./node_modules/rc-notification/es/Notifications.js\");\n\n\n\nvar _excluded = [\"getContainer\", \"motion\", \"prefixCls\", \"maxCount\", \"className\", \"style\", \"onAllRemoved\", \"stack\", \"renderNotifications\"];\n\n\nvar defaultGetContainer = function defaultGetContainer() {\n  return document.body;\n};\nvar uniqueKey = 0;\nfunction mergeConfig() {\n  var clone = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(function (obj) {\n    if (obj) {\n      Object.keys(obj).forEach(function (key) {\n        var val = obj[key];\n        if (val !== undefined) {\n          clone[key] = val;\n        }\n      });\n    }\n  });\n  return clone;\n}\nfunction useNotification() {\n  var rootConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _rootConfig$getContai = rootConfig.getContainer,\n    getContainer = _rootConfig$getContai === void 0 ? defaultGetContainer : _rootConfig$getContai,\n    motion = rootConfig.motion,\n    prefixCls = rootConfig.prefixCls,\n    maxCount = rootConfig.maxCount,\n    className = rootConfig.className,\n    style = rootConfig.style,\n    onAllRemoved = rootConfig.onAllRemoved,\n    stack = rootConfig.stack,\n    renderNotifications = rootConfig.renderNotifications,\n    shareConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rootConfig, _excluded);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 2),\n    container = _React$useState2[0],\n    setContainer = _React$useState2[1];\n  var notificationsRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef();\n  var contextHolder = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_Notifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n    container: container,\n    ref: notificationsRef,\n    prefixCls: prefixCls,\n    motion: motion,\n    maxCount: maxCount,\n    className: className,\n    style: style,\n    onAllRemoved: onAllRemoved,\n    stack: stack,\n    renderNotifications: renderNotifications\n  });\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState([]),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState3, 2),\n    taskQueue = _React$useState4[0],\n    setTaskQueue = _React$useState4[1];\n\n  // ========================= Refs =========================\n  var api = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function () {\n    return {\n      open: function open(config) {\n        var mergedConfig = mergeConfig(shareConfig, config);\n        if (mergedConfig.key === null || mergedConfig.key === undefined) {\n          mergedConfig.key = \"rc-notification-\".concat(uniqueKey);\n          uniqueKey += 1;\n        }\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'open',\n            config: mergedConfig\n          }]);\n        });\n      },\n      close: function close(key) {\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'close',\n            key: key\n          }]);\n        });\n      },\n      destroy: function destroy() {\n        setTaskQueue(function (queue) {\n          return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [{\n            type: 'destroy'\n          }]);\n        });\n      }\n    };\n  }, []);\n\n  // ======================= Container ======================\n  // React 18 should all in effect that we will check container in each render\n  // Which means getContainer should be stable.\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    setContainer(getContainer());\n  });\n\n  // ======================== Effect ========================\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    // Flush task when node ready\n    if (notificationsRef.current && taskQueue.length) {\n      taskQueue.forEach(function (task) {\n        switch (task.type) {\n          case 'open':\n            notificationsRef.current.open(task.config);\n            break;\n          case 'close':\n            notificationsRef.current.close(task.key);\n            break;\n          case 'destroy':\n            notificationsRef.current.destroy();\n            break;\n        }\n      });\n\n      // React 17 will mix order of effect & setState in async\n      // - open: setState[0]\n      // - effect[0]\n      // - open: setState[1]\n      // - effect setState([]) * here will clean up [0, 1] in React 17\n      setTaskQueue(function (oriQueue) {\n        return oriQueue.filter(function (task) {\n          return !taskQueue.includes(task);\n        });\n      });\n    }\n  }, [taskQueue]);\n\n  // ======================== Return ========================\n  return [api, contextHolder];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useStack.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useStack.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\nvar DEFAULT_OFFSET = 8;\nvar DEFAULT_THRESHOLD = 3;\nvar DEFAULT_GAP = 16;\nvar useStack = function useStack(config) {\n  var result = {\n    offset: DEFAULT_OFFSET,\n    threshold: DEFAULT_THRESHOLD,\n    gap: DEFAULT_GAP\n  };\n  if (config && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(config) === 'object') {\n    var _config$offset, _config$threshold, _config$gap;\n    result.offset = (_config$offset = config.offset) !== null && _config$offset !== void 0 ? _config$offset : DEFAULT_OFFSET;\n    result.threshold = (_config$threshold = config.threshold) !== null && _config$threshold !== void 0 ? _config$threshold : DEFAULT_THRESHOLD;\n    result.gap = (_config$gap = config.gap) !== null && _config$gap !== void 0 ? _config$gap : DEFAULT_GAP;\n  }\n  return [!!config, result];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useStack);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2hvb2tzL3VzZVN0YWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2RUFBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2hvb2tzL3VzZVN0YWNrLmpzPzc0ZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF90eXBlb2YgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3R5cGVvZlwiO1xudmFyIERFRkFVTFRfT0ZGU0VUID0gODtcbnZhciBERUZBVUxUX1RIUkVTSE9MRCA9IDM7XG52YXIgREVGQVVMVF9HQVAgPSAxNjtcbnZhciB1c2VTdGFjayA9IGZ1bmN0aW9uIHVzZVN0YWNrKGNvbmZpZykge1xuICB2YXIgcmVzdWx0ID0ge1xuICAgIG9mZnNldDogREVGQVVMVF9PRkZTRVQsXG4gICAgdGhyZXNob2xkOiBERUZBVUxUX1RIUkVTSE9MRCxcbiAgICBnYXA6IERFRkFVTFRfR0FQXG4gIH07XG4gIGlmIChjb25maWcgJiYgX3R5cGVvZihjb25maWcpID09PSAnb2JqZWN0Jykge1xuICAgIHZhciBfY29uZmlnJG9mZnNldCwgX2NvbmZpZyR0aHJlc2hvbGQsIF9jb25maWckZ2FwO1xuICAgIHJlc3VsdC5vZmZzZXQgPSAoX2NvbmZpZyRvZmZzZXQgPSBjb25maWcub2Zmc2V0KSAhPT0gbnVsbCAmJiBfY29uZmlnJG9mZnNldCAhPT0gdm9pZCAwID8gX2NvbmZpZyRvZmZzZXQgOiBERUZBVUxUX09GRlNFVDtcbiAgICByZXN1bHQudGhyZXNob2xkID0gKF9jb25maWckdGhyZXNob2xkID0gY29uZmlnLnRocmVzaG9sZCkgIT09IG51bGwgJiYgX2NvbmZpZyR0aHJlc2hvbGQgIT09IHZvaWQgMCA/IF9jb25maWckdGhyZXNob2xkIDogREVGQVVMVF9USFJFU0hPTEQ7XG4gICAgcmVzdWx0LmdhcCA9IChfY29uZmlnJGdhcCA9IGNvbmZpZy5nYXApICE9PSBudWxsICYmIF9jb25maWckZ2FwICE9PSB2b2lkIDAgPyBfY29uZmlnJGdhcCA6IERFRkFVTFRfR0FQO1xuICB9XG4gIHJldHVybiBbISFjb25maWcsIHJlc3VsdF07XG59O1xuZXhwb3J0IGRlZmF1bHQgdXNlU3RhY2s7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-notification/es/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notice: () => (/* reexport safe */ _Notice__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NotificationProvider: () => (/* reexport safe */ _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   useNotification: () => (/* reexport safe */ _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/useNotification */ \"(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzRDtBQUN4QjtBQUM0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy1ub3RpZmljYXRpb24vZXMvaW5kZXguanM/NjliZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdXNlTm90aWZpY2F0aW9uIGZyb20gXCIuL2hvb2tzL3VzZU5vdGlmaWNhdGlvblwiO1xuaW1wb3J0IE5vdGljZSBmcm9tIFwiLi9Ob3RpY2VcIjtcbmltcG9ydCBOb3RpZmljYXRpb25Qcm92aWRlciBmcm9tIFwiLi9Ob3RpZmljYXRpb25Qcm92aWRlclwiO1xuZXhwb3J0IHsgdXNlTm90aWZpY2F0aW9uLCBOb3RpY2UsIE5vdGlmaWNhdGlvblByb3ZpZGVyIH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/index.js\n");

/***/ })

};
;