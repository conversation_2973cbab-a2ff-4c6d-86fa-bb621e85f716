import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Repository } from 'typeorm';
import { Tag } from './entities/tag.entity';
export declare class TagService {
    private tagRepository;
    constructor(tagRepository: Repository<Tag>);
    create(createTagDto: CreateTagDto): Promise<Tag>;
    findAll(): Promise<Tag[]>;
    findOne(id: number): Promise<Tag>;
    update(id: number, updateTagDto: UpdateTagDto): Promise<Tag>;
    remove(id: number): Promise<void>;
    hardRemove(id: number): Promise<void>;
    list(params: {
        page?: number;
        size?: number;
        keyword?: string;
    }): Promise<{
        list: Tag[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    getById(id: number): Promise<Tag>;
}
