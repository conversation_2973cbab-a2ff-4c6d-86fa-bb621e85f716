/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./components/slide-captcha.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
.slide-captcha_captchaContainer__z_79Y {
    padding: 20px;
}

.slide-captcha_sliderContainer__nYG91 {
    position: relative;
    height: 40px;
    background: #f5f5f5;
    border-radius: 20px;
    margin: 20px 0;
    user-select: none;
}

.slide-captcha_sliderTrack__oV4e0 {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
}

.slide-captcha_sliderProgress__VPUoY {
    position: absolute;
    height: 100%;
    background: linear-gradient(90deg, #4766C2 0%, #5C7CE0 100%);
    transition: width 0.1s;
    border-radius: 20px;
}

.slide-captcha_sliderProgress__VPUoY.slide-captcha_dragging__kForP {
    transition: none;
}

.slide-captcha_slider__3Icb2 {
    position: absolute;
    top: 50%;
    left: 0;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: box-shadow 0.3s;
    touch-action: none;
    will-change: transform;
    transform: translate3d(0, -50%, 0);
}

.slide-captcha_slider__3Icb2:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.slide-captcha_sliderIcon__wCZHq {
    color: #4766C2;
    font-size: 20px;
}

.slide-captcha_sliderText__xEcZF {
    position: absolute;
    width: 100%;
    text-align: center;
    line-height: 40px;
    color: #999;
    user-select: none;
    font-size: 14px;
    pointer-events: none;
} 
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./components/form-transition.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/* 表单过渡容器样式 */
.form-transition-container {
  position: relative;
  width: 100%;
  max-height: 1000px;
}

/* 表单步骤基本样式 */
.form-step {
  position: absolute;
  width: 100%;
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); /* 使用更平滑的缓动函数 */
}

/* 初始渲染时禁用动画 */
.form-step.no-animation {
  transition: none !important;
  animation: none !important;
}

/* 当前活动表单步骤 */
.form-step.active {
  transform: translateX(0);
  opacity: 1;
  z-index: 10;
  visibility: visible;
}

/* 前一个表单步骤 - 滑向左侧 */
.form-step.prev {
  transform: translateX(-100%);
  opacity: 0;
  z-index: 5;
  visibility: hidden;
}

/* 下一个表单步骤 - 从右侧滑入 */
.form-step.next {
  transform: translateX(100%);
  opacity: 0;
  z-index: 5;
  visibility: hidden;
}

/* 水平滑动效果增强 */
.form-step.active:not(.no-animation) {
  animation: slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 从右侧滑入的特殊效果 */
.role-selection-enter {
  transform: translateX(100%);
  opacity: 0;
}

.role-selection-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.role-selection-exit {
  transform: translateX(0);
  opacity: 1;
}

.role-selection-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
} 
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/(main)/home/<USER>/announcement-pop.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 { font-size: 2em; }
.markdown-body h2 { font-size: 1.5em; }
.markdown-body h3 { font-size: 1.25em; }

.markdown-body p {
  margin-bottom: 16px;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0 0 16px;
}

.markdown-body pre {
  margin-bottom: 16px;
  overflow: auto;
}

.markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 16px;
  width: 100%;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
} 
