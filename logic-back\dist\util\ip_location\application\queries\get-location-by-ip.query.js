"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetLocationByIpQuery = void 0;
const ip_address_vo_1 = require("../../domain/value-objects/ip-address.vo");
class GetLocationByIpQuery {
    ipAddress;
    includeRisk;
    cacheEnabled;
    timestamp;
    constructor(ipAddress, includeRisk = false, cacheEnabled = true) {
        this.ipAddress = ipAddress;
        this.includeRisk = includeRisk;
        this.cacheEnabled = cacheEnabled;
        this.timestamp = new Date();
    }
    static create(ipAddressString, includeRisk = false, cacheEnabled = true) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new GetLocationByIpQuery(ipAddress, includeRisk, cacheEnabled);
    }
    static createWithRisk(ipAddressString, cacheEnabled = true) {
        return GetLocationByIpQuery.create(ipAddressString, true, cacheEnabled);
    }
    static createWithoutCache(ipAddressString, includeRisk = false) {
        return GetLocationByIpQuery.create(ipAddressString, includeRisk, false);
    }
    validate() {
        const errors = [];
        if (!this.ipAddress.canGeolocate) {
            errors.push('IP地址不支持地理位置解析');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    getCacheKey() {
        const riskSuffix = this.includeRisk ? '_with_risk' : '';
        return `ip_location:${this.ipAddress.value}${riskSuffix}`;
    }
    getSummary() {
        const riskInfo = this.includeRisk ? '(包含风险评估)' : '';
        const cacheInfo = this.cacheEnabled ? '' : '(不使用缓存)';
        return `查询IP位置: ${this.ipAddress.masked} ${riskInfo}${cacheInfo}`;
    }
    get needsRiskAssessment() {
        return this.includeRisk && this.ipAddress.isPublic;
    }
    get canUseCache() {
        return this.cacheEnabled && this.ipAddress.isPublic;
    }
    toJSON() {
        return {
            ipAddress: this.ipAddress.toJSON(),
            includeRisk: this.includeRisk,
            cacheEnabled: this.cacheEnabled,
            timestamp: this.timestamp.toISOString(),
            cacheKey: this.getCacheKey(),
            needsRiskAssessment: this.needsRiskAssessment,
            canUseCache: this.canUseCache
        };
    }
}
exports.GetLocationByIpQuery = GetLocationByIpQuery;
//# sourceMappingURL=get-location-by-ip.query.js.map