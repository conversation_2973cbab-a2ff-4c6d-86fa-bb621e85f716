import { PaymentChannel } from '../../util/database/mysql/payment_refund/dto/create-payment-refund.dto';
export declare class RefundRequestDto {
    businessOrderId: string;
    amount: number;
    reason?: string;
    description?: string;
    notifyUrl?: string;
    operatorId?: string;
    extraData?: Record<string, any>;
}
export declare class RefundQueryDto {
    refundNo: string;
    channel?: PaymentChannel;
}
export declare class RefundResultDto {
    refundNo: string;
    businessOrderId: string;
    amount: number;
    status: string;
    createTime: Date;
    finishTime?: Date;
    channelRefundNo?: string;
    failReason?: string;
    notifyUrl?: string;
}
