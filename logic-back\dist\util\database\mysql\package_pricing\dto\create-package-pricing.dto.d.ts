export declare enum PriceType {
    STANDARD = "standard",
    VIP = "vip",
    PROMOTION = "promotion"
}
export declare class CreatePackagePricingDto {
    packageId: number;
    originalPrice: number;
    currentPrice: number;
    discountRate?: number;
    currency?: string;
    priceType?: PriceType;
    startTime?: string;
    endTime?: string;
    status?: number;
    priority?: number;
    description?: string;
}
