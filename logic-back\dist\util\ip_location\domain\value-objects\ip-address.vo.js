"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpAddress = void 0;
const invalid_ip_exception_1 = require("../exceptions/invalid-ip.exception");
class IpAddress {
    _value;
    _type;
    _isPrivate;
    _isLoopback;
    constructor(value) {
        const cleanValue = this.cleanIpAddress(value);
        if (!this.isValidIpAddress(cleanValue)) {
            throw new invalid_ip_exception_1.InvalidIpException(`无效的IP地址格式: ${value}`);
        }
        this._value = cleanValue;
        this._type = this.determineIpType(cleanValue);
        this._isPrivate = this.isPrivateIp(cleanValue);
        this._isLoopback = this.isLoopbackIp(cleanValue);
    }
    get value() {
        return this._value;
    }
    get type() {
        return this._type;
    }
    get isPrivate() {
        return this._isPrivate;
    }
    get isLoopback() {
        return this._isLoopback;
    }
    get isPublic() {
        return !this._isPrivate && !this._isLoopback;
    }
    get canGeolocate() {
        return this.isPublic;
    }
    get masked() {
        if (this._type === 'IPv6') {
            const parts = this._value.split(':');
            if (parts.length >= 4) {
                return `${parts.slice(0, 4).join(':')}:****`;
            }
            return '****:****:****:****';
        }
        else {
            const parts = this._value.split('.');
            if (parts.length === 4) {
                return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
            }
            return '***.***.***.**';
        }
    }
    cleanIpAddress(ip) {
        if (!ip)
            return '';
        let cleanIp = ip.trim();
        cleanIp = cleanIp.replace('::ffff:', '');
        if (cleanIp === '::1') {
            return cleanIp;
        }
        return cleanIp;
    }
    isValidIpAddress(ip) {
        if (!ip)
            return false;
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    determineIpType(ip) {
        return ip.includes(':') ? 'IPv6' : 'IPv4';
    }
    isPrivateIp(ip) {
        if (this._type === 'IPv6') {
            return ip.startsWith('fc') || ip.startsWith('fd') || ip.startsWith('fe80');
        }
        else {
            const parts = ip.split('.').map(Number);
            if (parts.length !== 4)
                return false;
            if (parts[0] === 10)
                return true;
            if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31)
                return true;
            if (parts[0] === 192 && parts[1] === 168)
                return true;
            return false;
        }
    }
    isLoopbackIp(ip) {
        if (this._type === 'IPv6') {
            return ip === '::1';
        }
        else {
            return ip.startsWith('127.');
        }
    }
    static create(value) {
        return new IpAddress(value);
    }
    static tryCreate(value) {
        try {
            return new IpAddress(value);
        }
        catch {
            return null;
        }
    }
    equals(other) {
        return this._value === other._value;
    }
    toString() {
        return this._value;
    }
    toJSON() {
        return {
            value: this._value,
            type: this._type,
            isPrivate: this._isPrivate,
            isLoopback: this._isLoopback,
            isPublic: this.isPublic,
            canGeolocate: this.canGeolocate,
            masked: this.masked
        };
    }
}
exports.IpAddress = IpAddress;
//# sourceMappingURL=ip-address.vo.js.map