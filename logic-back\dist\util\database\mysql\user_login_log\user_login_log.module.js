"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLoginLogModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_login_log_entity_1 = require("./entities/user_login_log.entity");
const user_login_log_service_1 = require("./user_login_log.service");
const user_login_log_controller_1 = require("./user_login_log.controller");
let UserLoginLogModule = class UserLoginLogModule {
};
exports.UserLoginLogModule = UserLoginLogModule;
exports.UserLoginLogModule = UserLoginLogModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_login_log_entity_1.UserLoginLog])
        ],
        controllers: [user_login_log_controller_1.UserLoginLogController],
        providers: [user_login_log_service_1.UserLoginLogService],
        exports: [user_login_log_service_1.UserLoginLogService]
    })
], UserLoginLogModule);
//# sourceMappingURL=user_login_log.module.js.map