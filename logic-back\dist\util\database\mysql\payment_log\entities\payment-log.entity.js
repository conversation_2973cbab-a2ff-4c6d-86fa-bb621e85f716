"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentLog = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let PaymentLog = class PaymentLog {
    id;
    logType;
    orderNo;
    refundNo;
    paymentChannel;
    operation;
    operatorId;
    clientIp;
    requestData;
    responseData;
    status;
    errorMessage;
    executionTime;
    createdAt;
};
exports.PaymentLog = PaymentLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '日志类型', length: 32 }),
    (0, swagger_1.ApiProperty)({ description: '日志类型' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "logType", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '订单编号', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '订单编号' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "orderNo", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '退款编号', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '退款编号' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "refundNo", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '支付渠道', length: 32, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '支付渠道' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "paymentChannel", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '操作类型', length: 64 }),
    (0, swagger_1.ApiProperty)({ description: '操作类型' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "operation", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '操作人ID', length: 36, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '操作人ID' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '客户端IP', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '客户端IP' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "clientIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '请求数据', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '请求数据' }),
    __metadata("design:type", Object)
], PaymentLog.prototype, "requestData", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '响应数据', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '响应数据' }),
    __metadata("design:type", Object)
], PaymentLog.prototype, "responseData", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '状态', length: 16, default: 'success' }),
    (0, swagger_1.ApiProperty)({ description: '状态(success/fail)' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '错误信息', length: 1024, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '错误信息' }),
    __metadata("design:type", String)
], PaymentLog.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '执行时间(ms)', type: 'int', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '执行时间(ms)' }),
    __metadata("design:type", Number)
], PaymentLog.prototype, "executionTime", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PaymentLog.prototype, "createdAt", void 0);
exports.PaymentLog = PaymentLog = __decorate([
    (0, typeorm_1.Entity)('payment_log')
], PaymentLog);
//# sourceMappingURL=payment-log.entity.js.map