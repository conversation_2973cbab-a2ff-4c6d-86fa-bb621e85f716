"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskCheckRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class RiskCheckRequestDto {
    userId;
    ipAddress;
    userAgent;
    sessionId;
}
exports.RiskCheckRequestDto = RiskCheckRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', example: 12345 }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '用户ID必须是数字' }),
    __metadata("design:type", Number)
], RiskCheckRequestDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'IP地址', example: '**************' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'IP地址不能为空' }),
    (0, class_validator_1.IsString)({ message: 'IP地址必须是字符串' }),
    __metadata("design:type", String)
], RiskCheckRequestDto.prototype, "ipAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户代理信息',
        required: false,
        example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户代理信息必须是字符串' }),
    __metadata("design:type", String)
], RiskCheckRequestDto.prototype, "userAgent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '会话ID',
        required: false,
        example: 'sess_123456'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '会话ID必须是字符串' }),
    __metadata("design:type", String)
], RiskCheckRequestDto.prototype, "sessionId", void 0);
//# sourceMappingURL=risk-check.request.dto.js.map