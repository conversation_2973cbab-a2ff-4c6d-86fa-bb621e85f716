{"version": 3, "file": "risk-score.vo.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/domain/value-objects/risk-score.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,SAAS;IACH,MAAM,CAAS;IACf,MAAM,CAA4B;IAClC,QAAQ,CAAW;IACnB,OAAO,CAAS;IAEjC,YAAY,KAAa,EAAE,UAAoB,EAAE;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAKD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAKD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAKD,IAAI,OAAO;QACT,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAKD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAKD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/B,CAAC;IAKD,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAKD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC;IAChC,CAAC;IAKD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC7D,CAAC;IAKD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAKD,IAAI,SAAS;QACX,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,KAAK,CAAC,CAAC,OAAO,SAAS,CAAC;YAC7B,KAAK,QAAQ,CAAC,CAAC,OAAO,SAAS,CAAC;YAChC,KAAK,MAAM,CAAC,CAAC,OAAO,SAAS,CAAC;YAC9B,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC5B,CAAC;IACH,CAAC;IAKD,IAAI,gBAAgB;QAClB,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC;YACzB,KAAK,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC;YAC7B,KAAK,MAAM,CAAC,CAAC,OAAO,KAAK,CAAC;YAC1B,OAAO,CAAC,CAAC,OAAO,MAAM,CAAC;QACzB,CAAC;IACH,CAAC;IAKD,IAAI,8BAA8B;QAChC,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAKO,kBAAkB;QACxB,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACxE,CAAC;IAKO,kBAAkB,CAAC,KAAa;QACtC,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YAChB,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YACvB,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,KAAgC,EAAE,OAAiB;QAC5E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAE9B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,MAAM;gBACT,OAAO,OAAO,UAAU,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,OAAO,OAAO,UAAU,EAAE,CAAC;YAC7B,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,OAAO,UAAU,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAKD,SAAS,CAAC,MAAc;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,YAAY,CAAC,MAAc;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;QAC3D,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAChD,CAAC;IAKD,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAKD,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAKD,MAAM,CAAC,MAAM,CAAC,KAAa,EAAE,UAAoB,EAAE;QACjD,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAKD,MAAM,CAAC,UAAU;QACf,OAAO,IAAI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9B,CAAC;IAKD,MAAM,CAAC,SAAS,CAAC,UAAoB,EAAE;QACrC,OAAO,IAAI,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,UAAoB,EAAE;QACxC,OAAO,IAAI,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;IAKD,MAAM,CAAC,UAAU,CAAC,UAAoB,EAAE;QACtC,OAAO,IAAI,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;IAKD,MAAM,CAAC,WAAW,CAAC,OAAiB;QAClC,IAAI,KAAK,GAAG,CAAC,CAAC;QAGd,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,KAAK,IAAI,EAAE,CAAC;oBACZ,MAAM;gBACR,KAAK,MAAM;oBACT,KAAK,IAAI,EAAE,CAAC;oBACZ,MAAM;gBACR,KAAK,QAAQ;oBACX,KAAK,IAAI,EAAE,CAAC;oBACZ,MAAM;gBACR,KAAK,OAAO;oBACV,KAAK,IAAI,CAAC,CAAC;oBACX,MAAM;gBACR,KAAK,SAAS;oBACZ,KAAK,IAAI,EAAE,CAAC;oBACZ,MAAM;gBACR,KAAK,QAAQ;oBACX,KAAK,IAAI,EAAE,CAAC;oBACZ,MAAM;gBACR,KAAK,OAAO;oBACV,KAAK,IAAI,EAAE,CAAC;oBACZ,MAAM;gBACR;oBACE,KAAK,IAAI,CAAC,CAAC;YACf,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAKD,MAAM,CAAC,KAAgB;QACrB,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACxF,CAAC;IAKD,QAAQ;QACN,OAAO,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACtE,CAAC;IAKD,MAAM;QACJ,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,8BAA8B,EAAE,IAAI,CAAC,8BAA8B;SACpE,CAAC;IACJ,CAAC;CACF;AA1SD,8BA0SC"}