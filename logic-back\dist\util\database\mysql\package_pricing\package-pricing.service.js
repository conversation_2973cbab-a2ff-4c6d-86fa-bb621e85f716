"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePricingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const package_pricing_entity_1 = require("./entities/package-pricing.entity");
const dto_1 = require("./dto");
let PackagePricingService = class PackagePricingService {
    packagePricingRepository;
    constructor(packagePricingRepository) {
        this.packagePricingRepository = packagePricingRepository;
    }
    async create(createPackagePricingDto) {
        const pricing = this.packagePricingRepository.create({
            ...createPackagePricingDto,
            currency: createPackagePricingDto.currency || 'CNY',
            priceType: createPackagePricingDto.priceType || dto_1.PriceType.STANDARD,
            status: createPackagePricingDto.status ?? 1,
            priority: createPackagePricingDto.priority || 0,
            startTime: createPackagePricingDto.startTime ? new Date(createPackagePricingDto.startTime) : undefined,
            endTime: createPackagePricingDto.endTime ? new Date(createPackagePricingDto.endTime) : undefined,
        });
        return await this.packagePricingRepository.save(pricing);
    }
    async findAll() {
        return await this.packagePricingRepository.find({
            relations: ['packageInfo'],
            order: { packageId: 'ASC', priority: 'DESC', createTime: 'DESC' }
        });
    }
    async findOne(id) {
        const pricing = await this.packagePricingRepository.findOne({
            where: { id },
            relations: ['packageInfo']
        });
        if (!pricing) {
            throw new common_1.NotFoundException(`价格ID为${id}的记录不存在`);
        }
        return pricing;
    }
    async getCurrentPricing(packageId, priceType = dto_1.PriceType.STANDARD) {
        const now = new Date();
        const pricing = await this.packagePricingRepository.findOne({
            where: [
                {
                    packageId,
                    priceType,
                    status: 1,
                    startTime: (0, typeorm_2.LessThanOrEqual)(now),
                    endTime: (0, typeorm_2.MoreThanOrEqual)(now),
                },
                {
                    packageId,
                    priceType,
                    status: 1,
                    startTime: (0, typeorm_2.IsNull)(),
                    endTime: (0, typeorm_2.IsNull)(),
                },
                {
                    packageId,
                    priceType,
                    status: 1,
                    startTime: (0, typeorm_2.LessThanOrEqual)(now),
                    endTime: (0, typeorm_2.IsNull)(),
                }
            ],
            order: { priority: 'DESC', createTime: 'DESC' },
            relations: ['packageInfo']
        });
        return pricing;
    }
    async getPackagePricings(packageId) {
        return await this.packagePricingRepository.find({
            where: { packageId, status: 1 },
            order: { priority: 'DESC', createTime: 'DESC' },
            relations: ['packageInfo']
        });
    }
    async update(id, updatePackagePricingDto) {
        const pricing = await this.findOne(id);
        const updateData = {
            ...updatePackagePricingDto,
            startTime: updatePackagePricingDto.startTime ? new Date(updatePackagePricingDto.startTime) : pricing.startTime,
            endTime: updatePackagePricingDto.endTime ? new Date(updatePackagePricingDto.endTime) : pricing.endTime,
        };
        Object.assign(pricing, updateData);
        return await this.packagePricingRepository.save(pricing);
    }
    async remove(id) {
        const pricing = await this.findOne(id);
        await this.packagePricingRepository.remove(pricing);
    }
    async getAllCurrentPricings() {
        const pricings = await this.packagePricingRepository.find({
            where: { status: 1 },
            relations: ['packageInfo'],
            order: { packageId: 'ASC', priority: 'DESC' }
        });
        const packagePricingMap = new Map();
        for (const pricing of pricings) {
            if (!pricing.packageInfo || pricing.packageInfo.status !== 1) {
                continue;
            }
            const now = new Date();
            const isTimeValid = (!pricing.startTime || pricing.startTime <= now) &&
                (!pricing.endTime || pricing.endTime >= now);
            if (!isTimeValid) {
                continue;
            }
            if (!packagePricingMap.has(pricing.packageId)) {
                packagePricingMap.set(pricing.packageId, pricing);
            }
        }
        return Array.from(packagePricingMap.values()).map(pricing => {
            const savings = Number((pricing.originalPrice - pricing.currentPrice).toFixed(2));
            return {
                packageId: pricing.packageId,
                packageName: pricing.packageInfo.name,
                packageDescription: pricing.packageInfo.description || '',
                points: pricing.packageInfo.points,
                validityDays: pricing.packageInfo.validityDays,
                originalPrice: pricing.originalPrice,
                currentPrice: pricing.currentPrice,
                discountRate: pricing.discountRate || Number((pricing.currentPrice / pricing.originalPrice).toFixed(2)),
                savings,
                priceType: pricing.priceType,
                currency: pricing.currency,
                promotion: pricing.packageInfo.promotion,
            };
        });
    }
};
exports.PackagePricingService = PackagePricingService;
exports.PackagePricingService = PackagePricingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(package_pricing_entity_1.PackagePricing)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PackagePricingService);
//# sourceMappingURL=package-pricing.service.js.map