"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginLoggerInitializerService = void 0;
const common_1 = require("@nestjs/common");
const user_login_log_service_1 = require("../../util/database/mysql/user_login_log/user_login_log.service");
const login_logger_util_1 = require("./login-logger.util");
let LoginLoggerInitializerService = class LoginLoggerInitializerService {
    userLoginLogService;
    constructor(userLoginLogService) {
        this.userLoginLogService = userLoginLogService;
    }
    onModuleInit() {
        console.log('🚀 开始初始化 LoginLoggerUtil...');
        console.log('🔍 服务实例信息:', {
            hasService: !!this.userLoginLogService,
            serviceType: this.userLoginLogService?.constructor?.name
        });
        login_logger_util_1.LoginLoggerUtil.setService(this.userLoginLogService);
        console.log('✅ LoginLoggerUtil 初始化完成');
    }
};
exports.LoginLoggerInitializerService = LoginLoggerInitializerService;
exports.LoginLoggerInitializerService = LoginLoggerInitializerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [user_login_log_service_1.UserLoginLogService])
], LoginLoggerInitializerService);
//# sourceMappingURL=login-logger-initializer.service.js.map