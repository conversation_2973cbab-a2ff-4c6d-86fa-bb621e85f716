import { Repository } from 'typeorm';
import { PaymentLog } from './entities/payment-log.entity';
import { CreatePaymentLogDto, QueryPaymentLogDto } from './dto/payment-log.dto';
export declare class PaymentLogService {
    private readonly paymentLogRepository;
    private readonly logger;
    constructor(paymentLogRepository: Repository<PaymentLog>);
    create(createDto: CreatePaymentLogDto): Promise<PaymentLog>;
    findById(id: string): Promise<PaymentLog | null>;
    findAll(queryDto: QueryPaymentLogDto, page?: number, limit?: number): Promise<[PaymentLog[], number]>;
    findByOrderNo(orderNo: string): Promise<PaymentLog[]>;
    findByRefundNo(refundNo: string): Promise<PaymentLog[]>;
    cleanupOldLogs(days?: number): Promise<number>;
}
