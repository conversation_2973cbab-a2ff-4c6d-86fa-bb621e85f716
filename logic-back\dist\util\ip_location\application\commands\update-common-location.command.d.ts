import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
export declare class UpdateCommonLocationCommand {
    readonly userId: number;
    readonly ipAddress: IpAddress;
    readonly location: GeographicLocation;
    readonly sessionId?: string;
    readonly userAgent?: string;
    readonly timestamp: Date;
    constructor(userId: number, ipAddress: IpAddress, location: GeographicLocation, sessionId?: string, userAgent?: string);
    static create(userId: number, ipAddressString: string, location: GeographicLocation, sessionId?: string, userAgent?: string): UpdateCommonLocationCommand;
    validate(): {
        isValid: boolean;
        errors: string[];
    };
    getSummary(): string;
    toJSON(): object;
}
