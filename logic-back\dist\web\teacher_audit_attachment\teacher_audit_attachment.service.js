"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherAuditAttachmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const teacher_audit_attachment_entity_1 = require("./entities/teacher_audit_attachment.entity");
const attachment_entity_1 = require("../attachment/entities/attachment.entity");
let TeacherAuditAttachmentService = class TeacherAuditAttachmentService {
    teacherAuditAttachmentRepository;
    attachmentRepository;
    constructor(teacherAuditAttachmentRepository, attachmentRepository) {
        this.teacherAuditAttachmentRepository = teacherAuditAttachmentRepository;
        this.attachmentRepository = attachmentRepository;
    }
    async create(createTeacherAuditAttachmentDto) {
        const teacherAuditAttachment = this.teacherAuditAttachmentRepository.create(createTeacherAuditAttachmentDto);
        return this.teacherAuditAttachmentRepository.save(teacherAuditAttachment);
    }
    async createMany(dtos) {
        const teacherAuditAttachments = dtos.map(dto => this.teacherAuditAttachmentRepository.create(dto));
        return this.teacherAuditAttachmentRepository.save(teacherAuditAttachments);
    }
    async findAttachmentsByAuditId(teacherAuditId) {
        const relations = await this.teacherAuditAttachmentRepository.find({
            where: { teacher_audit_id: teacherAuditId },
            relations: ['attachment'],
        });
        return relations.map(relation => relation.attachment);
    }
    async findAll() {
        return this.teacherAuditAttachmentRepository.find({
            relations: ['teacherAudit', 'attachment'],
        });
    }
    async findByTeacherAuditId(teacherAuditId) {
        return this.teacherAuditAttachmentRepository.find({
            where: { teacher_audit_id: teacherAuditId },
            relations: ['attachment'],
        });
    }
    async remove(id) {
        await this.teacherAuditAttachmentRepository.delete(id);
    }
    async removeByTeacherAuditId(teacherAuditId) {
        await this.teacherAuditAttachmentRepository.delete({ teacher_audit_id: teacherAuditId });
    }
};
exports.TeacherAuditAttachmentService = TeacherAuditAttachmentService;
exports.TeacherAuditAttachmentService = TeacherAuditAttachmentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(teacher_audit_attachment_entity_1.TeacherAuditAttachment)),
    __param(1, (0, typeorm_1.InjectRepository)(attachment_entity_1.Attachment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], TeacherAuditAttachmentService);
//# sourceMappingURL=teacher_audit_attachment.service.js.map