"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundResultDto = exports.RefundQueryDto = exports.RefundRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const create_payment_refund_dto_1 = require("../../util/database/mysql/payment_refund/dto/create-payment-refund.dto");
class RefundRequestDto {
    businessOrderId;
    amount;
    reason;
    description;
    notifyUrl;
    operatorId;
    extraData;
}
exports.RefundRequestDto = RefundRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户订单号', example: 'P202307250001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '商户订单号不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RefundRequestDto.prototype, "businessOrderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额(元)', example: 99.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '退款金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '退款金额必须为数字' }),
    (0, class_validator_1.Min)(0.01, { message: '退款金额必须大于0.01元' }),
    __metadata("design:type", Number)
], RefundRequestDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因', required: false, example: '客户申请退款' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RefundRequestDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款说明', required: false, example: '商品质量问题' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RefundRequestDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款通知回调URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: '退款通知回调URL格式不正确' }),
    __metadata("design:type", String)
], RefundRequestDto.prototype, "notifyUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作人ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RefundRequestDto.prototype, "operatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '扩展参数', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], RefundRequestDto.prototype, "extraData", void 0);
class RefundQueryDto {
    refundNo;
    channel;
}
exports.RefundQueryDto = RefundQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款单号', example: 'R202307250001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '退款单号不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RefundQueryDto.prototype, "refundNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', enum: create_payment_refund_dto_1.PaymentChannel, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(create_payment_refund_dto_1.PaymentChannel, { message: '无效的支付渠道' }),
    __metadata("design:type", String)
], RefundQueryDto.prototype, "channel", void 0);
class RefundResultDto {
    refundNo;
    businessOrderId;
    amount;
    status;
    createTime;
    finishTime;
    channelRefundNo;
    failReason;
    notifyUrl;
}
exports.RefundResultDto = RefundResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款单号' }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "refundNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商户订单号' }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "businessOrderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额(元)' }),
    __metadata("design:type", Number)
], RefundResultDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款状态' }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款创建时间' }),
    __metadata("design:type", Date)
], RefundResultDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款完成时间', required: false }),
    __metadata("design:type", Date)
], RefundResultDto.prototype, "finishTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道退款单号', required: false }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "channelRefundNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款失败原因', required: false }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "failReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款通知回调URL', required: false }),
    __metadata("design:type", String)
], RefundResultDto.prototype, "notifyUrl", void 0);
//# sourceMappingURL=refund-dto.js.map