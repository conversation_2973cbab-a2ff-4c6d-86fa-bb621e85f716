"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityEventsTaskService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_events_task_entity_1 = require("./entities/activity_events_task.entity");
let ActivityEventsTaskService = class ActivityEventsTaskService {
    activityEventsTaskRepository;
    constructor(activityEventsTaskRepository) {
        this.activityEventsTaskRepository = activityEventsTaskRepository;
    }
    async create(createActivityEventsTaskDto) {
        if (new Date(createActivityEventsTaskDto.endTime) <= new Date(createActivityEventsTaskDto.startTime)) {
            throw new common_1.BadRequestException('结束时间必须晚于开始时间');
        }
        const activityEventsTask = this.activityEventsTaskRepository.create(createActivityEventsTaskDto);
        return this.activityEventsTaskRepository.save(activityEventsTask);
    }
    async findAll() {
        return this.activityEventsTaskRepository.find({
            where: { isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findOne(id) {
        const activityEventsTask = await this.activityEventsTaskRepository.findOne({
            where: { id, isDelete: false },
        });
        if (!activityEventsTask) {
            throw new common_1.NotFoundException(`赛事任务ID ${id} 未找到`);
        }
        return activityEventsTask;
    }
    async findByStatus(status) {
        return this.activityEventsTaskRepository.find({
            where: { status, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByUser(userId) {
        return this.activityEventsTaskRepository.find({
            where: { userId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByCreator(creatorId) {
        return this.activityEventsTaskRepository.find({
            where: { creatorId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findBySchool(schoolName) {
        return this.activityEventsTaskRepository.find({
            where: { schoolName, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByUserAndEvent(userId, eventName) {
        return this.activityEventsTaskRepository.findOne({
            where: { userId, eventName, isDelete: false },
        });
    }
    async findByUserAndActivity(userId, activityId) {
        return this.activityEventsTaskRepository.findOne({
            where: { userId, activityId, isDelete: false },
        });
    }
    async findByActivityId(activityId) {
        return this.activityEventsTaskRepository.find({
            where: { activityId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async updateStatusByUserAndEvent(userId, eventName, status) {
        const task = await this.findByUserAndEvent(userId, eventName);
        if (!task) {
            return null;
        }
        if (![0, 1, 2, 3].includes(status)) {
            throw new common_1.BadRequestException('无效的状态值');
        }
        task.status = status;
        return this.activityEventsTaskRepository.save(task);
    }
    async updateStatusByUserAndActivity(userId, activityId, status) {
        const task = await this.findByUserAndActivity(userId, activityId);
        if (!task) {
            return null;
        }
        if (![0, 1, 2, 3, 4].includes(status)) {
            throw new common_1.BadRequestException('无效的状态值');
        }
        task.status = status;
        return this.activityEventsTaskRepository.save(task);
    }
    async update(id, updateActivityEventsTaskDto) {
        const activityEventsTask = await this.findOne(id);
        if (updateActivityEventsTaskDto.startTime && updateActivityEventsTaskDto.endTime) {
            if (new Date(updateActivityEventsTaskDto.endTime) <= new Date(updateActivityEventsTaskDto.startTime)) {
                throw new common_1.BadRequestException('结束时间必须晚于开始时间');
            }
        }
        Object.assign(activityEventsTask, updateActivityEventsTaskDto);
        return this.activityEventsTaskRepository.save(activityEventsTask);
    }
    async updateStatus(id, status) {
        const activityEventsTask = await this.findOne(id);
        if (![0, 1, 2, 3, 4].includes(status)) {
            throw new common_1.BadRequestException('无效的状态值');
        }
        activityEventsTask.status = status;
        return this.activityEventsTaskRepository.save(activityEventsTask);
    }
    async remove(id) {
        const activityEventsTask = await this.findOne(id);
        activityEventsTask.isDelete = true;
        await this.activityEventsTaskRepository.save(activityEventsTask);
    }
    async getStatistics() {
        const total = await this.activityEventsTaskRepository.count({
            where: { isDelete: false }
        });
        const statusStats = await this.activityEventsTaskRepository
            .createQueryBuilder('task')
            .select('task.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .where('task.isDelete = :isDelete', { isDelete: false })
            .groupBy('task.status')
            .getRawMany();
        const schoolStats = await this.activityEventsTaskRepository
            .createQueryBuilder('task')
            .select('task.schoolName', 'schoolName')
            .addSelect('COUNT(*)', 'count')
            .where('task.isDelete = :isDelete AND task.schoolName IS NOT NULL', { isDelete: false })
            .groupBy('task.schoolName')
            .orderBy('count', 'DESC')
            .limit(10)
            .getRawMany();
        return {
            total,
            statusStats,
            schoolStats
        };
    }
};
exports.ActivityEventsTaskService = ActivityEventsTaskService;
exports.ActivityEventsTaskService = ActivityEventsTaskService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_events_task_entity_1.ActivityEventsTask)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ActivityEventsTaskService);
//# sourceMappingURL=activity_events_task.service.js.map