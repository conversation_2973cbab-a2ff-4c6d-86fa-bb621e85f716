import { PaymentLogService } from './payment-log.service';
import { PaymentLog } from './entities/payment-log.entity';
import { CreatePaymentLogDto, QueryPaymentLogDto } from './dto/payment-log.dto';
export declare class PaymentLogController {
    private readonly paymentLogService;
    private readonly logger;
    constructor(paymentLogService: PaymentLogService);
    create(createDto: CreatePaymentLogDto): Promise<{
        code: number;
        message: string;
        data: PaymentLog;
    }>;
    findAll(queryDto: QueryPaymentLogDto, page?: number, limit?: number): Promise<{
        code: number;
        message: string;
        data: {
            records: PaymentLog[];
            total: number;
            page: number;
            limit: number;
            pages: number;
        };
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        data: PaymentLog | null;
    }>;
    findByOrderNo(orderNo: string): Promise<{
        code: number;
        message: string;
        data: PaymentLog[];
    }>;
    findByRefundNo(refundNo: string): Promise<{
        code: number;
        message: string;
        data: PaymentLog[];
    }>;
    cleanupOldLogs(payload: {
        days: number;
    }): Promise<{
        code: number;
        message: string;
        data: {
            count: number;
        };
    }>;
}
