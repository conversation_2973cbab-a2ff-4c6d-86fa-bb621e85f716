import { LoggerService } from '../../../../common/logger/logger.service';
import { IpLocationCommandService } from './ip-location-command.service';
import { IpLocationQueryService } from './ip-location-query.service';
import { LoginType, LoginStatus } from '../commands/record-login-location.command';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService } from '../../domain/services/risk-assessment-domain.service';
import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
export declare class IpLocationFacadeService {
    private readonly commandService;
    private readonly queryService;
    private readonly domainService;
    private readonly riskAssessmentService;
    private readonly logger;
    constructor(commandService: IpLocationCommandService, queryService: IpLocationQueryService, domainService: IpLocationDomainService, riskAssessmentService: RiskAssessmentDomainService, logger: LoggerService);
    getLocationByIP(ip: string, includeRisk?: boolean): Promise<{
        success: boolean;
        data: import("./ip-location-query.service").LocationQueryResult | undefined;
        message: string;
        executionTime: number | undefined;
        fromCache: boolean | undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
        executionTime?: undefined;
        fromCache?: undefined;
    }>;
    assessLoginRisk(userId: number, ip: string, userAgent?: string, includeRecommendations?: boolean): Promise<{
        success: boolean;
        data: import("./ip-location-query.service").RiskAssessmentResult | undefined;
        message: string;
        executionTime: number | undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
        executionTime?: undefined;
    }>;
    getUserLocationStats(userId: number, days?: number, includeRiskAnalysis?: boolean): Promise<{
        success: boolean;
        data: import("./ip-location-query.service").UserLocationStatsResult | undefined;
        message: string;
        executionTime: number | undefined;
        fromCache: boolean | undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
        executionTime?: undefined;
        fromCache?: undefined;
    }>;
    recordLoginLocation(userId: number, ip: string, loginType?: LoginType, loginStatus?: LoginStatus, sessionId?: string, userAgent?: string): Promise<{
        success: boolean;
        data: {
            locationUpdated: boolean;
            loginRecorded: boolean;
        } | undefined;
        message: string;
        executionTime: number | undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
        executionTime?: undefined;
    }>;
    setTrustedLocation(userId: number, province: string, city: string, reason?: string, setBy?: 'USER' | 'SYSTEM' | 'ADMIN'): Promise<{
        success: boolean;
        data: import("../../domain/entities/user-common-location.entity").UserCommonLocation[] | undefined;
        message: string;
        executionTime: number | undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
        executionTime?: undefined;
    }>;
    updateUserCommonLocation(userId: number, ip: string, sessionId?: string, userAgent?: string): Promise<{
        success: boolean;
        data: import("../../domain/entities/user-common-location.entity").UserCommonLocation | undefined;
        message: string;
        executionTime: number | undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
        executionTime?: undefined;
    }>;
    testIpResolution(testIp?: string): Promise<{
        success: boolean;
        data: {
            success: boolean;
            ipAddress?: IpAddress;
            location?: GeographicLocation;
            error?: string;
            performanceMs: number;
        };
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        data: null;
        message?: undefined;
    }>;
}
