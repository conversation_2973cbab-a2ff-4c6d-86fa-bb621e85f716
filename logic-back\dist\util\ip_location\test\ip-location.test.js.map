{"version": 3, "file": "ip-location.test.js", "sourceRoot": "", "sources": ["../../../../src/util/ip_location/test/ip-location.test.ts"], "names": [], "mappings": ";;AAAA,6CAAsD;AACtD,6CAAqD;AAGrD,gEAA2D;AAC3D,wEAAmE;AACnE,6GAAuG;AACvG,gGAAoF;AACpF,sEAAkE;AAClE,0EAAsE;AAGtE,MAAM,gBAAgB,GAAG;IACvB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;IACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;CACf,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;CACvB,CAAC;AAEF,MAAM,gCAAgC,GAAG;IACvC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;IACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;IACjB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;CAChB,CAAC;AAEF,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,IAAI,OAAuB,CAAC;IAE5B,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,iCAAc;gBACd;oBACE,OAAO,EAAE,4BAAY;oBACrB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAiB,iCAAc,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;YAE/B,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,SAAS,CAAC;YACzB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAErD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;YAC1B,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,IAAI;gBACT,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,YAAY;aAC1B,CAAC;YAEF,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;YAErE,MAAM,MAAM,GAAG,gBAAgB,CAAC;YAChC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAErD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACrC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,4BAA4B,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;YAC3B,MAAM,UAAU,GAAG;gBACjB,YAAY;gBACZ,iBAAiB;gBACjB,WAAW;gBACX,WAAW;aACZ,CAAC;YAEF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACnC,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;qBAC7C,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;YAC5B,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,IAAI,GAAG,aAAa,CAAC;YAC3B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YACtB,MAAM,CAAC,iCAAc,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACrE,MAAM,CAAC,iCAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YACtB,MAAM,CAAC,iCAAc,CAAC,MAAM,CAAC,sCAAsC,CAAC,CAAC;iBAClE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAClC,MAAM,CAAC,iCAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAI,OAA2B,CAAC;IAEhC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,yCAAkB;gBAClB;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAqB,yCAAkB,CAAC,CAAC;IAC/D,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;YAC7B,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,IAAI;aACV,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,eAAe,EAAE,CAAC;wBAChB,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,KAAK;wBACX,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI;wBACf,WAAW,EAAE,IAAI,IAAI,EAAE;qBACxB,CAAC;gBACF,cAAc,EAAE,CAAC;gBACjB,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;YAC5B,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,QAAQ;gBAClB,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,QAAQ;aACd,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,eAAe,EAAE,CAAC;wBAChB,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,KAAK;wBACX,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI;wBACf,WAAW,EAAE,IAAI,IAAI,EAAE;qBACxB,CAAC;gBACF,cAAc,EAAE,CAAC;gBACjB,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;YAC7B,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,IAAI;aACV,CAAC;YAEF,MAAM,WAAW,GAAG;gBAClB,eAAe,EAAE,CAAC;wBAChB,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,KAAK;wBACX,UAAU,EAAE,EAAE;wBACd,SAAS,EAAE,IAAI;wBACf,WAAW,EAAE,IAAI,IAAI,EAAE;qBACxB,CAAC;gBACF,cAAc,EAAE,CAAC;gBACjB,eAAe,EAAE,EAAE;aACpB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,IAAI,OAAqC,CAAC;IAE1C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,8DAA4B;gBAC5B;oBACE,OAAO,EAAE,iCAAc;oBACvB,QAAQ,EAAE;wBACR,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC1B,oBAAoB,EAAE,IAAI,CAAC,EAAE,EAAE;qBAChC;iBACF;gBACD;oBACE,OAAO,EAAE,yCAAkB;oBAC3B,QAAQ,EAAE;wBACR,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;wBAC1B,iCAAiC,EAAE,IAAI,CAAC,EAAE,EAAE;qBAC7C;iBACF;gBACD;oBACE,OAAO,EAAE,8BAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,gDAAkB,CAAC;oBAC/C,QAAQ,EAAE,gCAAgC;iBAC3C;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAA+B,8DAA4B,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;YAC5B,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,KAAK;gBACf,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,IAAI;gBACT,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,YAAY;aAC1B,CAAC;YAEF,MAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAChD,cAAc,CAAC,eAA6B,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE9E,MAAM,OAAO,GAAG,EAAE,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;YAC7D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}