"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    common: {
        notifyDomain: 'http://7uz4301cb969.vicp.fun',
        orderExpireTime: 7200,
        retryInterval: 60,
        maxRetryCount: 5,
        defaultLockType: 'distributed',
    },
    alipay: {
        appId: '2021005170688349',
        privateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCA+QhaPVx8J2oNhG2Ytt+5LJxMx4FLzGxkupq49M0za8RaOTuyPVOtBifBb6wc3ZWltCdYwoAHRUEBjY8tW05m/HYVAUNrZ+S2V4c4NbrM/ipvBQaQ9rGSko1I5kLqPztDb5Xxf+grSwbyq9cNa5uHuJAICVmie7dIRuiMTxo8Osla9RQd3DKuW8SOhgHrKF3xxSvYSXxhnZCH9tmc7BziBionoo6vKbWDzVAUKIbi3/Qa2oXQeRqL7J4kj+IQNupCV7Lkphv5E5srEIolzWepqDWtC+LVAzOQLFHD8yl4d+gSa/ZcYrI/E9D4+3vobAuchBrC9jy8NxN55UINrFphAgMBAAECggEAGniCcVsEv2qrhqi2gdCO7IeucMfFztPfL3FpTirYbwRgYZA7o2KY6PtJb5dHAQmtrDvkk4AgHN3m8LWoPEvwNIz+g2Ml/kZtl8sxMqU8eNR1yyGB7oDjg0zW+K5h74gY1sdxRAbXlQMqIuaIFfYPDvUfgrbAkevIe2oqUpntrYysqhJCiNTiMTVO9wpb60nqd4CGqfis8u+h3eOILNhS6Dr55Kh+51koDw4ljkZmQ0AUGDpP5gyriOKHNMOYjnwya4PHf3PVbCdibwKtKshmwxv6jVHtoQbwh/Cd4sGeLYmFdpm7K4uVFqEsBhCUI1YKY5vjHHK0AZZBmOusqS7CXQKBgQC5nV908XmVAfgOFGYatOmF+PrSxciGJ+bf/UFNZ4Rw4xmaoxtNTvUnYiR+MligQ4UDhlQ4ukimGEMxiyGWGBhRVU34rsksD2aqA5SJ98brzRJMfkxT5EVM/Co5DCK+qy0fwrFLMr3yW13hoNWma0QkgT53By7QeUHj0eMzM3kMawKBgQCx4R5kvkbiwgnXF2FWCsxom37YPCOv8o+QQOoTLTsQwomhNJYb1Bv6ImD2VuXpJDFAxn65VYRgBRWeoQdFLFkfaW15XSOMZPzianmeSgx380u2HSws2aBGthd97F78yzROPNOfvPmtcYNzFGHbbbLNyBPlKMdu66cc+4P1rPjnYwKBgEj17EzKkpEY6gmWyR/cVyLbYLSzHARn8N/A7AdiFnRNQIZBcPzteX4WnOOiJKYyhDmDdt2pk97+EIOL/hLCu+RUlMaY5ejU0PUpXRzycZXE0VmSF6yuQPNm963e8FVkceNXI1u8wV9fvMJw9ypoZau3xfcBsswtrdHAcf9xTzarAoGAJ5d+x1eeuXwM6qYWkSsoIiRyEDkajGAF2krIjuojWnCiZ5In0xvb+knKoSnoVIMsv0kvDZteMm0PSdZ3osrjL12s8zVq4yXbL54ZYcUDj3NEp81IdtcdmH+RLQL+hW/JPZzGUaijPhqk8mlX+popESp2Wyw/b43eDVJiJb2RTRcCgYEAl/sJnJMj+babHR0TEMKBvvuFfNjfg4HFRXTjV5yzWJkSsT1YBxLE99jgyiVs0oUyfIyFfSEzHq/1/kYu3MX5nplugQk5r7Dk0AltUpMUW9nRDcGfn/p9OZkh6XzkixtZLVFGWkrXm61Eobf26/vrZi22BXIDRpEACCkGvleVWtI=',
        publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgPkIWj1cfCdqDYRtmLbfuSycTMeBS8xsZLqauPTNM2vEWjk7sj1TrQYnwW+sHN2VpbQnWMKAB0VBAY2PLVtOZvx2FQFDa2fktleHODW6zP4qbwUGkPaxkpKNSOZC6j87Q2+V8X/oK0sG8qvXDWubh7iQCAlZonu3SEbojE8aPDrJWvUUHdwyrlvEjoYB6yhd8cUr2El8YZ2Qh/bZnOwc4gYqJ6KOrym1g81QFCiG4t/0GtqF0Hkai+yeJI/iEDbqQley5KYb+RObKxCKJc1nqag1rQvi1QMzkCxRw/MpeHfoEmv2XGKyPxPQ+Pt76GwLnIQawvY8vDcTeeVCDaxaYQIDAQAB',
        aliPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnIl+bNbVzne5LQfj5A+81kcY+xEHA9OOsHGGkWw0ScVsGs8Q2S3Q3bCcM28UGXDxcMaHL2f2jvZFoOvnn3y36Q7gnYo33sKyyjELvCtRCLN2cX2qvgKlIEOVMDILaD9uLO7iePt7W/t71Iplm8TU7nI3ukygdX6IzHvcUkSW47vGtTJrUaQc2qXA90UnA2HdjROJ5g51yxXCsl23XGQYgX9OdRp3qZfiBBlbD8sBKaVWgLJpKNO3YonooCBBTxkBY/6aGoVBDlPE8aEH7+Fqzg7qaq48d/ANUkHygotkURe8Zyik+zWtoygpMCq8LqATa4u+aPK51BuhD4EJ3gGerQIDAQAB',
        gateway: 'https://openapi.alipay.com/gateway.do',
        notifyUrl: '/v1/payment/notify/alipay',
        returnUrl: '/v1/payment/return/alipay',
        signType: 'RSA2',
    },
    wechatpay: {
        appId: 'wx2b5d05a3dfebae30',
        mchId: '1714593622',
        apiV3Key: '8FODOOhgdgEHFuIo693h9aE48u3mjeJX',
        serialNo: '51E70A498DEFFB59A280B5F4B595DF0B766CB69D',
        privateKeyPath: process.env.WECHATPAY_PRIVATE_KEY_PATH || './keys/wechatpay/apiclient_key.pem',
        certificatePath: process.env.WECHATPAY_CERTIFICATE_PATH || './keys/wechatpay/apiclient_cert.pem',
        wechatPayPublicKeyPath: process.env.WECHATPAY_PUBLIC_KEY_PATH || './keys/wechatpay/pub_key.pem',
        notifyUrl: '/v1/payment/notify/wechatpay',
        signType: 'RSA',
    }
};
//# sourceMappingURL=payment.config.js.map