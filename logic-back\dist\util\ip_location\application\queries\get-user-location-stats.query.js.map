{"version": 3, "file": "get-user-location-stats.query.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/application/queries/get-user-location-stats.query.ts"], "names": [], "mappings": ";;;AAIA,MAAa,yBAAyB;IACpB,MAAM,CAAS;IACf,IAAI,CAAS;IACb,cAAc,CAAU;IACxB,mBAAmB,CAAU;IAC7B,aAAa,CAAS;IACtB,SAAS,CAAO;IAEhC,YACE,MAAc,EACd,OAAe,EAAE,EACjB,iBAA0B,IAAI,EAC9B,sBAA+B,KAAK,EACpC,gBAAwB,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAKD,MAAM,CAAC,WAAW,CAAC,MAAc,EAAE,OAAe,EAAE;QAClD,OAAO,IAAI,yBAAyB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAKD,MAAM,CAAC,sBAAsB,CAC3B,MAAc,EACd,OAAe,EAAE;QAEjB,OAAO,IAAI,yBAAyB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC;IAKD,MAAM,CAAC,iBAAiB,CACtB,MAAc,EACd,OAAe,EAAE;QAEjB,OAAO,IAAI,yBAAyB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAKD,MAAM,CAAC,qBAAqB,CAC1B,MAAc,EACd,OAAe,EAAE,EACjB,gBAAwB,CAAC;QAEzB,OAAO,IAAI,yBAAyB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;IACjF,CAAC;IAKD,QAAQ;QACN,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAKD,YAAY;QACV,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,UAAU;QACR,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,WAAW;QACT,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,OAAO,uBAAuB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,UAAU,EAAE,CAAC;IAC/G,CAAC;IAKD,UAAU;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1D,OAAO,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,UAAU,WAAW,GAAG,QAAQ,EAAE,CAAC;IAC5E,CAAC;IAKD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;IACxB,CAAC;IAKD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;IACxB,CAAC;IAKD,YAAY;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChC,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAKD,qBAAqB;QACnB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;YAC3B,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAKD,MAAM;QACJ,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE;YAC5C,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE;YACxC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;SACjD,CAAC;IACJ,CAAC;CACF;AApLD,8DAoLC"}