"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinMessageModule = void 0;
const common_1 = require("@nestjs/common");
const weixin_message_controller_1 = require("./weixin_message.controller");
const weixin_utils_module_1 = require("../utils/weixin_utils.module");
const web_weixin_scan_module_1 = require("../web_weixin_scan/web_weixin_scan.module");
let WeixinMessageModule = class WeixinMessageModule {
};
exports.WeixinMessageModule = WeixinMessageModule;
exports.WeixinMessageModule = WeixinMessageModule = __decorate([
    (0, common_1.Module)({
        imports: [weixin_utils_module_1.WeixinUtilsModule, web_weixin_scan_module_1.WebWeixinScanModule],
        controllers: [weixin_message_controller_1.WeixinMessageController],
        providers: [],
    })
], WeixinMessageModule);
//# sourceMappingURL=weixin_message.module.js.map