"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTagService = void 0;
const common_1 = require("@nestjs/common");
const tag_service_1 = require("../../util/database/mysql/tag/tag.service");
let UserTagService = class UserTagService {
    tagService;
    constructor(tagService) {
        this.tagService = tagService;
    }
    async create(data) {
        const createTagDto = {
            name: data.name,
            description: data.description,
            color: data.color,
        };
        return await this.tagService.create(createTagDto);
    }
    async updateTag(id, data) {
        const updateTagDto = {
            name: data.name,
            description: data.description,
            color: data.color,
        };
        return await this.tagService.update(id, updateTagDto);
    }
    async deleteTag(id) {
        return await this.tagService.remove(id);
    }
    async getById(id) {
        return await this.tagService.getById(id);
    }
    async list(params) {
        return await this.tagService.list(params);
    }
};
exports.UserTagService = UserTagService;
exports.UserTagService = UserTagService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tag_service_1.TagService])
], UserTagService);
//# sourceMappingURL=user_tag.service.js.map