"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-textarea";
exports.ids = ["vendor-chunks/rc-textarea"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-textarea/es/ResizableTextArea.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./calculateNodeHeight */ \"(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\");\n\n\n\n\n\n\nvar _excluded = [\"prefixCls\", \"defaultValue\", \"value\", \"autoSize\", \"onResize\", \"className\", \"style\", \"disabled\", \"onChange\", \"onInternalAutoSize\"];\n\n\n\n\n\n\n\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function (props, ref) {\n  var _ref = props,\n    prefixCls = _ref.prefixCls,\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    autoSize = _ref.autoSize,\n    onResize = _ref.onResize,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    onChange = _ref.onChange,\n    onInternalAutoSize = _ref.onInternalAutoSize,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n\n  // =============================== Value ================================\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(defaultValue, {\n      value: value,\n      postState: function postState(val) {\n        return val !== null && val !== void 0 ? val : '';\n      }\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setMergedValue = _useMergedState2[1];\n  var onInternalChange = function onInternalChange(event) {\n    setMergedValue(event.target.value);\n    onChange === null || onChange === void 0 || onChange(event);\n  };\n\n  // ================================ Ref =================================\n  var textareaRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n  react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle(ref, function () {\n    return {\n      textArea: textareaRef.current\n    };\n  });\n\n  // ============================== AutoSize ==============================\n  var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_11__.useMemo(function () {\n      if (autoSize && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(autoSize) === 'object') {\n        return [autoSize.minRows, autoSize.maxRows];\n      }\n      return [];\n    }, [autoSize]),\n    _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useMemo, 2),\n    minRows = _React$useMemo2[0],\n    maxRows = _React$useMemo2[1];\n  var needAutoSize = !!autoSize;\n\n  // =============================== Scroll ===============================\n  // https://github.com/ant-design/ant-design/issues/21870\n  var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n    try {\n      // FF has bug with jump of scroll to top. We force back here.\n      if (document.activeElement === textareaRef.current) {\n        var _textareaRef$current = textareaRef.current,\n          selectionStart = _textareaRef$current.selectionStart,\n          selectionEnd = _textareaRef$current.selectionEnd,\n          scrollTop = _textareaRef$current.scrollTop;\n\n        // Fix Safari bug which not rollback when break line\n        // This makes Chinese IME can't input. Do not fix this\n        // const { value: tmpValue } = textareaRef.current;\n        // textareaRef.current.value = '';\n        // textareaRef.current.value = tmpValue;\n\n        textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n        textareaRef.current.scrollTop = scrollTop;\n      }\n    } catch (e) {\n      // Fix error in Chrome:\n      // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n      // http://stackoverflow.com/q/21177489/3040605\n    }\n  };\n\n  // =============================== Resize ===============================\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_11__.useState(RESIZE_STABLE),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2),\n    resizeState = _React$useState2[0],\n    setResizeState = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11__.useState(),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2),\n    autoSizeStyle = _React$useState4[0],\n    setAutoSizeStyle = _React$useState4[1];\n  var startResize = function startResize() {\n    setResizeState(RESIZE_START);\n    if (false) {}\n  };\n\n  // Change to trigger resize measure\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function () {\n    if (needAutoSize) {\n      startResize();\n    }\n  }, [value, minRows, maxRows, needAutoSize]);\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function () {\n    if (resizeState === RESIZE_START) {\n      setResizeState(RESIZE_MEASURING);\n    } else if (resizeState === RESIZE_MEASURING) {\n      var textareaStyles = (0,_calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(textareaRef.current, false, minRows, maxRows);\n\n      // Safari has bug that text will keep break line on text cut when it's prev is break line.\n      // ZombieJ: This not often happen. So we just skip it.\n      // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n      // const { value: tmpValue } = textareaRef.current;\n      // textareaRef.current.value = '';\n      // textareaRef.current.value = tmpValue;\n\n      // if (document.activeElement === textareaRef.current) {\n      //   textareaRef.current.scrollTop = scrollTop;\n      //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n      // }\n\n      setResizeState(RESIZE_STABLE);\n      setAutoSizeStyle(textareaStyles);\n    } else {\n      fixFirefoxAutoScroll();\n    }\n  }, [resizeState]);\n\n  // We lock resize trigger by raf to avoid Safari warning\n  var resizeRafRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n  var cleanRaf = function cleanRaf() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"].cancel(resizeRafRef.current);\n  };\n  var onInternalResize = function onInternalResize(size) {\n    if (resizeState === RESIZE_STABLE) {\n      onResize === null || onResize === void 0 || onResize(size);\n      if (autoSize) {\n        cleanRaf();\n        resizeRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function () {\n          startResize();\n        });\n      }\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_11__.useEffect(function () {\n    return cleanRaf;\n  }, []);\n\n  // =============================== Render ===============================\n  var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n  var mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), mergedAutoSizeStyle);\n  if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n    mergedStyle.overflowY = 'hidden';\n    mergedStyle.overflowX = 'hidden';\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n    onResize: onInternalResize,\n    disabled: !(autoSize || onResize)\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, {\n    ref: textareaRef,\n    style: mergedStyle,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n    disabled: disabled,\n    value: mergedValue,\n    onChange: onInternalChange\n  })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResizableTextArea);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/TextArea.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-textarea/es/TextArea.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-input */ \"(ssr)/./node_modules/rc-input/es/index.js\");\n/* harmony import */ var rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-input/es/hooks/useCount */ \"(ssr)/./node_modules/rc-input/es/hooks/useCount.js\");\n/* harmony import */ var rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-input/es/utils/commonUtils */ \"(ssr)/./node_modules/rc-input/es/utils/commonUtils.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n\n\n\n\nvar _excluded = [\"defaultValue\", \"value\", \"onFocus\", \"onBlur\", \"onChange\", \"allowClear\", \"maxLength\", \"onCompositionStart\", \"onCompositionEnd\", \"suffix\", \"prefixCls\", \"showCount\", \"count\", \"className\", \"style\", \"disabled\", \"hidden\", \"classNames\", \"styles\", \"onResize\", \"onClear\", \"onPressEnter\", \"readOnly\", \"autoSize\", \"onKeyDown\"];\n\n\n\n\n\n\n\nvar TextArea = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().forwardRef(function (_ref, ref) {\n  var _countConfig$max;\n  var defaultValue = _ref.defaultValue,\n    customValue = _ref.value,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onChange = _ref.onChange,\n    allowClear = _ref.allowClear,\n    maxLength = _ref.maxLength,\n    onCompositionStart = _ref.onCompositionStart,\n    onCompositionEnd = _ref.onCompositionEnd,\n    suffix = _ref.suffix,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,\n    showCount = _ref.showCount,\n    count = _ref.count,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    hidden = _ref.hidden,\n    classNames = _ref.classNames,\n    styles = _ref.styles,\n    onResize = _ref.onResize,\n    onClear = _ref.onClear,\n    onPressEnter = _ref.onPressEnter,\n    readOnly = _ref.readOnly,\n    autoSize = _ref.autoSize,\n    onKeyDown = _ref.onKeyDown,\n    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(defaultValue, {\n      value: customValue,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var formatValue = value === undefined || value === null ? '' : String(value);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_11___default().useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var compositionRef = react__WEBPACK_IMPORTED_MODULE_11___default().useRef(false);\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2),\n    textareaResized = _React$useState4[0],\n    setTextareaResized = _React$useState4[1];\n\n  // =============================== Ref ================================\n  var holderRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var resizableTextAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n  var getTextArea = function getTextArea() {\n    var _resizableTextAreaRef;\n    return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;\n  };\n  var focus = function focus() {\n    getTextArea().focus();\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle)(ref, function () {\n    var _holderRef$current;\n    return {\n      resizableTextArea: resizableTextAreaRef.current,\n      focus: focus,\n      blur: function blur() {\n        getTextArea().blur();\n      },\n      nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || getTextArea()\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function () {\n    setFocused(function (prev) {\n      return !disabled && prev;\n    });\n  }, [disabled]);\n\n  // =========================== Select Range ===========================\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState5, 2),\n    selection = _React$useState6[0],\n    setSelection = _React$useState6[1];\n  react__WEBPACK_IMPORTED_MODULE_11___default().useEffect(function () {\n    if (selection) {\n      var _getTextArea;\n      (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(selection));\n    }\n  }, [selection]);\n\n  // ============================== Count ===============================\n  var countConfig = (0,rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(count, showCount);\n  var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;\n\n  // Max length value\n  var hasMaxLength = Number(mergedMax) > 0;\n  var valueLength = countConfig.strategy(formatValue);\n  var isOutOfRange = !!mergedMax && valueLength > mergedMax;\n\n  // ============================== Change ==============================\n  var triggerChange = function triggerChange(e, currentValue) {\n    var cutValue = currentValue;\n    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {\n      cutValue = countConfig.exceedFormatter(currentValue, {\n        max: countConfig.max\n      });\n      if (currentValue !== cutValue) {\n        setSelection([getTextArea().selectionStart || 0, getTextArea().selectionEnd || 0]);\n      }\n    }\n    setValue(cutValue);\n    (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(e.currentTarget, e, onChange, cutValue);\n  };\n\n  // =========================== Value Update ===========================\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    compositionRef.current = true;\n    onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    compositionRef.current = false;\n    triggerChange(e, e.currentTarget.value);\n    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);\n  };\n  var onInternalChange = function onInternalChange(e) {\n    triggerChange(e, e.target.value);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (e.key === 'Enter' && onPressEnter) {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 || onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 || onBlur(e);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    setValue('');\n    focus();\n    (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(getTextArea(), e, onChange);\n  };\n  var suffixNode = suffix;\n  var dataCount;\n  if (countConfig.show) {\n    if (countConfig.showFormatter) {\n      dataCount = countConfig.showFormatter({\n        value: formatValue,\n        count: valueLength,\n        maxLength: mergedMax\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(mergedMax) : '');\n    }\n    suffixNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement((react__WEBPACK_IMPORTED_MODULE_11___default().Fragment), null, suffixNode, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"span\", {\n      className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n      style: styles === null || styles === void 0 ? void 0 : styles.count\n    }, dataCount));\n  }\n  var handleResize = function handleResize(size) {\n    var _getTextArea2;\n    onResize === null || onResize === void 0 || onResize(size);\n    if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {\n      setTextareaResized(true);\n    }\n  };\n  var isPureTextArea = !autoSize && !showCount && !allowClear;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_input__WEBPACK_IMPORTED_MODULE_7__.BaseInput, {\n    ref: holderRef,\n    value: formatValue,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    suffix: suffixNode,\n    prefixCls: prefixCls,\n    classNames: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, classNames), {}, {\n      affixWrapper: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-show-count\"), showCount), \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear))\n    }),\n    disabled: disabled,\n    focused: focused,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(className, isOutOfRange && \"\".concat(prefixCls, \"-out-of-range\")),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), textareaResized && !isPureTextArea ? {\n      height: 'auto'\n    } : {}),\n    dataAttrs: {\n      affixWrapper: {\n        'data-count': typeof dataCount === 'string' ? dataCount : undefined\n      }\n    },\n    hidden: hidden,\n    readOnly: readOnly,\n    onClear: onClear\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, rest, {\n    autoSize: autoSize,\n    maxLength: maxLength,\n    onKeyDown: handleKeyDown,\n    onChange: onInternalChange,\n    onFocus: handleFocus,\n    onBlur: handleBlur,\n    onCompositionStart: onInternalCompositionStart,\n    onCompositionEnd: onInternalCompositionEnd,\n    className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n      resize: style === null || style === void 0 ? void 0 : style.resize\n    }),\n    disabled: disabled,\n    prefixCls: prefixCls,\n    onResize: handleResize,\n    ref: resizableTextAreaRef,\n    readOnly: readOnly\n  })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextArea);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/TextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-textarea/es/calculateNodeHeight.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNodeStyling: () => (/* binding */ calculateNodeStyling),\n/* harmony export */   \"default\": () => (/* binding */ calculateAutoSizeStyle)\n/* harmony export */ });\n// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */\n\nvar HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = ['letter-spacing', 'line-height', 'padding-top', 'padding-bottom', 'font-family', 'font-weight', 'font-size', 'font-variant', 'text-rendering', 'text-transform', 'width', 'text-indent', 'padding-left', 'padding-right', 'border-width', 'box-sizing', 'word-break', 'white-space'];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nfunction calculateNodeStyling(node) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var nodeRef = node.getAttribute('id') || node.getAttribute('data-reactid') || node.getAttribute('name');\n  if (useCache && computedStyleCache[nodeRef]) {\n    return computedStyleCache[nodeRef];\n  }\n  var style = window.getComputedStyle(node);\n  var boxSizing = style.getPropertyValue('box-sizing') || style.getPropertyValue('-moz-box-sizing') || style.getPropertyValue('-webkit-box-sizing');\n  var paddingSize = parseFloat(style.getPropertyValue('padding-bottom')) + parseFloat(style.getPropertyValue('padding-top'));\n  var borderSize = parseFloat(style.getPropertyValue('border-bottom-width')) + parseFloat(style.getPropertyValue('border-top-width'));\n  var sizingStyle = SIZING_STYLE.map(function (name) {\n    return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n  }).join(';');\n  var nodeInfo = {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize,\n    boxSizing: boxSizing\n  };\n  if (useCache && nodeRef) {\n    computedStyleCache[nodeRef] = nodeInfo;\n  }\n  return nodeInfo;\n}\nfunction calculateAutoSizeStyle(uiTextNode) {\n  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tab-index', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    // fix: A form field element should have an id or name attribute\n    // A form field element has neither an id nor a name attribute. This might prevent the browser from correctly autofilling the form.\n    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea\n    hiddenTextarea.setAttribute('name', 'hiddenTextarea');\n    document.body.appendChild(hiddenTextarea);\n  }\n\n  // Fix wrap=\"off\" issue\n  // https://github.com/ant-design/ant-design/issues/6577\n  if (uiTextNode.getAttribute('wrap')) {\n    hiddenTextarea.setAttribute('wrap', uiTextNode.getAttribute('wrap'));\n  } else {\n    hiddenTextarea.removeAttribute('wrap');\n  }\n\n  // Copy all CSS properties that have an impact on the height of the content in\n  // the textbox\n  var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache),\n    paddingSize = _calculateNodeStyling.paddingSize,\n    borderSize = _calculateNodeStyling.borderSize,\n    boxSizing = _calculateNodeStyling.boxSizing,\n    sizingStyle = _calculateNodeStyling.sizingStyle;\n\n  // Need to have the overflow attribute to hide the scrollbar otherwise\n  // text-lines will not calculated properly as the shadow will technically be\n  // narrower for content\n  hiddenTextarea.setAttribute('style', \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n  hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || '';\n  var minHeight = undefined;\n  var maxHeight = undefined;\n  var overflowY;\n  var height = hiddenTextarea.scrollHeight;\n  if (boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    height += borderSize;\n  } else if (boxSizing === 'content-box') {\n    // remove padding, since height = content\n    height -= paddingSize;\n  }\n  if (minRows !== null || maxRows !== null) {\n    // measure height of a textarea with a single row\n    hiddenTextarea.value = ' ';\n    var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n    if (minRows !== null) {\n      minHeight = singleRowHeight * minRows;\n      if (boxSizing === 'border-box') {\n        minHeight = minHeight + paddingSize + borderSize;\n      }\n      height = Math.max(minHeight, height);\n    }\n    if (maxRows !== null) {\n      maxHeight = singleRowHeight * maxRows;\n      if (boxSizing === 'border-box') {\n        maxHeight = maxHeight + paddingSize + borderSize;\n      }\n      overflowY = height > maxHeight ? '' : 'hidden';\n      height = Math.min(maxHeight, height);\n    }\n  }\n  var style = {\n    height: height,\n    overflowY: overflowY,\n    resize: 'none'\n  };\n  if (minHeight) {\n    style.minHeight = minHeight;\n  }\n  if (maxHeight) {\n    style.maxHeight = maxHeight;\n  }\n  return style;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-textarea/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResizableTextArea: () => (/* reexport safe */ _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _TextArea__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextArea */ \"(ssr)/./node_modules/rc-textarea/es/TextArea.js\");\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_TextArea__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNpQztBQUNuRSxpRUFBZSxpREFBUSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10ZXh0YXJlYS9lcy9pbmRleC5qcz84MjVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBUZXh0QXJlYSBmcm9tIFwiLi9UZXh0QXJlYVwiO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBSZXNpemFibGVUZXh0QXJlYSB9IGZyb20gXCIuL1Jlc2l6YWJsZVRleHRBcmVhXCI7XG5leHBvcnQgZGVmYXVsdCBUZXh0QXJlYTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/index.js\n");

/***/ })

};
;