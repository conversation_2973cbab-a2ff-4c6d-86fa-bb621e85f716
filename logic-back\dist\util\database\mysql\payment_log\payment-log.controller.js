"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentLogController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentLogController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_log_service_1 = require("./payment-log.service");
const payment_log_entity_1 = require("./entities/payment-log.entity");
const payment_log_dto_1 = require("./dto/payment-log.dto");
let PaymentLogController = PaymentLogController_1 = class PaymentLogController {
    paymentLogService;
    logger = new common_1.Logger(PaymentLogController_1.name);
    constructor(paymentLogService) {
        this.paymentLogService = paymentLogService;
    }
    async create(createDto) {
        this.logger.log(`创建支付日志请求: ${JSON.stringify(createDto)}`);
        const result = await this.paymentLogService.create(createDto);
        return { code: 0, message: 'success', data: result };
    }
    async findAll(queryDto, page = 1, limit = 20) {
        this.logger.log(`查询支付日志列表: page=${page}, limit=${limit}`);
        const [records, total] = await this.paymentLogService.findAll(queryDto, page, limit);
        return {
            code: 0,
            message: 'success',
            data: {
                records,
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        };
    }
    async findOne(id) {
        this.logger.log(`查询支付日志详情: ID=${id}`);
        const record = await this.paymentLogService.findById(id);
        return { code: 0, message: 'success', data: record };
    }
    async findByOrderNo(orderNo) {
        this.logger.log(`查询订单相关日志: orderNo=${orderNo}`);
        const records = await this.paymentLogService.findByOrderNo(orderNo);
        return { code: 0, message: 'success', data: records };
    }
    async findByRefundNo(refundNo) {
        this.logger.log(`查询退款相关日志: refundNo=${refundNo}`);
        const records = await this.paymentLogService.findByRefundNo(refundNo);
        return { code: 0, message: 'success', data: records };
    }
    async cleanupOldLogs(payload) {
        const days = payload.days || 90;
        this.logger.log(`清理${days}天前的旧日志`);
        const count = await this.paymentLogService.cleanupOldLogs(days);
        return { code: 0, message: 'success', data: { count } };
    }
};
exports.PaymentLogController = PaymentLogController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建支付日志' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: payment_log_entity_1.PaymentLog }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_log_dto_1.CreatePaymentLogDto]),
    __metadata("design:returntype", Promise)
], PaymentLogController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '查询支付日志列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [payment_log_entity_1.PaymentLog] }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码', type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量', type: Number }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_log_dto_1.QueryPaymentLogDto, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentLogController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '查询支付日志详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: payment_log_entity_1.PaymentLog }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentLogController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('order/:orderNo'),
    (0, swagger_1.ApiOperation)({ summary: '查询订单相关日志' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [payment_log_entity_1.PaymentLog] }),
    __param(0, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentLogController.prototype, "findByOrderNo", null);
__decorate([
    (0, common_1.Get)('refund/:refundNo'),
    (0, swagger_1.ApiOperation)({ summary: '查询退款相关日志' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [payment_log_entity_1.PaymentLog] }),
    __param(0, (0, common_1.Param)('refundNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentLogController.prototype, "findByRefundNo", null);
__decorate([
    (0, common_1.Post)('cleanup'),
    (0, swagger_1.ApiOperation)({ summary: '清理旧日志' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '清理成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentLogController.prototype, "cleanupOldLogs", null);
exports.PaymentLogController = PaymentLogController = PaymentLogController_1 = __decorate([
    (0, swagger_1.ApiTags)('支付日志'),
    (0, common_1.Controller)('api/v1/payment-logs'),
    __metadata("design:paramtypes", [payment_log_service_1.PaymentLogService])
], PaymentLogController);
//# sourceMappingURL=payment-log.controller.js.map