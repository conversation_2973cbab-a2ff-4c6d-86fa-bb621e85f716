import { PaymentRefundService } from './payment-refund.service';
import { CreatePaymentRefundDto, RefundStatus } from './dto/create-payment-refund.dto';
import { UpdatePaymentRefundDto } from './dto/update-payment-refund.dto';
export declare class PaymentRefundController {
    private readonly paymentRefundService;
    constructor(paymentRefundService: PaymentRefundService);
    create(createPaymentRefundDto: CreatePaymentRefundDto): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    findAll(): Promise<import("./entities/payment-refund.entity").PaymentRefund[]>;
    findOne(id: string): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    findByBusinessRefundId(businessRefundId: string): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    findByPaymentOrderId(paymentOrderId: string): Promise<import("./entities/payment-refund.entity").PaymentRefund[]>;
    findByChannelRefundId(channelRefundId: string): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    findByUserId(userId: string): Promise<import("./entities/payment-refund.entity").PaymentRefund[]>;
    findByStatus(status: RefundStatus): Promise<import("./entities/payment-refund.entity").PaymentRefund[]>;
    update(id: string, updatePaymentRefundDto: UpdatePaymentRefundDto): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    updateStatus(id: string, status: RefundStatus, result?: any): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    updateNotifyData(id: string, notifyData: any): Promise<import("./entities/payment-refund.entity").PaymentRefund>;
    remove(id: string): Promise<void>;
}
