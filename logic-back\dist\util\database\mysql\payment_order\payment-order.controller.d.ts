import { PaymentOrderService } from './payment-order.service';
import { CreatePaymentOrderDto, PaymentStatus } from './dto/create-payment-order.dto';
import { UpdatePaymentOrderDto } from './dto/update-payment-order.dto';
export declare class PaymentOrderController {
    private readonly paymentOrderService;
    constructor(paymentOrderService: PaymentOrderService);
    create(createPaymentOrderDto: CreatePaymentOrderDto): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    findAll(): Promise<import("./entities/payment-order.entity").PaymentOrder[]>;
    findOne(id: string): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    findByBusinessOrderId(businessOrderId: string): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    findByChannelOrderId(channelOrderId: string): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    findByUserId(userId: string): Promise<import("./entities/payment-order.entity").PaymentOrder[]>;
    findByStatus(status: PaymentStatus): Promise<import("./entities/payment-order.entity").PaymentOrder[]>;
    update(id: string, updatePaymentOrderDto: UpdatePaymentOrderDto): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    updateStatus(id: string, status: PaymentStatus, result?: any): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    updateNotifyData(id: string, notifyData: any): Promise<import("./entities/payment-order.entity").PaymentOrder>;
    remove(id: string): Promise<void>;
}
