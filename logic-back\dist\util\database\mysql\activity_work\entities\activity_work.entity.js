"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityWork = void 0;
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const activity_entity_1 = require("../../activity/entities/activity.entity");
const user_work_info_entity_1 = require("../../user_work_info/entities/user_work_info.entity");
let ActivityWork = class ActivityWork {
    id;
    activityId;
    workId;
    userId;
    workTitle;
    workCover;
    workDesc;
    isSelected;
    isWinner;
    awardId;
    awardName;
    isShow;
    creatorId;
    createTime;
    updateTime;
    isDelete;
    activity;
    userWorkInfo;
};
exports.ActivityWork = ActivityWork;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动ID' }),
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "activityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品ID' }),
    (0, swagger_1.ApiProperty)({ description: '作品ID' }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "workId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品标题' }),
    (0, swagger_1.ApiProperty)({ description: '作品标题' }),
    __metadata("design:type", String)
], ActivityWork.prototype, "workTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品封面' }),
    (0, swagger_1.ApiProperty)({ description: '作品封面' }),
    __metadata("design:type", String)
], ActivityWork.prototype, "workCover", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '作品描述', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '作品描述', required: false }),
    __metadata("design:type", String)
], ActivityWork.prototype, "workDesc", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否入选：0-未入选 1-已入选', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '是否入选：0-未入选 1-已入选', default: 0 }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "isSelected", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否获奖：0-未获奖 1-已获奖', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '是否获奖：0-未获奖 1-已获奖', default: 0 }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "isWinner", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '奖项ID', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '奖项ID', required: false }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "awardId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '奖项名称', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '奖项名称', required: false }),
    __metadata("design:type", String)
], ActivityWork.prototype, "awardName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否展示：0-不展示 1-展示', default: 1 }),
    (0, swagger_1.ApiProperty)({ description: '是否展示：0-不展示 1-展示', default: 1 }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "isShow", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建人ID' }),
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    __metadata("design:type", Number)
], ActivityWork.prototype, "creatorId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ActivityWork.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ActivityWork.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否删除', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false }),
    __metadata("design:type", Boolean)
], ActivityWork.prototype, "isDelete", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => activity_entity_1.Activity, activity => activity.activityWorks),
    (0, typeorm_1.JoinColumn)({ name: 'activityId' }),
    __metadata("design:type", activity_entity_1.Activity)
], ActivityWork.prototype, "activity", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_work_info_entity_1.UserWorkInfo),
    (0, typeorm_1.JoinColumn)({ name: 'workId' }),
    __metadata("design:type", user_work_info_entity_1.UserWorkInfo)
], ActivityWork.prototype, "userWorkInfo", void 0);
exports.ActivityWork = ActivityWork = __decorate([
    (0, typeorm_1.Entity)('activity_work')
], ActivityWork);
//# sourceMappingURL=activity_work.entity.js.map