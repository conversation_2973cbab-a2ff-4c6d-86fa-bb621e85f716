"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AlipayStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlipayStrategy = void 0;
const common_1 = require("@nestjs/common");
const payment_config_service_1 = require("../config/payment-config.service");
const payment_signature_service_1 = require("../security/payment-signature.service");
const moment = require("moment");
const node_fetch_1 = require("node-fetch");
let AlipayStrategy = AlipayStrategy_1 = class AlipayStrategy {
    configService;
    signatureService;
    logger = new common_1.Logger(AlipayStrategy_1.name);
    appId;
    privateKey;
    publicKey;
    aliPublicKey;
    gateway;
    signType;
    constructor(configService, signatureService) {
        this.configService = configService;
        this.signatureService = signatureService;
        this.logger.log('初始化支付宝支付策略...');
        const alipayConfig = this.configService.getAlipayConfig();
        this.logger.debug(`支付宝配置: appId=${alipayConfig.appId}, gateway=${alipayConfig.gateway}`);
        this.appId = alipayConfig.appId;
        this.privateKey = alipayConfig.privateKey;
        this.publicKey = alipayConfig.publicKey;
        this.aliPublicKey = alipayConfig.aliPublicKey;
        this.gateway = alipayConfig.gateway || 'https://openapi-sandbox.dl.alipaydev.com/gateway.do';
        this.signType = alipayConfig.signType || 'RSA2';
        this.logger.log('支付宝支付策略初始化成功');
    }
    getChannel() {
        return 'alipay';
    }
    async createPayment(params) {
        this.logger.log(`开始创建支付宝支付订单: outTradeNo=${params.outTradeNo}, subject=${params.subject}, amount=${params.totalAmount}`);
        try {
            this.logger.debug('构建支付业务参数...');
            const bizContent = {
                out_trade_no: params.outTradeNo,
                product_code: 'FAST_INSTANT_TRADE_PAY',
                total_amount: params.totalAmount.toFixed(2),
                subject: params.subject,
                body: params.body || params.subject,
            };
            if (params.timeExpire) {
                bizContent['time_expire'] = moment(params.timeExpire).format('YYYY-MM-DD HH:mm:ss');
                this.logger.debug(`设置订单过期时间: ${bizContent['time_expire']}`);
            }
            const returnUrl = params.returnUrl || this.configService.getReturnUrl('alipay');
            const notifyUrl = params.notifyUrl || this.configService.getNotifyUrl('alipay');
            this.logger.debug(`使用returnUrl=${returnUrl}, notifyUrl=${notifyUrl}`);
            const requestParams = {
                app_id: this.appId,
                method: 'alipay.trade.page.pay',
                charset: 'utf-8',
                sign_type: this.signType,
                timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
                version: '1.0',
                notify_url: notifyUrl,
                return_url: returnUrl,
                biz_content: JSON.stringify(bizContent),
            };
            const sign = this.generateSign(requestParams);
            const paymentUrl = this.buildPayUrl(requestParams, sign);
            this.logger.debug(`支付宝支付链接生成成功: ${paymentUrl}`);
            this.logger.log(`支付宝支付创建成功: outTradeNo=${params.outTradeNo}`);
            return {
                success: true,
                paymentUrl,
                rawResponse: { requestParams, sign },
            };
        }
        catch (error) {
            this.logger.error(`支付宝支付创建失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async createQrCodePayment(params) {
        this.logger.log(`开始创建支付宝电脑网站支付(二维码模式): outTradeNo=${params.outTradeNo}, subject=${params.subject}, amount=${params.totalAmount}`);
        try {
            const bizContent = {
                out_trade_no: params.outTradeNo,
                total_amount: params.totalAmount.toFixed(2),
                subject: params.subject,
                body: params.body || params.subject,
                product_code: 'FAST_INSTANT_TRADE_PAY',
                qr_pay_mode: '4',
            };
            if (params.timeExpire) {
                bizContent['time_expire'] = moment(params.timeExpire).format('YYYY-MM-DD HH:mm:ss');
                this.logger.debug(`设置订单过期时间: ${bizContent['time_expire']}`);
            }
            const returnUrl = params.returnUrl || this.configService.getReturnUrl('alipay');
            const notifyUrl = params.notifyUrl || this.configService.getNotifyUrl('alipay');
            this.logger.debug(`使用returnUrl=${returnUrl}, notifyUrl=${notifyUrl}`);
            const requestParams = {
                app_id: this.appId,
                method: 'alipay.trade.page.pay',
                charset: 'utf-8',
                sign_type: this.signType,
                timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
                version: '1.0',
                notify_url: notifyUrl,
                return_url: returnUrl,
                biz_content: JSON.stringify(bizContent),
            };
            const sign = this.generateSign(requestParams);
            const paymentUrl = this.buildPayUrl(requestParams, sign);
            this.logger.debug(`支付宝电脑网站支付链接生成成功: ${paymentUrl}`);
            this.logger.log(`支付宝电脑网站支付(二维码模式)创建成功: outTradeNo=${params.outTradeNo}`);
            return {
                success: true,
                paymentUrl,
                qrCode: paymentUrl,
                rawResponse: { requestParams, sign },
            };
        }
        catch (error) {
            this.logger.error(`支付宝电脑网站支付(二维码模式)创建失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async queryPayment(params) {
        this.logger.log(`开始查询支付宝订单: outTradeNo=${params.outTradeNo}, paymentId=${params.paymentId || '无'}`);
        try {
            this.logger.debug('构建查询参数...');
            const bizContent = {
                out_trade_no: params.outTradeNo,
            };
            if (params.paymentId) {
                bizContent['trade_no'] = params.paymentId;
                this.logger.debug(`使用支付宝交易号查询: trade_no=${params.paymentId}`);
            }
            const requestParams = {
                app_id: this.appId,
                method: 'alipay.trade.query',
                charset: 'utf-8',
                sign_type: this.signType,
                timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
                version: '1.0',
                biz_content: JSON.stringify(bizContent),
            };
            const sign = this.generateSign(requestParams);
            const queryUrl = this.gateway;
            const fullParams = { ...requestParams, sign };
            this.logger.debug(`开始请求支付宝查询API: ${queryUrl}`);
            this.logger.debug(`查询参数: ${JSON.stringify(fullParams)}`);
            const searchParams = new URLSearchParams();
            Object.keys(fullParams).forEach(key => {
                searchParams.append(key, fullParams[key]);
            });
            const response = await (0, node_fetch_1.default)(queryUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
                },
                body: searchParams,
            });
            if (!response.ok) {
                throw new Error(`支付宝API请求失败: HTTP ${response.status} ${response.statusText}`);
            }
            const responseText = await response.text();
            this.logger.debug(`支付宝查询API响应: ${responseText}`);
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            }
            catch (e) {
                throw new Error(`解析支付宝响应失败: ${e.message}, 原始响应: ${responseText}`);
            }
            const resultKey = 'alipay_trade_query_response';
            if (!responseData[resultKey]) {
                throw new Error(`支付宝响应格式不正确: 缺少 ${resultKey}`);
            }
            const result = responseData[resultKey];
            if (result.code !== '10000') {
                const errorMsg = `${result.msg || ''}: ${result.sub_msg || ''}`;
                this.logger.warn(`支付宝订单查询失败: ${errorMsg}`);
                return {
                    success: false,
                    paid: false,
                    status: 'failed',
                    errorMessage: errorMsg,
                    rawResponse: result,
                };
            }
            const isPaid = result.trade_status === 'TRADE_SUCCESS' || result.trade_status === 'TRADE_FINISHED';
            this.logger.log(`支付宝订单查询成功: outTradeNo=${params.outTradeNo}, 交易状态=${result.trade_status}, 是否已支付=${isPaid}`);
            return {
                success: true,
                paid: isPaid,
                paymentId: result.trade_no,
                amount: parseFloat(result.total_amount),
                status: this.mapTradeStatus(result.trade_status),
                paymentTime: result.send_pay_date ? new Date(result.send_pay_date) : undefined,
                rawResponse: result,
            };
        }
        catch (error) {
            this.logger.error(`支付宝支付查询失败: ${error.message}`, error.stack);
            return {
                success: false,
                paid: false,
                status: 'error',
                errorMessage: error.message,
            };
        }
    }
    async closePayment(params) {
        this.logger.log(`开始关闭支付宝订单: outTradeNo=${params.outTradeNo}, paymentId=${params.paymentId || '无'}`);
        try {
            this.logger.debug('构建关闭订单参数...');
            const bizContent = {
                out_trade_no: params.outTradeNo,
            };
            if (params.paymentId) {
                bizContent['trade_no'] = params.paymentId;
                this.logger.debug(`使用支付宝交易号关闭: trade_no=${params.paymentId}`);
            }
            const requestParams = {
                app_id: this.appId,
                method: 'alipay.trade.close',
                charset: 'utf-8',
                sign_type: this.signType,
                timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
                version: '1.0',
                biz_content: JSON.stringify(bizContent),
            };
            const sign = this.generateSign(requestParams);
            const closeUrl = this.gateway;
            const fullParams = { ...requestParams, sign };
            this.logger.debug(`开始请求支付宝关闭API: ${closeUrl}`);
            this.logger.debug(`关闭参数: ${JSON.stringify(fullParams)}`);
            const searchParams = new URLSearchParams();
            Object.keys(fullParams).forEach(key => {
                searchParams.append(key, fullParams[key]);
            });
            const response = await (0, node_fetch_1.default)(closeUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
                },
                body: searchParams,
            });
            if (!response.ok) {
                throw new Error(`支付宝API请求失败: HTTP ${response.status} ${response.statusText}`);
            }
            const responseText = await response.text();
            this.logger.debug(`支付宝关闭API响应: ${responseText}`);
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            }
            catch (e) {
                throw new Error(`解析支付宝响应失败: ${e.message}, 原始响应: ${responseText}`);
            }
            const resultKey = 'alipay_trade_close_response';
            if (!responseData[resultKey]) {
                throw new Error(`支付宝响应格式不正确: 缺少 ${resultKey}`);
            }
            const result = responseData[resultKey];
            if (result.code !== '10000') {
                const errorMsg = `${result.msg || ''}: ${result.sub_msg || ''}`;
                this.logger.warn(`支付宝订单关闭失败: ${errorMsg}`);
                return {
                    success: false,
                    errorMessage: errorMsg,
                    rawResponse: result,
                };
            }
            this.logger.log(`支付宝订单关闭成功: outTradeNo=${params.outTradeNo}`);
            return {
                success: true,
                rawResponse: result,
            };
        }
        catch (error) {
            this.logger.error(`支付宝支付关闭失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async verifyNotify(data) {
        this.logger.log(`收到支付宝支付通知: outTradeNo=${data.out_trade_no || '未知'}, 交易状态=${data.trade_status || '未知'}`);
        this.logger.debug(`支付宝通知完整内容: ${JSON.stringify(data)}`);
        try {
            const signVerified = this.verifySign(data);
            if (!signVerified) {
                this.logger.warn('支付宝通知签名验证失败');
                return { verified: false };
            }
            this.logger.debug('支付宝通知签名验证成功');
            if (data.trade_status !== 'TRADE_SUCCESS' && data.trade_status !== 'TRADE_FINISHED') {
                this.logger.warn(`支付宝通知交易状态不是成功状态: ${data.trade_status}`);
                return { verified: false };
            }
            this.logger.log(`支付宝通知验证成功: outTradeNo=${data.out_trade_no}, 交易号=${data.trade_no}, 金额=${data.total_amount}`);
            return {
                verified: true,
                outTradeNo: data.out_trade_no,
                paymentId: data.trade_no,
                totalAmount: parseFloat(data.total_amount),
                paymentTime: data.gmt_payment ? new Date(data.gmt_payment) : undefined,
                tradeStatus: data.trade_status,
                buyerId: data.buyer_id,
                buyerLogonId: data.buyer_logon_id,
                rawNotify: data,
            };
        }
        catch (error) {
            this.logger.error(`支付宝通知验证失败: ${error.message}`, error.stack);
            return { verified: false };
        }
    }
    mapTradeStatus(tradeStatus) {
        const statusMap = {
            'WAIT_BUYER_PAY': 'pending',
            'TRADE_CLOSED': 'closed',
            'TRADE_SUCCESS': 'success',
            'TRADE_FINISHED': 'success',
        };
        const mappedStatus = statusMap[tradeStatus] || 'unknown';
        this.logger.debug(`映射支付宝交易状态: ${tradeStatus} => ${mappedStatus}`);
        return mappedStatus;
    }
    buildPayUrl(params, sign) {
        const query = new URLSearchParams();
        Object.keys(params).forEach(key => {
            query.append(key, params[key]);
        });
        query.append('sign', sign);
        return `${this.gateway}?${query.toString()}`;
    }
    generateSign(params) {
        return this.signatureService.generateAlipaySignature(params);
    }
    verifySign(params) {
        return this.signatureService.verifyAlipaySignature(params);
    }
    async refund(params) {
        this.logger.log(`开始发起支付宝退款: outTradeNo=${params.outTradeNo}, outRefundNo=${params.outRefundNo}, amount=${params.refundAmount}`);
        try {
            const bizContent = {
                out_trade_no: params.outTradeNo,
                refund_amount: params.refundAmount.toFixed(2),
                out_request_no: params.outRefundNo,
                refund_reason: params.reason || '正常退款',
            };
            const requestParams = {
                app_id: this.appId,
                method: 'alipay.trade.refund',
                charset: 'utf-8',
                sign_type: this.signType,
                timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
                version: '1.0',
                biz_content: JSON.stringify(bizContent),
            };
            const sign = this.generateSign(requestParams);
            const fullParams = { ...requestParams, sign };
            const searchParams = new URLSearchParams();
            Object.keys(fullParams).forEach(key => {
                searchParams.append(key, fullParams[key]);
            });
            this.logger.debug(`发送支付宝退款请求: ${this.gateway}`);
            const response = await (0, node_fetch_1.default)(this.gateway, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
                },
                body: searchParams,
            });
            if (!response.ok) {
                throw new Error(`支付宝API请求失败: HTTP ${response.status} ${response.statusText}`);
            }
            const responseText = await response.text();
            this.logger.debug(`支付宝退款API响应: ${responseText}`);
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            }
            catch (e) {
                throw new Error(`解析支付宝响应失败: ${e.message}, 原始响应: ${responseText}`);
            }
            const resultKey = 'alipay_trade_refund_response';
            if (!responseData[resultKey]) {
                throw new Error(`支付宝响应格式不正确: 缺少 ${resultKey}`);
            }
            const refundResponse = responseData[resultKey];
            if (refundResponse.code === '10000' && refundResponse.msg === 'Success') {
                this.logger.log(`支付宝退款成功: outTradeNo=${params.outTradeNo}, outRefundNo=${params.outRefundNo}`);
                return {
                    success: true,
                    refundId: refundResponse.trade_no || '',
                    rawResult: refundResponse,
                };
            }
            else {
                this.logger.warn(`支付宝退款失败: code=${refundResponse.code}, msg=${refundResponse.msg}, sub_code=${refundResponse.sub_code}, sub_msg=${refundResponse.sub_msg}`);
                return {
                    success: false,
                    errorMessage: refundResponse.sub_msg || refundResponse.msg || '退款失败',
                    rawResult: refundResponse,
                };
            }
        }
        catch (error) {
            this.logger.error(`支付宝退款异常: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
                rawResult: { error: error.message },
            };
        }
    }
    async queryRefundStatus(outRefundNo) {
        this.logger.log(`开始查询支付宝退款状态: outRefundNo=${outRefundNo}`);
        try {
            const bizContent = {
                out_request_no: outRefundNo,
            };
            const requestParams = {
                app_id: this.appId,
                method: 'alipay.trade.fastpay.refund.query',
                charset: 'utf-8',
                sign_type: this.signType,
                timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
                version: '1.0',
                biz_content: JSON.stringify(bizContent),
            };
            const sign = this.generateSign(requestParams);
            const fullParams = { ...requestParams, sign };
            const searchParams = new URLSearchParams();
            Object.keys(fullParams).forEach(key => {
                searchParams.append(key, fullParams[key]);
            });
            this.logger.debug(`发送支付宝退款查询请求: ${this.gateway}`);
            const response = await (0, node_fetch_1.default)(this.gateway, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
                },
                body: searchParams,
            });
            if (!response.ok) {
                throw new Error(`支付宝API请求失败: HTTP ${response.status} ${response.statusText}`);
            }
            const responseText = await response.text();
            this.logger.debug(`支付宝退款查询API响应: ${responseText}`);
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            }
            catch (e) {
                throw new Error(`解析支付宝响应失败: ${e.message}, 原始响应: ${responseText}`);
            }
            const resultKey = 'alipay_trade_fastpay_refund_query_response';
            if (!responseData[resultKey]) {
                throw new Error(`支付宝响应格式不正确: 缺少 ${resultKey}`);
            }
            const queryResponse = responseData[resultKey];
            if (queryResponse.code === '10000' && queryResponse.msg === 'Success') {
                const hasRefund = queryResponse.refund_amount && parseFloat(queryResponse.refund_amount) > 0;
                if (hasRefund) {
                    this.logger.log(`支付宝退款已成功: outRefundNo=${outRefundNo}, refundAmount=${queryResponse.refund_amount}`);
                    return {
                        status: 'success',
                        refundId: queryResponse.trade_no || '',
                        rawResult: queryResponse,
                    };
                }
                else {
                    this.logger.log(`支付宝退款处理中: outRefundNo=${outRefundNo}`);
                    return {
                        status: 'processing',
                        refundId: queryResponse.trade_no || '',
                        rawResult: queryResponse,
                    };
                }
            }
            else if (queryResponse.code === '40004' && queryResponse.sub_code === 'ACQ.TRADE_NOT_EXIST') {
                this.logger.warn(`支付宝退款查询失败: 退款记录不存在, outRefundNo=${outRefundNo}`);
                return {
                    status: 'failed',
                    errorMessage: '退款记录不存在',
                    rawResult: queryResponse,
                };
            }
            else {
                this.logger.warn(`支付宝退款查询失败: code=${queryResponse.code}, msg=${queryResponse.msg}, sub_code=${queryResponse.sub_code}, sub_msg=${queryResponse.sub_msg}`);
                return {
                    status: 'failed',
                    errorMessage: queryResponse.sub_msg || queryResponse.msg || '退款查询失败',
                    rawResult: queryResponse,
                };
            }
        }
        catch (error) {
            this.logger.error(`支付宝退款查询异常: ${error.message}`, error.stack);
            return {
                status: 'failed',
                errorMessage: error.message,
                rawResult: { error: error.message },
            };
        }
    }
};
exports.AlipayStrategy = AlipayStrategy;
exports.AlipayStrategy = AlipayStrategy = AlipayStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_config_service_1.PaymentConfigService,
        payment_signature_service_1.PaymentSignatureService])
], AlipayStrategy);
//# sourceMappingURL=alipay.strategy.js.map