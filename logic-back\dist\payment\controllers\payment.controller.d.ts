import { Request, Response } from 'express';
import { PaymentService } from '../services/payment.service';
import { CreatePaymentDto, ClosePaymentDto } from '../dto/payment-request.dto';
import { PaymentNotifyHandler } from '../notification/payment-notify.handler';
import { RefundNotifyHandler } from '../notification/refund-notify.handler';
import { PaymentLoggerService } from '../services/payment-logger.service';
import { LockManager } from '../lock/lock.manager';
import { NotificationRecordService } from '../../util/database/mysql/notification_record/notification-record.service';
export declare class PaymentController {
    private readonly paymentService;
    private readonly paymentNotifyHandler;
    private readonly refundNotifyHandler;
    private readonly paymentLogger;
    private readonly lockManager;
    private readonly notificationRecordService;
    private readonly logger;
    constructor(paymentService: PaymentService, paymentNotifyHandler: PaymentNotifyHandler, refundNotifyHandler: RefundNotifyHandler, paymentLogger: PaymentLoggerService, lockManager: LockManager, notificationRecordService: NotificationRecordService);
    createPayment(createPaymentDto: CreatePaymentDto): Promise<{
        orderNo: string;
        paymentUrl?: string;
        qrCode?: string;
        redirectUrl?: string;
        extraData?: any;
    } | {
        success: boolean;
        errorMessage: any;
    }>;
    queryPaymentStatus(outTradeNo: string): Promise<{
        orderNo: string;
        status: string;
        paymentTime?: Date;
        paymentId?: string;
        amount: number;
        channel: string;
    } | {
        success: boolean;
        errorMessage: any;
    }>;
    closePayment(closePaymentDto: ClosePaymentDto): Promise<{
        code: number;
        message: string;
        data: {
            success: boolean;
        };
    }>;
    handlePaymentNotify(channel: string, notifyData: any, req: Request): Promise<"success" | "fail" | {
        code: string;
        message: string;
        success?: undefined;
    } | {
        success: boolean;
        message: any;
        code?: undefined;
    }>;
    handleRefundNotify(channel: string, req: Request, res: Response): Promise<any>;
    paymentReturn(channel: string, query: any, res: Response): Promise<void>;
    wechatNotify(req: Request, res: Response): Promise<void>;
    alipayRefundNotify(notifyData: any, res: Response): Promise<void>;
    wechatRefundNotify(req: Request, res: Response): Promise<void>;
    getOrderDetail(orderNo: string): Promise<any>;
    testWechatRefundNotify(testData: any): Promise<{
        success: boolean;
        message: string;
        result?: undefined;
        mockNotify?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        result: {
            success: boolean;
            message?: string;
        };
        mockNotify: {
            headers: {
                'wechatpay-signature': string;
                'wechatpay-timestamp': string;
                'wechatpay-nonce': string;
                'wechatpay-serial': any;
            };
            body: {
                resource_decoded: {
                    mchid: any;
                    out_trade_no: string;
                    transaction_id: string;
                    out_refund_no: any;
                    refund_id: string;
                    refund_status: string;
                    success_time: string;
                    amount: {
                        total: number;
                        refund: number;
                        payer_total: number;
                        payer_refund: number;
                    };
                    user_received_account: string;
                };
                id: string;
                create_time: string;
                resource_type: string;
                event_type: string;
                summary: string;
                resource: {
                    original_type: string;
                    algorithm: string;
                    ciphertext: string;
                    associated_data: string;
                    nonce: string;
                };
            };
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
        result?: undefined;
        mockNotify?: undefined;
    }>;
}
