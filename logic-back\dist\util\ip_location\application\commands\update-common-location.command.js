"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCommonLocationCommand = void 0;
const ip_address_vo_1 = require("../../domain/value-objects/ip-address.vo");
class UpdateCommonLocationCommand {
    userId;
    ipAddress;
    location;
    sessionId;
    userAgent;
    timestamp;
    constructor(userId, ipAddress, location, sessionId, userAgent) {
        this.userId = userId;
        this.ipAddress = ipAddress;
        this.location = location;
        this.sessionId = sessionId;
        this.userAgent = userAgent;
        this.timestamp = new Date();
    }
    static create(userId, ipAddressString, location, sessionId, userAgent) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new UpdateCommonLocationCommand(userId, ipAddress, location, sessionId, userAgent);
    }
    validate() {
        const errors = [];
        if (this.userId <= 0) {
            errors.push('用户ID必须大于0');
        }
        if (!this.ipAddress.canGeolocate) {
            errors.push('IP地址不支持地理位置解析');
        }
        if (!this.location.isHighQuality && this.location.confidence < 20) {
            errors.push('地理位置数据质量过低');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    getSummary() {
        return `更新用户${this.userId}的常用位置: ${this.location.displayName} (${this.ipAddress.masked})`;
    }
    toJSON() {
        return {
            userId: this.userId,
            ipAddress: this.ipAddress.toJSON(),
            location: this.location.toJSON(),
            sessionId: this.sessionId,
            userAgent: this.userAgent,
            timestamp: this.timestamp.toISOString()
        };
    }
}
exports.UpdateCommonLocationCommand = UpdateCommonLocationCommand;
//# sourceMappingURL=update-common-location.command.js.map