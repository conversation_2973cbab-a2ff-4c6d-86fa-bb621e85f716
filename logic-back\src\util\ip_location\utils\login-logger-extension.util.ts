import { Injectable } from '@nestjs/common';
import { IpLocationApplicationService } from '../application/services/ip-location-application.service';
import { LoginLoggerUtil } from '../../../web/user_login_log/login-logger.util';
import { LoginType, LoginStatus } from '../../database/mysql/user_login_log/entities/user_login_log.entity';
import { LoggerService } from '../../../common/logger/logger.service';

/**
 * 扩展登录日志记录数据接口
 */
export interface ExtendedLoginLogData {
  userId: number;
  loginType: LoginType;
  loginStatus: LoginStatus;
  clientIp: string;
  userAgent?: string;
  deviceInfo?: string;
  sessionId?: string;
  tokenExpireTime?: Date;
  failReason?: string;
  // 新增：是否启用地理位置解析
  enableLocationResolution?: boolean;
  // 新增：是否进行风险评估
  enableRiskAssessment?: boolean;
}

/**
 * 登录日志扩展工具类
 * 在原有登录日志功能基础上，集成IP地理位置解析和风险评估
 */
@Injectable()
export class LoginLoggerExtensionUtil {
  constructor(
    private readonly ipLocationService: IpLocationApplicationService,
    private readonly loggerService: LoggerService
  ) {}

  /**
   * 记录带有地理位置信息的登录成功日志
   * @param data 扩展的登录日志数据
   */
  async logSuccessLoginWithLocation(data: ExtendedLoginLogData): Promise<void> {
    const startTime = Date.now();

    try {
      let locationInfo: {
        country: string;
        province: string;
        city: string;
        isp: string;
        locationSource: string;
        dataQuality: number;
      } | null = null;
      let riskAssessment: {
        riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
        riskReason: string;
      } | null = null;

      // 解析IP地理位置
      if (data.enableLocationResolution !== false && data.clientIp) {
        try {
          const locationResult = await this.ipLocationService.queryIpLocation({
            ip: data.clientIp,
            includeRisk: false
          });
          
          locationInfo = {
            country: locationResult.country,
            province: locationResult.province,
            city: locationResult.city,
            isp: locationResult.isp,
            locationSource: locationResult.dataSource as string,
            dataQuality: locationResult.confidence
          };

          // 更新用户常用登录地统计
          await this.ipLocationService.updateUserCommonLocation(data.userId, {
            country: locationResult.country,
            province: locationResult.province,
            city: locationResult.city,
            isp: locationResult.isp,
            dataSource: locationResult.dataSource as 'ip2region' | 'fallback',
            hasEmptyFields: !locationResult.isHighQuality,
            confidence: locationResult.confidence,
            displayName: locationResult.displayName
          });

        } catch (locationError) {
          this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'resolveLocation', {
            userId: data.userId,
            ip: this.maskIP(data.clientIp)
          }, locationError);
          
          // 位置解析失败时使用默认值
          locationInfo = {
            country: '未知',
            province: '未知',
            city: '未知',
            isp: '未知',
            locationSource: 'fallback',
            dataQuality: 0
          };
        }
      }

      // 进行风险评估
      if (data.enableRiskAssessment && data.clientIp && locationInfo) {
        try {
          const riskResult = await this.ipLocationService.checkLoginRisk({
            userId: data.userId,
            ipAddress: data.clientIp,
            userAgent: data.userAgent,
            sessionId: data.sessionId
          });

          riskAssessment = {
            riskLevel: riskResult.riskAssessment.level,
            riskReason: riskResult.riskAssessment.reason
          };

          // 高风险登录记录安全日志
          if (riskResult.riskAssessment.level === 'HIGH') {
            this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'highRiskLogin', {
              userId: data.userId,
              ip: this.maskIP(data.clientIp),
              location: `${locationInfo.province} ${locationInfo.city}`,
              riskLevel: riskResult.riskAssessment.level,
              riskScore: riskResult.riskAssessment.score,
              factors: riskResult.riskAssessment.factors
            });
          }

        } catch (riskError) {
          this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'assessRisk', {
            userId: data.userId,
            ip: this.maskIP(data.clientIp)
          }, riskError);
          
          // 风险评估失败时使用默认值
          riskAssessment = {
            riskLevel: 'LOW' as const,
            riskReason: '风险评估异常'
          };
        }
      }

      // 构建完整的登录日志数据
      const completeLogData = {
        userId: data.userId,
        loginType: data.loginType,
        clientIp: data.clientIp,
        userAgent: data.userAgent,
        deviceInfo: data.deviceInfo,
        sessionId: data.sessionId,
        tokenExpireTime: data.tokenExpireTime,
        // 地理位置信息
        location: locationInfo ? `${locationInfo.province} ${locationInfo.city}` : undefined,
        // 扩展字段（如果数据库支持）
        ...(locationInfo && {
          country: locationInfo.country,
          province: locationInfo.province,
          city: locationInfo.city,
          isp: locationInfo.isp,
          locationSource: locationInfo.locationSource,
          dataQuality: locationInfo.dataQuality
        }),
        ...(riskAssessment && {
          riskLevel: riskAssessment.riskLevel,
          riskReason: riskAssessment.riskReason
        })
      };

      // 调用原有的登录日志记录方法
      await LoginLoggerUtil.logSuccessLogin(completeLogData);

      this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'logSuccessLoginWithLocation', {
        userId: data.userId,
        ip: this.maskIP(data.clientIp),
        location: locationInfo ? `${locationInfo.province} ${locationInfo.city}` : '未知',
        riskLevel: riskAssessment?.riskLevel || 'UNKNOWN',
        responseTime: Date.now() - startTime
      });

    } catch (error) {
      this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'logSuccessLoginWithLocation', {
        userId: data.userId,
        ip: this.maskIP(data.clientIp),
        responseTime: Date.now() - startTime
      }, error);
      
      // 即使扩展功能失败，也要记录基础登录日志
      try {
        await LoginLoggerUtil.logSuccessLogin({
          userId: data.userId,
          loginType: data.loginType,
          clientIp: data.clientIp,
          userAgent: data.userAgent,
          deviceInfo: data.deviceInfo,
          sessionId: data.sessionId
        });
      } catch (fallbackError) {
        this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'fallbackLogging', {
          userId: data.userId
        }, fallbackError);
      }
    }
  }

  /**
   * 记录带有地理位置信息的登录失败日志
   * @param data 扩展的登录日志数据
   */
  async logFailedLoginWithLocation(data: ExtendedLoginLogData): Promise<void> {
    try {
      let locationInfo: {
        country: string;
        province: string;
        city: string;
        isp: string;
      } | null = null;

      // 对于失败的登录，也记录地理位置信息用于安全分析
      if (data.enableLocationResolution !== false && data.clientIp) {
        try {
          const locationResult = await this.ipLocationService.queryIpLocation({
            ip: data.clientIp,
            includeRisk: false
          });
          
          locationInfo = {
            country: locationResult.country,
            province: locationResult.province,
            city: locationResult.city,
            isp: locationResult.isp
          };

        } catch (locationError) {
          // 位置解析失败时忽略，不影响失败日志记录
          locationInfo = null;
        }
      }

      // 构建登录失败日志数据
      const failLogData = {
        userId: data.userId,
        loginType: data.loginType,
        clientIp: data.clientIp,
        userAgent: data.userAgent,
        failReason: data.failReason || '登录失败'
      };

      // 调用原有的登录失败日志记录方法
      await LoginLoggerUtil.logFailedLogin(failLogData);

      // 记录可疑的失败登录（来自异常位置的失败尝试）
      if (locationInfo && (locationInfo.country !== '中国' || data.failReason?.includes('密码错误'))) {
        this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'suspiciousFailedLogin', {
          userId: data.userId,
          ip: this.maskIP(data.clientIp),
          location: `${locationInfo.province} ${locationInfo.city}`,
          failReason: data.failReason
        });
      }

    } catch (error) {
      this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'logFailedLoginWithLocation', {
        userId: data.userId,
        ip: this.maskIP(data.clientIp)
      }, error);
      
      // 确保基础失败日志能够记录
      try {
        await LoginLoggerUtil.logFailedLogin({
          userId: data.userId,
          loginType: data.loginType,
          clientIp: data.clientIp,
          userAgent: data.userAgent,
          failReason: data.failReason || '登录失败'
        });
      } catch (fallbackError) {
        this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'fallbackFailedLogging', {
          userId: data.userId
        }, fallbackError);
      }
    }
  }

  /**
   * 检查是否需要额外的安全验证
   * @param userId 用户ID
   * @param clientIp 客户端IP
   * @returns 是否需要额外验证
   */
  async checkNeedAdditionalVerification(userId: number, clientIp: string): Promise<{
    needVerification: boolean;
    reason: string;
    recommendedMethods: string[];
  }> {
    try {
      const riskResult = await this.ipLocationService.checkLoginRisk({
        userId,
        ipAddress: clientIp
      });

      return {
        needVerification: riskResult.riskAssessment.needVerification,
        reason: riskResult.riskAssessment.reason,
        recommendedMethods: riskResult.riskAssessment.recommendedActions
      };

    } catch (error) {
      this.loggerService.logBusiness('LoginLoggerExtensionUtil', 'checkNeedAdditionalVerification', {
        userId,
        ip: this.maskIP(clientIp)
      }, error);
      
      // 评估失败时默认不需要额外验证
      return {
        needVerification: false,
        reason: '风险评估异常，默认通过',
        recommendedMethods: []
      };
    }
  }

  /**
   * IP地址脱敏
   * @param ip IP地址
   * @returns 脱敏后的IP地址
   */
  private maskIP(ip: string): string {
    if (!ip) return '未知IP';
    
    if (ip.includes(':')) {
      // IPv6脱敏
      const parts = ip.split(':');
      if (parts.length >= 4) {
        return `${parts.slice(0, 4).join(':')}:****`;
      }
      return '****:****:****:****';
    } else {
      // IPv4脱敏
      const parts = ip.split('.');
      if (parts.length === 4) {
        return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
      }
      return '***.***.***.**';
    }
  }
}
