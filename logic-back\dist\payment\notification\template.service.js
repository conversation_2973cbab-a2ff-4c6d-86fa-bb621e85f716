"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PaymentTemplateService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentTemplateService = void 0;
const common_1 = require("@nestjs/common");
const notify_service_1 = require("./notify.service");
const fs = require("fs");
const path = require("path");
const Handlebars = require("handlebars");
let PaymentTemplateService = PaymentTemplateService_1 = class PaymentTemplateService {
    logger = new common_1.Logger(PaymentTemplateService_1.name);
    templates = new Map();
    templateDir;
    constructor() {
        this.templateDir = path.resolve(process.cwd(), './templates/payment');
        this.loadTemplates();
    }
    loadTemplates() {
        try {
            if (!fs.existsSync(this.templateDir)) {
                fs.mkdirSync(this.templateDir, { recursive: true });
                this.logger.log(`创建模板目录: ${this.templateDir}`);
                this.createDefaultTemplates();
            }
            this.registerHelpers();
            const files = fs.readdirSync(this.templateDir);
            for (const file of files) {
                if (file.endsWith('.hbs')) {
                    const templateName = file.replace('.hbs', '');
                    const templatePath = path.join(this.templateDir, file);
                    const templateContent = fs.readFileSync(templatePath, 'utf8');
                    try {
                        const template = Handlebars.compile(templateContent);
                        this.templates.set(templateName, template);
                        this.logger.log(`已加载模板: ${templateName}`);
                    }
                    catch (error) {
                        this.logger.error(`编译模板 ${templateName} 失败: ${error.message}`);
                    }
                }
            }
            this.logger.log(`已加载 ${this.templates.size} 个模板`);
        }
        catch (error) {
            this.logger.error(`加载模板失败: ${error.message}`, error.stack);
        }
    }
    createDefaultTemplates() {
        const defaultTemplates = {
            [notify_service_1.NotificationType.PAYMENT_SUCCESS]: `
<h1>支付成功通知</h1>
<p>订单号: {{outTradeNo}}</p>
<p>交易号: {{paymentId}}</p>
<p>支付金额: {{amount}}</p>
<p>支付渠道: {{channel}}</p>
<p>商品名称: {{subject}}</p>
<p>支付时间: {{payTime}}</p>
      `,
            [notify_service_1.NotificationType.PAYMENT_FAIL]: `
<h1>支付失败通知</h1>
<p>订单号: {{outTradeNo}}</p>
<p>失败原因: {{reason}}</p>
<p>支付渠道: {{channel}}</p>
<p>商品名称: {{subject}}</p>
<p>支付金额: {{amount}}</p>
<p>失败时间: {{failTime}}</p>
      `,
            [notify_service_1.NotificationType.REFUND_SUCCESS]: `
<h1>退款成功通知</h1>
<p>退款单号: {{outRefundNo}}</p>
<p>退款交易号: {{refundId}}</p>
<p>退款金额: {{amount}}</p>
<p>支付渠道: {{channel}}</p>
<p>退款时间: {{refundTime}}</p>
      `,
            [notify_service_1.NotificationType.REFUND_FAIL]: `
<h1>退款失败通知</h1>
<p>退款单号: {{outRefundNo}}</p>
<p>失败原因: {{reason}}</p>
<p>支付渠道: {{channel}}</p>
<p>失败时间: {{failTime}}</p>
      `,
        };
        for (const [templateName, content] of Object.entries(defaultTemplates)) {
            const templatePath = path.join(this.templateDir, `${templateName}.hbs`);
            fs.writeFileSync(templatePath, content.trim());
            this.logger.log(`创建默认模板: ${templateName}`);
        }
    }
    registerHelpers() {
        Handlebars.registerHelper('formatDate', function (date, format) {
            if (!date)
                return '';
            const d = new Date(date);
            return d.toLocaleString();
        });
        Handlebars.registerHelper('formatAmount', function (amount) {
            if (amount === undefined || amount === null)
                return '';
            return amount.toFixed(2);
        });
        Handlebars.registerHelper('ifCond', function (v1, operator, v2, options) {
            switch (operator) {
                case '==':
                    return (v1 == v2) ? options.fn(this) : options.inverse(this);
                case '===':
                    return (v1 === v2) ? options.fn(this) : options.inverse(this);
                case '!=':
                    return (v1 != v2) ? options.fn(this) : options.inverse(this);
                case '!==':
                    return (v1 !== v2) ? options.fn(this) : options.inverse(this);
                case '<':
                    return (v1 < v2) ? options.fn(this) : options.inverse(this);
                case '<=':
                    return (v1 <= v2) ? options.fn(this) : options.inverse(this);
                case '>':
                    return (v1 > v2) ? options.fn(this) : options.inverse(this);
                case '>=':
                    return (v1 >= v2) ? options.fn(this) : options.inverse(this);
                default:
                    return options.inverse(this);
            }
        });
    }
    async renderTemplate(type, data) {
        try {
            const template = this.templates.get(type);
            if (!template) {
                this.logger.warn(`未找到模板: ${type}，使用默认渲染`);
                return JSON.stringify(data, null, 2);
            }
            return template(data);
        }
        catch (error) {
            this.logger.error(`渲染模板失败: ${error.message}`, error.stack);
            return JSON.stringify(data, null, 2);
        }
    }
    getTemplateNames() {
        return Array.from(this.templates.keys());
    }
    async updateTemplate(name, content) {
        try {
            const templatePath = path.join(this.templateDir, `${name}.hbs`);
            fs.writeFileSync(templatePath, content);
            const template = Handlebars.compile(content);
            this.templates.set(name, template);
            this.logger.log(`更新模板成功: ${name}`);
            return true;
        }
        catch (error) {
            this.logger.error(`更新模板失败: ${error.message}`, error.stack);
            return false;
        }
    }
};
exports.PaymentTemplateService = PaymentTemplateService;
exports.PaymentTemplateService = PaymentTemplateService = PaymentTemplateService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PaymentTemplateService);
//# sourceMappingURL=template.service.js.map