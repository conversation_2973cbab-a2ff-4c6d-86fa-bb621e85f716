"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityTag = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const activity_entity_1 = require("../../activity/entities/activity.entity");
let ActivityTag = class ActivityTag {
    id;
    activityId;
    tagId;
    isDelete;
    createTime;
    updateTime;
    activity;
};
exports.ActivityTag = ActivityTag;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], ActivityTag.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动ID' }),
    (0, swagger_1.ApiProperty)({ description: '活动ID' }),
    __metadata("design:type", Number)
], ActivityTag.prototype, "activityId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '标签ID' }),
    (0, swagger_1.ApiProperty)({ description: '标签ID' }),
    __metadata("design:type", Number)
], ActivityTag.prototype, "tagId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否删除', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false }),
    __metadata("design:type", Boolean)
], ActivityTag.prototype, "isDelete", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], ActivityTag.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], ActivityTag.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => activity_entity_1.Activity, activity => activity.activityTags),
    (0, typeorm_1.JoinColumn)({ name: 'activityId' }),
    __metadata("design:type", activity_entity_1.Activity)
], ActivityTag.prototype, "activity", void 0);
exports.ActivityTag = ActivityTag = __decorate([
    (0, typeorm_1.Entity)('activity_tag')
], ActivityTag);
//# sourceMappingURL=activity_tag.entity.js.map