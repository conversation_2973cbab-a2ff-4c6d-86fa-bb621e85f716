import { Activity } from '../../activity/entities/activity.entity';
import { UserWorkInfo } from '../../user_work_info/entities/user_work_info.entity';
export declare class ActivityWork {
    id: number;
    activityId: number;
    workId: number;
    userId: number;
    workTitle: string;
    workCover: string;
    workDesc: string;
    isSelected: number;
    isWinner: number;
    awardId: number;
    awardName: string;
    isShow: number;
    creatorId: number;
    createTime: Date;
    updateTime: Date;
    isDelete: boolean;
    activity: Activity;
    userWorkInfo: UserWorkInfo;
}
