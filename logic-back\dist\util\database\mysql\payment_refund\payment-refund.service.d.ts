import { Repository } from 'typeorm';
import { PaymentRefund } from './entities/payment-refund.entity';
import { CreatePaymentRefundDto, RefundStatus } from './dto/create-payment-refund.dto';
import { UpdatePaymentRefundDto } from './dto/update-payment-refund.dto';
export declare class PaymentRefundService {
    private readonly paymentRefundRepository;
    constructor(paymentRefundRepository: Repository<PaymentRefund>);
    create(createPaymentRefundDto: CreatePaymentRefundDto): Promise<PaymentRefund>;
    findAll(): Promise<PaymentRefund[]>;
    findOne(id: string): Promise<PaymentRefund>;
    findByBusinessRefundId(businessRefundId: string): Promise<PaymentRefund>;
    findByPaymentOrderId(paymentOrderId: string): Promise<PaymentRefund[]>;
    findByChannelRefundId(channelRefundId: string): Promise<PaymentRefund>;
    findByUserId(userId: string): Promise<PaymentRefund[]>;
    findByStatus(status: RefundStatus): Promise<PaymentRefund[]>;
    update(id: string, updatePaymentRefundDto: UpdatePaymentRefundDto): Promise<PaymentRefund>;
    updateStatus(id: string, status: RefundStatus, result?: any): Promise<PaymentRefund>;
    updateNotifyData(id: string, notifyData: any): Promise<PaymentRefund>;
    remove(id: string): Promise<void>;
}
