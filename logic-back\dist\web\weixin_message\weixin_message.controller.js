"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WeixinMessageController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinMessageController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const receive_message_1 = require("../utils/receive_message");
const weixin_msg_util_1 = require("../utils/weixin_msg_util");
const weixin_config_service_1 = require("../config/weixin-config.service");
let WeixinMessageController = WeixinMessageController_1 = class WeixinMessageController {
    receiveMessageService;
    weixinMsgUtilService;
    weixinConfigService;
    logger = new common_1.Logger(WeixinMessageController_1.name);
    constructor(receiveMessageService, weixinMsgUtilService, weixinConfigService) {
        this.receiveMessageService = receiveMessageService;
        this.weixinMsgUtilService = weixinMsgUtilService;
        this.weixinConfigService = weixinConfigService;
    }
    async verifyUrl(signature, timestamp, nonce, echostr, encryptType, msgSignature, res) {
        try {
            this.logger.debug(`收到微信验证请求: signature=${signature}, timestamp=${timestamp}, nonce=${nonce}, echostr=${echostr}, encrypt_type=${encryptType}, msg_signature=${msgSignature}`);
            const token = this.weixinConfigService.getWeixinConfig().token;
            this.logger.debug(`验证URL请求参数: signature=${signature}, timestamp=${timestamp}, nonce=${nonce}`);
            this.logger.debug(`使用的token: ${token}`);
            if (encryptType === 'aes' && msgSignature) {
                this.logger.debug(`使用加密模式验证: msg_signature=${msgSignature}`);
                try {
                    const decryptedEchostr = this.weixinMsgUtilService.decrypt(echostr);
                    this.logger.debug(`解密后的echostr: ${decryptedEchostr}`);
                    const isValid = this.weixinMsgUtilService.verifyEncryptSignature(msgSignature, timestamp, nonce, echostr);
                    if (isValid) {
                        this.logger.debug(`加密验证通过，返回解密后的echostr: ${decryptedEchostr}`);
                        res.type('text/plain');
                        return res.end(decryptedEchostr);
                    }
                    else {
                        this.logger.error('加密签名验证失败');
                        return res.status(common_1.HttpStatus.UNAUTHORIZED).end('签名验证失败');
                    }
                }
                catch (decryptError) {
                    this.logger.error(`解密echostr失败: ${decryptError.message}`);
                    return res.status(common_1.HttpStatus.BAD_REQUEST).end('解密失败');
                }
            }
            else {
                const isValid = this.weixinMsgUtilService.verifySignature(signature, timestamp, nonce, token);
                this.logger.debug(`明文模式签名验证结果: ${isValid ? '通过' : '失败'}`);
                if (isValid) {
                    this.logger.debug(`验证通过，返回echostr: ${echostr}`);
                    res.type('text/plain');
                    return res.end(String(echostr));
                }
                else {
                    this.logger.error('签名验证失败');
                    return res.status(common_1.HttpStatus.UNAUTHORIZED).end('签名验证失败');
                }
            }
        }
        catch (error) {
            this.logger.error(`验证URL异常: ${error.message}`, error.stack);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).end('服务器错误');
        }
    }
    async receiveMessage(req, signature, timestamp, nonce, encryptType, msgSignature, res) {
        try {
            const token = this.weixinConfigService.getWeixinConfig().token;
            const xmlBody = req.body;
            this.logger.debug(`接收消息请求参数: signature=${signature}, timestamp=${timestamp}, nonce=${nonce}, encrypt_type=${encryptType}, msg_signature=${msgSignature}`);
            this.logger.debug(`使用的token: ${token}`);
            this.logger.debug(`接收到的XML数据: ${typeof xmlBody === 'string' ? xmlBody : JSON.stringify(xmlBody)}`);
            let messageToProcess = xmlBody;
            let isEncrypted = false;
            if (encryptType === 'aes' && msgSignature) {
                this.logger.debug(`检测到加密消息，进行解密`);
                isEncrypted = true;
                try {
                    messageToProcess = await this.weixinMsgUtilService.decryptMsg(xmlBody, timestamp, nonce, msgSignature);
                    this.logger.debug(`解密后的消息: ${messageToProcess}`);
                }
                catch (decryptError) {
                    this.logger.error(`解密消息失败: ${decryptError.message}`);
                    return res.end('');
                }
            }
            else {
                const isValid = this.weixinMsgUtilService.verifySignature(signature, timestamp, nonce, token);
                this.logger.debug(`明文模式签名验证结果: ${isValid ? '通过' : '失败'}`);
                if (!isValid) {
                    this.logger.error('签名验证失败');
                    return res.status(common_1.HttpStatus.UNAUTHORIZED).end('签名验证失败');
                }
            }
            const responseMsg = await this.receiveMessageService.handleMessage(messageToProcess);
            let finalResponseMsg = responseMsg;
            if (isEncrypted && responseMsg) {
                this.logger.debug(`加密回复消息`);
                const encryptedResponse = this.weixinMsgUtilService.encryptMsg(responseMsg, timestamp, nonce);
                finalResponseMsg = encryptedResponse.encrypt_msg;
                this.logger.debug(`加密后的回复消息: ${finalResponseMsg}`);
            }
            this.logger.debug(`回复消息: ${finalResponseMsg}`);
            res.type('text/xml');
            return res.end(finalResponseMsg || '');
        }
        catch (error) {
            this.logger.error(`处理微信消息错误: ${error.message}`, error.stack);
            return res.end('');
        }
    }
};
exports.WeixinMessageController = WeixinMessageController;
__decorate([
    (0, common_1.Get)('message'),
    (0, swagger_1.ApiOperation)({ summary: '微信服务器URL认证' }),
    (0, swagger_1.ApiQuery)({ name: 'signature', description: '微信加密签名' }),
    (0, swagger_1.ApiQuery)({ name: 'timestamp', description: '时间戳' }),
    (0, swagger_1.ApiQuery)({ name: 'nonce', description: '随机数' }),
    (0, swagger_1.ApiQuery)({ name: 'echostr', description: '随机字符串' }),
    (0, swagger_1.ApiQuery)({ name: 'encrypt_type', description: '加密类型' }),
    (0, swagger_1.ApiQuery)({ name: 'msg_signature', description: '消息签名' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '认证成功返回echostr' }),
    __param(0, (0, common_1.Query)('signature')),
    __param(1, (0, common_1.Query)('timestamp')),
    __param(2, (0, common_1.Query)('nonce')),
    __param(3, (0, common_1.Query)('echostr')),
    __param(4, (0, common_1.Query)('encrypt_type')),
    __param(5, (0, common_1.Query)('msg_signature')),
    __param(6, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], WeixinMessageController.prototype, "verifyUrl", null);
__decorate([
    (0, common_1.Post)('message'),
    (0, swagger_1.ApiOperation)({ summary: '接收微信消息和事件' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '处理微信消息' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('signature')),
    __param(2, (0, common_1.Query)('timestamp')),
    __param(3, (0, common_1.Query)('nonce')),
    __param(4, (0, common_1.Query)('encrypt_type')),
    __param(5, (0, common_1.Query)('msg_signature')),
    __param(6, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], WeixinMessageController.prototype, "receiveMessage", null);
exports.WeixinMessageController = WeixinMessageController = WeixinMessageController_1 = __decorate([
    (0, swagger_1.ApiTags)('微信公众号消息'),
    (0, common_1.Controller)('weixin'),
    __metadata("design:paramtypes", [receive_message_1.ReceiveMessageService,
        weixin_msg_util_1.WeixinMsgUtilService,
        weixin_config_service_1.WeixinConfigService])
], WeixinMessageController);
//# sourceMappingURL=weixin_message.controller.js.map