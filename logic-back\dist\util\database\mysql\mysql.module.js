"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MysqlModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const database_config_service_1 = require("../config/database-config.service");
const yaml_service_1 = require("../../yaml/yaml.service");
const announcement_read_record_module_1 = require("./announcement_read_record/announcement_read_record.module");
const announcement_module_1 = require("./announcement/announcement.module");
const audio_train_model_module_1 = require("./audio_train_model/audio_train_model.module");
const block_permissions_module_1 = require("./block_permissions/block_permissions.module");
const block_module_1 = require("./block/block.module");
const user_class_module_1 = require("./user_class/user_class.module");
const doc_module_1 = require("./doc/doc.module");
const extension_permissions_module_1 = require("./extension_permissions/extension_permissions.module");
const extensions_module_1 = require("./extensions/extensions.module");
const user_image_enhance_module_1 = require("./user_image_enhance/user_image_enhance.module");
const user_image_info_module_1 = require("./user_image_info/user_image_info.module");
const user_image_segment_module_1 = require("./user_image_segment/user_image_segment.module");
const image_train_model_module_1 = require("./image_train_model/image_train_model.module");
const user_info_module_1 = require("./user_info/user_info.module");
const key_package_record_module_1 = require("./key_package_record/key_package_record.module");
const package_info_module_1 = require("./package_info/package_info.module");
const user_password_reset_request_module_1 = require("./user_password_reset_request/user_password_reset_request.module");
const user_points_offline_message_module_1 = require("./user_points_offline_message/user_points_offline_message.module");
const user_points_permission_module_1 = require("./user_points_permission/user_points_permission.module");
const user_points_module_1 = require("./user_points/user_points.module");
const pose_train_model_module_1 = require("./pose_train_model/pose_train_model.module");
const user_report_module_1 = require("./user_report/user_report.module");
const role_permission_templates_module_1 = require("./role_permission_templates/role_permission_templates.module");
const user_role_permission_module_1 = require("./user_role_permission/user_role_permission.module");
const user_role_relation_module_1 = require("./user_role_relation/user_role_relation.module");
const role_template_block_permission_module_1 = require("./role_template_block_permission/role_template_block_permission.module");
const role_template_extension_permission_module_1 = require("./role_template_extension_permission/role_template_extension_permission.module");
const role_template_folder_join_template_module_1 = require("./role_template_folder_join_template/role_template_folder_join_template.module");
const role_template_folder_module_1 = require("./role_template_folder/role_template_folder.module");
const user_role_module_1 = require("./user_role/user_role.module");
const user_school_relation_module_1 = require("./user_school_relation/user_school_relation.module");
const user_school_module_1 = require("./user_school/user_school.module");
const user_student_module_1 = require("./user_student/user_student.module");
const teacher_task_assignment_module_1 = require("./teacher_task_assignment/teacher_task_assignment.module");
const teacher_task_module_1 = require("./teacher_task/teacher_task.module");
const user_join_role_module_1 = require("./user_join_role/user_join_role.module");
const user_package_module_1 = require("./user_package/user_package.module");
const user_voice_info_module_1 = require("./user_voice_info/user_voice_info.module");
const user_work_info_module_1 = require("./user_work_info/user_work_info.module");
const user_work_like_module_1 = require("./user_work_like/user_work_like.module");
const work_model_module_1 = require("./work_model/work_model.module");
const space_carousel_map_module_1 = require("./space_carousel_map/space_carousel_map.module");
const activity_tag_module_1 = require("./activity_tag/activity_tag.module");
const activity_work_module_1 = require("./activity_work/activity_work.module");
const activity_module_1 = require("./activity/activity.module");
const activity_audit_module_1 = require("./activity_audit/activity_audit.module");
const announcement_audit_module_1 = require("./announcement_audit/announcement_audit.module");
const carousel_audit_module_1 = require("./carousel_audit/carousel_audit.module");
const participation_audit_module_1 = require("./participation_audit/participation_audit.module");
const work_audit_module_1 = require("./work_audit/work_audit.module");
const tag_module_1 = require("./tag/tag.module");
const table_joing_module_1 = require("./table_joing/table_joing.module");
const web_weixin_scan_module_1 = require("./web_weixin_scan/web_weixin_scan.module");
const task_self_assessment_item_module_1 = require("./task_self_assessment_item/task_self_assessment_item.module");
const student_self_assessment_submission_module_1 = require("./student_self_assessment_submission/student_self_assessment_submission.module");
const payment_order_module_1 = require("./payment_order/payment-order.module");
const payment_refund_module_1 = require("./payment_refund/payment-refund.module");
const package_order_module_1 = require("./package_order/package-order.module");
const user_login_log_module_1 = require("./user_login_log/user_login_log.module");
const user_common_location_module_1 = require("./user_common_location/user_common_location.module");
let MysqlModule = class MysqlModule {
};
exports.MysqlModule = MysqlModule;
exports.MysqlModule = MysqlModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                inject: [yaml_service_1.YamlService],
                useFactory: async (yamlService) => {
                    const configService = new database_config_service_1.DatabaseConfigService(yamlService);
                    return configService.getMysqlConfig();
                },
            }),
            announcement_read_record_module_1.AnnouncementReadRecordModule,
            announcement_module_1.AnnouncementModule,
            audio_train_model_module_1.AudioTrainModelModule,
            block_permissions_module_1.BlockPermissionsModule,
            block_module_1.BlockModule,
            user_class_module_1.UserClassModule,
            doc_module_1.DocModule,
            extension_permissions_module_1.ExtensionPermissionsModule,
            extensions_module_1.ExtensionsModule,
            user_image_enhance_module_1.UserImageEnhanceModule,
            user_image_info_module_1.UserImageInfoModule,
            user_image_segment_module_1.UserImageSegmentModule,
            image_train_model_module_1.ImageTrainModelModule,
            user_info_module_1.UserInfoModule,
            key_package_record_module_1.KeyPackageRecordModule,
            package_info_module_1.PackageInfoModule,
            user_password_reset_request_module_1.UserPasswordResetRequestModule,
            user_points_offline_message_module_1.UserPointsOfflineMessageModule,
            user_points_permission_module_1.UserPointsPermissionModule,
            user_points_module_1.UserPointsModule,
            pose_train_model_module_1.PoseTrainModelModule,
            user_report_module_1.UserReportModule,
            role_permission_templates_module_1.RolePermissionTemplatesModule,
            user_role_permission_module_1.UserRolePermissionModule,
            user_role_relation_module_1.UserRoleRelationModule,
            role_template_block_permission_module_1.RoleTemplateBlockPermissionModule,
            role_template_extension_permission_module_1.RoleTemplateExtensionPermissionModule,
            role_template_folder_join_template_module_1.RoleTemplateFolderJoinTemplateModule,
            role_template_folder_module_1.RoleTemplateFolderModule,
            user_role_module_1.UserRoleModule,
            user_school_relation_module_1.UserSchoolRelationModule,
            user_school_module_1.UserSchoolModule,
            user_student_module_1.UserStudentModule,
            teacher_task_assignment_module_1.TeacherTaskAssignmentModule,
            teacher_task_module_1.TeacherTaskModule,
            user_join_role_module_1.UserJoinRoleModule,
            user_package_module_1.UserPackageModule,
            user_voice_info_module_1.UserVoiceInfoModule,
            user_work_info_module_1.UserWorkInfoModule,
            user_work_like_module_1.UserWorkLikeModule,
            work_model_module_1.WorkModelModule,
            space_carousel_map_module_1.SpaceCarouselMapModule,
            activity_tag_module_1.ActivityTagModule,
            activity_work_module_1.ActivityWorkModule,
            activity_module_1.ActivityModule,
            activity_audit_module_1.ActivityAuditModule,
            announcement_audit_module_1.AnnouncementAuditModule,
            carousel_audit_module_1.CarouselAuditModule,
            participation_audit_module_1.ParticipationAuditModule,
            work_audit_module_1.WorkAuditModule,
            tag_module_1.TagModule,
            table_joing_module_1.TableJoingModule,
            web_weixin_scan_module_1.WebWeixinScanModule,
            task_self_assessment_item_module_1.TaskSelfAssessmentItemModule,
            student_self_assessment_submission_module_1.StudentSelfAssessmentSubmissionModule,
            payment_order_module_1.PaymentOrderModule,
            payment_refund_module_1.PaymentRefundModule,
            package_order_module_1.PackageOrderModule,
            user_login_log_module_1.UserLoginLogModule,
            user_common_location_module_1.UserCommonLocationModule,
        ],
        providers: [database_config_service_1.DatabaseConfigService],
    })
], MysqlModule);
//# sourceMappingURL=mysql.module.js.map