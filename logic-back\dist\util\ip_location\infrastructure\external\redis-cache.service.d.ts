import { Cache } from 'cache-manager';
import { RedisService } from '../../../database/redis/redis.service';
import { LoggerService } from '../../../../common/logger/logger.service';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { RiskScore } from '../../domain/value-objects/risk-score.vo';
interface CacheStats {
    hits: number;
    misses: number;
    hitRate: number;
    totalRequests: number;
}
export declare class RedisCacheService {
    private cacheManager;
    private readonly redisService;
    private readonly logger;
    private readonly CACHE_PREFIX;
    private readonly DEFAULT_TTL;
    private readonly CACHE_VERSION;
    private stats;
    constructor(cacheManager: Cache, redisService: RedisService, logger: LoggerService);
    cacheLocation(ip: string, location: GeographicLocation, ttl?: number): Promise<void>;
    getLocation(ip: string): Promise<GeographicLocation | null>;
    cacheRiskAssessment(userId: number, ip: string, riskScore: RiskScore, ttl?: number): Promise<void>;
    getRiskAssessment(userId: number, ip: string): Promise<RiskScore | null>;
    cacheUserStats(userId: number, days: number, stats: any, ttl?: number): Promise<void>;
    getUserStats(userId: number, days: number): Promise<any | null>;
    deleteLocationCache(ip: string): Promise<void>;
    deleteUserCache(userId: number): Promise<void>;
    clearAllCache(): Promise<void>;
    getCacheStats(): CacheStats;
    resetStats(): void;
    private buildLocationKey;
    private buildRiskKey;
    private buildUserStatsKey;
    private isValidCacheItem;
    private updateHitRate;
}
export {};
