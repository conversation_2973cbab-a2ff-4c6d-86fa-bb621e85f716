import { OnModuleInit } from '@nestjs/common';
import { PaymentConfigService } from '../config/payment-config.service';
import { PaymentSignatureService } from './payment-signature.service';
export declare class PlatformCertificateService implements OnModuleInit {
    private readonly configService;
    private readonly signatureService;
    private readonly logger;
    private certificates;
    private certificateCacheDir;
    constructor(configService: PaymentConfigService, signatureService: PaymentSignatureService);
    onModuleInit(): Promise<void>;
    private ensureCacheDirExists;
    private loadCachedCertificates;
    private shouldUpdateCertificates;
    fetchAndUpdateCertificates(): Promise<boolean>;
    private decryptCertificate;
    getCertificate(serialNo: string): string | null;
    getLatestCertificate(): {
        serialNo: string;
        certificate: string;
    } | null;
}
