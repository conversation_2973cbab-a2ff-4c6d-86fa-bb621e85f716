"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const payment_order_entity_1 = require("../util/database/mysql/payment_order/entities/payment-order.entity");
const lock_manager_1 = require("./lock/lock.manager");
const distributed_lock_1 = require("./lock/distributed.lock");
const optimistic_lock_1 = require("./lock/optimistic.lock");
const pessimistic_lock_1 = require("./lock/pessimistic.lock");
const payment_config_service_1 = require("./config/payment-config.service");
const payment_service_1 = require("./services/payment.service");
const payment_controller_1 = require("./controllers/payment.controller");
const alipay_strategy_1 = require("./strategies/alipay.strategy");
const wechat_pay_strategy_1 = require("./strategies/wechat-pay.strategy");
const payment_config_1 = require("./config/payment.config");
const redis_module_1 = require("../util/database/redis/redis.module");
const database_config_module_1 = require("../util/database/config/database-config.module");
const yaml_module_1 = require("../util/yaml/yaml.module");
const record_helper_service_1 = require("./services/record-helper.service");
const payment_record_module_1 = require("../util/database/mysql/payment_record/payment-record.module");
const payment_signature_service_1 = require("./security/payment-signature.service");
const platform_certificate_service_1 = require("./security/platform-certificate.service");
const template_service_1 = require("./notification/template.service");
const notify_service_1 = require("./notification/notify.service");
const payment_notify_handler_1 = require("./notification/payment-notify.handler");
const refund_notify_handler_1 = require("./notification/refund-notify.handler");
const notification_record_module_1 = require("../util/database/mysql/notification_record/notification-record.module");
const payment_log_module_1 = require("../util/database/mysql/payment_log/payment-log.module");
const payment_logger_service_1 = require("./services/payment-logger.service");
const refund_service_1 = require("./services/refund.service");
const refund_controller_1 = require("./controllers/refund.controller");
const payment_order_module_1 = require("../util/database/mysql/payment_order/payment-order.module");
const payment_refund_module_1 = require("../util/database/mysql/payment_refund/payment-refund.module");
const web_socket_module_1 = require("../util/web_socket/web_socket.module");
let PaymentModule = class PaymentModule {
};
exports.PaymentModule = PaymentModule;
exports.PaymentModule = PaymentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([payment_order_entity_1.PaymentOrder]),
            config_1.ConfigModule.forRoot({
                load: [() => ({ payment: payment_config_1.default })],
            }),
            redis_module_1.RedisModule,
            database_config_module_1.DatabaseConfigModule,
            yaml_module_1.YamlModule,
            payment_record_module_1.PaymentRecordModule,
            notification_record_module_1.NotificationRecordModule,
            payment_log_module_1.PaymentLogModule,
            payment_order_module_1.PaymentOrderModule,
            payment_refund_module_1.PaymentRefundModule,
            web_socket_module_1.WebSocketModule,
        ],
        controllers: [
            payment_controller_1.PaymentController,
            refund_controller_1.RefundController,
        ],
        providers: [
            payment_service_1.PaymentService,
            refund_service_1.RefundService,
            alipay_strategy_1.AlipayStrategy,
            wechat_pay_strategy_1.WechatPayStrategy,
            lock_manager_1.LockManager,
            optimistic_lock_1.OptimisticLock,
            pessimistic_lock_1.PessimisticLock,
            distributed_lock_1.DistributedLock,
            payment_config_service_1.PaymentConfigService,
            record_helper_service_1.RecordHelperService,
            payment_signature_service_1.PaymentSignatureService,
            platform_certificate_service_1.PlatformCertificateService,
            template_service_1.PaymentTemplateService,
            notify_service_1.NotifyService,
            payment_notify_handler_1.PaymentNotifyHandler,
            refund_notify_handler_1.RefundNotifyHandler,
            payment_logger_service_1.PaymentLoggerService,
        ],
        exports: [
            payment_service_1.PaymentService,
            refund_service_1.RefundService,
            payment_signature_service_1.PaymentSignatureService,
            platform_certificate_service_1.PlatformCertificateService,
            template_service_1.PaymentTemplateService,
            notify_service_1.NotifyService,
            payment_notify_handler_1.PaymentNotifyHandler,
            refund_notify_handler_1.RefundNotifyHandler,
            lock_manager_1.LockManager,
            record_helper_service_1.RecordHelperService,
            payment_logger_service_1.PaymentLoggerService,
        ],
    })
], PaymentModule);
//# sourceMappingURL=payment.module.js.map