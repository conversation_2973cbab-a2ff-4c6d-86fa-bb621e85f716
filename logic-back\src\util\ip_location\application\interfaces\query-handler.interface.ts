/**
 * 查询处理器接口
 * 定义查询处理的统一规范
 */
export interface IQueryHandler<TQuery, TResult> {
  /**
   * 处理查询
   * @param query 要处理的查询
   * @returns 查询结果
   */
  handle(query: TQuery): Promise<TResult>;

  /**
   * 验证查询是否可以处理
   * @param query 要验证的查询
   * @returns 验证结果
   */
  canHandle(query: TQuery): boolean;

  /**
   * 获取处理器名称
   */
  getHandlerName(): string;

  /**
   * 获取缓存TTL（秒）
   * @param query 查询对象
   * @returns 缓存时间，返回0表示不缓存
   */
  getCacheTTL?(query: TQuery): number;
}

/**
 * 查询结果接口
 */
export interface QueryResult<TData> {
  success: boolean;
  data?: TData;
  message?: string;
  errors?: string[];
  timestamp: Date;
  executionTime?: number;
  fromCache?: boolean;
  cacheKey?: string;
}

/**
 * 创建成功的查询结果
 */
export function createSuccessQueryResult<TData>(
  data: TData,
  message?: string,
  executionTime?: number,
  fromCache?: boolean,
  cacheKey?: string
): QueryResult<TData> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date(),
    executionTime,
    fromCache,
    cacheKey
  };
}

/**
 * 创建失败的查询结果
 */
export function createFailureQueryResult<TData>(
  errors: string[],
  message?: string,
  executionTime?: number
): QueryResult<TData> {
  return {
    success: false,
    message,
    errors,
    timestamp: new Date(),
    executionTime
  };
}
