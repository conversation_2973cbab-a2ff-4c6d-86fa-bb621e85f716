"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DistributedLock_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistributedLock = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../util/database/redis/redis.service");
let DistributedLock = DistributedLock_1 = class DistributedLock {
    redisService;
    logger = new common_1.Logger(DistributedLock_1.name);
    lockPrefix = 'lock:payment:';
    constructor(redisService) {
        this.redisService = redisService;
    }
    async acquire(key, ttl) {
        const lockKey = this.formatKey(key);
        const lockValue = Date.now().toString();
        const redis = this.redisService.getClient();
        this.logger.debug(`尝试获取Redis锁: ${lockKey}, 超时时间: ${ttl}ms`);
        const result = await redis.set(lockKey, lockValue, 'PX', ttl, 'NX');
        if (result === 'OK') {
            this.logger.debug(`Redis锁获取成功: ${lockKey}`);
            return true;
        }
        else {
            this.logger.debug(`Redis锁获取失败: ${lockKey}, 可能已被其他进程锁定`);
            return false;
        }
    }
    async release(key) {
        const lockKey = this.formatKey(key);
        const script = `
      if redis.call("EXISTS", KEYS[1]) == 1 then
        return redis.call("DEL", KEYS[1])
      else
        return 0
      end
    `;
        this.logger.debug(`尝试释放Redis锁: ${lockKey}`);
        const redis = this.redisService.getClient();
        const result = await redis.eval(script, 1, lockKey);
        if (result === 1) {
            this.logger.debug(`Redis锁释放成功: ${lockKey}`);
            return true;
        }
        else {
            this.logger.debug(`Redis锁释放失败: ${lockKey}, 可能已过期或不存在`);
            return false;
        }
    }
    async withLock(key, callback, ttl = 10000) {
        let locked = false;
        let retries = 0;
        const maxRetries = 3;
        const retryDelay = 200;
        while (retries < maxRetries) {
            try {
                locked = await this.acquire(key, ttl);
                if (!locked) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay * (retries + 1)));
                    retries++;
                    continue;
                }
                return await callback();
            }
            catch (error) {
                throw error;
            }
            finally {
                if (locked) {
                    await this.release(key).catch(err => {
                        this.logger.error(`释放锁失败: ${err.message}`, err.stack);
                    });
                }
            }
        }
        throw new Error(`无法获取锁: ${key}`);
    }
    async tryWithLock(key, callback, defaultValue, ttl = 10000) {
        let locked = false;
        try {
            locked = await this.acquire(key, ttl);
            if (!locked) {
                return defaultValue;
            }
            return await callback();
        }
        finally {
            if (locked) {
                await this.release(key).catch(err => {
                    this.logger.error(`释放锁失败: ${err.message}`, err.stack);
                });
            }
        }
    }
    async isLockFree(key) {
        const lockKey = this.formatKey(key);
        const exists = await this.redisService.exists(lockKey);
        return exists === 0;
    }
    async extendLock(key, ttl) {
        const lockKey = this.formatKey(key);
        const exists = await this.redisService.exists(lockKey);
        if (exists === 1) {
            const result = await this.redisService.expire(lockKey, Math.ceil(ttl / 1000));
            return result === 1;
        }
        return false;
    }
    async getLockTTL(key) {
        const lockKey = this.formatKey(key);
        const ttlInSeconds = await this.redisService.ttl(lockKey);
        return ttlInSeconds * 1000;
    }
    formatKey(key) {
        return `${this.lockPrefix}${key}`;
    }
};
exports.DistributedLock = DistributedLock;
exports.DistributedLock = DistributedLock = DistributedLock_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], DistributedLock);
//# sourceMappingURL=distributed.lock.js.map