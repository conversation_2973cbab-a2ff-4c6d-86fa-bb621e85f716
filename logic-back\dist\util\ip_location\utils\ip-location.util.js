"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationUtil = void 0;
const common_1 = require("@nestjs/common");
const redis_service_1 = require("../../database/redis/redis.service");
const logger_service_1 = require("../../../common/logger/logger.service");
const IP2Region = require('ip2region').default;
let IpLocationUtil = class IpLocationUtil {
    redisService;
    loggerService;
    ip2region;
    CACHE_PREFIX = 'ip_location:';
    CACHE_TTL = 24 * 60 * 60;
    constructor(redisService, loggerService) {
        this.redisService = redisService;
        this.loggerService = loggerService;
        this.initializeIp2Region();
    }
    initializeIp2Region() {
        try {
            this.ip2region = new IP2Region();
            this.loggerService.logBusiness('IpLocationUtil', 'initialize', {
                message: 'ip2region初始化成功'
            });
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationUtil', 'initialize', null, error);
            throw new Error(`ip2region初始化失败: ${error.message}`);
        }
    }
    async getLocationByIP(ipAddress) {
        const startTime = Date.now();
        try {
            if (!this.isValidIP(ipAddress)) {
                throw new Error(`无效的IP地址格式: ${ipAddress}`);
            }
            const cacheKey = `${this.CACHE_PREFIX}${ipAddress}`;
            const cachedResult = await this.redisService.get(cacheKey);
            if (cachedResult) {
                this.loggerService.logBusiness('IpLocationUtil', 'getLocationByIP', {
                    ip: ipAddress,
                    source: 'cache',
                    responseTime: Date.now() - startTime
                });
                return JSON.parse(cachedResult);
            }
            const rawResult = this.ip2region.search(ipAddress);
            const processedResult = this.processRawLocationData(rawResult, ipAddress);
            await this.redisService.set(cacheKey, JSON.stringify(processedResult), this.CACHE_TTL);
            this.loggerService.logBusiness('IpLocationUtil', 'getLocationByIP', {
                ip: ipAddress,
                location: processedResult.displayName,
                confidence: processedResult.confidence,
                responseTime: Date.now() - startTime
            });
            return processedResult;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationUtil', 'getLocationByIP', {
                ip: ipAddress,
                responseTime: Date.now() - startTime
            }, error);
            return this.getFallbackLocation(ipAddress);
        }
    }
    async getBasicLocationByIP(ipAddress) {
        const fullLocation = await this.getLocationByIP(ipAddress);
        return {
            country: fullLocation.country,
            province: fullLocation.province,
            city: fullLocation.city,
            isp: fullLocation.isp
        };
    }
    processRawLocationData(rawResult, ipAddress) {
        const processed = {
            country: rawResult.country !== '0' ? rawResult.country : '未知',
            province: rawResult.province !== '0' ? rawResult.province : '未知',
            city: rawResult.city !== '0' ? rawResult.city : '未知',
            isp: rawResult.isp !== '0' ? rawResult.isp : '未知',
            dataSource: 'ip2region',
            hasEmptyFields: false,
            confidence: 100,
            rawData: rawResult,
            displayName: ''
        };
        const emptyFields = [processed.country, processed.province, processed.city, processed.isp]
            .filter(field => field === '未知').length;
        processed.hasEmptyFields = emptyFields > 0;
        processed.confidence = Math.max(100 - (emptyFields * 25), 0);
        if (this.isIPv6(ipAddress) && processed.city === '未知') {
            processed.confidence = Math.min(processed.confidence + 15, 100);
        }
        processed.displayName = this.formatLocationDisplay(processed);
        return processed;
    }
    isValidIP(ipAddress) {
        const cleanIp = ipAddress.replace(/[^0-9a-fA-F:.]/g, '');
        if (cleanIp.length > 45) {
            return false;
        }
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
        return ipv4Regex.test(cleanIp) || ipv6Regex.test(cleanIp);
    }
    isIPv6(ip) {
        return ip.includes(':');
    }
    getFallbackLocation(ipAddress) {
        return {
            country: '未知',
            province: '未知',
            city: '未知',
            isp: '未知',
            dataSource: 'fallback',
            hasEmptyFields: true,
            confidence: 0,
            displayName: '未知位置',
            rawData: { ip: ipAddress, error: 'fallback' }
        };
    }
    formatLocationDisplay(location) {
        const parts = [];
        if (location.country !== '未知')
            parts.push(location.country);
        if (location.province !== '未知')
            parts.push(location.province);
        if (location.city !== '未知' && location.city !== location.province) {
            parts.push(location.city);
        }
        return parts.length > 0 ? parts.join(' ') : '未知位置';
    }
    static maskIP(ip) {
        if (ip.includes(':')) {
            const parts = ip.split(':');
            if (parts.length >= 4) {
                return `${parts.slice(0, 4).join(':')}:****`;
            }
            return '****:****:****:****';
        }
        else {
            const parts = ip.split('.');
            if (parts.length === 4) {
                return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
            }
            return '***.***.***.**';
        }
    }
};
exports.IpLocationUtil = IpLocationUtil;
exports.IpLocationUtil = IpLocationUtil = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService,
        logger_service_1.LoggerService])
], IpLocationUtil);
//# sourceMappingURL=ip-location.util.js.map