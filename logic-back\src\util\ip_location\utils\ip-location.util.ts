import { Injectable } from '@nestjs/common';
import { RedisService } from '../../database/redis/redis.service';
import { LoggerService } from '../../../common/logger/logger.service';

// 导入ip2region库
const IP2Region = require('ip2region').default;

/**
 * 地理位置信息接口
 */
export interface LocationInfo {
  country: string;      // 国家
  province: string;     // 省份
  city: string;         // 城市
  isp: string;          // 运营商
}

/**
 * 增强的位置信息接口（包含数据质量）
 */
export interface LocationInfoWithQuality extends LocationInfo {
  dataSource: 'ip2region' | 'fallback';
  hasEmptyFields: boolean;  // 标识是否有字段为"0"或空
  confidence: number;       // 数据可信度 0-100
  rawData?: any;           // 原始ip2region返回数据
  displayName: string;     // 格式化显示名称
}

/**
 * 风险评估结果接口
 */
export interface LocationRisk {
  level: 'LOW' | 'MEDIUM' | 'HIGH';
  reason: string;
  needVerification: boolean;
  score: number;        // 风险评分 0-100
  factors: string[];    // 风险因素列表
}

/**
 * IP地理位置解析工具类
 */
@Injectable()
export class IpLocationUtil {
  private ip2region: any;
  private readonly CACHE_PREFIX = 'ip_location:';
  private readonly CACHE_TTL = 24 * 60 * 60; // 24小时

  constructor(
    private readonly redisService: RedisService,
    private readonly loggerService: LoggerService
  ) {
    this.initializeIp2Region();
  }

  /**
   * 初始化ip2region实例
   */
  private initializeIp2Region() {
    try {
      this.ip2region = new IP2Region();
      this.loggerService.logBusiness('IpLocationUtil', 'initialize', {
        message: 'ip2region初始化成功'
      });
    } catch (error) {
      this.loggerService.logBusiness('IpLocationUtil', 'initialize', null, error);
      throw new Error(`ip2region初始化失败: ${error.message}`);
    }
  }

  /**
   * 根据IP地址获取地理位置信息
   * @param ipAddress IP地址
   * @returns 地理位置信息
   */
  async getLocationByIP(ipAddress: string): Promise<LocationInfoWithQuality> {
    const startTime = Date.now();
    
    try {
      // 验证IP地址格式
      if (!this.isValidIP(ipAddress)) {
        throw new Error(`无效的IP地址格式: ${ipAddress}`);
      }

      // 检查缓存
      const cacheKey = `${this.CACHE_PREFIX}${ipAddress}`;
      const cachedResult = await this.redisService.get(cacheKey);
      
      if (cachedResult) {
        this.loggerService.logBusiness('IpLocationUtil', 'getLocationByIP', {
          ip: ipAddress,
          source: 'cache',
          responseTime: Date.now() - startTime
        });
        return JSON.parse(cachedResult);
      }

      // 查询ip2region
      const rawResult = this.ip2region.search(ipAddress);
      const processedResult = this.processRawLocationData(rawResult, ipAddress);

      // 缓存结果
      await this.redisService.set(cacheKey, JSON.stringify(processedResult), this.CACHE_TTL);

      this.loggerService.logBusiness('IpLocationUtil', 'getLocationByIP', {
        ip: ipAddress,
        location: processedResult.displayName,
        confidence: processedResult.confidence,
        responseTime: Date.now() - startTime
      });

      return processedResult;
    } catch (error) {
      this.loggerService.logBusiness('IpLocationUtil', 'getLocationByIP', {
        ip: ipAddress,
        responseTime: Date.now() - startTime
      }, error);
      
      // 返回默认位置信息
      return this.getFallbackLocation(ipAddress);
    }
  }

  /**
   * 获取基础位置信息（兼容旧接口）
   * @param ipAddress IP地址
   * @returns 基础地理位置信息
   */
  async getBasicLocationByIP(ipAddress: string): Promise<LocationInfo> {
    const fullLocation = await this.getLocationByIP(ipAddress);
    return {
      country: fullLocation.country,
      province: fullLocation.province,
      city: fullLocation.city,
      isp: fullLocation.isp
    };
  }

  /**
   * 处理ip2region原始数据
   * @param rawResult ip2region原始返回结果
   * @param ipAddress 原始IP地址
   * @returns 处理后的位置信息
   */
  private processRawLocationData(rawResult: any, ipAddress: string): LocationInfoWithQuality {
    const processed = {
      country: rawResult.country !== '0' ? rawResult.country : '未知',
      province: rawResult.province !== '0' ? rawResult.province : '未知',
      city: rawResult.city !== '0' ? rawResult.city : '未知',
      isp: rawResult.isp !== '0' ? rawResult.isp : '未知',
      dataSource: 'ip2region' as const,
      hasEmptyFields: false,
      confidence: 100,
      rawData: rawResult,
      displayName: ''
    };

    // 计算数据质量
    const emptyFields = [processed.country, processed.province, processed.city, processed.isp]
      .filter(field => field === '未知').length;

    processed.hasEmptyFields = emptyFields > 0;
    processed.confidence = Math.max(100 - (emptyFields * 25), 0);

    // IPv6地址特殊处理
    if (this.isIPv6(ipAddress) && processed.city === '未知') {
      processed.confidence = Math.min(processed.confidence + 15, 100);
    }

    // 生成显示名称
    processed.displayName = this.formatLocationDisplay(processed);

    return processed;
  }

  /**
   * 验证IP地址格式
   * @param ipAddress IP地址
   * @returns 是否为有效IP
   */
  private isValidIP(ipAddress: string): boolean {
    // 清理输入
    const cleanIp = ipAddress.replace(/[^0-9a-fA-F:.]/g, '');
    
    // 长度检查
    if (cleanIp.length > 45) {
      return false;
    }

    // IPv4格式验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6格式验证
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    return ipv4Regex.test(cleanIp) || ipv6Regex.test(cleanIp);
  }

  /**
   * 检查是否为IPv6地址
   * @param ip IP地址
   * @returns 是否为IPv6
   */
  private isIPv6(ip: string): boolean {
    return ip.includes(':');
  }

  /**
   * 获取备用位置信息
   * @param ipAddress IP地址
   * @returns 备用位置信息
   */
  private getFallbackLocation(ipAddress: string): LocationInfoWithQuality {
    return {
      country: '未知',
      province: '未知',
      city: '未知',
      isp: '未知',
      dataSource: 'fallback',
      hasEmptyFields: true,
      confidence: 0,
      displayName: '未知位置',
      rawData: { ip: ipAddress, error: 'fallback' }
    };
  }

  /**
   * 格式化位置显示名称
   * @param location 位置信息
   * @returns 格式化的显示名称
   */
  private formatLocationDisplay(location: LocationInfo): string {
    const parts = [];
    
    if (location.country !== '未知') parts.push(location.country);
    if (location.province !== '未知') parts.push(location.province);
    if (location.city !== '未知' && location.city !== location.province) {
      parts.push(location.city);
    }
    
    return parts.length > 0 ? parts.join(' ') : '未知位置';
  }

  /**
   * IP地址脱敏
   * @param ip IP地址
   * @returns 脱敏后的IP地址
   */
  static maskIP(ip: string): string {
    if (ip.includes(':')) {
      // IPv6脱敏
      const parts = ip.split(':');
      if (parts.length >= 4) {
        return `${parts.slice(0, 4).join(':')}:****`;
      }
      return '****:****:****:****';
    } else {
      // IPv4脱敏
      const parts = ip.split('.');
      if (parts.length === 4) {
        return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
      }
      return '***.***.***.**';
    }
  }
}
