{"version": 3, "file": "location-not-found.exception.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/domain/exceptions/location-not-found.exception.ts"], "names": [], "mappings": ";;;AAAA,iFAA2E;AAM3E,MAAa,yBAA0B,SAAQ,wDAAyB;IACtE,YACE,OAAe,EACf,SAAkB,EAClB,OAA6B;QAE7B,KAAK,CACH,OAAO,EACP,oBAAoB,EACpB;YACE,SAAS;YACT,GAAG,OAAO;SACX,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,kBAAkB,CAAC,SAAiB,EAAE,MAAe;QAC1D,OAAO,IAAI,yBAAyB,CAClC,kBAAkB,SAAS,EAAE,EAC7B,SAAS,EACT;YACE,MAAM,EAAE,MAAM,IAAI,mBAAmB;YACrC,OAAO,EAAE,WAAW;SACrB,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,mBAAmB,CAAC,SAAiB,EAAE,KAAa;QACzD,OAAO,IAAI,yBAAyB,CAClC,gBAAgB,SAAS,EAAE,EAC3B,SAAS,EACT;YACE,MAAM,EAAE,uBAAuB;YAC/B,aAAa,EAAE,KAAK,EAAE,OAAO;SAC9B,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,SAAiB;QACvC,OAAO,IAAI,yBAAyB,CAClC,qBAAqB,SAAS,EAAE,EAChC,SAAS,EACT;YACE,MAAM,EAAE,oBAAoB;YAC5B,SAAS,EAAE,IAAI;SAChB,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,SAAiB;QACxC,OAAO,IAAI,yBAAyB,CAClC,qBAAqB,SAAS,EAAE,EAChC,SAAS,EACT;YACE,MAAM,EAAE,qBAAqB;YAC7B,UAAU,EAAE,IAAI;SACjB,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,kBAAkB,CAAC,SAAiB,EAAE,OAAe;QAC1D,OAAO,IAAI,yBAAyB,CAClC,gBAAgB,OAAO,EAAE,EACzB,SAAS,EACT;YACE,MAAM,EAAE,qBAAqB;YAC7B,OAAO;SACR,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,cAAc,CAAC,SAAiB,EAAE,UAAkB;QACzD,OAAO,IAAI,yBAAyB,CAClC,eAAe,UAAU,GAAG,EAC5B,SAAS,EACT;YACE,MAAM,EAAE,kBAAkB;YAC1B,UAAU;SACX,CACF,CAAC;IACJ,CAAC;CACF;AAnGD,8DAmGC"}