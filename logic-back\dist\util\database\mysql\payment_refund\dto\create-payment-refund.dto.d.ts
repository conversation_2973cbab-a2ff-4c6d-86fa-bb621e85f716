export declare enum PaymentChannel {
    ALIPAY = "alipay",
    WECHAT = "wechat",
    UNIONPAY = "unionpay"
}
export declare enum RefundStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SUCCESS = "success",
    FAILED = "failed"
}
export declare class CreatePaymentRefundDto {
    paymentOrderId: string;
    businessRefundId: string;
    amount: number;
    channel: PaymentChannel;
    channelRefundId?: string;
    reason?: string;
    status?: RefundStatus;
    parameters?: Record<string, any>;
    userId?: string;
    operatorId?: string;
    notifyUrl?: string;
}
