"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_entity_1 = require("./entities/activity.entity");
const activity_tag_entity_1 = require("../activity_tag/entities/activity_tag.entity");
const activity_work_entity_1 = require("../activity_work/entities/activity_work.entity");
let ActivityService = class ActivityService {
    activityRepository;
    activityTagRepository;
    activityWorkRepository;
    constructor(activityRepository, activityTagRepository, activityWorkRepository) {
        this.activityRepository = activityRepository;
        this.activityTagRepository = activityTagRepository;
        this.activityWorkRepository = activityWorkRepository;
    }
    async create(createActivityDto) {
        const activity = this.activityRepository.create(createActivityDto);
        return this.activityRepository.save(activity);
    }
    async findAll() {
        return this.activityRepository.find({
            where: { isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findOne(id) {
        const activity = await this.activityRepository.findOne({
            where: { id, isDelete: false }
        });
        if (!activity) {
            throw new common_1.NotFoundException(`活动ID ${id} 未找到`);
        }
        return activity;
    }
    async update(id, updateActivityDto) {
        await this.activityRepository.update(id, updateActivityDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.activityRepository.update(id, { isDelete: true });
    }
    async hardRemove(id) {
        await this.activityRepository.delete(id);
    }
    async findByStatus(status) {
        return this.activityRepository.find({
            where: { status, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findActiveActivities() {
        const now = new Date();
        return this.activityRepository.find({
            where: {
                startTime: (0, typeorm_2.LessThanOrEqual)(now),
                endTime: (0, typeorm_2.MoreThanOrEqual)(now),
                status: 1,
                isDelete: false,
            },
            order: { createTime: 'DESC' },
        });
    }
    async findUpcomingActivities() {
        const now = new Date();
        return this.activityRepository.find({
            where: {
                startTime: (0, typeorm_2.MoreThanOrEqual)(now),
                status: 1,
                isDelete: false,
            },
            order: { startTime: 'ASC' },
        });
    }
    async findByCreator(creatorId) {
        return this.activityRepository.find({
            where: { creatorId, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async findByType(activityType) {
        return this.activityRepository.find({
            where: { activityType, isDelete: false },
            order: { createTime: 'DESC' },
        });
    }
    async updateStatus(id, status) {
        await this.activityRepository.update(id, { status });
        return this.findOne(id);
    }
    async getById(id) {
        return await this.findOne(id);
    }
    async deleteActivity(id) {
        await this.remove(id);
        return true;
    }
    async updateActivity(id, data) {
        await this.activityRepository.update(id, data);
        return true;
    }
    async list(params) {
        const { page = 1, size = 10, status, keyword } = params;
        const where = { isDelete: false };
        if (status !== undefined) {
            where.status = status;
        }
        if (keyword) {
            where.name = (0, typeorm_2.Like)(`%${keyword}%`);
        }
        const [activities, total] = await this.activityRepository.findAndCount({
            where,
            order: { createTime: 'DESC' },
            skip: (page - 1) * size,
            take: size,
        });
        return {
            list: activities,
            pagination: {
                page,
                size,
                total,
            },
        };
    }
    async getActivityWithWorks(id) {
        const activity = await this.findOne(id);
        const works = await this.activityWorkRepository.find({
            where: { activityId: id, isDelete: false },
            relations: ['userWorkInfo'],
            order: { createTime: 'DESC' },
        });
        return {
            ...activity,
            works,
        };
    }
    async getActivityContentByType(id) {
        const activity = await this.findOne(id);
        return {
            ...activity,
            contentType: activity.activityType,
        };
    }
    async addWorksToActivity(activityId, works) {
        await this.findOne(activityId);
        const activityWorks = works.map(work => ({
            activityId,
            workId: work.workId,
            userId: 0,
            workTitle: '',
            workCover: '',
            isWinner: work.isAwarded ? 1 : 0,
            isSelected: 1,
            isShow: 1,
            creatorId: 0,
            isDelete: false,
        }));
        await this.activityWorkRepository.save(activityWorks);
        return true;
    }
    async setAwardedWorks(activityId, workIds) {
        await this.findOne(activityId);
        await this.activityWorkRepository.update({ activityId, isDelete: false }, { isWinner: 0 });
        if (workIds.length > 0) {
            for (const workId of workIds) {
                await this.activityWorkRepository.update({ activityId, workId, isDelete: false }, { isWinner: 1 });
            }
        }
        return true;
    }
};
exports.ActivityService = ActivityService;
exports.ActivityService = ActivityService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_entity_1.Activity)),
    __param(1, (0, typeorm_1.InjectRepository)(activity_tag_entity_1.ActivityTag)),
    __param(2, (0, typeorm_1.InjectRepository)(activity_work_entity_1.ActivityWork)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ActivityService);
//# sourceMappingURL=activity.service.js.map