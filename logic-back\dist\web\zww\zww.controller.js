"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZwwController = void 0;
const common_1 = require("@nestjs/common");
const zww_service_1 = require("./zww.service");
const create_zww_dto_1 = require("./dto/create-zww.dto");
const update_zww_dto_1 = require("./dto/update-zww.dto");
let ZwwController = class ZwwController {
    zwwService;
    constructor(zwwService) {
        this.zwwService = zwwService;
    }
    create(createZwwDto) {
        return this.zwwService.create(createZwwDto);
    }
    findAll() {
        return this.zwwService.findAll();
    }
    findOne(id) {
        return this.zwwService.findOne(+id);
    }
    update(id, updateZwwDto) {
        return this.zwwService.update(+id, updateZwwDto);
    }
    remove(id) {
        return this.zwwService.remove(+id);
    }
};
exports.ZwwController = ZwwController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_zww_dto_1.CreateZwwDto]),
    __metadata("design:returntype", void 0)
], ZwwController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ZwwController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ZwwController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_zww_dto_1.UpdateZwwDto]),
    __metadata("design:returntype", void 0)
], ZwwController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ZwwController.prototype, "remove", null);
exports.ZwwController = ZwwController = __decorate([
    (0, common_1.Controller)('zww'),
    __metadata("design:paramtypes", [zww_service_1.ZwwService])
], ZwwController);
//# sourceMappingURL=zww.controller.js.map