"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskScore = void 0;
class RiskScore {
    _score;
    _level;
    _factors;
    _reason;
    constructor(score, factors = []) {
        this._score = Math.max(0, Math.min(100, Math.round(score)));
        this._level = this.calculateRiskLevel(this._score);
        this._factors = [...factors];
        this._reason = this.generateRiskReason(this._level, this._factors);
    }
    get score() {
        return this._score;
    }
    get level() {
        return this._level;
    }
    get factors() {
        return [...this._factors];
    }
    get reason() {
        return this._reason;
    }
    get isLowRisk() {
        return this._level === 'LOW';
    }
    get isMediumRisk() {
        return this._level === 'MEDIUM';
    }
    get isHighRisk() {
        return this._level === 'HIGH';
    }
    get needsVerification() {
        return this._level === 'HIGH' || this.hasHighRiskFactors();
    }
    get needsBlocking() {
        return this._score >= 90 || this._factors.includes('境外登录');
    }
    get colorCode() {
        switch (this._level) {
            case 'LOW': return '#28a745';
            case 'MEDIUM': return '#ffc107';
            case 'HIGH': return '#dc3545';
            default: return '#6c757d';
        }
    }
    get levelDescription() {
        switch (this._level) {
            case 'LOW': return '低风险';
            case 'MEDIUM': return '中等风险';
            case 'HIGH': return '高风险';
            default: return '未知风险';
        }
    }
    get recommendedVerificationMethods() {
        const methods = [];
        if (this._factors.includes('境外登录')) {
            methods.push('邮箱验证', '短信验证');
        }
        else if (this._factors.includes('跨省登录')) {
            methods.push('短信验证');
        }
        else if (this._level === 'MEDIUM') {
            methods.push('短信验证');
        }
        else if (this._level === 'HIGH') {
            methods.push('短信验证', '邮箱验证');
        }
        return methods.length > 0 ? methods : ['短信验证'];
    }
    hasHighRiskFactors() {
        const highRiskFactors = ['境外登录', '频繁异地登录', '可疑IP'];
        return this._factors.some(factor => highRiskFactors.includes(factor));
    }
    calculateRiskLevel(score) {
        if (score >= 70) {
            return 'HIGH';
        }
        else if (score >= 30) {
            return 'MEDIUM';
        }
        else {
            return 'LOW';
        }
    }
    generateRiskReason(level, factors) {
        if (factors.length === 0) {
            return '常用登录地，风险较低';
        }
        const mainFactor = factors[0];
        switch (level) {
            case 'HIGH':
                return `高风险：${mainFactor}`;
            case 'MEDIUM':
                return `中风险：${mainFactor}`;
            case 'LOW':
            default:
                return `低风险：${mainFactor}`;
        }
    }
    addFactor(factor) {
        if (!this._factors.includes(factor)) {
            return new RiskScore(this._score, [...this._factors, factor]);
        }
        return this;
    }
    removeFactor(factor) {
        const newFactors = this._factors.filter(f => f !== factor);
        return new RiskScore(this._score, newFactors);
    }
    increaseScore(points) {
        return new RiskScore(this._score + points, this._factors);
    }
    decreaseScore(points) {
        return new RiskScore(this._score - points, this._factors);
    }
    static create(score, factors = []) {
        return new RiskScore(score, factors);
    }
    static createZero() {
        return new RiskScore(0, []);
    }
    static createLow(factors = []) {
        return new RiskScore(15, factors);
    }
    static createMedium(factors = []) {
        return new RiskScore(50, factors);
    }
    static createHigh(factors = []) {
        return new RiskScore(85, factors);
    }
    static fromFactors(factors) {
        let score = 0;
        factors.forEach(factor => {
            switch (factor) {
                case '境外登录':
                    score += 60;
                    break;
                case '跨省登录':
                    score += 40;
                    break;
                case '省内异地登录':
                    score += 15;
                    break;
                case '运营商变化':
                    score += 5;
                    break;
                case '位置信息不完整':
                    score += 20;
                    break;
                case '频繁异地登录':
                    score += 10;
                    break;
                case '新用户保护':
                    score -= 15;
                    break;
                default:
                    score += 5;
            }
        });
        return new RiskScore(score, factors);
    }
    equals(other) {
        return this._score === other._score &&
            this._level === other._level &&
            JSON.stringify(this._factors.sort()) === JSON.stringify(other._factors.sort());
    }
    toString() {
        return `${this.levelDescription}(${this._score}分): ${this._reason}`;
    }
    toJSON() {
        return {
            score: this._score,
            level: this._level,
            factors: this._factors,
            reason: this._reason,
            levelDescription: this.levelDescription,
            needsVerification: this.needsVerification,
            needsBlocking: this.needsBlocking,
            colorCode: this.colorCode,
            recommendedVerificationMethods: this.recommendedVerificationMethods
        };
    }
}
exports.RiskScore = RiskScore;
//# sourceMappingURL=risk-score.vo.js.map