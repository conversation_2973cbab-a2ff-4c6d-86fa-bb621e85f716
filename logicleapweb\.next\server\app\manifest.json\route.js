"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.json/route";
exports.ids = ["app/manifest.json/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Fmanifest_json_2Froute_filePath_F_3A_5Clogicleap2_5Clogicleapweb_5Capp_5Cmanifest_json_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Fmanifest.json%2Froute&filePath=F%3A%5Clogicleap2%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=F%3A%5Clogicleap2%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/manifest.json/route\",\n        pathname: \"/manifest.json\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.json/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Fmanifest.json%2Froute&filePath=F%3A%5Clogicleap2%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Fmanifest_json_2Froute_filePath_F_3A_5Clogicleap2_5Clogicleapweb_5Capp_5Cmanifest_json_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/manifest.json/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=F%3A%5Clogicleap2%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__":
/*!***********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=F%3A%5Clogicleap2%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__ ***!
  \***********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"application/manifest+json\"\nconst buffer = Buffer.from(\"ew0KICAic2hvcnRfbmFtZSI6ICJSZWFjdCBBcHAiLA0KICAibmFtZSI6ICJDcmVhdGUgUmVhY3QgQXBwIFNhbXBsZSIsDQogICJpY29ucyI6IFsNCiAgICB7DQogICAgICAic3JjIjogImZhdmljb24uaWNvIiwNCiAgICAgICJzaXplcyI6ICI2NHg2NCAzMngzMiAyNHgyNCAxNngxNiIsDQogICAgICAidHlwZSI6ICJpbWFnZS94LWljb24iDQogICAgfSwNCiAgICB7DQogICAgICAic3JjIjogImxvZ28xOTIucG5nIiwNCiAgICAgICJ0eXBlIjogImltYWdlL3BuZyIsDQogICAgICAic2l6ZXMiOiAiMTkyeDE5MiINCiAgICB9LA0KICAgIHsNCiAgICAgICJzcmMiOiAibG9nbzUxMi5wbmciLA0KICAgICAgInR5cGUiOiAiaW1hZ2UvcG5nIiwNCiAgICAgICJzaXplcyI6ICI1MTJ4NTEyIg0KICAgIH0NCiAgXSwNCiAgInN0YXJ0X3VybCI6ICIuIiwNCiAgImRpc3BsYXkiOiAic3RhbmRhbG9uZSIsDQogICJ0aGVtZV9jb2xvciI6ICIjMDAwMDAwIiwNCiAgImJhY2tncm91bmRfY29sb3IiOiAiI2ZmZmZmZiINCn0NCg==\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"no-cache, no-store\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=F%3A%5Clogicleap2%5Clogicleapweb%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=F%3A%5Clogicleap2%5Clogicleapweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Clogicleap2%5Clogicleapweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();