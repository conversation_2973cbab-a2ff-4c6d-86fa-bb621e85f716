/**
 * 风险评分值对象
 * 封装风险评分的计算逻辑和业务规则
 */
export class RiskScore {
  private readonly _score: number;
  private readonly _level: 'LOW' | 'MEDIUM' | 'HIGH';
  private readonly _factors: string[];
  private readonly _reason: string;

  constructor(score: number, factors: string[] = []) {
    this._score = Math.max(0, Math.min(100, Math.round(score)));
    this._level = this.calculateRiskLevel(this._score);
    this._factors = [...factors];
    this._reason = this.generateRiskReason(this._level, this._factors);
  }

  /**
   * 获取风险评分 (0-100)
   */
  get score(): number {
    return this._score;
  }

  /**
   * 获取风险等级
   */
  get level(): 'LOW' | 'MEDIUM' | 'HIGH' {
    return this._level;
  }

  /**
   * 获取风险因素列表
   */
  get factors(): string[] {
    return [...this._factors];
  }

  /**
   * 获取风险原因描述
   */
  get reason(): string {
    return this._reason;
  }

  /**
   * 是否为低风险
   */
  get isLowRisk(): boolean {
    return this._level === 'LOW';
  }

  /**
   * 是否为中等风险
   */
  get isMediumRisk(): boolean {
    return this._level === 'MEDIUM';
  }

  /**
   * 是否为高风险
   */
  get isHighRisk(): boolean {
    return this._level === 'HIGH';
  }

  /**
   * 是否需要额外验证
   */
  get needsVerification(): boolean {
    return this._level === 'HIGH' || this.hasHighRiskFactors();
  }

  /**
   * 是否需要立即阻止
   */
  get needsBlocking(): boolean {
    return this._score >= 90 || this._factors.includes('境外登录');
  }

  /**
   * 获取风险等级的颜色代码
   */
  get colorCode(): string {
    switch (this._level) {
      case 'LOW': return '#28a745';    // 绿色
      case 'MEDIUM': return '#ffc107'; // 黄色
      case 'HIGH': return '#dc3545';   // 红色
      default: return '#6c757d';       // 灰色
    }
  }

  /**
   * 获取风险等级的中文描述
   */
  get levelDescription(): string {
    switch (this._level) {
      case 'LOW': return '低风险';
      case 'MEDIUM': return '中等风险';
      case 'HIGH': return '高风险';
      default: return '未知风险';
    }
  }

  /**
   * 获取推荐的验证方式
   */
  get recommendedVerificationMethods(): string[] {
    const methods: string[] = [];

    if (this._factors.includes('境外登录')) {
      methods.push('邮箱验证', '短信验证');
    } else if (this._factors.includes('跨省登录')) {
      methods.push('短信验证');
    } else if (this._level === 'MEDIUM') {
      methods.push('短信验证');
    } else if (this._level === 'HIGH') {
      methods.push('短信验证', '邮箱验证');
    }

    return methods.length > 0 ? methods : ['短信验证'];
  }

  /**
   * 检查是否包含高风险因素
   */
  private hasHighRiskFactors(): boolean {
    const highRiskFactors = ['境外登录', '频繁异地登录', '可疑IP'];
    return this._factors.some(factor => highRiskFactors.includes(factor));
  }

  /**
   * 计算风险等级
   */
  private calculateRiskLevel(score: number): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (score >= 70) {
      return 'HIGH';
    } else if (score >= 30) {
      return 'MEDIUM';
    } else {
      return 'LOW';
    }
  }

  /**
   * 生成风险原因描述
   */
  private generateRiskReason(level: 'LOW' | 'MEDIUM' | 'HIGH', factors: string[]): string {
    if (factors.length === 0) {
      return '常用登录地，风险较低';
    }

    const mainFactor = factors[0];
    
    switch (level) {
      case 'HIGH':
        return `高风险：${mainFactor}`;
      case 'MEDIUM':
        return `中风险：${mainFactor}`;
      case 'LOW':
      default:
        return `低风险：${mainFactor}`;
    }
  }

  /**
   * 添加风险因素
   */
  addFactor(factor: string): RiskScore {
    if (!this._factors.includes(factor)) {
      return new RiskScore(this._score, [...this._factors, factor]);
    }
    return this;
  }

  /**
   * 移除风险因素
   */
  removeFactor(factor: string): RiskScore {
    const newFactors = this._factors.filter(f => f !== factor);
    return new RiskScore(this._score, newFactors);
  }

  /**
   * 增加风险评分
   */
  increaseScore(points: number): RiskScore {
    return new RiskScore(this._score + points, this._factors);
  }

  /**
   * 减少风险评分
   */
  decreaseScore(points: number): RiskScore {
    return new RiskScore(this._score - points, this._factors);
  }

  /**
   * 创建风险评分值对象的静态工厂方法
   */
  static create(score: number, factors: string[] = []): RiskScore {
    return new RiskScore(score, factors);
  }

  /**
   * 创建零风险评分
   */
  static createZero(): RiskScore {
    return new RiskScore(0, []);
  }

  /**
   * 创建低风险评分
   */
  static createLow(factors: string[] = []): RiskScore {
    return new RiskScore(15, factors);
  }

  /**
   * 创建中等风险评分
   */
  static createMedium(factors: string[] = []): RiskScore {
    return new RiskScore(50, factors);
  }

  /**
   * 创建高风险评分
   */
  static createHigh(factors: string[] = []): RiskScore {
    return new RiskScore(85, factors);
  }

  /**
   * 从多个风险因素计算评分
   */
  static fromFactors(factors: string[]): RiskScore {
    let score = 0;
    
    // 根据风险因素计算评分
    factors.forEach(factor => {
      switch (factor) {
        case '境外登录':
          score += 60;
          break;
        case '跨省登录':
          score += 40;
          break;
        case '省内异地登录':
          score += 15;
          break;
        case '运营商变化':
          score += 5;
          break;
        case '位置信息不完整':
          score += 20;
          break;
        case '频繁异地登录':
          score += 10;
          break;
        case '新用户保护':
          score -= 15;
          break;
        default:
          score += 5;
      }
    });

    return new RiskScore(score, factors);
  }

  /**
   * 值对象相等性比较
   */
  equals(other: RiskScore): boolean {
    return this._score === other._score &&
           this._level === other._level &&
           JSON.stringify(this._factors.sort()) === JSON.stringify(other._factors.sort());
  }

  /**
   * 转换为字符串
   */
  toString(): string {
    return `${this._levelDescription}(${this._score}分): ${this._reason}`;
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      score: this._score,
      level: this._level,
      factors: this._factors,
      reason: this._reason,
      levelDescription: this.levelDescription,
      needsVerification: this.needsVerification,
      needsBlocking: this.needsBlocking,
      colorCode: this.colorCode,
      recommendedVerificationMethods: this.recommendedVerificationMethods
    };
  }
}
