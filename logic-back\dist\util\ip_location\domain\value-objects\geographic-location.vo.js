"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeographicLocation = void 0;
class GeographicLocation {
    _country;
    _province;
    _city;
    _isp;
    _dataSource;
    _confidence;
    constructor(country, province, city, isp, dataSource = 'ip2region', confidence = 100) {
        this._country = this.normalizeLocationField(country);
        this._province = this.normalizeLocationField(province);
        this._city = this.normalizeLocationField(city);
        this._isp = this.normalizeLocationField(isp);
        this._dataSource = dataSource;
        this._confidence = Math.max(0, Math.min(100, confidence));
    }
    get country() {
        return this._country;
    }
    get province() {
        return this._province;
    }
    get city() {
        return this._city;
    }
    get isp() {
        return this._isp;
    }
    get dataSource() {
        return this._dataSource;
    }
    get confidence() {
        return this._confidence;
    }
    get isDomestic() {
        return this._country === '中国';
    }
    get isForeign() {
        return !this.isDomestic && this._country !== '未知';
    }
    get hasEmptyFields() {
        return [this._country, this._province, this._city, this._isp]
            .some(field => field === '未知' || field === '');
    }
    get isHighQuality() {
        return this._confidence >= 80 && !this.hasEmptyFields;
    }
    get emptyFieldCount() {
        return [this._country, this._province, this._city, this._isp]
            .filter(field => field === '未知' || field === '').length;
    }
    get displayName() {
        const parts = [];
        if (this._country !== '未知' && this._country !== '') {
            parts.push(this._country);
        }
        if (this._province !== '未知' && this._province !== '') {
            parts.push(this._province);
        }
        if (this._city !== '未知' && this._city !== '' && this._city !== this._province) {
            parts.push(this._city);
        }
        return parts.length > 0 ? parts.join(' ') : '未知位置';
    }
    get fullDescription() {
        const location = this.displayName;
        const ispInfo = this._isp !== '未知' ? ` (${this._isp})` : '';
        return `${location}${ispInfo}`;
    }
    isSameProvince(other) {
        return this._province !== '未知' &&
            other._province !== '未知' &&
            this._province === other._province;
    }
    isSameCity(other) {
        return this.isSameProvince(other) &&
            this._city !== '未知' &&
            other._city !== '未知' &&
            this._city === other._city;
    }
    isSameISP(other) {
        return this._isp !== '未知' &&
            other._isp !== '未知' &&
            this._isp === other._isp;
    }
    calculateSimilarity(other) {
        let score = 0;
        let maxScore = 0;
        maxScore += 40;
        if (this._country === other._country && this._country !== '未知') {
            score += 40;
        }
        maxScore += 30;
        if (this._province === other._province && this._province !== '未知') {
            score += 30;
        }
        maxScore += 20;
        if (this._city === other._city && this._city !== '未知') {
            score += 20;
        }
        maxScore += 10;
        if (this._isp === other._isp && this._isp !== '未知') {
            score += 10;
        }
        return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
    }
    normalizeLocationField(field) {
        if (!field || field.trim() === '' || field === '0') {
            return '未知';
        }
        return field.trim();
    }
    static create(country, province, city, isp, dataSource = 'ip2region', confidence = 100) {
        return new GeographicLocation(country, province, city, isp, dataSource, confidence);
    }
    static createUnknown() {
        return new GeographicLocation('未知', '未知', '未知', '未知', 'fallback', 0);
    }
    static fromIp2RegionData(rawData) {
        const emptyFields = [rawData.country, rawData.province, rawData.city, rawData.isp]
            .filter(field => field === '0' || field === '' || !field).length;
        const confidence = Math.max(100 - (emptyFields * 25), 0);
        return new GeographicLocation(rawData.country || '未知', rawData.province || '未知', rawData.city || '未知', rawData.isp || '未知', 'ip2region', confidence);
    }
    equals(other) {
        return this._country === other._country &&
            this._province === other._province &&
            this._city === other._city &&
            this._isp === other._isp;
    }
    toString() {
        return this.displayName;
    }
    toJSON() {
        return {
            country: this._country,
            province: this._province,
            city: this._city,
            isp: this._isp,
            dataSource: this._dataSource,
            confidence: this._confidence,
            isDomestic: this.isDomestic,
            isForeign: this.isForeign,
            hasEmptyFields: this.hasEmptyFields,
            isHighQuality: this.isHighQuality,
            displayName: this.displayName,
            fullDescription: this.fullDescription
        };
    }
}
exports.GeographicLocation = GeographicLocation;
//# sourceMappingURL=geographic-location.vo.js.map