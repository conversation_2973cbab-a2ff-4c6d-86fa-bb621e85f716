"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRecordController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_record_service_1 = require("./payment-record.service");
const create_payment_record_dto_1 = require("./dto/create-payment-record.dto");
const update_payment_record_dto_1 = require("./dto/update-payment-record.dto");
const query_payment_record_dto_1 = require("./dto/query-payment-record.dto");
const payment_record_entity_1 = require("./entities/payment-record.entity");
let PaymentRecordController = class PaymentRecordController {
    paymentRecordService;
    constructor(paymentRecordService) {
        this.paymentRecordService = paymentRecordService;
    }
    async create(createPaymentRecordDto) {
        const result = await this.paymentRecordService.create(createPaymentRecordDto);
        return {
            code: 0,
            message: 'success',
            data: result,
        };
    }
    async findAll(queryPaymentRecordDto, page = 1, limit = 10) {
        const result = await this.paymentRecordService.findAll(queryPaymentRecordDto, page, limit);
        return {
            code: 0,
            message: 'success',
            data: result,
        };
    }
    async findOne(id) {
        const result = await this.paymentRecordService.findById(id);
        return {
            code: 0,
            message: 'success',
            data: result,
        };
    }
    async findByOrderNo(orderNo) {
        const result = await this.paymentRecordService.findByOrderNo(orderNo);
        return {
            code: 0,
            message: 'success',
            data: result,
        };
    }
    async update(id, updatePaymentRecordDto) {
        const result = await this.paymentRecordService.update(id, updatePaymentRecordDto);
        return {
            code: 0,
            message: 'success',
            data: result,
        };
    }
    async updateStatus(id, body) {
        const result = await this.paymentRecordService.updateStatus(id, body.status, body.paymentId, body.paymentTime, body.rawResponse);
        return {
            code: 0,
            message: 'success',
            data: result,
        };
    }
};
exports.PaymentRecordController = PaymentRecordController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建支付记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: payment_record_entity_1.PaymentRecord }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_record_dto_1.CreatePaymentRecordDto]),
    __metadata("design:returntype", Promise)
], PaymentRecordController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '查询支付记录列表' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页条数' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_payment_record_dto_1.QueryPaymentRecordDto, Number, Number]),
    __metadata("design:returntype", Promise)
], PaymentRecordController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID查询支付记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '支付记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: payment_record_entity_1.PaymentRecord }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentRecordController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('order/:orderNo'),
    (0, swagger_1.ApiOperation)({ summary: '根据订单编号查询支付记录' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [payment_record_entity_1.PaymentRecord] }),
    __param(0, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentRecordController.prototype, "findByOrderNo", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新支付记录' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '支付记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: payment_record_entity_1.PaymentRecord }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_payment_record_dto_1.UpdatePaymentRecordDto]),
    __metadata("design:returntype", Promise)
], PaymentRecordController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新支付记录状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '支付记录ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: payment_record_entity_1.PaymentRecord }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentRecordController.prototype, "updateStatus", null);
exports.PaymentRecordController = PaymentRecordController = __decorate([
    (0, swagger_1.ApiTags)('支付记录'),
    (0, common_1.Controller)('api/v1/payment-records'),
    __metadata("design:paramtypes", [payment_record_service_1.PaymentRecordService])
], PaymentRecordController);
//# sourceMappingURL=payment-record.controller.js.map