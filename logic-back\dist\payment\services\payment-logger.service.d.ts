import { PaymentLogService } from '../../util/database/mysql/payment_log/payment-log.service';
import { LogType, LogStatus, OperationType } from '../../util/database/mysql/payment_log/dto/payment-log.dto';
export declare class PaymentLoggerService {
    private readonly paymentLogService;
    private readonly logger;
    constructor(paymentLogService: PaymentLogService);
    log(params: {
        logType: LogType;
        operation: OperationType;
        orderNo?: string;
        refundNo?: string;
        paymentChannel?: string;
        operatorId?: string;
        clientIp?: string;
        requestData?: any;
        responseData?: any;
        status?: LogStatus;
        errorMessage?: string;
        executionTime?: number;
    }): Promise<void>;
    logPaymentRequest(orderNo: string, channel: string, requestData: any, responseData?: any, status?: LogStatus, errorMessage?: string, executionTime?: number, operatorId?: string, clientIp?: string): Promise<void>;
    logPaymentQuery(orderNo: string, channel: string, requestData: any, responseData?: any, status?: LogStatus, errorMessage?: string, executionTime?: number): Promise<void>;
    logPaymentCallback(orderNo: string, channel: string, notifyData: any, result?: any, status?: LogStatus, errorMessage?: string, executionTime?: number, skipDetails?: boolean): Promise<void>;
    logRefundRequest(orderNo: string, refundNo: string, channel: string, requestData: any, responseData?: any, status?: LogStatus, errorMessage?: string, executionTime?: number): Promise<void>;
    logSystemOperation(operation: string, requestData: any, responseData?: any, status?: LogStatus, errorMessage?: string, operatorId?: string): Promise<void>;
}
