"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentCallbackDto = exports.PurchasePackageDto = exports.PaymentMode = exports.PaymentChannel = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var PaymentChannel;
(function (PaymentChannel) {
    PaymentChannel["ALIPAY"] = "alipay";
    PaymentChannel["WECHATPAY"] = "wechatpay";
})(PaymentChannel || (exports.PaymentChannel = PaymentChannel = {}));
var PaymentMode;
(function (PaymentMode) {
    PaymentMode["REDIRECT"] = "redirect";
    PaymentMode["QR_CODE"] = "qrcode";
})(PaymentMode || (exports.PaymentMode = PaymentMode = {}));
class PurchasePackageDto {
    packageId;
    channel;
    paymentMode;
    clientIp;
    returnUrl;
}
exports.PurchasePackageDto = PurchasePackageDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '套餐ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '套餐ID必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '套餐ID', example: 1 }),
    __metadata("design:type", Number)
], PurchasePackageDto.prototype, "packageId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '支付渠道不能为空' }),
    (0, class_validator_1.IsEnum)(PaymentChannel, { message: '无效的支付渠道' }),
    (0, swagger_1.ApiProperty)({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY }),
    __metadata("design:type", String)
], PurchasePackageDto.prototype, "channel", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PaymentMode, { message: '无效的支付方式' }),
    (0, swagger_1.ApiProperty)({
        description: '支付方式',
        enum: PaymentMode,
        default: PaymentMode.REDIRECT,
        required: false
    }),
    __metadata("design:type", String)
], PurchasePackageDto.prototype, "paymentMode", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '客户端IP必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '客户端IP', required: false }),
    __metadata("design:type", String)
], PurchasePackageDto.prototype, "clientIp", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '支付成功跳转URL必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '支付成功跳转URL', required: false }),
    __metadata("design:type", String)
], PurchasePackageDto.prototype, "returnUrl", void 0);
class PaymentCallbackDto {
    orderNo;
    paymentId;
}
exports.PaymentCallbackDto = PaymentCallbackDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '订单编号不能为空' }),
    (0, class_validator_1.IsString)({ message: '订单编号必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '订单编号' }),
    __metadata("design:type", String)
], PaymentCallbackDto.prototype, "orderNo", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '支付平台交易号不能为空' }),
    (0, class_validator_1.IsString)({ message: '支付平台交易号必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '支付平台交易号' }),
    __metadata("design:type", String)
], PaymentCallbackDto.prototype, "paymentId", void 0);
//# sourceMappingURL=purchase-package.dto.js.map