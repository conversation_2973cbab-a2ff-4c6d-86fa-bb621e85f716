"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherAuditAttachmentModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const teacher_audit_attachment_service_1 = require("./teacher_audit_attachment.service");
const teacher_audit_attachment_entity_1 = require("./entities/teacher_audit_attachment.entity");
const attachment_entity_1 = require("../attachment/entities/attachment.entity");
let TeacherAuditAttachmentModule = class TeacherAuditAttachmentModule {
};
exports.TeacherAuditAttachmentModule = TeacherAuditAttachmentModule;
exports.TeacherAuditAttachmentModule = TeacherAuditAttachmentModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([teacher_audit_attachment_entity_1.TeacherAuditAttachment, attachment_entity_1.Attachment])],
        providers: [teacher_audit_attachment_service_1.TeacherAuditAttachmentService],
        exports: [teacher_audit_attachment_service_1.TeacherAuditAttachmentService],
    })
], TeacherAuditAttachmentModule);
//# sourceMappingURL=teacher_audit_attachment.module.js.map