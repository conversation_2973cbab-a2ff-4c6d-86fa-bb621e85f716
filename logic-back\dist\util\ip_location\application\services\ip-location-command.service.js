"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationCommandService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const update_common_location_command_1 = require("../commands/update-common-location.command");
const command_handler_interface_1 = require("../interfaces/command-handler.interface");
const ip_location_domain_service_1 = require("../../domain/services/ip-location-domain.service");
const user_common_location_entity_1 = require("../../domain/entities/user-common-location.entity");
const ip_location_domain_exception_1 = require("../../domain/exceptions/ip-location-domain.exception");
let IpLocationCommandService = class IpLocationCommandService {
    userLocationRepository;
    ipLocationDomainService;
    constructor(userLocationRepository, ipLocationDomainService) {
        this.userLocationRepository = userLocationRepository;
        this.ipLocationDomainService = ipLocationDomainService;
    }
    async handleUpdateCommonLocation(command) {
        const startTime = Date.now();
        try {
            const validation = command.validate();
            if (!validation.isValid) {
                return (0, command_handler_interface_1.createFailureResult)(validation.errors, '命令验证失败', Date.now() - startTime);
            }
            let userLocation = await this.userLocationRepository.findOne({
                where: {
                    userId: command.userId,
                    country: command.location.country,
                    province: command.location.province,
                    city: command.location.city
                }
            });
            if (!userLocation) {
                userLocation = this.userLocationRepository.create({
                    userId: command.userId,
                    country: command.location.country,
                    province: command.location.province,
                    city: command.location.city,
                    isp: command.location.isp,
                    dataSource: command.location.dataSource,
                    hasEmptyFields: command.location.hasEmptyFields,
                    confidence: command.location.confidence,
                    displayName: command.location.displayName,
                    loginCount: 1,
                    lastLoginAt: command.timestamp,
                    firstLoginAt: command.timestamp,
                    isTrusted: false,
                    trustScore: this.calculateInitialTrustScore(command.location)
                });
            }
            else {
                userLocation.loginCount += 1;
                userLocation.lastLoginAt = command.timestamp;
                userLocation.isp = command.location.isp;
                userLocation.confidence = Math.max(userLocation.confidence, command.location.confidence);
                userLocation.trustScore = this.calculateUpdatedTrustScore(userLocation, command.location);
                if (command.location.confidence > userLocation.confidence) {
                    userLocation.dataSource = command.location.dataSource;
                    userLocation.hasEmptyFields = command.location.hasEmptyFields;
                    userLocation.displayName = command.location.displayName;
                }
            }
            const savedLocation = await this.userLocationRepository.save(userLocation);
            return (0, command_handler_interface_1.createSuccessResult)(savedLocation, '用户常用位置更新成功', Date.now() - startTime);
        }
        catch (error) {
            if (error instanceof ip_location_domain_exception_1.IpLocationDomainException) {
                return (0, command_handler_interface_1.createFailureResult)([error.message], '领域业务错误', Date.now() - startTime);
            }
            return (0, command_handler_interface_1.createFailureResult)(['更新用户常用位置时发生未知错误'], error.message, Date.now() - startTime);
        }
    }
    async handleSetTrustedLocation(command) {
        const startTime = Date.now();
        try {
            const validation = command.validate();
            if (!validation.isValid) {
                return (0, command_handler_interface_1.createFailureResult)(validation.errors, '命令验证失败', Date.now() - startTime);
            }
            const matchingLocations = await this.userLocationRepository.find({
                where: {
                    userId: command.userId,
                    province: command.province,
                    city: command.city
                }
            });
            if (matchingLocations.length === 0) {
                return (0, command_handler_interface_1.createFailureResult)(['未找到匹配的位置记录'], '无法设置可信位置', Date.now() - startTime);
            }
            const updatedLocations = [];
            for (const location of matchingLocations) {
                location.isTrusted = true;
                location.trustScore = Math.min(location.trustScore + 20, 100);
                location.trustedAt = command.timestamp;
                location.trustedBy = command.setBy;
                location.trustReason = command.reason;
                const saved = await this.userLocationRepository.save(location);
                updatedLocations.push(saved);
            }
            return (0, command_handler_interface_1.createSuccessResult)(updatedLocations, `成功设置${updatedLocations.length}个位置为可信位置`, Date.now() - startTime);
        }
        catch (error) {
            if (error instanceof ip_location_domain_exception_1.IpLocationDomainException) {
                return (0, command_handler_interface_1.createFailureResult)([error.message], '领域业务错误', Date.now() - startTime);
            }
            return (0, command_handler_interface_1.createFailureResult)(['设置可信位置时发生未知错误'], error.message, Date.now() - startTime);
        }
    }
    async handleRecordLoginLocation(command) {
        const startTime = Date.now();
        try {
            const validation = command.validate();
            if (!validation.isValid) {
                return (0, command_handler_interface_1.createFailureResult)(validation.errors, '命令验证失败', Date.now() - startTime);
            }
            let locationUpdated = false;
            let loginRecorded = false;
            if (command.isSuccessLogin) {
                const updateCommand = new update_common_location_command_1.UpdateCommonLocationCommand(command.userId, command.ipAddress, command.location, command.sessionId, command.userAgent);
                const updateResult = await this.handleUpdateCommonLocation(updateCommand);
                locationUpdated = updateResult.success;
            }
            try {
                loginRecorded = true;
            }
            catch (logError) {
                console.warn('登录日志记录失败:', logError);
                loginRecorded = false;
            }
            const result = {
                locationUpdated,
                loginRecorded
            };
            return (0, command_handler_interface_1.createSuccessResult)(result, '登录位置记录处理完成', Date.now() - startTime);
        }
        catch (error) {
            if (error instanceof ip_location_domain_exception_1.IpLocationDomainException) {
                return (0, command_handler_interface_1.createFailureResult)([error.message], '领域业务错误', Date.now() - startTime);
            }
            return (0, command_handler_interface_1.createFailureResult)(['记录登录位置时发生未知错误'], error.message, Date.now() - startTime);
        }
    }
    calculateInitialTrustScore(location) {
        let score = 50;
        if (location.isHighQuality) {
            score += 20;
        }
        else if (location.confidence > 50) {
            score += 10;
        }
        if (location.isDomestic) {
            score += 10;
        }
        return Math.min(score, 100);
    }
    calculateUpdatedTrustScore(userLocation, newLocation) {
        let score = userLocation.trustScore;
        if (userLocation.loginCount > 10) {
            score += 5;
        }
        else if (userLocation.loginCount > 5) {
            score += 2;
        }
        if (newLocation.confidence > userLocation.confidence) {
            score += 5;
        }
        return Math.min(score, 100);
    }
};
exports.IpLocationCommandService = IpLocationCommandService;
exports.IpLocationCommandService = IpLocationCommandService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_common_location_entity_1.UserCommonLocation)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        ip_location_domain_service_1.IpLocationDomainService])
], IpLocationCommandService);
//# sourceMappingURL=ip-location-command.service.js.map