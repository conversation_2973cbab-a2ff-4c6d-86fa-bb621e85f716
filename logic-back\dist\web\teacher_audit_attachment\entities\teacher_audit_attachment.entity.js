"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherAuditAttachment = void 0;
const typeorm_1 = require("typeorm");
const teacher_audit_entity_1 = require("../../teacher_audit/entities/teacher_audit.entity");
const attachment_entity_1 = require("../../attachment/entities/attachment.entity");
let TeacherAuditAttachment = class TeacherAuditAttachment {
    id;
    teacher_audit_id;
    attachment_id;
    teacherAudit;
    attachment;
};
exports.TeacherAuditAttachment = TeacherAuditAttachment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'bigint' }),
    __metadata("design:type", Number)
], TeacherAuditAttachment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], TeacherAuditAttachment.prototype, "teacher_audit_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], TeacherAuditAttachment.prototype, "attachment_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => teacher_audit_entity_1.TeacherAudit),
    (0, typeorm_1.JoinColumn)({ name: 'teacher_audit_id' }),
    __metadata("design:type", teacher_audit_entity_1.TeacherAudit)
], TeacherAuditAttachment.prototype, "teacherAudit", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => attachment_entity_1.Attachment),
    (0, typeorm_1.JoinColumn)({ name: 'attachment_id' }),
    __metadata("design:type", attachment_entity_1.Attachment)
], TeacherAuditAttachment.prototype, "attachment", void 0);
exports.TeacherAuditAttachment = TeacherAuditAttachment = __decorate([
    (0, typeorm_1.Entity)('teacher_audit_attachment')
], TeacherAuditAttachment);
//# sourceMappingURL=teacher_audit_attachment.entity.js.map