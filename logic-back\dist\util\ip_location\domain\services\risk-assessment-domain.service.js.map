{"version": 3, "file": "risk-assessment-domain.service.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/domain/services/risk-assessment-domain.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAG5C,kEAA2D;AA+CpD,IAAM,2BAA2B,GAAjC,MAAM,2BAA2B;IACrB,aAAa,GAAyB;QACrD,YAAY,EAAE,EAAE;QAChB,eAAe,EAAE,EAAE;QACnB,aAAa,EAAE,GAAG;QAClB,kBAAkB,EAAE,EAAE;QACtB,mBAAmB,EAAE,EAAE;QACvB,iBAAiB,EAAE,EAAE;QACrB,eAAe,EAAE,CAAC;QAClB,iBAAiB,EAAE,EAAE;QACrB,qBAAqB,EAAE,CAAC;QACxB,oBAAoB,EAAE,EAAE;QACxB,cAAc,EAAE,EAAE;KACnB,CAAC;IAEM,MAAM,CAAuB;IAErC;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IAC1C,CAAC;IASD,eAAe,CACb,eAAmC,EACnC,WAAgC,EAChC,SAAoB;QAEpB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,SAAS,GAAG,CAAC,CAAC;QAGlB,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;YAC9B,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;YAC5C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAGD,IAAI,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC5D,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;YAC3C,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;QAGD,MAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAClD,eAAe,EACf,WAAW,EACX,UAAU,CACX,CAAC;QAEF,IAAI,CAAC,kBAAkB,IAAI,eAAe,CAAC,UAAU,IAAI,eAAe,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3F,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAC7C,WAAW,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;QAGD,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAC9C,eAAe,EACf,WAAW,EACX,MAAM,CACP,CAAC;YAEF,IAAI,CAAC,cAAc,IAAI,eAAe,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACrD,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC,UAAU,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAGD,IAAI,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,WAAW,CAAC,EAAE,CAAC;YAC9E,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;YACzC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAGD,IAAI,WAAW,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YACnC,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAGD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;YAChC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACtE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAGD,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACrD,SAAS,IAAI,CAAC,CAAC;YACf,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;QAGD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAEzD,OAAO,yBAAS,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IACnD,CAAC;IAQD,wBAAwB,CACtB,gBAAoC,EACpC,eAAmC;QAEnC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,SAAS,GAAG,CAAC,CAAC;QAGlB,MAAM,UAAU,GAAG,eAAe,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAEzE,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;YACpB,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;YAC3B,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;YAC3B,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAGD,IAAI,gBAAgB,CAAC,OAAO,KAAK,eAAe,CAAC,OAAO,EAAE,CAAC;YACzD,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAGD,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACtD,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAGD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAClD,SAAS,IAAI,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,yBAAS,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IAClD,CAAC;IAQD,wBAAwB,CACtB,SAAoB,EACpB,WAAgC;QAOhC,IAAI,iBAAiB,GAAG,SAAS,CAAC,iBAAiB,CAAC;QACpD,IAAI,OAAO,GAA8B,KAAK,CAAC;QAG/C,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,iBAAiB,GAAG,IAAI,CAAC;YACzB,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC;QAGD,IAAI,SAAS,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;YAC1D,iBAAiB,GAAG,IAAI,CAAC;YACzB,OAAO,GAAG,QAAQ,CAAC;QACrB,CAAC;QAGD,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACvC,iBAAiB,GAAG,IAAI,CAAC;YACzB,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC;QAED,OAAO;YACL,iBAAiB;YACjB,mBAAmB,EAAE,SAAS,CAAC,8BAA8B;YAC7D,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,OAAO;SACR,CAAC;IACJ,CAAC;IAOD,wBAAwB,CAAC,WAAgC;QAMvD,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,IAAI,UAAU,GAAG,EAAE,CAAC;QAGpB,IAAI,WAAW,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACjC,UAAU,IAAI,EAAE,CAAC;YACjB,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;aAAM,IAAI,WAAW,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;YACvC,UAAU,IAAI,EAAE,CAAC;YACjB,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;QAGD,MAAM,QAAQ,GAAG,WAAW,CAAC,eAAe,GAAG,CAAC;YAC9C,CAAC,CAAC,CAAC,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,eAAe,CAAC,GAAG,GAAG;YAClE,CAAC,CAAC,CAAC,CAAC;QAEN,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;YAClB,UAAU,IAAI,EAAE,CAAC;YACjB,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;aAAM,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACxB,UAAU,IAAI,EAAE,CAAC;YACjB,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;QAGD,MAAM,oBAAoB,GAAG,WAAW,CAAC,eAAe;aACrD,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAEvC,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAC7B,UAAU,IAAI,oBAAoB,GAAG,CAAC,CAAC;YACvC,eAAe,CAAC,IAAI,CAAC,GAAG,oBAAoB,OAAO,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;QAGD,IAAI,SAAoC,CAAC;QACzC,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;YACrB,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC;aAAM,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;YAC5B,SAAS,GAAG,QAAQ,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,MAAM,CAAC;YACnB,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrC,CAAC;QAED,OAAO;YACL,SAAS;YACT,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YAClD,eAAe;YACf,eAAe;SAChB,CAAC;IACJ,CAAC;IAKO,oBAAoB,CAC1B,QAA4B,EAC5B,WAAgC,EAChC,KAAsC;QAEtC,OAAO,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YACpD,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,SAAS;oBACZ,OAAO,WAAW,CAAC,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO;wBACjD,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC;gBACnC,KAAK,UAAU;oBACb,OAAO,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACvD,KAAK,MAAM;oBACT,OAAO,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACnD;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,eAAe,CACrB,QAA4B,EAC5B,WAAgC;QAEhC,OAAO,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CACpD,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC7C,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CACzC,CAAC;IACJ,CAAC;IAKO,SAAS,CAAC,WAAgC;QAChD,OAAO,WAAW,CAAC,eAAe,GAAG,CAAC;YAC/B,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;IACpE,CAAC;IAKD,YAAY,CAAC,SAAwC;QACnD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;IACjD,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF,CAAA;AAlUY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;;GACA,2BAA2B,CAkUvC"}