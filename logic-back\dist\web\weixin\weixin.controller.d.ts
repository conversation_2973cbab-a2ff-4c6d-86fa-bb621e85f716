import { UserAuthService } from '../user_auth/user_auth.service';
import { WeixinApiUtilService } from '../utils/weixin_api_util';
import { Response } from 'express';
import { WeixinConfigService } from '../config/weixin-config.service';
export declare class WeixinController {
    private readonly userAuthService;
    private readonly weixinApiUtilService;
    private readonly weixinConfigService;
    private readonly logger;
    constructor(userAuthService: UserAuthService, weixinApiUtilService: WeixinApiUtilService, weixinConfigService: WeixinConfigService);
    bindPhone(bindDto: any): Promise<{
        success: boolean;
        message: string;
        data: {
            userId: any;
        };
    } | {
        success: boolean;
        message: any;
        data: null;
    }>;
    checkBindStatus(queryParams: any): Promise<{
        success: boolean;
        message: string;
        data: {
            bound: boolean;
            user?: undefined;
        };
    } | {
        success: boolean;
        message: string;
        data: {
            bound: boolean;
            user: {
                id: any;
                nickName: any;
                phone: any;
            } | null;
        };
    }>;
    bindPage(openid: string, sceneStr: string, res: Response): Promise<Response<any, Record<string, any>>>;
}
