"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const attachment_entity_1 = require("./entities/attachment.entity");
let AttachmentService = class AttachmentService {
    attachmentRepository;
    constructor(attachmentRepository) {
        this.attachmentRepository = attachmentRepository;
    }
    async create(createAttachmentDto) {
        const attachment = this.attachmentRepository.create(createAttachmentDto);
        return this.attachmentRepository.save(attachment);
    }
    async findAll() {
        return this.attachmentRepository.find();
    }
    async findOne(id) {
        const attachment = await this.attachmentRepository.findOne({ where: { id } });
        if (!attachment) {
            throw new common_1.NotFoundException(`附件ID为${id}的记录不存在`);
        }
        return attachment;
    }
    async update(id, updateAttachmentDto) {
        const attachment = await this.findOne(id);
        Object.assign(attachment, updateAttachmentDto);
        return this.attachmentRepository.save(attachment);
    }
    async remove(id) {
        const result = await this.attachmentRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`附件ID为${id}的记录不存在`);
        }
    }
    async createMany(createAttachmentDtos) {
        const attachments = createAttachmentDtos.map(dto => this.attachmentRepository.create(dto));
        return this.attachmentRepository.save(attachments);
    }
};
exports.AttachmentService = AttachmentService;
exports.AttachmentService = AttachmentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(attachment_entity_1.Attachment)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AttachmentService);
//# sourceMappingURL=attachment.service.js.map