export declare class GeographicLocation {
    private readonly _country;
    private readonly _province;
    private readonly _city;
    private readonly _isp;
    private readonly _dataSource;
    private readonly _confidence;
    constructor(country: string, province: string, city: string, isp: string, dataSource?: 'ip2region' | 'fallback', confidence?: number);
    get country(): string;
    get province(): string;
    get city(): string;
    get isp(): string;
    get dataSource(): 'ip2region' | 'fallback';
    get confidence(): number;
    get isDomestic(): boolean;
    get isForeign(): boolean;
    get hasEmptyFields(): boolean;
    get isHighQuality(): boolean;
    get emptyFieldCount(): number;
    get displayName(): string;
    get fullDescription(): string;
    isSameProvince(other: GeographicLocation): boolean;
    isSameCity(other: GeographicLocation): boolean;
    isSameISP(other: GeographicLocation): boolean;
    calculateSimilarity(other: GeographicLocation): number;
    private normalizeLocationField;
    static create(country: string, province: string, city: string, isp: string, dataSource?: 'ip2region' | 'fallback', confidence?: number): GeographicLocation;
    static createUnknown(): GeographicLocation;
    static fromIp2RegionData(rawData: any): GeographicLocation;
    equals(other: GeographicLocation): boolean;
    toString(): string;
    toJSON(): object;
}
