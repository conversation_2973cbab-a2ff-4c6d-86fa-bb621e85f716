import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';

/**
 * 更新用户常用位置命令
 * 封装更新用户常用登录地的写操作
 */
export class UpdateCommonLocationCommand {
  public readonly userId: number;
  public readonly ipAddress: IpAddress;
  public readonly location: GeographicLocation;
  public readonly sessionId?: string;
  public readonly userAgent?: string;
  public readonly timestamp: Date;

  constructor(
    userId: number,
    ipAddress: IpAddress,
    location: GeographicLocation,
    sessionId?: string,
    userAgent?: string
  ) {
    this.userId = userId;
    this.ipAddress = ipAddress;
    this.location = location;
    this.sessionId = sessionId;
    this.userAgent = userAgent;
    this.timestamp = new Date();
  }

  /**
   * 创建命令的静态工厂方法
   */
  static create(
    userId: number,
    ipAddressString: string,
    location: GeographicLocation,
    sessionId?: string,
    userAgent?: string
  ): UpdateCommonLocationCommand {
    const ipAddress = IpAddress.create(ipAddressString);
    return new UpdateCommonLocationCommand(userId, ipAddress, location, sessionId, userAgent);
  }

  /**
   * 验证命令的有效性
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.userId <= 0) {
      errors.push('用户ID必须大于0');
    }

    if (!this.ipAddress.canGeolocate) {
      errors.push('IP地址不支持地理位置解析');
    }

    if (!this.location.isHighQuality && this.location.confidence < 20) {
      errors.push('地理位置数据质量过低');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取命令摘要信息
   */
  getSummary(): string {
    return `更新用户${this.userId}的常用位置: ${this.location.displayName} (${this.ipAddress.masked})`;
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      userId: this.userId,
      ipAddress: this.ipAddress.toJSON(),
      location: this.location.toJSON(),
      sessionId: this.sessionId,
      userAgent: this.userAgent,
      timestamp: this.timestamp.toISOString()
    };
  }
}
