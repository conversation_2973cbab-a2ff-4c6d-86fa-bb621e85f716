"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationDomainException = void 0;
class IpLocationDomainException extends Error {
    code;
    timestamp;
    context;
    constructor(message, code, context) {
        super(message);
        this.name = this.constructor.name;
        this.code = code;
        this.timestamp = new Date();
        this.context = context;
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
    getDetails() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            timestamp: this.timestamp.toISOString(),
            context: this.context,
            stack: this.stack
        };
    }
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            timestamp: this.timestamp.toISOString(),
            context: this.context
        };
    }
}
exports.IpLocationDomainException = IpLocationDomainException;
//# sourceMappingURL=ip-location-domain.exception.js.map