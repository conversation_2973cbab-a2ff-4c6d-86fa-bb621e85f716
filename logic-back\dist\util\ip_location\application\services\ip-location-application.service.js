"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationApplicationService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../../../common/logger/logger.service");
const ip_location_command_service_1 = require("./ip-location-command.service");
const ip_location_query_service_1 = require("./ip-location-query.service");
const get_location_by_ip_query_1 = require("../queries/get-location-by-ip.query");
const get_user_location_stats_query_1 = require("../queries/get-user-location-stats.query");
const ip_location_domain_service_1 = require("../../domain/services/ip-location-domain.service");
const risk_assessment_domain_service_1 = require("../../domain/services/risk-assessment-domain.service");
const ip_location_util_1 = require("../../utils/ip-location.util");
const risk_assessment_util_1 = require("../../utils/risk-assessment.util");
let IpLocationApplicationService = class IpLocationApplicationService {
    commandService;
    queryService;
    domainService;
    riskAssessmentDomainService;
    ipLocationUtil;
    riskAssessmentUtil;
    loggerService;
    constructor(commandService, queryService, domainService, riskAssessmentDomainService, ipLocationUtil, riskAssessmentUtil, loggerService) {
        this.commandService = commandService;
        this.queryService = queryService;
        this.domainService = domainService;
        this.riskAssessmentDomainService = riskAssessmentDomainService;
        this.ipLocationUtil = ipLocationUtil;
        this.riskAssessmentUtil = riskAssessmentUtil;
        this.loggerService = loggerService;
    }
    async queryIpLocation(request) {
        const startTime = Date.now();
        try {
            const query = get_location_by_ip_query_1.GetLocationByIpQuery.create(request.ip, request.includeRisk);
            const queryResult = await this.queryService.handleGetLocationByIp(query);
            if (!queryResult.success) {
                throw new Error(queryResult.errors?.join(', ') || '查询失败');
            }
            const locationData = queryResult.data;
            const response = {
                ip: request.ip,
                country: locationData.country,
                province: locationData.province,
                city: locationData.city,
                isp: locationData.isp,
                dataSource: locationData.dataSource,
                confidence: locationData.confidence,
                isHighQuality: locationData.isHighQuality,
                displayName: locationData.displayName
            };
            if (request.includeRisk && locationData.riskAssessment) {
                response.risk = {
                    level: locationData.riskAssessment.level,
                    score: locationData.riskAssessment.score,
                    reason: locationData.riskAssessment.reason,
                    needVerification: locationData.riskAssessment.needVerification
                };
            }
            this.loggerService.logBusiness('IpLocationApplicationService', 'queryIpLocation', {
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ip),
                location: response.displayName,
                confidence: response.confidence,
                responseTime: Date.now() - startTime
            });
            return response;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'queryIpLocation', {
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ip),
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async checkLoginRisk(request) {
        const startTime = Date.now();
        try {
            const locationInfo = await this.ipLocationUtil.getBasicLocationByIP(request.ipAddress);
            const userStats = await this.getUserLocationStats(request.userId);
            const riskAssessment = await this.riskAssessmentUtil.assessLoginRisk(request.userId, locationInfo, userStats);
            const recommendedActions = this.riskAssessmentUtil.getRecommendedVerificationMethods(riskAssessment);
            const response = {
                riskAssessment: {
                    level: riskAssessment.level,
                    score: riskAssessment.score,
                    reason: riskAssessment.reason,
                    factors: riskAssessment.factors,
                    needVerification: riskAssessment.needVerification,
                    recommendedActions
                },
                location: {
                    country: locationInfo.country,
                    province: locationInfo.province,
                    city: locationInfo.city,
                    isp: locationInfo.isp,
                    displayName: `${locationInfo.country} ${locationInfo.province} ${locationInfo.city}`
                },
                userHistory: {
                    lastLoginLocation: this.getLastLoginLocationDisplay(userStats),
                    commonLocationCount: userStats.commonLocations.length,
                    isNewLocation: !this.isLocationInHistory(locationInfo, userStats)
                }
            };
            this.loggerService.logBusiness('IpLocationApplicationService', 'checkLoginRisk', {
                userId: request.userId,
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ipAddress),
                riskLevel: response.riskAssessment.level,
                riskScore: response.riskAssessment.score,
                responseTime: Date.now() - startTime
            });
            return response;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'checkLoginRisk', {
                userId: request.userId,
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ipAddress),
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async getUserLocationStatistics(userId, days = 30) {
        const startTime = Date.now();
        try {
            const query = get_user_location_stats_query_1.GetUserLocationStatsQuery.createBasic(userId, days);
            const queryResult = await this.queryService.handleGetUserLocationStats(query);
            if (!queryResult.success) {
                throw new Error(queryResult.errors?.join(', ') || '查询失败');
            }
            const statsData = queryResult.data;
            const totalLoginCount = commonLocations.reduce((sum, loc) => sum + loc.loginCount, 0);
            const trustedLocations = commonLocations.filter(loc => loc.isTrusted).length;
            const riskLoginCount = 0;
            const response = {
                userId,
                statisticsPeriod: {
                    days,
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString()
                },
                commonLocations: commonLocations.map(loc => ({
                    province: loc.province,
                    city: loc.city,
                    loginCount: loc.loginCount,
                    isTrusted: loc.isTrusted,
                    trustScore: Number(loc.trustScore),
                    firstLoginAt: loc.firstLoginAt?.toISOString() || '',
                    lastLoginAt: loc.lastLoginAt?.toISOString() || ''
                })),
                summary: {
                    totalLocations: commonLocations.length,
                    trustedLocations,
                    riskLoginCount,
                    totalLoginCount,
                    riskRate: totalLoginCount > 0 ? (riskLoginCount / totalLoginCount) * 100 : 0
                }
            };
            this.loggerService.logBusiness('IpLocationApplicationService', 'getUserLocationStatistics', {
                userId,
                totalLocations: response.summary.totalLocations,
                totalLoginCount: response.summary.totalLoginCount,
                responseTime: Date.now() - startTime
            });
            return response;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'getUserLocationStatistics', {
                userId,
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async setTrustedLocation(userId, request) {
        const startTime = Date.now();
        try {
            let commonLocation = await this.userCommonLocationRepository.findOne({
                where: {
                    userId,
                    province: request.province,
                    city: request.city
                }
            });
            if (!commonLocation) {
                commonLocation = this.userCommonLocationRepository.create({
                    userId,
                    country: '中国',
                    province: request.province,
                    city: request.city,
                    loginCount: 0,
                    isTrusted: true,
                    trustScore: 80
                });
            }
            else {
                commonLocation.setAsTrusted(request.reason);
            }
            await this.userCommonLocationRepository.save(commonLocation);
            this.loggerService.logBusiness('IpLocationApplicationService', 'setTrustedLocation', {
                userId,
                location: `${request.province} ${request.city}`,
                reason: request.reason,
                responseTime: Date.now() - startTime
            });
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'setTrustedLocation', {
                userId,
                location: `${request.province} ${request.city}`,
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async updateUserCommonLocation(userId, locationInfo) {
        try {
            let commonLocation = await this.userCommonLocationRepository.findOne({
                where: {
                    userId,
                    province: locationInfo.province,
                    city: locationInfo.city
                }
            });
            if (!commonLocation) {
                commonLocation = this.userCommonLocationRepository.create({
                    userId,
                    country: locationInfo.country,
                    province: locationInfo.province,
                    city: locationInfo.city,
                    isp: locationInfo.isp,
                    loginCount: 1,
                    firstLoginAt: new Date(),
                    lastLoginAt: new Date(),
                    isTrusted: false,
                    trustScore: 10
                });
            }
            else {
                commonLocation.updateLoginStats();
                if (locationInfo.isp && locationInfo.isp !== '未知') {
                    commonLocation.isp = locationInfo.isp;
                }
            }
            await this.userCommonLocationRepository.save(commonLocation);
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'updateUserCommonLocation', {
                userId,
                location: locationInfo.displayName
            }, error);
        }
    }
    async getUserLocationStats(userId) {
        const commonLocations = await this.userCommonLocationRepository.find({
            where: { userId },
            order: { loginCount: 'DESC' }
        });
        const totalLoginCount = commonLocations.reduce((sum, loc) => sum + loc.loginCount, 0);
        return {
            commonLocations: commonLocations.map(loc => ({
                province: loc.province,
                city: loc.city,
                loginCount: loc.loginCount,
                isTrusted: loc.isTrusted,
                lastLoginAt: loc.lastLoginAt || new Date()
            })),
            riskLoginCount: 0,
            totalLoginCount
        };
    }
    getLastLoginLocationDisplay(userStats) {
        if (userStats.commonLocations.length === 0) {
            return '无历史记录';
        }
        const lastLocation = userStats.commonLocations
            .sort((a, b) => b.lastLoginAt.getTime() - a.lastLoginAt.getTime())[0];
        return `${lastLocation.province} ${lastLocation.city}`;
    }
    isLocationInHistory(location, userStats) {
        return userStats.commonLocations.some(loc => loc.province === location.province && loc.city === location.city);
    }
};
exports.IpLocationApplicationService = IpLocationApplicationService;
exports.IpLocationApplicationService = IpLocationApplicationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ip_location_command_service_1.IpLocationCommandService,
        ip_location_query_service_1.IpLocationQueryService,
        ip_location_domain_service_1.IpLocationDomainService,
        risk_assessment_domain_service_1.RiskAssessmentDomainService,
        ip_location_util_1.IpLocationUtil,
        risk_assessment_util_1.RiskAssessmentUtil,
        logger_service_1.LoggerService])
], IpLocationApplicationService);
//# sourceMappingURL=ip-location-application.service.js.map