"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationApplicationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ip_location_util_1 = require("../../utils/ip-location.util");
const risk_assessment_util_1 = require("../../utils/risk-assessment.util");
const user_common_location_entity_1 = require("../../domain/entities/user-common-location.entity");
const logger_service_1 = require("../../../../common/logger/logger.service");
let IpLocationApplicationService = class IpLocationApplicationService {
    ipLocationUtil;
    riskAssessmentUtil;
    loggerService;
    userCommonLocationRepository;
    constructor(ipLocationUtil, riskAssessmentUtil, loggerService, userCommonLocationRepository) {
        this.ipLocationUtil = ipLocationUtil;
        this.riskAssessmentUtil = riskAssessmentUtil;
        this.loggerService = loggerService;
        this.userCommonLocationRepository = userCommonLocationRepository;
    }
    async queryIpLocation(request) {
        const startTime = Date.now();
        try {
            const locationInfo = await this.ipLocationUtil.getLocationByIP(request.ip);
            const response = {
                ip: request.ip,
                country: locationInfo.country,
                province: locationInfo.province,
                city: locationInfo.city,
                isp: locationInfo.isp,
                dataSource: locationInfo.dataSource,
                confidence: locationInfo.confidence,
                isHighQuality: locationInfo.confidence >= 80,
                displayName: locationInfo.displayName
            };
            if (request.includeRisk) {
                response.risk = {
                    level: 'LOW',
                    score: 0,
                    reason: '需要用户ID进行完整风险评估',
                    needVerification: false
                };
            }
            this.loggerService.logBusiness('IpLocationApplicationService', 'queryIpLocation', {
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ip),
                location: response.displayName,
                confidence: response.confidence,
                responseTime: Date.now() - startTime
            });
            return response;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'queryIpLocation', {
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ip),
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async checkLoginRisk(request) {
        const startTime = Date.now();
        try {
            const locationInfo = await this.ipLocationUtil.getBasicLocationByIP(request.ipAddress);
            const userStats = await this.getUserLocationStats(request.userId);
            const riskAssessment = await this.riskAssessmentUtil.assessLoginRisk(request.userId, locationInfo, userStats);
            const recommendedActions = this.riskAssessmentUtil.getRecommendedVerificationMethods(riskAssessment);
            const response = {
                riskAssessment: {
                    level: riskAssessment.level,
                    score: riskAssessment.score,
                    reason: riskAssessment.reason,
                    factors: riskAssessment.factors,
                    needVerification: riskAssessment.needVerification,
                    recommendedActions
                },
                location: {
                    country: locationInfo.country,
                    province: locationInfo.province,
                    city: locationInfo.city,
                    isp: locationInfo.isp,
                    displayName: `${locationInfo.country} ${locationInfo.province} ${locationInfo.city}`
                },
                userHistory: {
                    lastLoginLocation: this.getLastLoginLocationDisplay(userStats),
                    commonLocationCount: userStats.commonLocations.length,
                    isNewLocation: !this.isLocationInHistory(locationInfo, userStats)
                }
            };
            this.loggerService.logBusiness('IpLocationApplicationService', 'checkLoginRisk', {
                userId: request.userId,
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ipAddress),
                riskLevel: response.riskAssessment.level,
                riskScore: response.riskAssessment.score,
                responseTime: Date.now() - startTime
            });
            return response;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'checkLoginRisk', {
                userId: request.userId,
                ip: ip_location_util_1.IpLocationUtil.maskIP(request.ipAddress),
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async getUserLocationStatistics(userId, days = 30) {
        const startTime = Date.now();
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - days);
            const commonLocations = await this.userCommonLocationRepository.find({
                where: { userId },
                order: { loginCount: 'DESC' }
            });
            const totalLoginCount = commonLocations.reduce((sum, loc) => sum + loc.loginCount, 0);
            const trustedLocations = commonLocations.filter(loc => loc.isTrusted).length;
            const riskLoginCount = 0;
            const response = {
                userId,
                statisticsPeriod: {
                    days,
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString()
                },
                commonLocations: commonLocations.map(loc => ({
                    province: loc.province,
                    city: loc.city,
                    loginCount: loc.loginCount,
                    isTrusted: loc.isTrusted,
                    trustScore: Number(loc.trustScore),
                    firstLoginAt: loc.firstLoginAt?.toISOString() || '',
                    lastLoginAt: loc.lastLoginAt?.toISOString() || ''
                })),
                summary: {
                    totalLocations: commonLocations.length,
                    trustedLocations,
                    riskLoginCount,
                    totalLoginCount,
                    riskRate: totalLoginCount > 0 ? (riskLoginCount / totalLoginCount) * 100 : 0
                }
            };
            this.loggerService.logBusiness('IpLocationApplicationService', 'getUserLocationStatistics', {
                userId,
                totalLocations: response.summary.totalLocations,
                totalLoginCount: response.summary.totalLoginCount,
                responseTime: Date.now() - startTime
            });
            return response;
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'getUserLocationStatistics', {
                userId,
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async setTrustedLocation(userId, request) {
        const startTime = Date.now();
        try {
            let commonLocation = await this.userCommonLocationRepository.findOne({
                where: {
                    userId,
                    province: request.province,
                    city: request.city
                }
            });
            if (!commonLocation) {
                commonLocation = this.userCommonLocationRepository.create({
                    userId,
                    country: '中国',
                    province: request.province,
                    city: request.city,
                    loginCount: 0,
                    isTrusted: true,
                    trustScore: 80
                });
            }
            else {
                commonLocation.setAsTrusted(request.reason);
            }
            await this.userCommonLocationRepository.save(commonLocation);
            this.loggerService.logBusiness('IpLocationApplicationService', 'setTrustedLocation', {
                userId,
                location: `${request.province} ${request.city}`,
                reason: request.reason,
                responseTime: Date.now() - startTime
            });
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'setTrustedLocation', {
                userId,
                location: `${request.province} ${request.city}`,
                responseTime: Date.now() - startTime
            }, error);
            throw error;
        }
    }
    async updateUserCommonLocation(userId, locationInfo) {
        try {
            let commonLocation = await this.userCommonLocationRepository.findOne({
                where: {
                    userId,
                    province: locationInfo.province,
                    city: locationInfo.city
                }
            });
            if (!commonLocation) {
                commonLocation = this.userCommonLocationRepository.create({
                    userId,
                    country: locationInfo.country,
                    province: locationInfo.province,
                    city: locationInfo.city,
                    isp: locationInfo.isp,
                    loginCount: 1,
                    firstLoginAt: new Date(),
                    lastLoginAt: new Date(),
                    isTrusted: false,
                    trustScore: 10
                });
            }
            else {
                commonLocation.updateLoginStats();
                if (locationInfo.isp && locationInfo.isp !== '未知') {
                    commonLocation.isp = locationInfo.isp;
                }
            }
            await this.userCommonLocationRepository.save(commonLocation);
        }
        catch (error) {
            this.loggerService.logBusiness('IpLocationApplicationService', 'updateUserCommonLocation', {
                userId,
                location: locationInfo.displayName
            }, error);
        }
    }
    async getUserLocationStats(userId) {
        const commonLocations = await this.userCommonLocationRepository.find({
            where: { userId },
            order: { loginCount: 'DESC' }
        });
        const totalLoginCount = commonLocations.reduce((sum, loc) => sum + loc.loginCount, 0);
        return {
            commonLocations: commonLocations.map(loc => ({
                province: loc.province,
                city: loc.city,
                loginCount: loc.loginCount,
                isTrusted: loc.isTrusted,
                lastLoginAt: loc.lastLoginAt || new Date()
            })),
            riskLoginCount: 0,
            totalLoginCount
        };
    }
    getLastLoginLocationDisplay(userStats) {
        if (userStats.commonLocations.length === 0) {
            return '无历史记录';
        }
        const lastLocation = userStats.commonLocations
            .sort((a, b) => b.lastLoginAt.getTime() - a.lastLoginAt.getTime())[0];
        return `${lastLocation.province} ${lastLocation.city}`;
    }
    isLocationInHistory(location, userStats) {
        return userStats.commonLocations.some(loc => loc.province === location.province && loc.city === location.city);
    }
};
exports.IpLocationApplicationService = IpLocationApplicationService;
exports.IpLocationApplicationService = IpLocationApplicationService = __decorate([
    (0, common_1.Injectable)(),
    __param(3, (0, typeorm_1.InjectRepository)(user_common_location_entity_1.UserCommonLocation)),
    __metadata("design:paramtypes", [ip_location_util_1.IpLocationUtil,
        risk_assessment_util_1.RiskAssessmentUtil,
        logger_service_1.LoggerService,
        typeorm_2.Repository])
], IpLocationApplicationService);
//# sourceMappingURL=ip-location-application.service.js.map