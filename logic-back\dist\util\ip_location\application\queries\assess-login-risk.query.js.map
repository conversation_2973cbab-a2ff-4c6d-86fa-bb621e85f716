{"version": 3, "file": "assess-login-risk.query.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/application/queries/assess-login-risk.query.ts"], "names": [], "mappings": ";;;AAAA,4EAAqE;AAMrE,MAAa,oBAAoB;IACf,MAAM,CAAS;IACf,SAAS,CAAY;IACrB,SAAS,CAAU;IACnB,SAAS,CAAU;IACnB,UAAU,CAAU;IACpB,sBAAsB,CAAU;IAChC,kBAAkB,CAAU;IAC5B,SAAS,CAAO;IAEhC,YACE,MAAc,EACd,SAAoB,EACpB,SAAkB,EAClB,SAAkB,EAClB,UAAmB,EACnB,yBAAkC,IAAI,EACtC,qBAA8B,KAAK;QAEnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;QACrD,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAKD,MAAM,CAAC,WAAW,CAChB,MAAc,EACd,eAAuB,EACvB,SAAkB;QAElB,MAAM,SAAS,GAAG,yBAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACpD,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACnG,CAAC;IAKD,MAAM,CAAC,cAAc,CACnB,MAAc,EACd,eAAuB,EACvB,SAAkB,EAClB,SAAkB,EAClB,UAAmB;QAEnB,MAAM,SAAS,GAAG,yBAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACpD,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnG,CAAC;IAKD,MAAM,CAAC,YAAY,CACjB,MAAc,EACd,eAAuB;QAEvB,MAAM,SAAS,GAAG,yBAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACpD,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACpG,CAAC;IAKD,QAAQ;QACN,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAKD,WAAW;QACT,MAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QACxE,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,OAAO,cAAc,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,qBAAqB,GAAG,aAAa,EAAE,CAAC;IACrG,CAAC;IAKD,UAAU;QACR,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9D,OAAO,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,SAAS,OAAO,GAAG,WAAW,EAAE,CAAC;IACrF,CAAC;IAKD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC;IAKD,IAAI,cAAc;QAEhB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC7F,CAAC;IAKD,oBAAoB;QAMlB,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;SACrD,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,IAAI,CAAC,sBAAsB;YAAE,UAAU,IAAI,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,kBAAkB;YAAE,UAAU,IAAI,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,SAAS;YAAE,UAAU,IAAI,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,UAAU;YAAE,UAAU,IAAI,CAAC,CAAC;QAErC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QACrC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QACrC,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,uBAAuB;QACrB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAExC,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC;YAC1B,KAAK,QAAQ,CAAC,CAAC,OAAO,GAAG,CAAC;YAC1B,KAAK,SAAS,CAAC,CAAC,OAAO,GAAG,CAAC;YAC3B,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;QACtB,CAAC;IACH,CAAC;IAKD,MAAM;QACJ,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YAClC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACvC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACpD,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;SAC/C,CAAC;IACJ,CAAC;CACF;AAnMD,oDAmMC"}