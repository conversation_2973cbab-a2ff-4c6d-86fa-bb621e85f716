"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrder = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let PackageOrder = class PackageOrder {
    id;
    orderNo;
    userId;
    packageId;
    packageName;
    points;
    validityDays;
    price;
    originalPrice;
    discountRate;
    status;
    paymentId;
    paidTime;
    createTime;
    updateTime;
    promotion;
};
exports.PackageOrder = PackageOrder;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('uk_order_no', { unique: true }),
    (0, typeorm_1.Column)({ comment: '订单编号', length: 50 }),
    (0, swagger_1.ApiProperty)({ description: '订单编号' }),
    __metadata("design:type", String)
], PackageOrder.prototype, "orderNo", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_user_id'),
    (0, typeorm_1.Column)({ comment: '用户ID', length: 50 }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], PackageOrder.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_package_id'),
    (0, typeorm_1.Column)({ comment: '套餐ID' }),
    (0, swagger_1.ApiProperty)({ description: '套餐ID' }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "packageId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '套餐名称', length: 100 }),
    (0, swagger_1.ApiProperty)({ description: '套餐名称' }),
    __metadata("design:type", String)
], PackageOrder.prototype, "packageName", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '点数', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '点数' }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "points", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '有效期(天)' }),
    (0, swagger_1.ApiProperty)({ description: '有效期(天)' }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "validityDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '实付金额', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '实付金额' }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '原价', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '原价' }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "originalPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '折扣率', type: 'decimal', precision: 5, scale: 2, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '折扣率', required: false }),
    __metadata("design:type", Number)
], PackageOrder.prototype, "discountRate", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Column)({ comment: '订单状态(pending-待支付/paid-已支付/cancelled-已取消)', length: 20 }),
    (0, swagger_1.ApiProperty)({ description: '订单状态(pending-待支付/paid-已支付/cancelled-已取消)' }),
    __metadata("design:type", String)
], PackageOrder.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付平台交易号', length: 64, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '支付平台交易号', required: false }),
    __metadata("design:type", String)
], PackageOrder.prototype, "paymentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '支付时间', type: 'datetime', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '支付时间', required: false }),
    __metadata("design:type", Date)
], PackageOrder.prototype, "paidTime", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_create_time'),
    (0, typeorm_1.Column)({ comment: '创建时间', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PackageOrder.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '更新时间', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], PackageOrder.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '宣传话语', type: 'json', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '宣传话语' }),
    __metadata("design:type", Object)
], PackageOrder.prototype, "promotion", void 0);
exports.PackageOrder = PackageOrder = __decorate([
    (0, typeorm_1.Entity)('package_order')
], PackageOrder);
//# sourceMappingURL=package-order.entity.js.map