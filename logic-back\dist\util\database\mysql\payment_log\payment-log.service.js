"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentLogService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentLogService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_log_entity_1 = require("./entities/payment-log.entity");
const payment_log_dto_1 = require("./dto/payment-log.dto");
const uuid_1 = require("uuid");
let PaymentLogService = PaymentLogService_1 = class PaymentLogService {
    paymentLogRepository;
    logger = new common_1.Logger(PaymentLogService_1.name);
    constructor(paymentLogRepository) {
        this.paymentLogRepository = paymentLogRepository;
    }
    async create(createDto) {
        try {
            const startTime = Date.now();
            const paymentLog = this.paymentLogRepository.create({
                id: (0, uuid_1.v4)(),
                ...createDto,
                status: createDto.status || payment_log_dto_1.LogStatus.SUCCESS,
            });
            const result = await this.paymentLogRepository.save(paymentLog);
            const executionTime = Date.now() - startTime;
            this.logger.debug(`创建支付日志耗时: ${executionTime}ms`);
            return result;
        }
        catch (error) {
            this.logger.error(`创建支付日志失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findById(id) {
        return this.paymentLogRepository.findOne({ where: { id } });
    }
    async findAll(queryDto, page = 1, limit = 20) {
        try {
            const where = {};
            if (queryDto.logType) {
                where.logType = queryDto.logType;
            }
            if (queryDto.orderNo) {
                where.orderNo = queryDto.orderNo;
            }
            if (queryDto.refundNo) {
                where.refundNo = queryDto.refundNo;
            }
            if (queryDto.paymentChannel) {
                where.paymentChannel = queryDto.paymentChannel;
            }
            if (queryDto.operation) {
                where.operation = queryDto.operation;
            }
            if (queryDto.operatorId) {
                where.operatorId = queryDto.operatorId;
            }
            if (queryDto.status) {
                where.status = queryDto.status;
            }
            if (queryDto.startTime && queryDto.endTime) {
                return await this.paymentLogRepository.findAndCount({
                    where: {
                        ...where,
                        createdAt: (0, typeorm_2.Between)(new Date(queryDto.startTime), new Date(queryDto.endTime)),
                    },
                    order: { createdAt: 'DESC' },
                    skip: (page - 1) * limit,
                    take: limit,
                });
            }
            else if (queryDto.startTime) {
                return await this.paymentLogRepository.findAndCount({
                    where: {
                        ...where,
                        createdAt: (0, typeorm_2.MoreThanOrEqual)(new Date(queryDto.startTime)),
                    },
                    order: { createdAt: 'DESC' },
                    skip: (page - 1) * limit,
                    take: limit,
                });
            }
            else if (queryDto.endTime) {
                return await this.paymentLogRepository.findAndCount({
                    where: {
                        ...where,
                        createdAt: (0, typeorm_2.LessThanOrEqual)(new Date(queryDto.endTime)),
                    },
                    order: { createdAt: 'DESC' },
                    skip: (page - 1) * limit,
                    take: limit,
                });
            }
            else {
                return await this.paymentLogRepository.findAndCount({
                    where,
                    order: { createdAt: 'DESC' },
                    skip: (page - 1) * limit,
                    take: limit,
                });
            }
        }
        catch (error) {
            this.logger.error(`查询支付日志失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByOrderNo(orderNo) {
        try {
            return await this.paymentLogRepository.find({
                where: { orderNo },
                order: { createdAt: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error(`查询订单日志失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findByRefundNo(refundNo) {
        try {
            return await this.paymentLogRepository.find({
                where: { refundNo },
                order: { createdAt: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error(`查询退款日志失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async cleanupOldLogs(days = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            const result = await this.paymentLogRepository.delete({
                createdAt: (0, typeorm_2.LessThanOrEqual)(cutoffDate),
            });
            this.logger.log(`清理${days}天前的旧日志，共删除${result.affected}条记录`);
            return result.affected || 0;
        }
        catch (error) {
            this.logger.error(`清理旧日志失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.PaymentLogService = PaymentLogService;
exports.PaymentLogService = PaymentLogService = PaymentLogService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_log_entity_1.PaymentLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PaymentLogService);
//# sourceMappingURL=payment-log.service.js.map