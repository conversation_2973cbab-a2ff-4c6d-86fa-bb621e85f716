import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { RiskScore } from '../../domain/value-objects/risk-score.vo';

/**
 * 登录类型枚举
 */
export enum LoginType {
  PASSWORD = 'PASSWORD',
  SMS = 'SMS',
  QR_CODE = 'QR_CODE',
  OAUTH = 'OAUTH'
}

/**
 * 登录状态枚举
 */
export enum LoginStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  BLOCKED = 'BLOCKED'
}

/**
 * 记录登录位置命令
 * 封装记录用户登录位置信息的写操作
 */
export class RecordLoginLocationCommand {
  public readonly userId: number;
  public readonly ipAddress: IpAddress;
  public readonly location: GeographicLocation;
  public readonly riskScore: RiskScore;
  public readonly loginType: LoginType;
  public readonly loginStatus: LoginStatus;
  public readonly sessionId?: string;
  public readonly userAgent?: string;
  public readonly deviceInfo?: string;
  public readonly failReason?: string;
  public readonly timestamp: Date;

  constructor(
    userId: number,
    ipAddress: IpAddress,
    location: GeographicLocation,
    riskScore: RiskScore,
    loginType: LoginType,
    loginStatus: LoginStatus,
    sessionId?: string,
    userAgent?: string,
    deviceInfo?: string,
    failReason?: string
  ) {
    this.userId = userId;
    this.ipAddress = ipAddress;
    this.location = location;
    this.riskScore = riskScore;
    this.loginType = loginType;
    this.loginStatus = loginStatus;
    this.sessionId = sessionId;
    this.userAgent = userAgent;
    this.deviceInfo = deviceInfo;
    this.failReason = failReason;
    this.timestamp = new Date();
  }

  /**
   * 创建成功登录记录命令
   */
  static createSuccessLogin(
    userId: number,
    ipAddressString: string,
    location: GeographicLocation,
    riskScore: RiskScore,
    loginType: LoginType,
    sessionId?: string,
    userAgent?: string,
    deviceInfo?: string
  ): RecordLoginLocationCommand {
    const ipAddress = IpAddress.create(ipAddressString);
    return new RecordLoginLocationCommand(
      userId,
      ipAddress,
      location,
      riskScore,
      loginType,
      LoginStatus.SUCCESS,
      sessionId,
      userAgent,
      deviceInfo
    );
  }

  /**
   * 创建失败登录记录命令
   */
  static createFailedLogin(
    userId: number,
    ipAddressString: string,
    location: GeographicLocation,
    riskScore: RiskScore,
    loginType: LoginType,
    failReason: string,
    userAgent?: string,
    deviceInfo?: string
  ): RecordLoginLocationCommand {
    const ipAddress = IpAddress.create(ipAddressString);
    return new RecordLoginLocationCommand(
      userId,
      ipAddress,
      location,
      riskScore,
      loginType,
      LoginStatus.FAILED,
      undefined,
      userAgent,
      deviceInfo,
      failReason
    );
  }

  /**
   * 创建被阻止登录记录命令
   */
  static createBlockedLogin(
    userId: number,
    ipAddressString: string,
    location: GeographicLocation,
    riskScore: RiskScore,
    loginType: LoginType,
    blockReason: string,
    userAgent?: string,
    deviceInfo?: string
  ): RecordLoginLocationCommand {
    const ipAddress = IpAddress.create(ipAddressString);
    return new RecordLoginLocationCommand(
      userId,
      ipAddress,
      location,
      riskScore,
      loginType,
      LoginStatus.BLOCKED,
      undefined,
      userAgent,
      deviceInfo,
      blockReason
    );
  }

  /**
   * 验证命令的有效性
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.userId <= 0) {
      errors.push('用户ID必须大于0');
    }

    if (this.loginStatus === LoginStatus.FAILED && !this.failReason) {
      errors.push('失败登录必须提供失败原因');
    }

    if (this.loginStatus === LoginStatus.BLOCKED && !this.failReason) {
      errors.push('被阻止登录必须提供阻止原因');
    }

    if (this.loginStatus === LoginStatus.SUCCESS && !this.sessionId) {
      errors.push('成功登录必须提供会话ID');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 是否为成功登录
   */
  get isSuccessLogin(): boolean {
    return this.loginStatus === LoginStatus.SUCCESS;
  }

  /**
   * 是否为失败登录
   */
  get isFailedLogin(): boolean {
    return this.loginStatus === LoginStatus.FAILED;
  }

  /**
   * 是否为被阻止登录
   */
  get isBlockedLogin(): boolean {
    return this.loginStatus === LoginStatus.BLOCKED;
  }

  /**
   * 是否为高风险登录
   */
  get isHighRiskLogin(): boolean {
    return this.riskScore.isHighRisk;
  }

  /**
   * 是否为境外登录
   */
  get isForeignLogin(): boolean {
    return this.location.isForeign;
  }

  /**
   * 获取登录摘要信息
   */
  getSummary(): string {
    const status = this.loginStatus === LoginStatus.SUCCESS ? '成功' : 
                  this.loginStatus === LoginStatus.FAILED ? '失败' : '被阻止';
    
    return `用户${this.userId}${status}登录: ${this.location.displayName} (${this.ipAddress.masked}) - ${this.riskScore.levelDescription}`;
  }

  /**
   * 获取安全相关信息
   */
  getSecurityInfo(): {
    riskLevel: string;
    riskFactors: string[];
    needsVerification: boolean;
    isForeign: boolean;
    isNewLocation: boolean;
  } {
    return {
      riskLevel: this.riskScore.level,
      riskFactors: this.riskScore.factors,
      needsVerification: this.riskScore.needsVerification,
      isForeign: this.location.isForeign,
      isNewLocation: this.riskScore.factors.includes('新位置')
    };
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      userId: this.userId,
      ipAddress: this.ipAddress.toJSON(),
      location: this.location.toJSON(),
      riskScore: this.riskScore.toJSON(),
      loginType: this.loginType,
      loginStatus: this.loginStatus,
      sessionId: this.sessionId,
      userAgent: this.userAgent,
      deviceInfo: this.deviceInfo,
      failReason: this.failReason,
      timestamp: this.timestamp.toISOString(),
      securityInfo: this.getSecurityInfo()
    };
  }
}
