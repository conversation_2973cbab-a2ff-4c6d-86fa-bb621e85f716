import { Repository } from 'typeorm';
import { IpLocationUtil, LocationInfoWithQuality } from '../../utils/ip-location.util';
import { RiskAssessmentUtil } from '../../utils/risk-assessment.util';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';
import { LoggerService } from '../../../../common/logger/logger.service';
import { IpQueryRequestDto } from '../dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../dto/responses/location-stats.response.dto';
export declare class IpLocationApplicationService {
    private readonly ipLocationUtil;
    private readonly riskAssessmentUtil;
    private readonly loggerService;
    private readonly userCommonLocationRepository;
    constructor(ipLocationUtil: IpLocationUtil, riskAssessmentUtil: RiskAssessmentUtil, loggerService: LoggerService, userCommonLocationRepository: Repository<UserCommonLocation>);
    queryIpLocation(request: IpQueryRequestDto): Promise<LocationInfoResponseDto>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<RiskAssessmentResponseDto>;
    getUserLocationStatistics(userId: number, days?: number): Promise<LocationStatsResponseDto>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<void>;
    updateUserCommonLocation(userId: number, locationInfo: LocationInfoWithQuality): Promise<void>;
    private getUserLocationStats;
    private getLastLoginLocationDisplay;
    private isLocationInHistory;
}
