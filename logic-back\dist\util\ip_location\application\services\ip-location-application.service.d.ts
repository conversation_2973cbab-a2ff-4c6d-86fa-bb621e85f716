import { Repository } from 'typeorm';
import { LoggerService } from '../../../../common/logger/logger.service';
import { IpLocationCommandService } from './ip-location-command.service';
import { IpLocationQueryService } from './ip-location-query.service';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService } from '../../domain/services/risk-assessment-domain.service';
import { IpQueryRequestDto } from '../dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../dto/responses/location-stats.response.dto';
import { IpLocationUtil, LocationInfoWithQuality } from '../../utils/ip-location.util';
import { RiskAssessmentUtil } from '../../utils/risk-assessment.util';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';
export declare class IpLocationApplicationService {
    private readonly commandService;
    private readonly queryService;
    private readonly domainService;
    private readonly riskAssessmentDomainService;
    private readonly ipLocationUtil;
    private readonly riskAssessmentUtil;
    private readonly userCommonLocationRepository;
    private readonly loggerService;
    constructor(commandService: IpLocationCommandService, queryService: IpLocationQueryService, domainService: IpLocationDomainService, riskAssessmentDomainService: RiskAssessmentDomainService, ipLocationUtil: IpLocationUtil, riskAssessmentUtil: RiskAssessmentUtil, userCommonLocationRepository: Repository<UserCommonLocation>, loggerService: LoggerService);
    queryIpLocation(request: IpQueryRequestDto): Promise<LocationInfoResponseDto>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<RiskAssessmentResponseDto>;
    getUserLocationStatistics(userId: number, days?: number): Promise<LocationStatsResponseDto>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<void>;
    updateUserCommonLocation(userId: number, locationInfo: LocationInfoWithQuality): Promise<void>;
    private getUserLocationStats;
    private getLastLoginLocationDisplay;
    private isLocationInHistory;
}
