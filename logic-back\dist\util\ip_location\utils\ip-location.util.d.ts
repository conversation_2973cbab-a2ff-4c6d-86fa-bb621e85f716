import { RedisService } from '../../database/redis/redis.service';
import { LoggerService } from '../../../common/logger/logger.service';
export interface LocationInfo {
    country: string;
    province: string;
    city: string;
    isp: string;
}
export interface LocationInfoWithQuality extends LocationInfo {
    dataSource: 'ip2region' | 'fallback';
    hasEmptyFields: boolean;
    confidence: number;
    rawData?: any;
    displayName: string;
}
export interface LocationRisk {
    level: 'LOW' | 'MEDIUM' | 'HIGH';
    reason: string;
    needVerification: boolean;
    score: number;
    factors: string[];
}
export declare class IpLocationUtil {
    private readonly redisService;
    private readonly loggerService;
    private ip2region;
    private readonly CACHE_PREFIX;
    private readonly CACHE_TTL;
    constructor(redisService: RedisService, loggerService: LoggerService);
    private initializeIp2Region;
    getLocationByIP(ipAddress: string): Promise<LocationInfoWithQuality>;
    getBasicLocationByIP(ipAddress: string): Promise<LocationInfo>;
    private processRawLocationData;
    private isValidIP;
    private isIPv6;
    private getFallbackLocation;
    private formatLocationDisplay;
    static maskIP(ip: string): string;
}
