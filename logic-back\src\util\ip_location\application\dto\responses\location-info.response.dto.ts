import { ApiProperty } from '@nestjs/swagger';

/**
 * 地理位置信息响应DTO
 */
export class LocationInfoResponseDto {
  @ApiProperty({ description: 'IP地址', example: '**************' })
  ip: string;

  @ApiProperty({ description: '国家', example: '中国' })
  country: string;

  @ApiProperty({ description: '省份', example: '北京市' })
  province: string;

  @ApiProperty({ description: '城市', example: '北京市' })
  city: string;

  @ApiProperty({ description: '网络运营商', example: '联通' })
  isp: string;

  @ApiProperty({ description: '数据来源', example: 'ip2region' })
  dataSource: string;

  @ApiProperty({ description: '数据置信度', example: 95 })
  confidence: number;

  @ApiProperty({ description: '是否为高质量数据', example: true })
  isHighQuality: boolean;

  @ApiProperty({ description: '格式化显示名称', example: '中国 北京市 北京市' })
  displayName: string;

  @ApiProperty({
    description: '风险评估信息',
    required: false,
    example: {
      level: 'LOW',
      score: 15,
      reason: '常用登录地',
      needVerification: false
    }
  })
  risk?: {
    level: string;
    score: number;
    reason: string;
    needVerification: boolean;
  };
}
