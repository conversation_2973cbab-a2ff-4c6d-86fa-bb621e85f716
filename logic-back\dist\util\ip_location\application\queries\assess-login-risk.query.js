"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssessLoginRiskQuery = void 0;
const ip_address_vo_1 = require("../../domain/value-objects/ip-address.vo");
class AssessLoginRiskQuery {
    userId;
    ipAddress;
    userAgent;
    sessionId;
    deviceInfo;
    includeRecommendations;
    includeUserProfile;
    timestamp;
    constructor(userId, ipAddress, userAgent, sessionId, deviceInfo, includeRecommendations = true, includeUserProfile = false) {
        this.userId = userId;
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
        this.sessionId = sessionId;
        this.deviceInfo = deviceInfo;
        this.includeRecommendations = includeRecommendations;
        this.includeUserProfile = includeUserProfile;
        this.timestamp = new Date();
    }
    static createBasic(userId, ipAddressString, userAgent) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new AssessLoginRiskQuery(userId, ipAddress, userAgent, undefined, undefined, true, false);
    }
    static createComplete(userId, ipAddressString, userAgent, sessionId, deviceInfo) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new AssessLoginRiskQuery(userId, ipAddress, userAgent, sessionId, deviceInfo, true, true);
    }
    static createSimple(userId, ipAddressString) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new AssessLoginRiskQuery(userId, ipAddress, undefined, undefined, undefined, false, false);
    }
    validate() {
        const errors = [];
        if (this.userId <= 0) {
            errors.push('用户ID必须大于0');
        }
        if (!this.ipAddress.canGeolocate) {
            errors.push('IP地址不支持地理位置解析');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    getCacheKey() {
        const recommendationsSuffix = this.includeRecommendations ? '_rec' : '';
        const profileSuffix = this.includeUserProfile ? '_profile' : '';
        return `login_risk:${this.userId}:${this.ipAddress.value}${recommendationsSuffix}${profileSuffix}`;
    }
    getSummary() {
        const recInfo = this.includeRecommendations ? '(包含建议)' : '';
        const profileInfo = this.includeUserProfile ? '(包含用户档案)' : '';
        return `评估用户${this.userId}从${this.ipAddress.masked}登录的风险 ${recInfo}${profileInfo}`;
    }
    get needsUserHistory() {
        return true;
    }
    get needsLocationResolution() {
        return this.ipAddress.canGeolocate;
    }
    get isHighPriority() {
        return this.ipAddress.isPublic && (this.includeRecommendations || this.includeUserProfile);
    }
    getDeviceFingerprint() {
        return {
            userAgent: this.userAgent,
            deviceInfo: this.deviceInfo,
            sessionId: this.sessionId,
            hasDeviceInfo: !!(this.userAgent || this.deviceInfo)
        };
    }
    getComplexity() {
        let complexity = 0;
        if (this.includeRecommendations)
            complexity += 1;
        if (this.includeUserProfile)
            complexity += 2;
        if (this.userAgent)
            complexity += 1;
        if (this.deviceInfo)
            complexity += 1;
        if (complexity <= 1)
            return 'SIMPLE';
        if (complexity <= 3)
            return 'MEDIUM';
        return 'COMPLEX';
    }
    getExpectedResponseTime() {
        const complexity = this.getComplexity();
        switch (complexity) {
            case 'SIMPLE': return 100;
            case 'MEDIUM': return 300;
            case 'COMPLEX': return 500;
            default: return 300;
        }
    }
    toJSON() {
        return {
            userId: this.userId,
            ipAddress: this.ipAddress.toJSON(),
            userAgent: this.userAgent,
            sessionId: this.sessionId,
            deviceInfo: this.deviceInfo,
            includeRecommendations: this.includeRecommendations,
            includeUserProfile: this.includeUserProfile,
            timestamp: this.timestamp.toISOString(),
            cacheKey: this.getCacheKey(),
            needsUserHistory: this.needsUserHistory,
            needsLocationResolution: this.needsLocationResolution,
            isHighPriority: this.isHighPriority,
            complexity: this.getComplexity(),
            expectedResponseTime: this.getExpectedResponseTime(),
            deviceFingerprint: this.getDeviceFingerprint()
        };
    }
}
exports.AssessLoginRiskQuery = AssessLoginRiskQuery;
//# sourceMappingURL=assess-login-risk.query.js.map