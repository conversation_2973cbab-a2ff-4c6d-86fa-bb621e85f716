import { Repository } from 'typeorm';
import { CreateActivityDto } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { Activity } from './entities/activity.entity';
import { ActivityTag } from '../activity_tag/entities/activity_tag.entity';
import { ActivityWork } from '../activity_work/entities/activity_work.entity';
export declare class ActivityService {
    private readonly activityRepository;
    private readonly activityTagRepository;
    private readonly activityWorkRepository;
    constructor(activityRepository: Repository<Activity>, activityTagRepository: Repository<ActivityTag>, activityWorkRepository: Repository<ActivityWork>);
    create(createActivityDto: CreateActivityDto): Promise<Activity>;
    findAll(): Promise<Activity[]>;
    findOne(id: number): Promise<Activity>;
    update(id: number, updateActivityDto: UpdateActivityDto): Promise<Activity>;
    remove(id: number): Promise<void>;
    hardRemove(id: number): Promise<void>;
    findByStatus(status: number): Promise<Activity[]>;
    findActiveActivities(): Promise<Activity[]>;
    findUpcomingActivities(): Promise<Activity[]>;
    findByCreator(creatorId: number): Promise<Activity[]>;
    findByType(activityType: number): Promise<Activity[]>;
    updateStatus(id: number, status: number): Promise<Activity>;
    getById(id: number): Promise<Activity>;
    deleteActivity(id: number): Promise<boolean>;
    updateActivity(id: number, data: any): Promise<boolean>;
    list(params: {
        page?: number;
        size?: number;
        status?: number;
        keyword?: string;
    }): Promise<{
        list: Activity[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    getActivityWithWorks(id: number): Promise<{
        works: ActivityWork[];
        id: number;
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage: string;
        organizer: string;
        creatorId: number;
        createTime: Date;
        updateTime: Date;
        status: number;
        activityType: number;
        isDelete: boolean;
        reviewReason: string;
        attachmentFiles: string;
        promotionImage: string;
        competitionGroups: string;
        registrationForm: string;
        activityTags: ActivityTag[];
        activityWorks: ActivityWork[];
    }>;
    getActivityContentByType(id: number): Promise<{
        contentType: number;
        id: number;
        name: string;
        startTime: Date;
        endTime: Date;
        coverImage: string;
        organizer: string;
        creatorId: number;
        createTime: Date;
        updateTime: Date;
        status: number;
        activityType: number;
        isDelete: boolean;
        reviewReason: string;
        attachmentFiles: string;
        promotionImage: string;
        competitionGroups: string;
        registrationForm: string;
        activityTags: ActivityTag[];
        activityWorks: ActivityWork[];
    }>;
    addWorksToActivity(activityId: number, works: {
        workId: number;
        isAwarded?: boolean;
        category?: string;
        sort?: number;
    }[]): Promise<boolean>;
    setAwardedWorks(activityId: number, workIds: number[]): Promise<boolean>;
}
