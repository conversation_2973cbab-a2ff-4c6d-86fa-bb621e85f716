"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PessimisticLock_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PessimisticLock = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let PessimisticLock = PessimisticLock_1 = class PessimisticLock {
    connection;
    logger = new common_1.Logger(PessimisticLock_1.name);
    constructor(connection) {
        this.connection = connection;
    }
    async withRowLock(tableName, condition, callback) {
        this.logger.log(`尝试获取悲观锁: 表=${tableName}, 条件=${JSON.stringify(condition)}`);
        const startTime = Date.now();
        const connection = await this.connection.transaction(async (manager) => {
            try {
                const whereClauses = [];
                const params = [];
                Object.entries(condition).forEach(([key, value]) => {
                    whereClauses.push(`${key} = ?`);
                    params.push(value);
                });
                const whereClause = whereClauses.join(' AND ');
                const lockQuery = `SELECT * FROM ${tableName} WHERE ${whereClause} FOR UPDATE`;
                this.logger.debug(`执行锁定查询: ${lockQuery}, 参数: ${JSON.stringify(params)}`);
                const result = await manager.query(lockQuery, params);
                if (!result || result.length === 0) {
                    this.logger.warn(`悲观锁获取失败: 未找到符合条件的记录`);
                    throw new Error(`悲观锁获取失败: 未找到符合条件的记录`);
                }
                this.logger.log(`悲观锁获取成功: 表=${tableName}, 耗时=${Date.now() - startTime}ms`);
                try {
                    const callbackResult = await callback(manager);
                    this.logger.log(`悲观锁内操作执行完成, 总耗时=${Date.now() - startTime}ms`);
                    return callbackResult;
                }
                catch (error) {
                    this.logger.error(`悲观锁内操作执行失败: ${error.message}`, error.stack);
                    throw error;
                }
            }
            catch (error) {
                this.logger.error(`悲观锁事务执行失败: ${error.message}`, error.stack);
                throw error;
            }
        });
        this.logger.log(`悲观锁事务已提交: 表=${tableName}, 总时间=${Date.now() - startTime}ms`);
        return connection;
    }
    async withTableLock(tableName, lockMode, callback) {
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            await queryRunner.query(`LOCK TABLES ${tableName} ${lockMode}`);
            const result = await callback(queryRunner.manager);
            await queryRunner.query('UNLOCK TABLES');
            await queryRunner.commitTransaction();
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            try {
                await queryRunner.query('UNLOCK TABLES');
            }
            catch (unlockError) {
                this.logger.error(`释放表锁失败: ${unlockError.message}`, unlockError.stack);
            }
            this.logger.error(`悲观锁操作失败: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async withEntityLock(entityClass, findOptions, callback) {
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const repository = queryRunner.manager.getRepository(entityClass);
            const entities = await repository.find({
                ...findOptions,
                lock: { mode: 'pessimistic_write' }
            });
            const result = await callback(entities, queryRunner.manager);
            await queryRunner.commitTransaction();
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`悲观锁操作失败: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async withTransaction(callback) {
        const queryRunner = this.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const result = await callback(queryRunner);
            await queryRunner.commitTransaction();
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`事务操作失败: ${error.message}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.PessimisticLock = PessimisticLock;
exports.PessimisticLock = PessimisticLock = PessimisticLock_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.Connection])
], PessimisticLock);
//# sourceMappingURL=pessimistic.lock.js.map