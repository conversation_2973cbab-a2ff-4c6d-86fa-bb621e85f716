"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const tag_entity_1 = require("./entities/tag.entity");
let TagService = class TagService {
    tagRepository;
    constructor(tagRepository) {
        this.tagRepository = tagRepository;
    }
    async create(createTagDto) {
        const existTag = await this.tagRepository.findOne({
            where: { name: createTagDto.name, isDelete: false },
        });
        if (existTag) {
            throw new common_1.ConflictException('标签名称已存在');
        }
        const tag = this.tagRepository.create(createTagDto);
        return await this.tagRepository.save(tag);
    }
    async findAll() {
        return await this.tagRepository.find({ where: { isDelete: false } });
    }
    async findOne(id) {
        const tag = await this.tagRepository.findOne({ where: { id, isDelete: false } });
        if (!tag) {
            throw new common_1.NotFoundException(`标签 #${id} 未找到`);
        }
        return tag;
    }
    async update(id, updateTagDto) {
        const tag = await this.findOne(id);
        if (updateTagDto.name && updateTagDto.name !== tag.name) {
            const existTag = await this.tagRepository.findOne({
                where: { name: updateTagDto.name, isDelete: false },
            });
            if (existTag) {
                throw new common_1.ConflictException('标签名称已存在');
            }
        }
        Object.assign(tag, updateTagDto);
        return await this.tagRepository.save(tag);
    }
    async remove(id) {
        const tag = await this.findOne(id);
        tag.isDelete = true;
        await this.tagRepository.save(tag);
    }
    async hardRemove(id) {
        const result = await this.tagRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`标签 #${id} 未找到`);
        }
    }
    async list(params) {
        const { page = 1, size = 10, keyword } = params;
        const where = { isDelete: false };
        if (keyword) {
            where.name = (0, typeorm_2.Like)(`%${keyword}%`);
        }
        const [tags, total] = await this.tagRepository.findAndCount({
            where,
            order: { createTime: 'DESC' },
            skip: (page - 1) * size,
            take: size,
        });
        return {
            list: tags,
            pagination: {
                page,
                size,
                total,
            },
        };
    }
    async getById(id) {
        return await this.findOne(id);
    }
};
exports.TagService = TagService;
exports.TagService = TagService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(tag_entity_1.Tag)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], TagService);
//# sourceMappingURL=tag.service.js.map