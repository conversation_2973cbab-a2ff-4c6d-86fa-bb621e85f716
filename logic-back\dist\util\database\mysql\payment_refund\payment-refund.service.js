"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRefundService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_refund_entity_1 = require("./entities/payment-refund.entity");
const create_payment_refund_dto_1 = require("./dto/create-payment-refund.dto");
let PaymentRefundService = class PaymentRefundService {
    paymentRefundRepository;
    constructor(paymentRefundRepository) {
        this.paymentRefundRepository = paymentRefundRepository;
    }
    async create(createPaymentRefundDto) {
        const existingRefund = await this.paymentRefundRepository.findOne({
            where: { businessRefundId: createPaymentRefundDto.businessRefundId }
        });
        if (existingRefund) {
            throw new common_1.ConflictException(`业务退款单号 ${createPaymentRefundDto.businessRefundId} 已存在`);
        }
        const entity = this.paymentRefundRepository.create(createPaymentRefundDto);
        return await this.paymentRefundRepository.save(entity);
    }
    async findAll() {
        return await this.paymentRefundRepository.find({
            order: {
                createdAt: 'DESC'
            }
        });
    }
    async findOne(id) {
        const refund = await this.paymentRefundRepository.findOne({ where: { id } });
        if (!refund) {
            throw new common_1.NotFoundException(`ID为 ${id} 的退款记录不存在`);
        }
        return refund;
    }
    async findByBusinessRefundId(businessRefundId) {
        const refund = await this.paymentRefundRepository.findOne({ where: { businessRefundId } });
        if (!refund) {
            throw new common_1.NotFoundException(`业务退款单号为 ${businessRefundId} 的退款记录不存在`);
        }
        return refund;
    }
    async findByPaymentOrderId(paymentOrderId) {
        return await this.paymentRefundRepository.find({
            where: { paymentOrderId },
            order: { createdAt: 'DESC' }
        });
    }
    async findByChannelRefundId(channelRefundId) {
        const refund = await this.paymentRefundRepository.findOne({ where: { channelRefundId } });
        if (!refund) {
            throw new common_1.NotFoundException(`渠道退款单号为 ${channelRefundId} 的退款记录不存在`);
        }
        return refund;
    }
    async findByUserId(userId) {
        return await this.paymentRefundRepository.find({
            where: { userId },
            order: { createdAt: 'DESC' }
        });
    }
    async findByStatus(status) {
        return await this.paymentRefundRepository.find({
            where: { status },
            order: { createdAt: 'DESC' }
        });
    }
    async update(id, updatePaymentRefundDto) {
        const refund = await this.findOne(id);
        if (updatePaymentRefundDto.businessRefundId) {
            const existingRefund = await this.paymentRefundRepository.findOne({
                where: { businessRefundId: updatePaymentRefundDto.businessRefundId }
            });
            if (existingRefund && existingRefund.id !== id) {
                throw new common_1.ConflictException(`业务退款单号 ${updatePaymentRefundDto.businessRefundId} 已存在`);
            }
        }
        await this.paymentRefundRepository.update(id, updatePaymentRefundDto);
        return this.findOne(id);
    }
    async updateStatus(id, status, result) {
        const refund = await this.findOne(id);
        const updateData = { status };
        if (result) {
            updateData.result = result;
        }
        if (status === create_payment_refund_dto_1.RefundStatus.SUCCESS) {
            updateData.refundedAt = new Date();
        }
        await this.paymentRefundRepository.update(id, updateData);
        return this.findOne(id);
    }
    async updateNotifyData(id, notifyData) {
        const refund = await this.findOne(id);
        await this.paymentRefundRepository.update(id, { notifyData });
        return this.findOne(id);
    }
    async remove(id) {
        const result = await this.paymentRefundRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`ID为 ${id} 的退款记录不存在`);
        }
    }
};
exports.PaymentRefundService = PaymentRefundService;
exports.PaymentRefundService = PaymentRefundService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_refund_entity_1.PaymentRefund)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PaymentRefundService);
//# sourceMappingURL=payment-refund.service.js.map