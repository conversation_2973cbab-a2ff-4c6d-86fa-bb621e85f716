/**
 * 查询参数类型转换测试脚本
 * 用于验证布尔值和数字类型的查询参数是否正确处理
 */

const API_BASE_URL = 'http://localhost:8003/api/v1/ip-location';

async function testQueryParams() {
    console.log('🧪 开始测试查询参数类型转换...\n');

    // 测试用例
    const testCases = [
        {
            name: 'IP查询 - includeRisk=false (字符串)',
            url: `${API_BASE_URL}/query?ip=*******&includeRisk=false`,
            expected: 'success'
        },
        {
            name: 'IP查询 - includeRisk=true (字符串)',
            url: `${API_BASE_URL}/query?ip=*******&includeRisk=true`,
            expected: 'success'
        },
        {
            name: 'IP查询 - 无includeRisk参数',
            url: `${API_BASE_URL}/query?ip=*******`,
            expected: 'success'
        },
        {
            name: 'IP查询 - includeRisk=invalid',
            url: `${API_BASE_URL}/query?ip=*******&includeRisk=invalid`,
            expected: 'error'
        },
        {
            name: '用户统计 - days=30 (字符串)',
            url: `${API_BASE_URL}/user/12345/stats?days=30`,
            expected: 'success'
        },
        {
            name: '用户统计 - includeTrusted=true (字符串)',
            url: `${API_BASE_URL}/user/12345/stats?days=30&includeTrusted=true`,
            expected: 'success'
        },
        {
            name: '健康检查',
            url: `${API_BASE_URL}/health`,
            expected: 'success'
        }
    ];

    // 执行测试
    for (const testCase of testCases) {
        console.log(`📋 测试: ${testCase.name}`);
        console.log(`🔗 URL: ${testCase.url}`);
        
        try {
            const response = await fetch(testCase.url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (response.ok) {
                console.log(`✅ 成功 (${response.status})`);
                if (testCase.expected === 'error') {
                    console.log(`⚠️  预期失败但实际成功`);
                }
            } else {
                console.log(`❌ 失败 (${response.status}): ${data.message || '未知错误'}`);
                if (testCase.expected === 'success') {
                    console.log(`⚠️  预期成功但实际失败`);
                }
            }
            
            // 显示响应数据（简化版）
            if (response.ok && data.data) {
                if (data.data.ip) {
                    console.log(`📍 IP: ${data.data.ip}, 位置: ${data.data.displayName || '未知'}`);
                } else if (data.data.userId) {
                    console.log(`👤 用户: ${data.data.userId}, 统计: ${data.data.summary?.totalLocations || 0}个位置`);
                } else if (data.status) {
                    console.log(`🏥 服务状态: ${data.status}`);
                }
            }
            
        } catch (error) {
            console.log(`💥 网络错误: ${error.message}`);
        }
        
        console.log(''); // 空行分隔
    }

    console.log('🎉 查询参数类型转换测试完成！');
}

// 运行测试
testQueryParams().catch(console.error);
