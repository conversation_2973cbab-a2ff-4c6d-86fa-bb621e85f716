import { Injectable, OnModuleInit } from '@nestjs/common';
import { LoggerService } from '../../../../common/logger/logger.service';
import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { LocationNotFoundException } from '../../domain/exceptions/location-not-found.exception';

// 复用现有的ip2region库导入
const IP2Region = require('ip2region').default;

/**
 * IP2Region外部服务封装
 * 封装现有的ip2region库，提供领域友好的接口
 */
@Injectable()
export class Ip2RegionService implements OnModuleInit {
  private ip2region: any;
  private isInitialized = false;

  constructor(
    private readonly logger: LoggerService
  ) {}

  /**
   * 模块初始化时初始化ip2region
   */
  async onModuleInit() {
    await this.initialize();
  }

  /**
   * 初始化ip2region实例
   */
  private async initialize(): Promise<void> {
    try {
      this.ip2region = new IP2Region();
      this.isInitialized = true;
      this.logger.log('IP2Region服务初始化成功', 'Ip2RegionService');
    } catch (error) {
      this.logger.error('IP2Region服务初始化失败', error, 'Ip2RegionService');
      throw new Error(`IP2Region初始化失败: ${error.message}`);
    }
  }

  /**
   * 解析IP地址的地理位置
   * @param ipAddress IP地址值对象
   * @returns 地理位置值对象
   */
  async resolveLocation(ipAddress: IpAddress): Promise<GeographicLocation> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const rawResult = this.ip2region.search(ipAddress.value);
      
      if (!rawResult) {
        throw LocationNotFoundException.ipResolutionFailed(
          ipAddress.value,
          'ip2region返回空结果'
        );
      }

      // 转换原始数据为地理位置值对象
      return this.convertRawDataToLocation(rawResult);

    } catch (error) {
      if (error instanceof LocationNotFoundException) {
        throw error;
      }

      this.logger.error(
        `IP地址解析失败: ${ipAddress.value}`,
        error,
        'Ip2RegionService'
      );

      throw LocationNotFoundException.ipResolutionFailed(
        ipAddress.value,
        error.message
      );
    }
  }

  /**
   * 批量解析IP地址
   * @param ipAddresses IP地址值对象数组
   * @returns 地理位置值对象数组
   */
  async resolveMultipleLocations(ipAddresses: IpAddress[]): Promise<GeographicLocation[]> {
    const results: GeographicLocation[] = [];

    for (const ipAddress of ipAddresses) {
      try {
        const location = await this.resolveLocation(ipAddress);
        results.push(location);
      } catch (error) {
        // 批量解析时，失败的IP返回未知位置
        this.logger.warn(
          `批量解析中IP地址解析失败: ${ipAddress.value}`,
          'Ip2RegionService'
        );
        results.push(GeographicLocation.createUnknown());
      }
    }

    return results;
  }

  /**
   * 测试IP地址解析功能
   * @param testIp 测试IP地址
   * @returns 测试结果
   */
  async testResolution(testIp: string = '*******'): Promise<{
    success: boolean;
    location?: GeographicLocation;
    error?: string;
    performanceMs: number;
  }> {
    const startTime = Date.now();

    try {
      const ipAddress = IpAddress.create(testIp);
      const location = await this.resolveLocation(ipAddress);

      return {
        success: true,
        location,
        performanceMs: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        performanceMs: Date.now() - startTime
      };
    }
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): {
    isAvailable: boolean;
    isInitialized: boolean;
    version: string;
    supportedTypes: string[];
  } {
    return {
      isAvailable: !!this.ip2region,
      isInitialized: this.isInitialized,
      version: 'ip2region-v2.0',
      supportedTypes: ['IPv4', 'IPv6']
    };
  }

  /**
   * 获取原始ip2region实例（用于兼容现有代码）
   */
  getRawInstance(): any {
    if (!this.isInitialized) {
      throw new Error('IP2Region服务未初始化');
    }
    return this.ip2region;
  }

  /**
   * 转换原始数据为地理位置值对象
   */
  private convertRawDataToLocation(rawData: any): GeographicLocation {
    // 处理ip2region返回的数据格式
    // 通常格式为: "国家|区域|省份|城市|ISP"
    let country = '未知';
    let province = '未知';
    let city = '未知';
    let isp = '未知';

    if (rawData) {
      // 如果是字符串格式
      if (typeof rawData === 'string') {
        const parts = rawData.split('|');
        if (parts.length >= 4) {
          country = this.normalizeField(parts[0]);
          province = this.normalizeField(parts[2]);
          city = this.normalizeField(parts[3]);
          isp = parts.length > 4 ? this.normalizeField(parts[4]) : '未知';
        }
      }
      // 如果是对象格式
      else if (typeof rawData === 'object') {
        country = this.normalizeField(rawData.country || rawData.Country);
        province = this.normalizeField(rawData.province || rawData.Province);
        city = this.normalizeField(rawData.city || rawData.City);
        isp = this.normalizeField(rawData.isp || rawData.ISP);
      }
    }

    // 计算数据置信度
    const emptyFields = [country, province, city, isp]
      .filter(field => field === '未知' || field === '').length;
    const confidence = Math.max(100 - (emptyFields * 25), 0);

    return GeographicLocation.create(
      country,
      province,
      city,
      isp,
      'ip2region',
      confidence
    );
  }

  /**
   * 标准化字段值
   */
  private normalizeField(field: any): string {
    if (!field || field === '0' || field === '' || field === null || field === undefined) {
      return '未知';
    }
    return String(field).trim();
  }

  /**
   * 重新初始化服务（用于错误恢复）
   */
  async reinitialize(): Promise<void> {
    this.isInitialized = false;
    this.ip2region = null;
    await this.initialize();
  }

  /**
   * 检查IP地址是否支持解析
   */
  canResolve(ipAddress: IpAddress): boolean {
    return ipAddress.canGeolocate && this.isInitialized;
  }

  /**
   * 获取性能统计信息
   */
  async getPerformanceStats(): Promise<{
    averageResponseTime: number;
    successRate: number;
    totalRequests: number;
  }> {
    // 这里可以实现性能统计逻辑
    // 目前返回模拟数据
    return {
      averageResponseTime: 50, // 毫秒
      successRate: 95.5, // 百分比
      totalRequests: 1000
    };
  }
}
