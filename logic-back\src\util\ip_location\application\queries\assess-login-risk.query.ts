import { IpAddress } from '../../domain/value-objects/ip-address.vo';

/**
 * 评估登录风险查询
 * 封装评估用户登录风险的读操作
 */
export class AssessLoginRiskQuery {
  public readonly userId: number;
  public readonly ipAddress: IpAddress;
  public readonly userAgent?: string;
  public readonly sessionId?: string;
  public readonly deviceInfo?: string;
  public readonly includeRecommendations: boolean;
  public readonly includeUserProfile: boolean;
  public readonly timestamp: Date;

  constructor(
    userId: number,
    ipAddress: IpAddress,
    userAgent?: string,
    sessionId?: string,
    deviceInfo?: string,
    includeRecommendations: boolean = true,
    includeUserProfile: boolean = false
  ) {
    this.userId = userId;
    this.ipAddress = ipAddress;
    this.userAgent = userAgent;
    this.sessionId = sessionId;
    this.deviceInfo = deviceInfo;
    this.includeRecommendations = includeRecommendations;
    this.includeUserProfile = includeUserProfile;
    this.timestamp = new Date();
  }

  /**
   * 创建基础风险评估查询
   */
  static createBasic(
    userId: number,
    ipAddressString: string,
    userAgent?: string
  ): AssessLoginRiskQuery {
    const ipAddress = IpAddress.create(ipAddressString);
    return new AssessLoginRiskQuery(userId, ipAddress, userAgent, undefined, undefined, true, false);
  }

  /**
   * 创建完整风险评估查询
   */
  static createComplete(
    userId: number,
    ipAddressString: string,
    userAgent?: string,
    sessionId?: string,
    deviceInfo?: string
  ): AssessLoginRiskQuery {
    const ipAddress = IpAddress.create(ipAddressString);
    return new AssessLoginRiskQuery(userId, ipAddress, userAgent, sessionId, deviceInfo, true, true);
  }

  /**
   * 创建简化风险评估查询（不包含建议和用户档案）
   */
  static createSimple(
    userId: number,
    ipAddressString: string
  ): AssessLoginRiskQuery {
    const ipAddress = IpAddress.create(ipAddressString);
    return new AssessLoginRiskQuery(userId, ipAddress, undefined, undefined, undefined, false, false);
  }

  /**
   * 验证查询的有效性
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.userId <= 0) {
      errors.push('用户ID必须大于0');
    }

    if (!this.ipAddress.canGeolocate) {
      errors.push('IP地址不支持地理位置解析');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取缓存键
   */
  getCacheKey(): string {
    const recommendationsSuffix = this.includeRecommendations ? '_rec' : '';
    const profileSuffix = this.includeUserProfile ? '_profile' : '';
    return `login_risk:${this.userId}:${this.ipAddress.value}${recommendationsSuffix}${profileSuffix}`;
  }

  /**
   * 获取查询摘要信息
   */
  getSummary(): string {
    const recInfo = this.includeRecommendations ? '(包含建议)' : '';
    const profileInfo = this.includeUserProfile ? '(包含用户档案)' : '';
    return `评估用户${this.userId}从${this.ipAddress.masked}登录的风险 ${recInfo}${profileInfo}`;
  }

  /**
   * 检查是否需要用户历史数据
   */
  get needsUserHistory(): boolean {
    return true; // 风险评估总是需要用户历史数据
  }

  /**
   * 检查是否需要位置解析
   */
  get needsLocationResolution(): boolean {
    return this.ipAddress.canGeolocate;
  }

  /**
   * 检查是否为高优先级查询
   */
  get isHighPriority(): boolean {
    // 如果IP是公网IP且需要完整分析，则为高优先级
    return this.ipAddress.isPublic && (this.includeRecommendations || this.includeUserProfile);
  }

  /**
   * 获取设备指纹信息
   */
  getDeviceFingerprint(): {
    userAgent?: string;
    deviceInfo?: string;
    sessionId?: string;
    hasDeviceInfo: boolean;
  } {
    return {
      userAgent: this.userAgent,
      deviceInfo: this.deviceInfo,
      sessionId: this.sessionId,
      hasDeviceInfo: !!(this.userAgent || this.deviceInfo)
    };
  }

  /**
   * 获取查询复杂度
   */
  getComplexity(): 'SIMPLE' | 'MEDIUM' | 'COMPLEX' {
    let complexity = 0;
    
    if (this.includeRecommendations) complexity += 1;
    if (this.includeUserProfile) complexity += 2;
    if (this.userAgent) complexity += 1;
    if (this.deviceInfo) complexity += 1;

    if (complexity <= 1) return 'SIMPLE';
    if (complexity <= 3) return 'MEDIUM';
    return 'COMPLEX';
  }

  /**
   * 获取预期响应时间（毫秒）
   */
  getExpectedResponseTime(): number {
    const complexity = this.getComplexity();
    
    switch (complexity) {
      case 'SIMPLE': return 100;
      case 'MEDIUM': return 300;
      case 'COMPLEX': return 500;
      default: return 300;
    }
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      userId: this.userId,
      ipAddress: this.ipAddress.toJSON(),
      userAgent: this.userAgent,
      sessionId: this.sessionId,
      deviceInfo: this.deviceInfo,
      includeRecommendations: this.includeRecommendations,
      includeUserProfile: this.includeUserProfile,
      timestamp: this.timestamp.toISOString(),
      cacheKey: this.getCacheKey(),
      needsUserHistory: this.needsUserHistory,
      needsLocationResolution: this.needsLocationResolution,
      isHighPriority: this.isHighPriority,
      complexity: this.getComplexity(),
      expectedResponseTime: this.getExpectedResponseTime(),
      deviceFingerprint: this.getDeviceFingerprint()
    };
  }
}
