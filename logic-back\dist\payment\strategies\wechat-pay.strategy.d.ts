import { PaymentStrategy, PaymentResult, PaymentQueryResult } from './payment.strategy';
import { PaymentConfigService } from '../config/payment-config.service';
import { PaymentSignatureService } from '../security/payment-signature.service';
export declare class WechatPayStrategy implements PaymentStrategy {
    private readonly configService;
    private readonly signatureService;
    private readonly logger;
    private wechatpayClient;
    constructor(configService: PaymentConfigService, signatureService: PaymentSignatureService);
    private initWechatPayClient;
    getChannel(): string;
    private generateSignature;
    createPayment(params: {
        outTradeNo: string;
        subject: string;
        totalAmount: number;
        returnUrl?: string;
        notifyUrl?: string;
        clientIp?: string;
        timeExpire?: Date;
        [key: string]: any;
    }): Promise<PaymentResult>;
    queryPayment(params: {
        outTradeNo: string;
        paymentId?: string;
    }): Promise<PaymentQueryResult>;
    closePayment(params: {
        outTradeNo: string;
        paymentId?: string;
    }): Promise<{
        success: boolean;
        errorMessage?: string;
        rawResponse?: any;
    }>;
    private decryptResource;
    verifyNotify(data: any): Promise<{
        verified: boolean;
        outTradeNo?: string;
        paymentId?: string;
        totalAmount?: number;
        paymentTime?: Date;
        [key: string]: any;
    }>;
    private mapTradeState;
    refund(params: {
        outTradeNo: string;
        outRefundNo: string;
        refundAmount: number;
        reason?: string;
    }): Promise<{
        success: boolean;
        refundId?: string;
        errorMessage?: string;
        rawResult?: any;
    }>;
    queryRefundStatus(outRefundNo: string): Promise<{
        status: string;
        refundId?: string;
        errorMessage?: string;
        rawResult?: any;
    }>;
}
