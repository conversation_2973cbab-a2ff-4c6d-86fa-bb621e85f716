self.__BUILD_MANIFEST = (function(a,b){return {__rewrites:{afterFiles:[{has:a,source:"\u002Fapi\u002F:path*",destination:a},{has:a,source:"\u002Feditor",destination:a},{has:a,source:"\u002Feditor\u002F:path*",destination:a},{has:a,source:"\u002Fsocket.io\u002F:path*",destination:a},{has:a,source:"\u002Flibs\u002F:path*",destination:a},{has:a,source:"\u002Ftoken\u002F:path*",destination:a},{has:a,source:b,destination:a},{has:a,source:"\u002Flogicleap",destination:a},{has:a,source:"\u002Fstatic\u002F:path*",destination:a},{has:a,source:b,destination:a},{has:a,source:"\u002Fgui.js",destination:a},{has:a,source:"\u002Fassets\u002F:path*",destination:a},{has:a,source:"\u002Flogicleap\u002F:path*",destination:a}],beforeFiles:[],fallback:[]},"/_error":["static\u002Fchunks\u002Fpages\u002F_error.js"],sortedPages:["\u002F_app","\u002F_error"]}}(void 0,"\u002Fjs\u002F:path*"));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()