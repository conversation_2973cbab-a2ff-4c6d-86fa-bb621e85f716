"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrderBusinessModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const package_order_business_controller_1 = require("./package-order-business.controller");
const package_order_business_service_1 = require("./package-order-business.service");
const package_order_module_1 = require("../../util/database/mysql/package_order/package-order.module");
const package_info_module_1 = require("../../util/database/mysql/package_info/package_info.module");
const package_pricing_module_1 = require("../../util/database/mysql/package_pricing/package-pricing.module");
const user_info_module_1 = require("../user_info/user_info.module");
const user_package_module_1 = require("../../util/database/mysql/user_package/user_package.module");
const user_points_module_1 = require("../../util/database/mysql/user_points/user_points.module");
const payment_module_1 = require("../../payment/payment.module");
const payment_order_module_1 = require("../../util/database/mysql/payment_order/payment-order.module");
const package_order_entity_1 = require("../../util/database/mysql/package_order/entities/package-order.entity");
const user_package_entity_1 = require("../../util/database/mysql/user_package/entities/user_package.entity");
const user_info_entity_1 = require("../../util/database/mysql/user_info/entities/user_info.entity");
let PackageOrderBusinessModule = class PackageOrderBusinessModule {
};
exports.PackageOrderBusinessModule = PackageOrderBusinessModule;
exports.PackageOrderBusinessModule = PackageOrderBusinessModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                package_order_entity_1.PackageOrder,
                user_package_entity_1.UserPackage,
                user_info_entity_1.UserInfo,
            ]),
            package_order_module_1.PackageOrderModule,
            package_info_module_1.PackageInfoModule,
            package_pricing_module_1.PackagePricingModule,
            user_info_module_1.UserInfoModule,
            user_package_module_1.UserPackageModule,
            user_points_module_1.UserPointsModule,
            payment_module_1.PaymentModule,
            payment_order_module_1.PaymentOrderModule,
        ],
        controllers: [package_order_business_controller_1.PackageOrderBusinessController],
        providers: [package_order_business_service_1.PackageOrderBusinessService],
        exports: [package_order_business_service_1.PackageOrderBusinessService],
    })
], PackageOrderBusinessModule);
//# sourceMappingURL=package-order-business.module.js.map