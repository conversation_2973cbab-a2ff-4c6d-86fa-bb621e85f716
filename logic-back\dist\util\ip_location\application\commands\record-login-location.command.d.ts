import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { RiskScore } from '../../domain/value-objects/risk-score.vo';
export declare enum LoginType {
    PASSWORD = "PASSWORD",
    SMS = "SMS",
    QR_CODE = "QR_CODE",
    OAUTH = "OAUTH"
}
export declare enum LoginStatus {
    SUCCESS = "SUCCESS",
    FAILED = "FAILED",
    BLOCKED = "BLOCKED"
}
export declare class RecordLoginLocationCommand {
    readonly userId: number;
    readonly ipAddress: IpAddress;
    readonly location: GeographicLocation;
    readonly riskScore: RiskScore;
    readonly loginType: LoginType;
    readonly loginStatus: LoginStatus;
    readonly sessionId?: string;
    readonly userAgent?: string;
    readonly deviceInfo?: string;
    readonly failReason?: string;
    readonly timestamp: Date;
    constructor(userId: number, ipAddress: IpAddress, location: GeographicLocation, riskScore: RiskScore, loginType: LoginType, loginStatus: LoginStatus, sessionId?: string, userAgent?: string, deviceInfo?: string, failReason?: string);
    static createSuccessLogin(userId: number, ipAddressString: string, location: GeographicLocation, riskScore: RiskScore, loginType: LoginType, sessionId?: string, userAgent?: string, deviceInfo?: string): RecordLoginLocationCommand;
    static createFailedLogin(userId: number, ipAddressString: string, location: GeographicLocation, riskScore: RiskScore, loginType: LoginType, failReason: string, userAgent?: string, deviceInfo?: string): RecordLoginLocationCommand;
    static createBlockedLogin(userId: number, ipAddressString: string, location: GeographicLocation, riskScore: RiskScore, loginType: LoginType, blockReason: string, userAgent?: string, deviceInfo?: string): RecordLoginLocationCommand;
    validate(): {
        isValid: boolean;
        errors: string[];
    };
    get isSuccessLogin(): boolean;
    get isFailedLogin(): boolean;
    get isBlockedLogin(): boolean;
    get isHighRiskLogin(): boolean;
    get isForeignLogin(): boolean;
    getSummary(): string;
    getSecurityInfo(): {
        riskLevel: string;
        riskFactors: string[];
        needsVerification: boolean;
        isForeign: boolean;
        isNewLocation: boolean;
    };
    toJSON(): object;
}
