"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const attachment_service_1 = require("./attachment.service");
const create_attachment_dto_1 = require("./dto/create-attachment.dto");
const update_attachment_dto_1 = require("./dto/update-attachment.dto");
const attachment_info_dto_1 = require("./dto/attachment-info.dto");
let AttachmentController = class AttachmentController {
    attachmentService;
    constructor(attachmentService) {
        this.attachmentService = attachmentService;
    }
    create(createAttachmentDto, req) {
        if (!createAttachmentDto.created_by && req.user) {
            createAttachmentDto.created_by = req.user.id;
        }
        return this.attachmentService.create(createAttachmentDto);
    }
    createMany(createAttachmentDtos, req) {
        if (req.user) {
            createAttachmentDtos.forEach(dto => {
                if (!dto.created_by) {
                    dto.created_by = req.user.id;
                }
            });
        }
        return this.attachmentService.createMany(createAttachmentDtos);
    }
    findAll() {
        return this.attachmentService.findAll();
    }
    findOne(id) {
        return this.attachmentService.findOne(+id);
    }
    update(id, updateAttachmentDto, req) {
        if (!updateAttachmentDto.updated_by && req.user) {
            updateAttachmentDto.updated_by = req.user.id;
        }
        return this.attachmentService.update(+id, updateAttachmentDto);
    }
    remove(id) {
        return this.attachmentService.remove(+id);
    }
};
exports.AttachmentController = AttachmentController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建附件' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '创建成功', type: attachment_info_dto_1.AttachmentInfoDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_attachment_dto_1.CreateAttachmentDto, Object]),
    __metadata("design:returntype", void 0)
], AttachmentController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '批量创建附件' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '创建成功', type: [attachment_info_dto_1.AttachmentInfoDto] }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", void 0)
], AttachmentController.prototype, "createMany", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有附件' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [attachment_info_dto_1.AttachmentInfoDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AttachmentController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取附件' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: attachment_info_dto_1.AttachmentInfoDto }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AttachmentController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新附件信息' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: attachment_info_dto_1.AttachmentInfoDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_attachment_dto_1.UpdateAttachmentDto, Object]),
    __metadata("design:returntype", void 0)
], AttachmentController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '删除附件' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AttachmentController.prototype, "remove", null);
exports.AttachmentController = AttachmentController = __decorate([
    (0, swagger_1.ApiTags)('附件管理'),
    (0, common_1.Controller)('api/attachment'),
    __metadata("design:paramtypes", [attachment_service_1.AttachmentService])
], AttachmentController);
//# sourceMappingURL=attachment.controller.js.map