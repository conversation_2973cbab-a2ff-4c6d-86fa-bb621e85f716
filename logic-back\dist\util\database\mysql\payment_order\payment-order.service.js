"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentOrderService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_order_entity_1 = require("./entities/payment-order.entity");
const create_payment_order_dto_1 = require("./dto/create-payment-order.dto");
let PaymentOrderService = class PaymentOrderService {
    paymentOrderRepository;
    constructor(paymentOrderRepository) {
        this.paymentOrderRepository = paymentOrderRepository;
    }
    async create(createPaymentOrderDto) {
        const existingOrder = await this.paymentOrderRepository.findOne({
            where: { businessOrderId: createPaymentOrderDto.businessOrderId }
        });
        if (existingOrder) {
            throw new common_1.ConflictException(`业务订单号 ${createPaymentOrderDto.businessOrderId} 已存在`);
        }
        const entity = this.paymentOrderRepository.create(createPaymentOrderDto);
        return await this.paymentOrderRepository.save(entity);
    }
    async findAll() {
        return await this.paymentOrderRepository.find({
            order: {
                createdAt: 'DESC'
            }
        });
    }
    async findOne(id) {
        const order = await this.paymentOrderRepository.findOne({ where: { id } });
        if (!order) {
            throw new common_1.NotFoundException(`ID为 ${id} 的支付订单不存在`);
        }
        return order;
    }
    async findByBusinessOrderId(businessOrderId) {
        const order = await this.paymentOrderRepository.findOne({ where: { businessOrderId } });
        if (!order) {
            throw new common_1.NotFoundException(`业务订单号为 ${businessOrderId} 的支付订单不存在`);
        }
        return order;
    }
    async findByChannelOrderId(channelOrderId) {
        const order = await this.paymentOrderRepository.findOne({ where: { channelOrderId } });
        if (!order) {
            throw new common_1.NotFoundException(`渠道订单号为 ${channelOrderId} 的支付订单不存在`);
        }
        return order;
    }
    async findByUserId(userId) {
        return await this.paymentOrderRepository.find({
            where: { userId },
            order: { createdAt: 'DESC' }
        });
    }
    async findByStatus(status) {
        return await this.paymentOrderRepository.find({
            where: { status },
            order: { createdAt: 'DESC' }
        });
    }
    async update(id, updatePaymentOrderDto) {
        const order = await this.findOne(id);
        if (updatePaymentOrderDto.businessOrderId) {
            const existingOrder = await this.paymentOrderRepository.findOne({
                where: { businessOrderId: updatePaymentOrderDto.businessOrderId }
            });
            if (existingOrder && existingOrder.id !== id) {
                throw new common_1.ConflictException(`业务订单号 ${updatePaymentOrderDto.businessOrderId} 已存在`);
            }
        }
        await this.paymentOrderRepository.update(id, updatePaymentOrderDto);
        return this.findOne(id);
    }
    async updateStatus(id, status, result) {
        const order = await this.findOne(id);
        const updateData = { status };
        if (result) {
            updateData.result = result;
        }
        if (status === create_payment_order_dto_1.PaymentStatus.SUCCESS) {
            updateData.paidAt = new Date();
        }
        if (status === create_payment_order_dto_1.PaymentStatus.CLOSED) {
            updateData.closedAt = new Date();
        }
        await this.paymentOrderRepository.update(id, updateData);
        return this.findOne(id);
    }
    async updateNotifyData(id, notifyData) {
        const order = await this.findOne(id);
        await this.paymentOrderRepository.update(id, { notifyData });
        return this.findOne(id);
    }
    async remove(id) {
        const result = await this.paymentOrderRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`ID为 ${id} 的支付订单不存在`);
        }
    }
};
exports.PaymentOrderService = PaymentOrderService;
exports.PaymentOrderService = PaymentOrderService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_order_entity_1.PaymentOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PaymentOrderService);
//# sourceMappingURL=payment-order.service.js.map