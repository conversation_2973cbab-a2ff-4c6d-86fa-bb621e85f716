"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-motion";
exports.ids = ["vendor-chunks/rc-motion"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotion.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotion.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotion: () => (/* binding */ genCSSMotion)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n/* harmony import */ var _DomWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DomWrapper */ \"(ssr)/./node_modules/rc-motion/es/DomWrapper.js\");\n/* harmony import */ var _hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./hooks/useStatus */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\");\n/* harmony import */ var _hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n/* eslint-disable react/default-props-match-prop-types, react/no-multi-comp, react/prop-types */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * `transitionSupport` is used for none transition test case.\n * Default we use browser transition event support check.\n */\nfunction genCSSMotion(config) {\n  var transitionSupport = config;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(config) === 'object') {\n    transitionSupport = config.transitionSupport;\n  }\n  function isSupportTransition(props, contextMotion) {\n    return !!(props.motionName && transitionSupport && contextMotion !== false);\n  }\n  var CSSMotion = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(function (props, ref) {\n    var _props$visible = props.visible,\n      visible = _props$visible === void 0 ? true : _props$visible,\n      _props$removeOnLeave = props.removeOnLeave,\n      removeOnLeave = _props$removeOnLeave === void 0 ? true : _props$removeOnLeave,\n      forceRender = props.forceRender,\n      children = props.children,\n      motionName = props.motionName,\n      leavedClassName = props.leavedClassName,\n      eventProps = props.eventProps;\n    var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_context__WEBPACK_IMPORTED_MODULE_8__.Context),\n      contextMotion = _React$useContext.motion;\n    var supportMotion = isSupportTransition(props, contextMotion);\n\n    // Ref to the react node, it may be a HTMLElement\n    var nodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    // Ref to the dom wrapper in case ref can not pass to HTMLElement\n    var wrapperNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_7__.useRef)();\n    function getDomElement() {\n      try {\n        // Here we're avoiding call for findDOMNode since it's deprecated\n        // in strict mode. We're calling it only when node ref is not\n        // an instance of DOM HTMLElement. Otherwise use\n        // findDOMNode as a final resort\n        return nodeRef.current instanceof HTMLElement ? nodeRef.current : (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(wrapperNodeRef.current);\n      } catch (e) {\n        // Only happen when `motionDeadline` trigger but element removed.\n        return null;\n      }\n    }\n    var _useStatus = (0,_hooks_useStatus__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(supportMotion, visible, getDomElement, props),\n      _useStatus2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStatus, 4),\n      status = _useStatus2[0],\n      statusStep = _useStatus2[1],\n      statusStyle = _useStatus2[2],\n      mergedVisible = _useStatus2[3];\n\n    // Record whether content has rendered\n    // Will return null for un-rendered even when `removeOnLeave={false}`\n    var renderedRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(mergedVisible);\n    if (mergedVisible) {\n      renderedRef.current = true;\n    }\n\n    // ====================== Refs ======================\n    var setNodeRef = react__WEBPACK_IMPORTED_MODULE_7__.useCallback(function (node) {\n      nodeRef.current = node;\n      (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.fillRef)(ref, node);\n    }, [ref]);\n\n    // ===================== Render =====================\n    var motionChildren;\n    var mergedProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, eventProps), {}, {\n      visible: visible\n    });\n    if (!children) {\n      // No children\n      motionChildren = null;\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_12__.STATUS_NONE) {\n      // Stable children\n      if (mergedVisible) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), setNodeRef);\n      } else if (!removeOnLeave && renderedRef.current && leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          className: leavedClassName\n        }), setNodeRef);\n      } else if (forceRender || !removeOnLeave && !leavedClassName) {\n        motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n          style: {\n            display: 'none'\n          }\n        }), setNodeRef);\n      } else {\n        motionChildren = null;\n      }\n    } else {\n      // In motion\n      var statusSuffix;\n      if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_PREPARE) {\n        statusSuffix = 'prepare';\n      } else if ((0,_hooks_useStepQueue__WEBPACK_IMPORTED_MODULE_11__.isActive)(statusStep)) {\n        statusSuffix = 'active';\n      } else if (statusStep === _interface__WEBPACK_IMPORTED_MODULE_12__.STEP_START) {\n        statusSuffix = 'start';\n      }\n      var motionCls = (0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, \"\".concat(status, \"-\").concat(statusSuffix));\n      motionChildren = children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, mergedProps), {}, {\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()((0,_util_motion__WEBPACK_IMPORTED_MODULE_13__.getTransitionName)(motionName, status), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionCls, motionCls && statusSuffix), motionName, typeof motionName === 'string')),\n        style: statusStyle\n      }), setNodeRef);\n    }\n\n    // Auto inject ref if child node not have `ref` props\n    if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.isValidElement(motionChildren) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(motionChildren)) {\n      var _ref = motionChildren,\n        originNodeRef = _ref.ref;\n      if (!originNodeRef) {\n        motionChildren = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.cloneElement(motionChildren, {\n          ref: setNodeRef\n        });\n      }\n    }\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_DomWrapper__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: wrapperNodeRef\n    }, motionChildren);\n  });\n  CSSMotion.displayName = 'CSSMotion';\n  return CSSMotion;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotion(_util_motion__WEBPACK_IMPORTED_MODULE_13__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/CSSMotionList.js":
/*!****************************************************!*\
  !*** ./node_modules/rc-motion/es/CSSMotionList.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   genCSSMotionList: () => (/* binding */ genCSSMotionList)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _util_diff__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./util/diff */ \"(ssr)/./node_modules/rc-motion/es/util/diff.js\");\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"component\", \"children\", \"onVisibleChanged\", \"onAllRemoved\"],\n  _excluded2 = [\"status\"];\n/* eslint react/prop-types: 0 */\n\n\n\n\nvar MOTION_PROP_NAMES = ['eventProps', 'visible', 'children', 'motionName', 'motionAppear', 'motionEnter', 'motionLeave', 'motionLeaveImmediately', 'motionDeadline', 'removeOnLeave', 'leavedClassName', 'onAppearPrepare', 'onAppearStart', 'onAppearActive', 'onAppearEnd', 'onEnterStart', 'onEnterActive', 'onEnterEnd', 'onLeaveStart', 'onLeaveActive', 'onLeaveEnd'];\n/**\n * Generate a CSSMotionList component with config\n * @param transitionSupport No need since CSSMotionList no longer depends on transition support\n * @param CSSMotion CSSMotion component\n */\nfunction genCSSMotionList(transitionSupport) {\n  var CSSMotion = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _CSSMotion__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n  var CSSMotionList = /*#__PURE__*/function (_React$Component) {\n    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(CSSMotionList, _React$Component);\n    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(CSSMotionList);\n    function CSSMotionList() {\n      var _this;\n      (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, CSSMotionList);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _super.call.apply(_super, [this].concat(args));\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"state\", {\n        keyEntities: []\n      });\n      // ZombieJ: Return the count of rest keys. It's safe to refactor if need more info.\n      (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"removeKey\", function (removeKey) {\n        _this.setState(function (prevState) {\n          var nextKeyEntities = prevState.keyEntities.map(function (entity) {\n            if (entity.key !== removeKey) return entity;\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, entity), {}, {\n              status: _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED\n            });\n          });\n          return {\n            keyEntities: nextKeyEntities\n          };\n        }, function () {\n          var keyEntities = _this.state.keyEntities;\n          var restKeysCount = keyEntities.filter(function (_ref) {\n            var status = _ref.status;\n            return status !== _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED;\n          }).length;\n          if (restKeysCount === 0 && _this.props.onAllRemoved) {\n            _this.props.onAllRemoved();\n          }\n        });\n      });\n      return _this;\n    }\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(CSSMotionList, [{\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n        var keyEntities = this.state.keyEntities;\n        var _this$props = this.props,\n          component = _this$props.component,\n          children = _this$props.children,\n          _onVisibleChanged = _this$props.onVisibleChanged,\n          onAllRemoved = _this$props.onAllRemoved,\n          restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this$props, _excluded);\n        var Component = component || react__WEBPACK_IMPORTED_MODULE_9__.Fragment;\n        var motionProps = {};\n        MOTION_PROP_NAMES.forEach(function (prop) {\n          motionProps[prop] = restProps[prop];\n          delete restProps[prop];\n        });\n        delete restProps.keys;\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(Component, restProps, keyEntities.map(function (_ref2, index) {\n          var status = _ref2.status,\n            eventProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref2, _excluded2);\n          var visible = status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_ADD || status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_KEEP;\n          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_9__.createElement(CSSMotion, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motionProps, {\n            key: eventProps.key,\n            visible: visible,\n            eventProps: eventProps,\n            onVisibleChanged: function onVisibleChanged(changedVisible) {\n              _onVisibleChanged === null || _onVisibleChanged === void 0 || _onVisibleChanged(changedVisible, {\n                key: eventProps.key\n              });\n              if (!changedVisible) {\n                _this2.removeKey(eventProps.key);\n              }\n            }\n          }), function (props, ref) {\n            return children((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, props), {}, {\n              index: index\n            }), ref);\n          });\n        }));\n      }\n    }], [{\n      key: \"getDerivedStateFromProps\",\n      value: function getDerivedStateFromProps(_ref3, _ref4) {\n        var keys = _ref3.keys;\n        var keyEntities = _ref4.keyEntities;\n        var parsedKeyObjects = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.parseKeys)(keys);\n        var mixedKeyEntities = (0,_util_diff__WEBPACK_IMPORTED_MODULE_11__.diffKeys)(keyEntities, parsedKeyObjects);\n        return {\n          keyEntities: mixedKeyEntities.filter(function (entity) {\n            var prevEntity = keyEntities.find(function (_ref5) {\n              var key = _ref5.key;\n              return entity.key === key;\n            });\n\n            // Remove if already mark as removed\n            if (prevEntity && prevEntity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVED && entity.status === _util_diff__WEBPACK_IMPORTED_MODULE_11__.STATUS_REMOVE) {\n              return false;\n            }\n            return true;\n          })\n        };\n      }\n    }]);\n    return CSSMotionList;\n  }(react__WEBPACK_IMPORTED_MODULE_9__.Component);\n  (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(CSSMotionList, \"defaultProps\", {\n    component: 'div'\n  });\n  return CSSMotionList;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (genCSSMotionList(_util_motion__WEBPACK_IMPORTED_MODULE_12__.supportTransition));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/DomWrapper.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-motion/es/DomWrapper.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nvar DomWrapper = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(DomWrapper, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(DomWrapper);\n  function DomWrapper() {\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, DomWrapper);\n    return _super.apply(this, arguments);\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DomWrapper, [{\n    key: \"render\",\n    value: function render() {\n      return this.props.children;\n    }\n  }]);\n  return DomWrapper;\n}(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomWrapper);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0RvbVdyYXBwZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RTtBQUNOO0FBQ047QUFDTTtBQUNuQztBQUMvQjtBQUNBLEVBQUUsK0VBQVM7QUFDWCxlQUFlLGtGQUFZO0FBQzNCO0FBQ0EsSUFBSSxxRkFBZTtBQUNuQjtBQUNBO0FBQ0EsRUFBRSxrRkFBWTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsQ0FBQyxDQUFDLDRDQUFlO0FBQ2pCLGlFQUFlLFVBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL0RvbVdyYXBwZXIuanM/YTQ4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2NsYXNzQ2FsbENoZWNrIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jbGFzc0NhbGxDaGVja1wiO1xuaW1wb3J0IF9jcmVhdGVDbGFzcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vY3JlYXRlQ2xhc3NcIjtcbmltcG9ydCBfaW5oZXJpdHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2luaGVyaXRzXCI7XG5pbXBvcnQgX2NyZWF0ZVN1cGVyIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVTdXBlclwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xudmFyIERvbVdyYXBwZXIgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKF9SZWFjdCRDb21wb25lbnQpIHtcbiAgX2luaGVyaXRzKERvbVdyYXBwZXIsIF9SZWFjdCRDb21wb25lbnQpO1xuICB2YXIgX3N1cGVyID0gX2NyZWF0ZVN1cGVyKERvbVdyYXBwZXIpO1xuICBmdW5jdGlvbiBEb21XcmFwcGVyKCkge1xuICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBEb21XcmFwcGVyKTtcbiAgICByZXR1cm4gX3N1cGVyLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gIH1cbiAgX2NyZWF0ZUNsYXNzKERvbVdyYXBwZXIsIFt7XG4gICAga2V5OiBcInJlbmRlclwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZW5kZXIoKSB7XG4gICAgICByZXR1cm4gdGhpcy5wcm9wcy5jaGlsZHJlbjtcbiAgICB9XG4gIH1dKTtcbiAgcmV0dXJuIERvbVdyYXBwZXI7XG59KFJlYWN0LkNvbXBvbmVudCk7XG5leHBvcnQgZGVmYXVsdCBEb21XcmFwcGVyOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/DomWrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/context.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-motion/es/context.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   \"default\": () => (/* binding */ MotionProvider)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _excluded = [\"children\"];\n\nvar Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nfunction MotionProvider(_ref) {\n  var children = _ref.children,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, _excluded);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Context.Provider, {\n    value: props\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEY7QUFDMUY7QUFDK0I7QUFDeEIsMkJBQTJCLGdEQUFtQixHQUFHO0FBQ3pDO0FBQ2Y7QUFDQSxZQUFZLDhGQUF3QjtBQUNwQyxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9jb250ZXh0LmpzP2YwMWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbnZhciBfZXhjbHVkZWQgPSBbXCJjaGlsZHJlblwiXTtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgQ29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KHt9KTtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1vdGlvblByb3ZpZGVyKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICBwcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfcmVmLCBfZXhjbHVkZWQpO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiBwcm9wc1xuICB9LCBjaGlsZHJlbik7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useDomMotionEvents.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/motion */ \"(ssr)/./node_modules/rc-motion/es/util/motion.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (onInternalMotionEnd) {\n  var cacheElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n\n  // Remove events\n  function removeMotionEvents(element) {\n    if (element) {\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.removeEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n    }\n  }\n\n  // Patch events\n  function patchMotionEvents(element) {\n    if (cacheElementRef.current && cacheElementRef.current !== element) {\n      removeMotionEvents(cacheElementRef.current);\n    }\n    if (element && element !== cacheElementRef.current) {\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.transitionEndName, onInternalMotionEnd);\n      element.addEventListener(_util_motion__WEBPACK_IMPORTED_MODULE_1__.animationEndName, onInternalMotionEnd);\n\n      // Save as cache in case dom removed trigger by `motionDeadline`\n      cacheElementRef.current = element;\n    }\n  }\n\n  // Clean up when removed\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      removeMotionEvents(cacheElementRef.current);\n    };\n  }, []);\n  return [patchMotionEvents, removeMotionEvents];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n// It's safe to use `useLayoutEffect` but the warning is annoying\nvar useIsomorphicLayoutEffect = (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_0__[\"default\"])() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDtBQUNFOztBQUVuRDtBQUNBLGdDQUFnQyxvRUFBUyxLQUFLLGtEQUFlLEdBQUcsNENBQVM7QUFDekUsaUVBQWUseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG9naWNsZWFwd2ViLy4vbm9kZV9tb2R1bGVzL3JjLW1vdGlvbi9lcy9ob29rcy91c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0LmpzPzhmMjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNhblVzZURvbSBmcm9tIFwicmMtdXRpbC9lcy9Eb20vY2FuVXNlRG9tXCI7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuLy8gSXQncyBzYWZlIHRvIHVzZSBgdXNlTGF5b3V0RWZmZWN0YCBidXQgdGhlIHdhcm5pbmcgaXMgYW5ub3lpbmdcbnZhciB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gY2FuVXNlRG9tKCkgPyB1c2VMYXlvdXRFZmZlY3QgOiB1c2VFZmZlY3Q7XG5leHBvcnQgZGVmYXVsdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useNextFrame.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function () {\n  var nextFrameRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  function cancelNextFrame() {\n    rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"].cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZU5leHRGcmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWlDO0FBQ0Y7QUFDL0IsaUVBQWdCO0FBQ2hCLHFCQUFxQix5Q0FBWTtBQUNqQztBQUNBLElBQUksc0RBQUc7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2hvb2tzL3VzZU5leHRGcmFtZS5qcz85ZTQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCByYWYgZnJvbSBcInJjLXV0aWwvZXMvcmFmXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gKCkge1xuICB2YXIgbmV4dEZyYW1lUmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICBmdW5jdGlvbiBjYW5jZWxOZXh0RnJhbWUoKSB7XG4gICAgcmFmLmNhbmNlbChuZXh0RnJhbWVSZWYuY3VycmVudCk7XG4gIH1cbiAgZnVuY3Rpb24gbmV4dEZyYW1lKGNhbGxiYWNrKSB7XG4gICAgdmFyIGRlbGF5ID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiAyO1xuICAgIGNhbmNlbE5leHRGcmFtZSgpO1xuICAgIHZhciBuZXh0RnJhbWVJZCA9IHJhZihmdW5jdGlvbiAoKSB7XG4gICAgICBpZiAoZGVsYXkgPD0gMSkge1xuICAgICAgICBjYWxsYmFjayh7XG4gICAgICAgICAgaXNDYW5jZWxlZDogZnVuY3Rpb24gaXNDYW5jZWxlZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBuZXh0RnJhbWVJZCAhPT0gbmV4dEZyYW1lUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5leHRGcmFtZShjYWxsYmFjaywgZGVsYXkgLSAxKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBuZXh0RnJhbWVSZWYuY3VycmVudCA9IG5leHRGcmFtZUlkO1xuICB9XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIGNhbmNlbE5leHRGcmFtZSgpO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgcmV0dXJuIFtuZXh0RnJhbWUsIGNhbmNlbE5leHRGcmFtZV07XG59KTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStatus.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useStatus)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util */ \"(ssr)/./node_modules/rc-util/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useSyncState */ \"(ssr)/./node_modules/rc-util/es/hooks/useSyncState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useDomMotionEvents */ \"(ssr)/./node_modules/rc-motion/es/hooks/useDomMotionEvents.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useStepQueue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useStepQueue */ \"(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useSyncState = (0,rc_util_es_hooks_useSyncState__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE),\n    _useSyncState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useSyncState, 2),\n    getStatus = _useSyncState2[0],\n    setStatus = _useSyncState2[1];\n  var _useState3 = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    style = _useState4[0],\n    setStyle = _useState4[1];\n  var currentStatus = getStatus();\n  var mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n  var deadlineRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    setStyle(null, true);\n  }\n  var onInternalMotionEnd = (0,rc_util__WEBPACK_IMPORTED_MODULE_3__.useEvent)(function (event) {\n    var status = getStatus();\n    // Do nothing since not in any transition status.\n    // This may happen when `motionDeadline` trigger.\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      return;\n    }\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  });\n  var _useDomMotionEvents = (0,_useDomMotionEvents__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(onInternalMotionEnd),\n    _useDomMotionEvents2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    switch (targetStatus) {\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onAppearPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onAppearStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onAppearActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onEnterPrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onEnterStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onEnterActive);\n      case _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE:\n        return (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE, onLeavePrepare), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START, onLeaveStart), _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE, onLeaveActive);\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function () {\n    return getEventHandlers(currentStatus);\n  }, [currentStatus]);\n  var _useStepQueue = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(currentStatus, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE) {\n        var onPrepare = eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE];\n        if (!onPrepare) {\n          return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_ACTIVE && currentStatus !== _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return _useStepQueue__WEBPACK_IMPORTED_MODULE_10__.DoStep;\n    }),\n    _useStepQueue2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = (0,_useStepQueue__WEBPACK_IMPORTED_MODULE_10__.isActive)(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  // Update with new status\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function () {\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    if (\n    // Cancel appear\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_LEAVE && !motionLeave) {\n      setStatus(_interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && currentStatus === _interface__WEBPACK_IMPORTED_MODULE_7__.STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 || onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, currentStatus]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[_interface__WEBPACK_IMPORTED_MODULE_7__.STEP_PREPARE] && step === _interface__WEBPACK_IMPORTED_MODULE_7__.STEP_START) {\n    mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [currentStatus, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStatus.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js":
/*!*********************************************************!*\
  !*** ./node_modules/rc-motion/es/hooks/useStepQueue.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DoStep: () => (/* binding */ DoStep),\n/* harmony export */   SkipStep: () => (/* binding */ SkipStep),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isActive: () => (/* binding */ isActive)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useState */ \"(ssr)/./node_modules/rc-util/es/hooks/useState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _interface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interface */ \"(ssr)/./node_modules/rc-motion/es/interface.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/rc-motion/es/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useNextFrame__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useNextFrame */ \"(ssr)/./node_modules/rc-motion/es/hooks/useNextFrame.js\");\n\n\n\n\n\n\nvar FULL_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_START, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARED];\n\n/** Skip current step */\nvar SkipStep = false;\n/** Current step should be update in */\nvar DoStep = true;\nfunction isActive(step) {\n  return step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVE || step === _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (status, prepareOnly, callback) {\n  var _useState = (0,rc_util_es_hooks_useState__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = (0,_useNextFrame__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n    _useNextFrame2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(_interface__WEBPACK_IMPORTED_MODULE_3__.STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n    if (step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_NONE && step !== _interface__WEBPACK_IMPORTED_MODULE_3__.STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/hooks/useStepQueue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/index.js":
/*!********************************************!*\
  !*** ./node_modules/rc-motion/es/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSSMotionList: () => (/* reexport safe */ _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Provider: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _CSSMotion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CSSMotion */ \"(ssr)/./node_modules/rc-motion/es/CSSMotion.js\");\n/* harmony import */ var _CSSMotionList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CSSMotionList */ \"(ssr)/./node_modules/rc-motion/es/CSSMotionList.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-motion/es/context.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_CSSMotion__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFvQztBQUNRO0FBQ0k7QUFDdkI7QUFDekIsaUVBQWUsa0RBQVMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2luZGV4LmpzPzFjMzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENTU01vdGlvbiBmcm9tIFwiLi9DU1NNb3Rpb25cIjtcbmltcG9ydCBDU1NNb3Rpb25MaXN0IGZyb20gXCIuL0NTU01vdGlvbkxpc3RcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUHJvdmlkZXIgfSBmcm9tIFwiLi9jb250ZXh0XCI7XG5leHBvcnQgeyBDU1NNb3Rpb25MaXN0IH07XG5leHBvcnQgZGVmYXVsdCBDU1NNb3Rpb247Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/interface.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/interface.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_APPEAR: () => (/* binding */ STATUS_APPEAR),\n/* harmony export */   STATUS_ENTER: () => (/* binding */ STATUS_ENTER),\n/* harmony export */   STATUS_LEAVE: () => (/* binding */ STATUS_LEAVE),\n/* harmony export */   STATUS_NONE: () => (/* binding */ STATUS_NONE),\n/* harmony export */   STEP_ACTIVATED: () => (/* binding */ STEP_ACTIVATED),\n/* harmony export */   STEP_ACTIVE: () => (/* binding */ STEP_ACTIVE),\n/* harmony export */   STEP_NONE: () => (/* binding */ STEP_NONE),\n/* harmony export */   STEP_PREPARE: () => (/* binding */ STEP_PREPARE),\n/* harmony export */   STEP_PREPARED: () => (/* binding */ STEP_PREPARED),\n/* harmony export */   STEP_START: () => (/* binding */ STEP_START)\n/* harmony export */ });\nvar STATUS_NONE = 'none';\nvar STATUS_APPEAR = 'appear';\nvar STATUS_ENTER = 'enter';\nvar STATUS_LEAVE = 'leave';\nvar STEP_NONE = 'none';\nvar STEP_PREPARE = 'prepare';\nvar STEP_START = 'start';\nvar STEP_ACTIVE = 'active';\nvar STEP_ACTIVATED = 'end';\n/**\n * Used for disabled motion case.\n * Prepare stage will still work but start & active will be skipped.\n */\nvar STEP_PREPARED = 'prepared';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbW90aW9uL2VzL2ludGVyZmFjZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy1tb3Rpb24vZXMvaW50ZXJmYWNlLmpzP2RiNTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBTVEFUVVNfTk9ORSA9ICdub25lJztcbmV4cG9ydCB2YXIgU1RBVFVTX0FQUEVBUiA9ICdhcHBlYXInO1xuZXhwb3J0IHZhciBTVEFUVVNfRU5URVIgPSAnZW50ZXInO1xuZXhwb3J0IHZhciBTVEFUVVNfTEVBVkUgPSAnbGVhdmUnO1xuZXhwb3J0IHZhciBTVEVQX05PTkUgPSAnbm9uZSc7XG5leHBvcnQgdmFyIFNURVBfUFJFUEFSRSA9ICdwcmVwYXJlJztcbmV4cG9ydCB2YXIgU1RFUF9TVEFSVCA9ICdzdGFydCc7XG5leHBvcnQgdmFyIFNURVBfQUNUSVZFID0gJ2FjdGl2ZSc7XG5leHBvcnQgdmFyIFNURVBfQUNUSVZBVEVEID0gJ2VuZCc7XG4vKipcbiAqIFVzZWQgZm9yIGRpc2FibGVkIG1vdGlvbiBjYXNlLlxuICogUHJlcGFyZSBzdGFnZSB3aWxsIHN0aWxsIHdvcmsgYnV0IHN0YXJ0ICYgYWN0aXZlIHdpbGwgYmUgc2tpcHBlZC5cbiAqL1xuZXhwb3J0IHZhciBTVEVQX1BSRVBBUkVEID0gJ3ByZXBhcmVkJzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/interface.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/diff.js":
/*!************************************************!*\
  !*** ./node_modules/rc-motion/es/util/diff.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_ADD: () => (/* binding */ STATUS_ADD),\n/* harmony export */   STATUS_KEEP: () => (/* binding */ STATUS_KEEP),\n/* harmony export */   STATUS_REMOVE: () => (/* binding */ STATUS_REMOVE),\n/* harmony export */   STATUS_REMOVED: () => (/* binding */ STATUS_REMOVED),\n/* harmony export */   diffKeys: () => (/* binding */ diffKeys),\n/* harmony export */   parseKeys: () => (/* binding */ parseKeys),\n/* harmony export */   wrapKeyToObject: () => (/* binding */ wrapKeyToObject)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\n\nvar STATUS_ADD = 'add';\nvar STATUS_KEEP = 'keep';\nvar STATUS_REMOVE = 'remove';\nvar STATUS_REMOVED = 'removed';\nfunction wrapKeyToObject(key) {\n  var keyObj;\n  if (key && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nfunction parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nfunction diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/diff.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-motion/es/util/motion.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-motion/es/util/motion.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animationEndName: () => (/* binding */ animationEndName),\n/* harmony export */   getTransitionName: () => (/* binding */ getTransitionName),\n/* harmony export */   getVendorPrefixedEventName: () => (/* binding */ getVendorPrefixedEventName),\n/* harmony export */   getVendorPrefixes: () => (/* binding */ getVendorPrefixes),\n/* harmony export */   supportTransition: () => (/* binding */ supportTransition),\n/* harmony export */   transitionEndName: () => (/* binding */ transitionEndName)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n\n\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nfunction getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif ((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nfunction getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nvar supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nvar animationEndName = internalAnimationEndName || 'animationend';\nvar transitionEndName = internalTransitionEndName || 'transitionend';\nfunction getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-motion/es/util/motion.js\n");

/***/ })

};
;