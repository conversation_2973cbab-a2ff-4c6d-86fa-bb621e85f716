"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const moment = require("moment");
const payment_order_entity_1 = require("../../util/database/mysql/payment_order/entities/payment-order.entity");
const payment_request_dto_1 = require("../dto/payment-request.dto");
const alipay_strategy_1 = require("../strategies/alipay.strategy");
const wechat_pay_strategy_1 = require("../strategies/wechat-pay.strategy");
const lock_manager_1 = require("../lock/lock.manager");
const payment_config_service_1 = require("../config/payment-config.service");
const core_1 = require("@nestjs/core");
const record_helper_service_1 = require("./record-helper.service");
const payment_order_service_1 = require("../../util/database/mysql/payment_order/payment-order.service");
let PaymentService = PaymentService_1 = class PaymentService {
    paymentOrderRepository;
    alipayStrategy;
    wechatPayStrategy;
    lockManager;
    configService;
    moduleRef;
    recordHelper;
    paymentOrderService;
    logger = new common_1.Logger(PaymentService_1.name);
    strategies = new Map();
    constructor(paymentOrderRepository, alipayStrategy, wechatPayStrategy, lockManager, configService, moduleRef, recordHelper, paymentOrderService) {
        this.paymentOrderRepository = paymentOrderRepository;
        this.alipayStrategy = alipayStrategy;
        this.wechatPayStrategy = wechatPayStrategy;
        this.lockManager = lockManager;
        this.configService = configService;
        this.moduleRef = moduleRef;
        this.recordHelper = recordHelper;
        this.paymentOrderService = paymentOrderService;
        this.registerStrategy(this.alipayStrategy);
        this.registerStrategy(this.wechatPayStrategy);
    }
    registerStrategy(strategy) {
        this.strategies.set(strategy.getChannel(), strategy);
    }
    getPaymentStrategy(channel) {
        const strategy = this.strategies.get(channel);
        if (!strategy) {
            throw new common_1.BadRequestException(`不支持的支付渠道: ${channel}`);
        }
        return strategy;
    }
    getStrategy(channel) {
        return this.getPaymentStrategy(channel);
    }
    generateOrderNo() {
        const date = moment().format('YYYYMMDDHHmmss');
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `P${date}${random}`;
    }
    async createPayment(createPaymentDto) {
        const { userId, amount, subject, description, channel, clientIp, returnUrl, extraData, paymentMode = payment_request_dto_1.PaymentMode.REDIRECT } = createPaymentDto;
        const orderNo = this.generateOrderNo();
        return this.lockManager.withDistributedLock(`payment:create:${userId}:${orderNo}`, async () => {
            try {
                const strategy = this.getStrategy(channel);
                const orderExpireTime = this.configService.getCommonConfig().orderExpireTime;
                const expiredAt = moment().add(orderExpireTime, 'seconds').toDate();
                const paymentOrder = this.paymentOrderRepository.create({
                    id: (0, uuid_1.v4)(),
                    businessOrderId: orderNo,
                    channel,
                    amount,
                    description: description || subject,
                    status: 'pending',
                    userId,
                    clientIp,
                    notifyUrl: this.configService.getNotifyUrl(channel),
                    returnUrl,
                    expiredAt,
                    parameters: {
                        subject,
                        paymentMode,
                        ...extraData,
                    },
                    version: 1,
                });
                await this.paymentOrderRepository.save(paymentOrder);
                let paymentResult;
                if (channel === payment_request_dto_1.PaymentChannel.ALIPAY && paymentMode === payment_request_dto_1.PaymentMode.QR_CODE) {
                    this.logger.log(`为订单 ${orderNo} 创建支付宝电脑网站支付(二维码模式)`);
                    paymentResult = await strategy.createQrCodePayment({
                        outTradeNo: orderNo,
                        subject,
                        totalAmount: amount,
                        returnUrl,
                        notifyUrl: this.configService.getNotifyUrl(channel),
                        timeExpire: expiredAt,
                        ...extraData,
                    });
                }
                else {
                    this.logger.log(`为订单 ${orderNo} 创建${channel}常规支付，方式: ${paymentMode}`);
                    paymentResult = await strategy.createPayment({
                        outTradeNo: orderNo,
                        subject,
                        totalAmount: amount,
                        returnUrl,
                        notifyUrl: this.configService.getNotifyUrl(channel),
                        clientIp,
                        timeExpire: expiredAt,
                        ...extraData,
                    });
                }
                if (!paymentResult.success) {
                    throw new common_1.BadRequestException(`支付创建失败: ${paymentResult.errorMessage}`);
                }
                const updatedParams = {
                    ...paymentOrder.parameters,
                    qrCode: paymentResult.qrCode,
                    paymentUrl: paymentResult.paymentUrl,
                    redirectUrl: paymentResult.redirectUrl,
                };
                await this.paymentOrderRepository.update({ id: paymentOrder.id }, {
                    parameters: updatedParams,
                });
                return {
                    orderNo,
                    paymentUrl: paymentResult.paymentUrl,
                    qrCode: paymentResult.qrCode,
                    redirectUrl: paymentResult.redirectUrl,
                    extraData: {
                        expireTime: orderExpireTime,
                    },
                };
            }
            catch (error) {
                this.logger.error(`创建支付订单失败: ${error.message}`, error.stack);
                throw error;
            }
        }, 10000);
    }
    async queryPaymentStatus(orderNo) {
        const paymentOrder = await this.paymentOrderRepository.findOne({
            where: { businessOrderId: orderNo },
        });
        if (!paymentOrder) {
            throw new common_1.NotFoundException(`订单不存在: ${orderNo}`);
        }
        if (['success', 'closed', 'refund'].includes(paymentOrder.status)) {
            return {
                orderNo,
                status: paymentOrder.status,
                paymentTime: paymentOrder.paidAt,
                paymentId: paymentOrder.channelOrderId,
                amount: Number(paymentOrder.amount),
                channel: paymentOrder.channel,
            };
        }
        const strategy = this.getStrategy(paymentOrder.channel);
        const queryResult = await strategy.queryPayment({
            outTradeNo: orderNo,
            paymentId: paymentOrder.channelOrderId,
        });
        if (queryResult.success) {
            const updates = {
                result: queryResult.rawResponse,
            };
            if (queryResult.paid && paymentOrder.status !== 'success') {
                updates.status = 'success';
                updates.paidAt = queryResult.paymentTime || new Date();
                updates.channelOrderId = queryResult.paymentId;
            }
            else if (queryResult.status === 'closed' && paymentOrder.status !== 'closed') {
                updates.status = 'closed';
                updates.closedAt = new Date();
            }
            if (Object.keys(updates).length > 0) {
                await this.paymentOrderRepository.update({ id: paymentOrder.id }, updates);
                const updatedOrder = await this.paymentOrderRepository.findOne({
                    where: { id: paymentOrder.id },
                });
                if (updatedOrder) {
                    return {
                        orderNo,
                        status: updatedOrder.status,
                        paymentTime: updatedOrder.paidAt,
                        paymentId: updatedOrder.channelOrderId,
                        amount: Number(updatedOrder.amount),
                        channel: updatedOrder.channel,
                    };
                }
            }
        }
        return {
            orderNo,
            status: paymentOrder.status,
            paymentTime: paymentOrder.paidAt,
            paymentId: paymentOrder.channelOrderId,
            amount: Number(paymentOrder.amount),
            channel: paymentOrder.channel,
        };
    }
    async closePayment(orderNo, reason) {
        return this.lockManager.withDistributedLock(`payment:close:${orderNo}`, async () => {
            const paymentOrder = await this.paymentOrderRepository.findOne({
                where: { businessOrderId: orderNo },
            });
            if (!paymentOrder) {
                throw new common_1.NotFoundException(`订单不存在: ${orderNo}`);
            }
            if (paymentOrder.status === 'success') {
                throw new common_1.ConflictException('订单已支付，无法关闭');
            }
            if (paymentOrder.status === 'closed') {
                return true;
            }
            const strategy = this.getStrategy(paymentOrder.channel);
            const closeResult = await strategy.closePayment({
                outTradeNo: orderNo,
                paymentId: paymentOrder.channelOrderId,
            });
            if (!closeResult.success) {
                throw new common_1.BadRequestException(`关闭订单失败: ${closeResult.errorMessage}`);
            }
            const updatedResult = {
                ...paymentOrder.result,
                closeReason: reason,
                closeTime: new Date(),
                closeResponse: closeResult.rawResponse,
            };
            await this.paymentOrderRepository.update({ id: paymentOrder.id }, {
                status: 'closed',
                closedAt: new Date(),
                result: updatedResult,
            });
            return true;
        }, 10000);
    }
    async handlePaymentNotify(channel, notifyData) {
        try {
            const strategy = this.getStrategy(channel);
            if (channel === 'wechatpay') {
                this.logger.debug(`处理微信支付通知: headers=${JSON.stringify(notifyData.headers || {})}`);
                this.logger.debug(`处理微信支付通知: body=${JSON.stringify(notifyData.body || {})}`);
            }
            else {
                this.logger.debug(`处理${channel}支付通知: ${JSON.stringify(notifyData)}`);
            }
            const verifyResult = await strategy.verifyNotify(notifyData);
            if (!verifyResult.verified) {
                this.logger.warn(`支付通知验证失败: ${channel}`);
                return this.getNotifyFailResponse(channel);
            }
            this.logger.log(`支付通知验证成功: ${channel}, outTradeNo=${verifyResult.outTradeNo}, paymentId=${verifyResult.paymentId}`);
            const { outTradeNo, paymentId, totalAmount, paymentTime, tradeStatus } = verifyResult;
            return await this.lockManager.withDistributedLock(`payment:notify:${outTradeNo}`, async () => {
                const paymentOrder = await this.paymentOrderRepository.findOne({
                    where: { businessOrderId: outTradeNo },
                });
                if (!paymentOrder) {
                    this.logger.warn(`通知对应的订单不存在: ${outTradeNo}`);
                    return this.getNotifyFailResponse(channel);
                }
                if (['success', 'closed', 'refund'].includes(paymentOrder.status)) {
                    this.logger.log(`订单已处于终态 ${paymentOrder.status}，忽略通知: ${outTradeNo}`);
                    return this.getNotifySuccessResponse(channel);
                }
                await this.lockManager.withPessimisticRowLock('payment_order', { id: paymentOrder.id }, async (manager) => {
                    const updatedResult = {
                        ...paymentOrder.result,
                        paymentTime,
                        paymentId,
                        notifyData: verifyResult,
                    };
                    let orderStatus = 'pending';
                    if (channel === 'alipay') {
                        if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
                            orderStatus = 'success';
                        }
                        else if (tradeStatus === 'TRADE_CLOSED') {
                            orderStatus = 'closed';
                        }
                    }
                    else if (channel === 'wechatpay') {
                        if (verifyResult.tradeState === 'SUCCESS') {
                            orderStatus = 'success';
                        }
                        else if (verifyResult.tradeState === 'CLOSED' || verifyResult.tradeState === 'REVOKED') {
                            orderStatus = 'closed';
                        }
                    }
                    const updateData = {
                        status: orderStatus,
                        result: updatedResult,
                        notifyData: verifyResult,
                    };
                    if (orderStatus === 'success') {
                        updateData.paidAt = paymentTime || new Date();
                        updateData.channelOrderId = paymentId;
                    }
                    else if (orderStatus === 'closed') {
                        updateData.closedAt = new Date();
                    }
                    await manager.update(payment_order_entity_1.PaymentOrder, { id: paymentOrder.id }, updateData);
                    try {
                        this.logger.log(`开始创建/更新支付记录: channel=${channel}, orderNo=${outTradeNo}, amount=${totalAmount}, status=${orderStatus}`);
                        await this.recordHelper.createOrUpdatePaymentRecord({
                            tradeNo: outTradeNo || '',
                            paymentId: paymentId || '',
                            amount: totalAmount || 0,
                            status: orderStatus,
                            paymentTime: paymentTime || new Date(),
                            payMethod: channel,
                            userId: paymentOrder.userId || '',
                            description: paymentOrder.description || `${channel}支付`,
                            remark: `通过${channel}支付通知创建，状态: ${orderStatus}`,
                            extraData: {
                                notifyData: verifyResult,
                                orderId: paymentOrder.id,
                                tradeStatus: tradeStatus || verifyResult.tradeState,
                            },
                        });
                        this.logger.log(`成功创建/更新支付记录: channel=${channel}, orderNo=${outTradeNo}`);
                        if (orderStatus === 'success') {
                            this.logger.log(`订单 ${outTradeNo} 支付成功，金额: ${totalAmount}`);
                        }
                        else if (orderStatus === 'closed') {
                            this.logger.log(`订单 ${outTradeNo} 已关闭`);
                        }
                        else {
                            this.logger.log(`订单 ${outTradeNo} 状态更新为: ${orderStatus}`);
                        }
                    }
                    catch (error) {
                        this.logger.error(`创建/更新支付记录失败: ${error.message}`, error.stack);
                    }
                });
                return this.getNotifySuccessResponse(channel);
            }, 10000);
        }
        catch (error) {
            this.logger.error(`处理支付通知失败: ${error.message}`, error.stack);
            return this.getNotifyFailResponse(channel);
        }
    }
    getNotifySuccessResponse(channel) {
        if (channel === payment_request_dto_1.PaymentChannel.ALIPAY) {
            return 'success';
        }
        else if (channel === payment_request_dto_1.PaymentChannel.WECHATPAY) {
            return JSON.stringify({
                code: 'SUCCESS',
                message: 'OK',
            });
        }
        return 'success';
    }
    getNotifyFailResponse(channel) {
        if (channel === payment_request_dto_1.PaymentChannel.ALIPAY) {
            return 'fail';
        }
        else if (channel === payment_request_dto_1.PaymentChannel.WECHATPAY) {
            return JSON.stringify({
                code: 'FAIL',
                message: 'Signature verification failed',
            });
        }
        return 'fail';
    }
    async getOrderDetail(orderNo) {
        try {
            this.logger.log(`获取订单详情: ${orderNo}`);
            const orderInfo = await this.paymentOrderService.findByBusinessOrderId(orderNo);
            const result = {
                success: true,
                orderNo: orderInfo.businessOrderId,
                channel: orderInfo.channel,
                amount: orderInfo.amount,
                status: orderInfo.status,
                paymentTime: orderInfo.paidAt,
                createTime: orderInfo.createdAt,
                subject: orderInfo.description,
                description: orderInfo.description,
                userId: orderInfo.userId,
                paymentId: orderInfo.channelOrderId,
                extraData: orderInfo.parameters || {}
            };
            if (orderInfo.result) {
                if (orderInfo.result.qrCode) {
                    result['qrCode'] = orderInfo.result.qrCode;
                }
            }
            return result;
        }
        catch (error) {
            this.logger.error(`获取订单详情失败: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.PaymentService = PaymentService;
exports.PaymentService = PaymentService = PaymentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_order_entity_1.PaymentOrder)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        alipay_strategy_1.AlipayStrategy,
        wechat_pay_strategy_1.WechatPayStrategy,
        lock_manager_1.LockManager,
        payment_config_service_1.PaymentConfigService,
        core_1.ModuleRef,
        record_helper_service_1.RecordHelperService,
        payment_order_service_1.PaymentOrderService])
], PaymentService);
//# sourceMappingURL=payment.service.js.map