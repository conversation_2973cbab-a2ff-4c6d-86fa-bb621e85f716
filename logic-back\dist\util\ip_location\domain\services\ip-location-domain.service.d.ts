import { IpAddress } from '../value-objects/ip-address.vo';
import { GeographicLocation } from '../value-objects/geographic-location.vo';
export declare class IpLocationDomainService {
    private ip2region;
    constructor();
    resolveLocation(ipAddress: IpAddress): Promise<GeographicLocation>;
    resolveMultipleLocations(ipAddresses: IpAddress[]): Promise<GeographicLocation[]>;
    canResolveLocation(ipAddress: IpAddress): boolean;
    getIpBasicInfo(ipAddress: IpAddress): {
        value: string;
        type: 'IPv4' | 'IPv6';
        isPrivate: boolean;
        isLoopback: boolean;
        isPublic: boolean;
        canGeolocate: boolean;
        masked: string;
    };
    compareLocations(ipAddress1: IpAddress, ipAddress2: IpAddress): Promise<{
        location1: GeographicLocation;
        location2: GeographicLocation;
        similarity: number;
        isSameCountry: boolean;
        isSameProvince: boolean;
        isSameCity: boolean;
        isSameISP: boolean;
    }>;
    private initializeIp2Region;
    private validateIpForGeolocation;
    private validateLocationQuality;
    getSupportedIpTypes(): string[];
    getServiceStatus(): {
        isAvailable: boolean;
        version: string;
        supportedTypes: string[];
    };
    testResolution(testIp?: string): Promise<{
        success: boolean;
        ipAddress?: IpAddress;
        location?: GeographicLocation;
        error?: string;
        performanceMs: number;
    }>;
    private isDevelopmentEnvironment;
    private createDefaultLocationForDevelopment;
}
