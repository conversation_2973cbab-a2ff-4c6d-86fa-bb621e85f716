import { ConfigService } from '@nestjs/config';
export interface AlipayConfig {
    appId: string;
    privateKey: string;
    publicKey: string;
    aliPublicKey: string;
    gateway: string;
    notifyUrl: string;
    returnUrl: string;
    signType: string;
}
export interface WechatPayConfig {
    appId: string;
    mchId: string;
    mchKey: string;
    privateKeyPath: string;
    certificatePath: string;
    notifyUrl: string;
    refundNotifyUrl: string;
    returnUrl: string;
    signType: string;
    serialNo?: string;
    apiV3Key?: string;
    wechatPayPublicKeyPath?: string;
}
export interface CommonConfig {
    notifyDomain: string;
    orderExpireTime: number;
    retryInterval: number;
    maxRetryCount: number;
    defaultLockType: string;
}
export declare class PaymentConfigService {
    private readonly configService;
    constructor(configService: ConfigService);
    private checkWechatPayConfig;
    getAlipayConfig(): AlipayConfig;
    getWechatPayConfig(): WechatPayConfig;
    getCommonConfig(): CommonConfig;
    getNotifyUrl(channel: string): string;
    getReturnUrl(channel: string): string;
}
