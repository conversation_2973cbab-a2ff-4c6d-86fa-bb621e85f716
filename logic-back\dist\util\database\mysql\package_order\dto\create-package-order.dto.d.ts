export declare enum PackageOrderStatus {
    PENDING = "pending",
    PAID = "paid",
    CANCELLED = "cancelled"
}
export declare class CreatePackageOrderDto {
    orderNo: string;
    userId: string;
    packageId: number;
    packageName: string;
    points: number;
    validityDays: number;
    price: number;
    originalPrice: number;
    discountRate?: number;
    status?: PackageOrderStatus;
    paymentId?: string;
    paidTime?: Date;
}
