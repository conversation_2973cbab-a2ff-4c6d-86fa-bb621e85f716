import { IpAddress } from '../value-objects/ip-address.vo';
import { GeographicLocation } from '../value-objects/geographic-location.vo';
import { RiskScore } from '../value-objects/risk-score.vo';
export interface UserLocationHistory {
    commonLocations: Array<{
        location: GeographicLocation;
        loginCount: number;
        isTrusted: boolean;
        lastLoginAt: Date;
        firstLoginAt: Date;
    }>;
    totalLoginCount: number;
    riskLoginCount: number;
    accountAge: number;
}
export interface RiskAssessmentConfig {
    lowThreshold: number;
    mediumThreshold: number;
    highThreshold: number;
    foreignLoginWeight: number;
    crossProvinceWeight: number;
    newLocationWeight: number;
    ispChangeWeight: number;
    dataQualityWeight: number;
    newUserProtectionDays: number;
    newUserRiskReduction: number;
    minDataQuality: number;
}
export declare class RiskAssessmentDomainService {
    private readonly defaultConfig;
    private config;
    constructor(config?: Partial<RiskAssessmentConfig>);
    assessLoginRisk(currentLocation: GeographicLocation, userHistory: UserLocationHistory, ipAddress: IpAddress): RiskScore;
    assessLocationChangeRisk(previousLocation: GeographicLocation, currentLocation: GeographicLocation): RiskScore;
    analyzeVerificationNeeds(riskScore: RiskScore, userHistory: UserLocationHistory): {
        needsVerification: boolean;
        verificationMethods: string[];
        reason: string;
        urgency: 'LOW' | 'MEDIUM' | 'HIGH';
    };
    calculateUserRiskProfile(userHistory: UserLocationHistory): {
        riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
        trustScore: number;
        characteristics: string[];
        recommendations: string[];
    };
    private hasLocationInHistory;
    private hasISPInHistory;
    private isNewUser;
    updateConfig(newConfig: Partial<RiskAssessmentConfig>): void;
    getConfig(): RiskAssessmentConfig;
}
