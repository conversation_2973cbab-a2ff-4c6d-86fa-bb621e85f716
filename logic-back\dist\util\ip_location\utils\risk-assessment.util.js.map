{"version": 3, "file": "risk-assessment.util.js", "sourceRoot": "", "sources": ["../../../../src/util/ip_location/utils/risk-assessment.util.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAE5C,0EAAsE;AA0C/D,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAcA;IAbZ,UAAU,GAAe;QACxC,YAAY,EAAE,EAAE;QAChB,eAAe,EAAE,EAAE;QACnB,aAAa,EAAE,GAAG;QAClB,kBAAkB,EAAE,EAAE;QACtB,mBAAmB,EAAE,EAAE;QACvB,iBAAiB,EAAE,EAAE;QACrB,eAAe,EAAE,CAAC;QAClB,iBAAiB,EAAE,EAAE;QACrB,qBAAqB,EAAE,CAAC;QACxB,oBAAoB,EAAE,EAAE;KACzB,CAAC;IAEF,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAS7D,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,eAA6B,EAC7B,WAA8B;QAE9B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YACzE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE5D,MAAM,IAAI,GAAiB;gBACzB,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC;gBAC9D,gBAAgB,EAAE,SAAS,KAAK,MAAM;gBACtC,OAAO,EAAE,UAAU,CAAC,OAAO;aAC5B,CAAC;YAEF,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,oBAAoB,EAAE,iBAAiB,EAAE;gBACtE,MAAM;gBACN,QAAQ,EAAE,GAAG,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,EAAE;gBAC9D,SAAS,EAAE,IAAI,CAAC,KAAK;gBACrB,SAAS,EAAE,IAAI,CAAC,KAAK;gBACrB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACrC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,oBAAoB,EAAE,iBAAiB,EAAE;gBACtE,MAAM;gBACN,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACrC,EAAE,KAAK,CAAC,CAAC;YAGV,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,cAAc;gBACtB,gBAAgB,EAAE,KAAK;gBACvB,OAAO,EAAE,CAAC,MAAM,CAAC;aAClB,CAAC;QACJ,CAAC;IACH,CAAC;IAQO,kBAAkB,CACxB,eAA6B,EAC7B,WAA8B;QAE9B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,WAAW,GAAa,EAAE,CAAC;QAGjC,IAAI,eAAe,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YACrC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;YAC5C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAGD,IAAI,eAAe,CAAC,QAAQ,KAAK,IAAI,IAAI,eAAe,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YACvE,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;YAC3C,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;QAGD,MAAM,kBAAkB,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAChE,GAAG,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,IAAI,eAAe,CAAC,QAAQ,KAAK,IAAI,CAC/E,CAAC;QAEF,IAAI,CAAC,kBAAkB,IAAI,eAAe,CAAC,OAAO,KAAK,IAAI,IAAI,eAAe,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YACjG,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;YAC7C,WAAW,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;QAGD,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,WAAW,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACzD,GAAG,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ;gBACzC,GAAG,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI;gBACjC,eAAe,CAAC,IAAI,KAAK,IAAI,CAC9B,CAAC;YAEF,IAAI,CAAC,WAAW,IAAI,eAAe,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClD,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC,UAAU,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAGD,MAAM,aAAa,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAC3D,GAAG,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ;YACzC,eAAe,CAAC,GAAG,KAAK,IAAI,CAC7B,CAAC;QAEF,IAAI,kBAAkB,IAAI,CAAC,aAAa,IAAI,eAAe,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;YACzE,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;YACzC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAGD,IAAI,WAAW,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YACnC,KAAK,IAAI,EAAE,CAAC;YACZ,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAGD,IAAI,WAAW,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YACpC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YAClE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;YACxC,OAAO,EAAE,WAAW;SACrB,CAAC;IACJ,CAAC;IAOO,kBAAkB,CAAC,KAAa;QACtC,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YAC3C,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;YACpD,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAQO,kBAAkB,CAAC,KAAgC,EAAE,OAAiB;QAC5E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAE9B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,MAAM;gBACT,OAAO,OAAO,UAAU,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,OAAO,OAAO,UAAU,EAAE,CAAC;YAC7B,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,OAAO,UAAU,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAQD,0BAA0B,CAAC,IAAkB,EAAE,WAA8B;QAE3E,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,WAAW,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAOD,iCAAiC,CAAC,IAAkB;QAClD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAMD,gBAAgB,CAAC,SAA8B;QAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAE1C,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,oBAAoB,EAAE,kBAAkB,EAAE;YACvE,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACrC,SAAS,EAAE,IAAI,CAAC,UAAU;SAC3B,CAAC,CAAC;IACL,CAAC;IAMD,aAAa;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IAChC,CAAC;CACF,CAAA;AA1PY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAeiC,8BAAa;GAd9C,kBAAkB,CA0P9B"}