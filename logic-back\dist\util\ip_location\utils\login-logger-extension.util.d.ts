import { IpLocationApplicationService } from '../application/services/ip-location-application.service';
import { LoginType, LoginStatus } from '../../database/mysql/user_login_log/entities/user_login_log.entity';
import { LoggerService } from '../../../common/logger/logger.service';
export interface ExtendedLoginLogData {
    userId: number;
    loginType: LoginType;
    loginStatus: LoginStatus;
    clientIp: string;
    userAgent?: string;
    deviceInfo?: string;
    sessionId?: string;
    tokenExpireTime?: Date;
    failReason?: string;
    enableLocationResolution?: boolean;
    enableRiskAssessment?: boolean;
}
export declare class LoginLoggerExtensionUtil {
    private readonly ipLocationService;
    private readonly loggerService;
    constructor(ipLocationService: IpLocationApplicationService, loggerService: LoggerService);
    logSuccessLoginWithLocation(data: ExtendedLoginLogData): Promise<void>;
    logFailedLoginWithLocation(data: ExtendedLoginLogData): Promise<void>;
    checkNeedAdditionalVerification(userId: number, clientIp: string): Promise<{
        needVerification: boolean;
        reason: string;
        recommendedMethods: string[];
    }>;
    private maskIP;
}
