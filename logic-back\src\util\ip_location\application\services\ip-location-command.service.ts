import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

// 命令对象
import { UpdateCommonLocationCommand } from '../commands/update-common-location.command';
import { SetTrustedLocationCommand } from '../commands/set-trusted-location.command';
import { RecordLoginLocationCommand } from '../commands/record-login-location.command';

// 接口
import { I<PERSON>ommandHand<PERSON>, CommandResult, createSuccessResult, createFailureResult } from '../interfaces/command-handler.interface';

// 领域服务
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';

// 实体
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';

// 异常
import { IpLocationDomainException } from '../../domain/exceptions/ip-location-domain.exception';

/**
 * IP地理位置命令处理服务
 * 处理所有与IP地理位置相关的写操作命令
 */
@Injectable()
export class IpLocationCommandService {
  constructor(
    @InjectRepository(UserCommonLocation)
    private readonly userLocationRepository: Repository<UserCommonLocation>,
    private readonly ipLocationDomainService: IpLocationDomainService
  ) {}

  /**
   * 处理更新用户常用位置命令
   */
  async handleUpdateCommonLocation(
    command: UpdateCommonLocationCommand
  ): Promise<CommandResult<UserCommonLocation>> {
    const startTime = Date.now();

    try {
      // 1. 验证命令
      const validation = command.validate();
      if (!validation.isValid) {
        return createFailureResult(
          validation.errors,
          '命令验证失败',
          Date.now() - startTime
        );
      }

      // 2. 查找或创建用户位置记录
      let userLocation = await this.userLocationRepository.findOne({
        where: {
          userId: command.userId,
          country: command.location.country,
          province: command.location.province,
          city: command.location.city
        }
      });

      if (!userLocation) {
        // 创建新的位置记录
        userLocation = this.userLocationRepository.create({
          userId: command.userId,
          country: command.location.country,
          province: command.location.province,
          city: command.location.city,
          isp: command.location.isp,
          dataSource: command.location.dataSource,
          hasEmptyFields: command.location.hasEmptyFields,
          confidence: command.location.confidence,
          displayName: command.location.displayName,
          loginCount: 1,
          lastLoginAt: command.timestamp,
          firstLoginAt: command.timestamp,
          isTrusted: false,
          trustScore: this.calculateInitialTrustScore(command.location)
        });
      } else {
        // 更新现有记录
        userLocation.loginCount += 1;
        userLocation.lastLoginAt = command.timestamp;
        userLocation.isp = command.location.isp; // 更新ISP信息
        userLocation.confidence = Math.max(userLocation.confidence, command.location.confidence);
        userLocation.trustScore = this.calculateUpdatedTrustScore(userLocation, command.location);
        
        // 如果数据质量提升，更新相关字段
        if (command.location.confidence > userLocation.confidence) {
          userLocation.dataSource = command.location.dataSource;
          userLocation.hasEmptyFields = command.location.hasEmptyFields;
          userLocation.displayName = command.location.displayName;
        }
      }

      // 3. 保存到数据库
      const savedLocation = await this.userLocationRepository.save(userLocation);

      return createSuccessResult(
        savedLocation,
        '用户常用位置更新成功',
        Date.now() - startTime
      );

    } catch (error) {
      if (error instanceof IpLocationDomainException) {
        return createFailureResult(
          [error.message],
          '领域业务错误',
          Date.now() - startTime
        );
      }

      return createFailureResult(
        ['更新用户常用位置时发生未知错误'],
        error.message,
        Date.now() - startTime
      );
    }
  }

  /**
   * 处理设置可信位置命令
   */
  async handleSetTrustedLocation(
    command: SetTrustedLocationCommand
  ): Promise<CommandResult<UserCommonLocation[]>> {
    const startTime = Date.now();

    try {
      // 1. 验证命令
      const validation = command.validate();
      if (!validation.isValid) {
        return createFailureResult(
          validation.errors,
          '命令验证失败',
          Date.now() - startTime
        );
      }

      // 2. 查找匹配的位置记录
      const matchingLocations = await this.userLocationRepository.find({
        where: {
          userId: command.userId,
          province: command.province,
          city: command.city
        }
      });

      if (matchingLocations.length === 0) {
        return createFailureResult(
          ['未找到匹配的位置记录'],
          '无法设置可信位置',
          Date.now() - startTime
        );
      }

      // 3. 更新为可信位置
      const updatedLocations: UserCommonLocation[] = [];
      
      for (const location of matchingLocations) {
        location.isTrusted = true;
        location.trustScore = Math.min(location.trustScore + 20, 100); // 提升信任分数
        location.trustedAt = command.timestamp;
        location.trustedBy = command.setBy;
        location.trustReason = command.reason;
        
        const saved = await this.userLocationRepository.save(location);
        updatedLocations.push(saved);
      }

      return createSuccessResult(
        updatedLocations,
        `成功设置${updatedLocations.length}个位置为可信位置`,
        Date.now() - startTime
      );

    } catch (error) {
      if (error instanceof IpLocationDomainException) {
        return createFailureResult(
          [error.message],
          '领域业务错误',
          Date.now() - startTime
        );
      }

      return createFailureResult(
        ['设置可信位置时发生未知错误'],
        error.message,
        Date.now() - startTime
      );
    }
  }

  /**
   * 处理记录登录位置命令
   */
  async handleRecordLoginLocation(
    command: RecordLoginLocationCommand
  ): Promise<CommandResult<{ locationUpdated: boolean; loginRecorded: boolean }>> {
    const startTime = Date.now();

    try {
      // 1. 验证命令
      const validation = command.validate();
      if (!validation.isValid) {
        return createFailureResult(
          validation.errors,
          '命令验证失败',
          Date.now() - startTime
        );
      }

      let locationUpdated = false;
      let loginRecorded = false;

      // 2. 如果是成功登录，更新常用位置
      if (command.isSuccessLogin) {
        const updateCommand = new UpdateCommonLocationCommand(
          command.userId,
          command.ipAddress,
          command.location,
          command.sessionId,
          command.userAgent
        );

        const updateResult = await this.handleUpdateCommonLocation(updateCommand);
        locationUpdated = updateResult.success;
      }

      // 3. 记录登录日志（这里应该调用登录日志服务）
      // 注意：这里应该集成现有的登录日志系统
      try {
        // TODO: 集成现有的 LoginLoggerUtil 或创建新的登录日志服务
        loginRecorded = true;
      } catch (logError) {
        // 登录日志记录失败不应该影响主要流程
        console.warn('登录日志记录失败:', logError);
        loginRecorded = false;
      }

      const result = {
        locationUpdated,
        loginRecorded
      };

      return createSuccessResult(
        result,
        '登录位置记录处理完成',
        Date.now() - startTime
      );

    } catch (error) {
      if (error instanceof IpLocationDomainException) {
        return createFailureResult(
          [error.message],
          '领域业务错误',
          Date.now() - startTime
        );
      }

      return createFailureResult(
        ['记录登录位置时发生未知错误'],
        error.message,
        Date.now() - startTime
      );
    }
  }

  /**
   * 计算初始信任分数
   */
  private calculateInitialTrustScore(location: any): number {
    let score = 50; // 基础分数

    // 数据质量影响
    if (location.isHighQuality) {
      score += 20;
    } else if (location.confidence > 50) {
      score += 10;
    }

    // 境内位置加分
    if (location.isDomestic) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  /**
   * 计算更新后的信任分数
   */
  private calculateUpdatedTrustScore(userLocation: UserCommonLocation, newLocation: any): number {
    let score = userLocation.trustScore;

    // 登录次数影响
    if (userLocation.loginCount > 10) {
      score += 5;
    } else if (userLocation.loginCount > 5) {
      score += 2;
    }

    // 数据质量提升
    if (newLocation.confidence > userLocation.confidence) {
      score += 5;
    }

    return Math.min(score, 100);
  }
}
