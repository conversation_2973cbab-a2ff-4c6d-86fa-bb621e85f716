export declare class PaymentRefund {
    id: string;
    paymentOrderId: string;
    businessRefundId: string;
    channelRefundId: string;
    channel: string;
    amount: number;
    reason: string;
    status: string;
    parameters: Record<string, any>;
    result: Record<string, any>;
    notifyData: Record<string, any>;
    userId: string;
    operatorId: string;
    notifyUrl: string;
    refundedAt: Date;
    version: number;
    createdAt: Date;
    updatedAt: Date;
}
