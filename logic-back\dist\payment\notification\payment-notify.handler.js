"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PaymentNotifyHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentNotifyHandler = void 0;
const common_1 = require("@nestjs/common");
const payment_service_1 = require("../services/payment.service");
const notify_service_1 = require("./notify.service");
const payment_record_service_1 = require("../../util/database/mysql/payment_record/payment-record.service");
const lock_manager_1 = require("../lock/lock.manager");
let PaymentNotifyHandler = PaymentNotifyHandler_1 = class PaymentNotifyHandler {
    paymentService;
    notifyService;
    paymentRecordService;
    lockManager;
    logger = new common_1.Logger(PaymentNotifyHandler_1.name);
    constructor(paymentService, notifyService, paymentRecordService, lockManager) {
        this.paymentService = paymentService;
        this.notifyService = notifyService;
        this.paymentRecordService = paymentRecordService;
        this.lockManager = lockManager;
    }
    async handlePaymentNotify(channel, data) {
        const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        this.logger.log(`[${requestId}] 开始处理${channel}支付通知`);
        try {
            this.logger.log(`[${requestId}] 开始验证${channel}支付通知签名`);
            try {
                const outTradeNo = await this.extractOrderNoFromNotify(channel, data);
                if (!outTradeNo) {
                    this.logger.warn(`[${requestId}] ${channel}支付通知无法提取订单号`);
                    return { success: false, message: '无法提取订单号' };
                }
                this.logger.log(`[${requestId}] 支付通知中提取到订单号: ${outTradeNo}`);
                this.logger.log(`[${requestId}] ${channel}支付通知验证成功: 订单号=${outTradeNo}`);
                const queryResult = await this.paymentService.queryPaymentStatus(outTradeNo);
                if (!queryResult || !queryResult.orderNo) {
                    this.logger.warn(`[${requestId}] 查询支付状态失败: ${outTradeNo}`);
                    return { success: true, message: '通知验证成功，但查询状态失败' };
                }
                if (queryResult.status === 'success' || queryResult.status === 'paid') {
                    return this.lockManager.withDistributedLock(`payment:notify:${outTradeNo}`, async () => {
                        this.logger.log(`[${requestId}] 获取支付通知分布式锁成功: ${outTradeNo}, 准备处理支付成功通知`);
                        try {
                            const success = await this.notifyService.handlePaymentSuccess(outTradeNo, queryResult.paymentId || '', queryResult.amount || 0, channel, { ...queryResult, rawNotify: data });
                            if (success) {
                                this.logger.log(`[${requestId}] ${channel}支付通知处理成功: ${outTradeNo}`);
                                return { success: true, message: '处理成功' };
                            }
                            else {
                                this.logger.warn(`[${requestId}] ${channel}支付通知处理失败: ${outTradeNo}`);
                                return { success: false, message: '处理失败' };
                            }
                        }
                        catch (error) {
                            this.logger.error(`[${requestId}] 处理${channel}支付通知异常: ${error.message}`, error.stack);
                            return { success: false, message: `处理异常: ${error.message}` };
                        }
                        finally {
                            this.logger.log(`[${requestId}] 支付通知处理完成，分布式锁即将释放: ${outTradeNo}`);
                        }
                    }, 10000);
                }
                else {
                    this.logger.warn(`[${requestId}] 支付尚未成功，状态为: ${queryResult.status}`);
                    return { success: true, message: '通知接收成功，但支付尚未完成' };
                }
            }
            catch (error) {
                this.logger.error(`[${requestId}] 验证${channel}支付通知异常: ${error.message}`, error.stack);
                return { success: false, message: `验证异常: ${error.message}` };
            }
        }
        catch (error) {
            this.logger.error(`[${requestId}] 处理${channel}支付通知异常: ${error.message}`, error.stack);
            return { success: false, message: `处理异常: ${error.message}` };
        }
    }
    async extractOrderNoFromNotify(channel, data) {
        try {
            this.logger.debug(`提取订单号，传入数据: ${JSON.stringify(data, null, 2).substring(0, 500)}...`);
            if (channel === 'alipay') {
                return data.out_trade_no;
            }
            else if (channel === 'wechatpay') {
                if (data.decryptedResource && data.decryptedResource.out_trade_no) {
                    this.logger.debug(`从decryptedResource中获取订单号: ${data.decryptedResource.out_trade_no}`);
                    return data.decryptedResource.out_trade_no;
                }
                if (data.body && data.body.out_trade_no) {
                    this.logger.debug(`从body中获取订单号: ${data.body.out_trade_no}`);
                    return data.body.out_trade_no;
                }
                if (data.body && data.body.resource_decoded && data.body.resource_decoded.out_trade_no) {
                    this.logger.debug(`从body.resource_decoded中获取订单号: ${data.body.resource_decoded.out_trade_no}`);
                    return data.body.resource_decoded.out_trade_no;
                }
                const notifyBody = data.body || data;
                if (notifyBody.resource_decoded && notifyBody.resource_decoded.out_trade_no) {
                    this.logger.debug(`从notifyBody.resource_decoded中获取订单号: ${notifyBody.resource_decoded.out_trade_no}`);
                    return notifyBody.resource_decoded.out_trade_no;
                }
                if (notifyBody.resource && notifyBody.resource.ciphertext) {
                    try {
                        this.logger.debug(`尝试解密微信支付通知resource数据`);
                        const wechatStrategy = this.paymentService.getPaymentStrategy('wechatpay');
                        const resourceData = await wechatStrategy.verifyNotify({
                            headers: data.headers || {},
                            body: notifyBody
                        });
                        if (resourceData.verified && resourceData.outTradeNo) {
                            this.logger.debug(`成功从解密数据中获取订单号: ${resourceData.outTradeNo}`);
                            return resourceData.outTradeNo;
                        }
                        if (resourceData.rawNotify) {
                            const decrypted = resourceData.rawNotify;
                            if (decrypted.out_trade_no) {
                                this.logger.debug(`从解密后的rawNotify中获取订单号: ${decrypted.out_trade_no}`);
                                return decrypted.out_trade_no;
                            }
                        }
                    }
                    catch (error) {
                        this.logger.error(`解密微信支付通知数据失败: ${error.message}`, error.stack);
                    }
                }
                this.logger.debug(`无法直接从数据结构中获取订单号，检查日志记录的解密数据`);
                if (notifyBody.event_type) {
                    this.logger.debug(`微信支付通知事件类型: ${notifyBody.event_type}, 摘要: ${notifyBody.summary || '无摘要'}`);
                }
                this.logger.debug(`微信支付通知结构无法提取订单号，请确保在PaymentService中正确解密并解析数据`);
            }
            return undefined;
        }
        catch (error) {
            this.logger.error(`提取订单号失败: ${error.message}`, error.stack);
            return undefined;
        }
    }
    async manualTriggerPaymentSuccess(outTradeNo, channel) {
        return this.lockManager.withDistributedLock(`payment:manual:${outTradeNo}`, async () => {
            try {
                this.logger.log(`手动触发支付成功处理: ${outTradeNo}, 渠道: ${channel}`);
                const paymentRecords = await this.paymentRecordService.findByOrderNo(outTradeNo);
                if (!paymentRecords || paymentRecords.length === 0) {
                    this.logger.error(`未找到支付记录: ${outTradeNo}`);
                    return false;
                }
                const paymentRecord = paymentRecords[0];
                const queryResult = await this.paymentService.queryPaymentStatus(outTradeNo);
                if (queryResult.status !== 'success') {
                    this.logger.warn(`支付未完成，无法手动触发: ${outTradeNo}, 状态: ${queryResult.status}`);
                    return false;
                }
                const result = await this.notifyService.handlePaymentSuccess(outTradeNo, queryResult.paymentId || '', queryResult.amount, channel, {
                    rawQuery: queryResult,
                    userId: paymentRecord.userId,
                });
                if (!result) {
                    this.logger.error(`手动触发支付成功处理失败: ${outTradeNo}`);
                    return false;
                }
                this.logger.log(`手动触发支付成功处理完成: ${outTradeNo}`);
                return true;
            }
            catch (error) {
                this.logger.error(`手动触发支付成功处理异常: ${error.message}`, error.stack);
                return false;
            }
        }, 10000);
    }
};
exports.PaymentNotifyHandler = PaymentNotifyHandler;
exports.PaymentNotifyHandler = PaymentNotifyHandler = PaymentNotifyHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_service_1.PaymentService,
        notify_service_1.NotifyService,
        payment_record_service_1.PaymentRecordService,
        lock_manager_1.LockManager])
], PaymentNotifyHandler);
//# sourceMappingURL=payment-notify.handler.js.map