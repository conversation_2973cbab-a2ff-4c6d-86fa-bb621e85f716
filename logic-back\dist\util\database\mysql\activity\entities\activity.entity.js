"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Activity = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const activity_tag_entity_1 = require("../../activity_tag/entities/activity_tag.entity");
const activity_work_entity_1 = require("../../activity_work/entities/activity_work.entity");
let Activity = class Activity {
    id;
    name;
    startTime;
    endTime;
    coverImage;
    organizer;
    creatorId;
    createTime;
    updateTime;
    status;
    activityType;
    isDelete;
    reviewReason;
    attachmentFiles;
    promotionImage;
    competitionGroups;
    registrationForm;
    activityTags;
    activityWorks;
};
exports.Activity = Activity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], Activity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动名称' }),
    (0, swagger_1.ApiProperty)({ description: '活动名称' }),
    __metadata("design:type", String)
], Activity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动开始时间', type: 'datetime' }),
    (0, swagger_1.ApiProperty)({ description: '活动开始时间' }),
    __metadata("design:type", Date)
], Activity.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动结束时间', type: 'datetime' }),
    (0, swagger_1.ApiProperty)({ description: '活动结束时间' }),
    __metadata("design:type", Date)
], Activity.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动封面', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '活动封面', required: false }),
    __metadata("design:type", String)
], Activity.prototype, "coverImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '主办单位' }),
    (0, swagger_1.ApiProperty)({ description: '主办单位' }),
    __metadata("design:type", String)
], Activity.prototype, "organizer", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建人ID' }),
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    __metadata("design:type", Number)
], Activity.prototype, "creatorId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], Activity.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], Activity.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动状态：0-草稿 1-已发布 2-已结束 3-已取消 4-审核中', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '活动状态：0-草稿 1-已发布 2-已结束 3-已取消 4-审核中', default: 0 }),
    __metadata("design:type", Number)
], Activity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动类型：1-作品活动 2-图片活动 3-其他', default: 1 }),
    (0, swagger_1.ApiProperty)({ description: '活动类型：1-作品活动 2-图片活动 3-其他', default: 1 }),
    __metadata("design:type", Number)
], Activity.prototype, "activityType", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '是否删除', default: false }),
    (0, swagger_1.ApiProperty)({ description: '是否删除', default: false }),
    __metadata("design:type", Boolean)
], Activity.prototype, "isDelete", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '审核原因', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '审核原因', required: false }),
    __metadata("design:type", String)
], Activity.prototype, "reviewReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动附件文件URL列表，多个文件用逗号分隔', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '活动附件文件URL列表', required: false }),
    __metadata("design:type", String)
], Activity.prototype, "attachmentFiles", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '活动宣传图片URL', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '活动宣传图片URL', required: false }),
    __metadata("design:type", String)
], Activity.prototype, "promotionImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '参赛组别列表，多个组别用逗号分隔', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '参赛组别列表，多个组别用逗号分隔', required: false }),
    __metadata("design:type", String)
], Activity.prototype, "competitionGroups", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '报名表信息，格式：报名表URL,示例图URL', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '报名表信息，格式：报名表URL,示例图URL', required: false }),
    __metadata("design:type", String)
], Activity.prototype, "registrationForm", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => activity_tag_entity_1.ActivityTag, activityTag => activityTag.activity),
    __metadata("design:type", Array)
], Activity.prototype, "activityTags", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => activity_work_entity_1.ActivityWork, activityWork => activityWork.activity),
    __metadata("design:type", Array)
], Activity.prototype, "activityWorks", void 0);
exports.Activity = Activity = __decorate([
    (0, typeorm_1.Entity)('activity')
], Activity);
//# sourceMappingURL=activity.entity.js.map