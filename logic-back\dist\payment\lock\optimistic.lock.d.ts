import { Connection, EntityManager, ObjectLiteral } from 'typeorm';
export declare class OptimisticLock {
    private readonly connection;
    private readonly logger;
    constructor(connection: Connection);
    updateWithOptimisticLock<Entity extends ObjectLiteral>(entityClass: {
        new (): Entity;
    } & any, id: any, updateFn: (entity: Entity) => Promise<Entity> | Entity, maxRetries?: number): Promise<Entity>;
    withOptimisticLock<T>(callback: (manager: EntityManager) => Promise<T>): Promise<T>;
    bulkUpdateWithOptimisticLock<Entity extends ObjectLiteral>(entities: Entity[], entityClass: {
        new (): Entity;
    } & any): Promise<Entity[]>;
    createVersionedEntity<Entity extends ObjectLiteral>(entityClass: {
        new (): Entity;
    } & any, data: Partial<Entity>): Promise<Entity>;
    checkVersionConflict<Entity extends ObjectLiteral>(entityClass: {
        new (): Entity;
    } & any, id: any, version: number): Promise<boolean>;
}
