import { ActivityTag } from '../../activity_tag/entities/activity_tag.entity';
import { ActivityWork } from '../../activity_work/entities/activity_work.entity';
export declare class Activity {
    id: number;
    name: string;
    startTime: Date;
    endTime: Date;
    coverImage: string;
    organizer: string;
    creatorId: number;
    createTime: Date;
    updateTime: Date;
    status: number;
    activityType: number;
    isDelete: boolean;
    reviewReason: string;
    attachmentFiles: string;
    promotionImage: string;
    competitionGroups: string;
    registrationForm: string;
    activityTags: ActivityTag[];
    activityWorks: ActivityWork[];
}
