"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var NotifyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotifyService = exports.NotificationChannel = exports.NotificationType = void 0;
const common_1 = require("@nestjs/common");
const payment_record_service_1 = require("../../util/database/mysql/payment_record/payment-record.service");
const payment_service_1 = require("../services/payment.service");
const template_service_1 = require("./template.service");
const record_helper_service_1 = require("../services/record-helper.service");
const notification_record_service_1 = require("../../util/database/mysql/notification_record/notification-record.service");
const notification_record_dto_1 = require("../../util/database/mysql/notification_record/dto/notification-record.dto");
const payment_logger_service_1 = require("../services/payment-logger.service");
const payment_log_dto_1 = require("../../util/database/mysql/payment_log/dto/payment-log.dto");
const notification_record_dto_2 = require("../../util/database/mysql/notification_record/dto/notification-record.dto");
const payment_order_service_1 = require("../../util/database/mysql/payment_order/payment-order.service");
const web_socket_service_1 = require("../../util/web_socket/web_socket.service");
var NotificationType;
(function (NotificationType) {
    NotificationType["PAYMENT_SUCCESS"] = "payment_success";
    NotificationType["PAYMENT_FAIL"] = "payment_fail";
    NotificationType["REFUND_SUCCESS"] = "refund_success";
    NotificationType["REFUND_FAIL"] = "refund_fail";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["EMAIL"] = "email";
    NotificationChannel["SMS"] = "sms";
    NotificationChannel["WEBHOOK"] = "webhook";
    NotificationChannel["INTERNAL"] = "internal";
})(NotificationChannel || (exports.NotificationChannel = NotificationChannel = {}));
let NotifyService = NotifyService_1 = class NotifyService {
    paymentRecordService;
    paymentService;
    templateService;
    recordHelper;
    notificationRecordService;
    paymentLogger;
    paymentOrderService;
    webSocketService;
    logger = new common_1.Logger(NotifyService_1.name);
    constructor(paymentRecordService, paymentService, templateService, recordHelper, notificationRecordService, paymentLogger, paymentOrderService, webSocketService) {
        this.paymentRecordService = paymentRecordService;
        this.paymentService = paymentService;
        this.templateService = templateService;
        this.recordHelper = recordHelper;
        this.notificationRecordService = notificationRecordService;
        this.paymentLogger = paymentLogger;
        this.paymentOrderService = paymentOrderService;
        this.webSocketService = webSocketService;
    }
    async handlePaymentSuccess(outTradeNo, paymentId, amount, channel, extraData) {
        try {
            this.logger.log(`处理支付成功通知: ${outTradeNo}, 金额: ${amount}, 渠道: ${channel}`);
            const updateResult = await this.recordHelper.createOrUpdatePaymentRecord({
                tradeNo: outTradeNo,
                paymentId,
                amount,
                status: 'success',
                paymentTime: new Date(),
                payMethod: channel,
                userId: extraData?.userId || '',
                description: extraData?.subject || `${channel}支付`,
                remark: '支付成功',
                extraData,
            });
            if (!updateResult) {
                this.logger.error(`更新支付记录状态失败: ${outTradeNo}`);
                return false;
            }
            const paymentRecords = await this.paymentRecordService.findByOrderNo(outTradeNo);
            if (!paymentRecords || paymentRecords.length === 0) {
                this.logger.error(`未找到支付记录: ${outTradeNo}`);
                return false;
            }
            const paymentRecord = paymentRecords[0];
            await this.sendNotification(NotificationType.PAYMENT_SUCCESS, {
                outTradeNo,
                paymentId,
                amount,
                channel,
                subject: paymentRecord.orderId,
                createTime: paymentRecord.createdAt,
                payTime: new Date(),
                userId: paymentRecord.userId,
                businessType: extraData?.businessType,
                ...extraData,
            }, [NotificationChannel.INTERNAL, NotificationChannel.WEBHOOK]);
            this.logger.log(`支付成功通知处理完成: ${outTradeNo}`);
            return true;
        }
        catch (error) {
            this.logger.error(`处理支付成功通知出错: ${error.message}`, error.stack);
            return false;
        }
    }
    async handlePaymentFail(outTradeNo, reason, channel, extraData) {
        try {
            this.logger.log(`处理支付失败通知: ${outTradeNo}, 原因: ${reason}, 渠道: ${channel}`);
            const updateResult = await this.recordHelper.createOrUpdatePaymentRecord({
                tradeNo: outTradeNo,
                paymentId: extraData?.paymentId || '',
                amount: extraData?.amount || 0,
                status: 'failed',
                paymentTime: new Date(),
                payMethod: channel,
                userId: extraData?.userId || '',
                description: extraData?.subject || `${channel}支付失败`,
                remark: reason,
                extraData: { failReason: reason, ...extraData },
            });
            if (!updateResult) {
                this.logger.error(`更新支付记录状态失败: ${outTradeNo}`);
                return false;
            }
            const paymentRecords = await this.paymentRecordService.findByOrderNo(outTradeNo);
            if (!paymentRecords || paymentRecords.length === 0) {
                this.logger.error(`未找到支付记录: ${outTradeNo}`);
                return false;
            }
            const paymentRecord = paymentRecords[0];
            await this.sendNotification(NotificationType.PAYMENT_FAIL, {
                outTradeNo,
                reason,
                channel,
                subject: paymentRecord.orderId,
                amount: paymentRecord.amount,
                createTime: paymentRecord.createdAt,
                failTime: new Date(),
                userId: paymentRecord.userId,
                businessType: extraData?.businessType,
                ...extraData,
            }, [NotificationChannel.INTERNAL]);
            this.logger.log(`支付失败通知处理完成: ${outTradeNo}`);
            return true;
        }
        catch (error) {
            this.logger.error(`处理支付失败通知出错: ${error.message}`, error.stack);
            return false;
        }
    }
    async handleRefundSuccess(refundNo, refundId, amount, channel, extraData) {
        try {
            this.logger.log(`处理退款成功通知: ${refundNo}, 金额: ${amount}, 渠道: ${channel}`);
            const updateResult = await this.recordHelper.createOrUpdatePaymentRecord({
                tradeNo: extraData?.businessOrderId || '',
                paymentId: refundId,
                amount,
                status: 'refund',
                paymentTime: new Date(),
                payMethod: channel,
                userId: extraData?.userId || '',
                description: `退款 - ${channel}`,
                remark: `退款成功 - 退款单号: ${refundNo}`,
                extraData: {
                    refundNo,
                    refundId,
                    ...extraData
                },
            });
            if (!updateResult) {
                this.logger.error(`更新退款记录状态失败: ${refundNo}`);
                return false;
            }
            await this.sendNotification(NotificationType.REFUND_SUCCESS, {
                refundNo,
                refundId,
                amount,
                channel,
                businessOrderId: extraData?.businessOrderId,
                createTime: new Date(),
                refundTime: new Date(),
                userId: extraData?.userId,
                operatorId: extraData?.operatorId,
                ...extraData,
            }, [NotificationChannel.INTERNAL, NotificationChannel.WEBHOOK]);
            this.logger.log(`退款成功通知处理完成: ${refundNo}`);
            return true;
        }
        catch (error) {
            this.logger.error(`处理退款成功通知出错: ${error.message}`, error.stack);
            return false;
        }
    }
    async handleRefundFail(refundNo, reason, channel, extraData) {
        try {
            this.logger.log(`处理退款失败通知: ${refundNo}, 原因: ${reason}, 渠道: ${channel}`);
            const updateResult = await this.recordHelper.createOrUpdatePaymentRecord({
                tradeNo: extraData?.businessOrderId || '',
                paymentId: extraData?.refundId || '',
                amount: extraData?.amount || 0,
                status: 'refund_failed',
                paymentTime: new Date(),
                payMethod: channel,
                userId: extraData?.userId || '',
                description: `退款失败 - ${channel}`,
                remark: reason,
                extraData: {
                    refundNo,
                    failReason: reason,
                    ...extraData
                },
            });
            if (!updateResult) {
                this.logger.error(`更新退款失败记录状态失败: ${refundNo}`);
                return false;
            }
            await this.sendNotification(NotificationType.REFUND_FAIL, {
                refundNo,
                reason,
                channel,
                businessOrderId: extraData?.businessOrderId,
                amount: extraData?.amount,
                createTime: new Date(),
                failTime: new Date(),
                userId: extraData?.userId,
                operatorId: extraData?.operatorId,
                ...extraData,
            }, [NotificationChannel.INTERNAL]);
            this.logger.log(`退款失败通知处理完成: ${refundNo}`);
            return true;
        }
        catch (error) {
            this.logger.error(`处理退款失败通知出错: ${error.message}`, error.stack);
            return false;
        }
    }
    async sendNotification(type, data, channels) {
        try {
            this.logger.debug(`准备发送通知: ${type}, 渠道: ${channels.join(',')}`);
            const content = await this.templateService.renderTemplate(type, data);
            await this.createNotificationRecord(type, data, content);
            const orderNo = data.outTradeNo || '';
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.NOTIFICATION,
                operation: payment_log_dto_1.OperationType.NOTIFY,
                orderNo,
                paymentChannel: data.channel,
                requestData: { type, data },
                status: payment_log_dto_1.LogStatus.SUCCESS,
            });
            for (const channel of channels) {
                try {
                    switch (channel) {
                        case NotificationChannel.EMAIL:
                            this.logger.debug(`发送邮件通知: ${type}`);
                            break;
                        case NotificationChannel.SMS:
                            this.logger.debug(`发送短信通知: ${type}`);
                            break;
                        case NotificationChannel.WEBHOOK:
                            this.logger.debug(`发送Webhook通知: ${type}`);
                            break;
                        case NotificationChannel.INTERNAL:
                            await this.handleInternalNotification(type, data);
                            break;
                        default:
                            this.logger.warn(`未知的通知渠道: ${channel}`);
                    }
                }
                catch (error) {
                    this.logger.error(`通过渠道 ${channel} 发送通知失败: ${error.message}`, error.stack);
                }
            }
            this.logger.debug(`通知发送完成: ${type}`);
        }
        catch (error) {
            this.logger.error(`发送通知失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createNotificationRecord(type, data, content) {
        try {
            const notificationType = this.mapNotificationType(type);
            const targetId = data.outTradeNo || data.outRefundNo || '';
            const userId = data.userId || '';
            let status;
            if (type === NotificationType.PAYMENT_SUCCESS || type === NotificationType.REFUND_SUCCESS) {
                status = notification_record_dto_2.NotificationStatus.SUCCESS;
            }
            else if (type === NotificationType.PAYMENT_FAIL || type === NotificationType.REFUND_FAIL) {
                status = notification_record_dto_2.NotificationStatus.FAILED;
            }
            else {
                status = notification_record_dto_2.NotificationStatus.PENDING;
            }
            const createDto = {
                notificationType,
                targetId,
                userId,
                content,
                status,
            };
            const result = await this.notificationRecordService.create(createDto);
            this.logger.log(`创建通知记录成功: 类型=${type}, 目标ID=${targetId}, 状态=${status}`);
        }
        catch (error) {
            this.logger.error(`创建通知记录失败: ${error.message}`, error.stack);
        }
    }
    mapNotificationType(type) {
        switch (type) {
            case NotificationType.PAYMENT_SUCCESS:
                return notification_record_dto_1.NotificationType.PAYMENT_SUCCESS;
            case NotificationType.PAYMENT_FAIL:
                return notification_record_dto_1.NotificationType.PAYMENT_FAIL;
            case NotificationType.REFUND_SUCCESS:
                return notification_record_dto_1.NotificationType.REFUND_SUCCESS;
            case NotificationType.REFUND_FAIL:
                return notification_record_dto_1.NotificationType.REFUND_FAIL;
            default:
                return 'unknown';
        }
    }
    async handleInternalNotification(type, data) {
        try {
            this.logger.log(`处理内部业务通知: ${type}, 订单号: ${data.outTradeNo}`);
            if (type === NotificationType.PAYMENT_SUCCESS) {
                await this.handlePaymentSuccessBusinessLogic(data);
            }
            else if (type === NotificationType.REFUND_SUCCESS) {
                await this.handleRefundSuccessBusinessLogic(data);
            }
            this.logger.log(`内部业务通知处理完成: ${type}, 订单号: ${data.outTradeNo}`);
        }
        catch (error) {
            this.logger.error(`处理内部业务通知失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handlePaymentSuccessBusinessLogic(data) {
        try {
            const { outTradeNo, paymentId } = data;
            this.logger.log(`处理支付成功业务逻辑: 支付订单号=${outTradeNo}, 支付ID=${paymentId}`);
            const paymentOrder = await this.paymentOrderService.findByBusinessOrderId(outTradeNo);
            if (!paymentOrder) {
                this.logger.error(`未找到支付订单: ${outTradeNo}`);
                return;
            }
            const parameters = paymentOrder.parameters || {};
            if (parameters.packageOrderNo) {
                const packageOrderNo = parameters.packageOrderNo;
                this.logger.log(`处理套餐订单支付成功: 支付订单=${outTradeNo}, 套餐订单=${packageOrderNo}`);
                await this.callPackageOrderCallback(packageOrderNo, paymentId);
            }
            else if (parameters.rechargeOrderNo) {
                const rechargeOrderNo = parameters.rechargeOrderNo;
                this.logger.log(`处理充值订单支付成功: 支付订单=${outTradeNo}, 充值订单=${rechargeOrderNo}`);
            }
            else {
                const description = paymentOrder.description || '';
                if (description.includes('套餐') || description.includes('package')) {
                    this.logger.log(`根据描述判断为套餐订单: ${outTradeNo}`);
                    await this.callPackageOrderCallback(outTradeNo, paymentId);
                }
                else {
                    this.logger.warn(`无法识别的订单类型: ${outTradeNo}, 描述: ${description}`);
                }
            }
        }
        catch (error) {
            this.logger.error(`处理支付成功业务逻辑失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async handleRefundSuccessBusinessLogic(data) {
        try {
            const { businessOrderId } = data;
            this.logger.log(`处理退款成功业务逻辑: ${businessOrderId}`);
        }
        catch (error) {
            this.logger.error(`处理退款成功业务逻辑失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async callPackageOrderCallback(packageOrderNo, paymentId) {
        try {
            const axios = require('axios');
            const callbackUrl = 'http://localhost:8003/api/v1/package-order/payment-callback';
            const callbackData = {
                orderNo: packageOrderNo,
                paymentId
            };
            this.logger.log(`调用套餐订单回调: ${callbackUrl}`, callbackData);
            const response = await axios.post(callbackUrl, callbackData, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            if (response.data && response.data.code === 200) {
                this.logger.log(`套餐订单回调成功: 套餐订单=${packageOrderNo}, 支付ID=${paymentId}`);
                await this.sendPackagePurchaseSuccessNotification(packageOrderNo, paymentId);
            }
            else {
                this.logger.error(`套餐订单回调失败: 套餐订单=${packageOrderNo}, 响应: ${JSON.stringify(response.data)}`);
            }
        }
        catch (error) {
            this.logger.error(`调用套餐订单回调异常: 套餐订单=${packageOrderNo}, 错误: ${error.message}`, error.stack);
            throw error;
        }
    }
    async sendPackagePurchaseSuccessNotification(packageOrderNo, paymentId) {
        try {
            const axios = require('axios');
            const orderDetailUrl = `http://localhost:8003/api/v1/package-order/order-status/${packageOrderNo}`;
            const orderResponse = await axios.get(orderDetailUrl, {
                timeout: 5000
            });
            if (orderResponse.data && orderResponse.data.code === 200) {
                const orderData = orderResponse.data.data.data;
                let userId = orderData.userId;
                if (!userId) {
                    this.logger.warn(`套餐订单中没有用户ID，尝试其他方式获取: 套餐订单=${packageOrderNo}`);
                    this.logger.warn(`无法获取用户ID，跳过WebSocket通知: 套餐订单=${packageOrderNo}`);
                    return;
                }
                const notificationData = {
                    type: 'package_purchase_success',
                    title: '套餐购买成功',
                    message: `恭喜您成功购买 ${orderData.packageName}！`,
                    data: {
                        orderNo: packageOrderNo,
                        packageName: orderData.packageName,
                        points: orderData.points,
                        amount: orderData.amount,
                        paidTime: orderData.paidTime,
                    },
                    timestamp: new Date().toISOString()
                };
                const success = await this.webSocketService.sendMessage('payment_success_notification', notificationData, { userId: userId.toString() });
                if (success) {
                    this.logger.log(`WebSocket通知发送成功: 用户=${userId}, 套餐订单=${packageOrderNo}`);
                }
                else {
                    this.logger.warn(`WebSocket通知发送失败: 用户=${userId}, 套餐订单=${packageOrderNo}`);
                }
            }
            else {
                this.logger.warn(`无法获取套餐订单详情，跳过WebSocket通知: 套餐订单=${packageOrderNo}`);
            }
        }
        catch (error) {
            this.logger.error(`发送WebSocket通知异常: ${error.message}`, error.stack);
        }
    }
};
exports.NotifyService = NotifyService;
exports.NotifyService = NotifyService = NotifyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_record_service_1.PaymentRecordService,
        payment_service_1.PaymentService,
        template_service_1.PaymentTemplateService,
        record_helper_service_1.RecordHelperService,
        notification_record_service_1.NotificationRecordService,
        payment_logger_service_1.PaymentLoggerService,
        payment_order_service_1.PaymentOrderService,
        web_socket_service_1.WebSocketService])
], NotifyService);
//# sourceMappingURL=notify.service.js.map