"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePricingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const package_pricing_service_1 = require("./package-pricing.service");
const dto_1 = require("./dto");
const package_pricing_entity_1 = require("./entities/package-pricing.entity");
let PackagePricingController = class PackagePricingController {
    packagePricingService;
    constructor(packagePricingService) {
        this.packagePricingService = packagePricingService;
    }
    async create(createPackagePricingDto) {
        return await this.packagePricingService.create(createPackagePricingDto);
    }
    async findAll() {
        return await this.packagePricingService.findAll();
    }
    async getCurrentPricings() {
        return await this.packagePricingService.getAllCurrentPricings();
    }
    async getCurrentPricing(packageId, priceType) {
        return await this.packagePricingService.getCurrentPricing(+packageId, priceType || dto_1.PriceType.STANDARD);
    }
    async getPackagePricings(packageId) {
        return await this.packagePricingService.getPackagePricings(+packageId);
    }
    async findOne(id) {
        return await this.packagePricingService.findOne(+id);
    }
    async update(id, updatePackagePricingDto) {
        return await this.packagePricingService.update(+id, updatePackagePricingDto);
    }
    async remove(id) {
        return await this.packagePricingService.remove(+id);
    }
};
exports.PackagePricingController = PackagePricingController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建套餐价格' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: package_pricing_entity_1.PackagePricing }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreatePackagePricingDto]),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有套餐价格' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [package_pricing_entity_1.PackagePricing] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('current-pricings'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有套餐的当前价格信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    packageId: { type: 'number', description: '套餐ID' },
                    packageName: { type: 'string', description: '套餐名称' },
                    packageDescription: { type: 'string', description: '套餐描述' },
                    points: { type: 'number', description: '点数' },
                    validityDays: { type: 'number', description: '有效期(天)' },
                    originalPrice: { type: 'number', description: '原价' },
                    currentPrice: { type: 'number', description: '现价' },
                    discountRate: { type: 'number', description: '折扣率' },
                    savings: { type: 'number', description: '节省金额' },
                    priceType: { type: 'string', description: '价格类型' },
                    currency: { type: 'string', description: '货币类型' }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "getCurrentPricings", null);
__decorate([
    (0, common_1.Get)('package/:packageId'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定套餐的当前价格' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: '套餐ID' }),
    (0, swagger_1.ApiQuery)({ name: 'priceType', required: false, description: '价格类型', enum: dto_1.PriceType }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: package_pricing_entity_1.PackagePricing }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '价格不存在' }),
    __param(0, (0, common_1.Param)('packageId')),
    __param(1, (0, common_1.Query)('priceType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "getCurrentPricing", null);
__decorate([
    (0, common_1.Get)('package/:packageId/all'),
    (0, swagger_1.ApiOperation)({ summary: '获取指定套餐的所有价格' }),
    (0, swagger_1.ApiParam)({ name: 'packageId', description: '套餐ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: [package_pricing_entity_1.PackagePricing] }),
    __param(0, (0, common_1.Param)('packageId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "getPackagePricings", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取套餐价格' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '价格ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: package_pricing_entity_1.PackagePricing }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '价格不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新套餐价格' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '价格ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: package_pricing_entity_1.PackagePricing }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '价格不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdatePackagePricingDto]),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除套餐价格' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '价格ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '价格不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackagePricingController.prototype, "remove", null);
exports.PackagePricingController = PackagePricingController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/套餐价格(package_pricing)'),
    (0, common_1.Controller)('package-pricing'),
    __metadata("design:paramtypes", [package_pricing_service_1.PackagePricingService])
], PackagePricingController);
//# sourceMappingURL=package-pricing.controller.js.map