"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const refund_service_1 = require("../services/refund.service");
const refund_dto_1 = require("../dto/refund-dto");
let RefundController = class RefundController {
    refundService;
    constructor(refundService) {
        this.refundService = refundService;
    }
    async create(refundRequestDto, req) {
        const userId = req.user?.id || 'anonymous';
        return this.refundService.createRefund(userId, refundRequestDto);
    }
    async queryRefund(refundQueryDto) {
        return this.refundService.queryRefundStatus(refundQueryDto);
    }
    async getRefundDetail(refundNo) {
        return this.refundService.queryRefundStatus({ refundNo });
    }
    async checkRefundStatus(refundNo) {
        return this.refundService.checkRefundStatus(refundNo);
    }
};
exports.RefundController = RefundController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '创建退款申请' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '退款申请已创建', type: refund_dto_1.RefundResultDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refund_dto_1.RefundRequestDto, Object]),
    __metadata("design:returntype", Promise)
], RefundController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('query'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '查询退款状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退款信息', type: refund_dto_1.RefundResultDto }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refund_dto_1.RefundQueryDto]),
    __metadata("design:returntype", Promise)
], RefundController.prototype, "queryRefund", null);
__decorate([
    (0, common_1.Get)(':refundNo'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '根据退款单号查询退款状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退款信息', type: refund_dto_1.RefundResultDto }),
    __param(0, (0, common_1.Param)('refundNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RefundController.prototype, "getRefundDetail", null);
__decorate([
    (0, common_1.Post)(':refundNo/check'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '手动检查退款状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '退款信息', type: refund_dto_1.RefundResultDto }),
    __param(0, (0, common_1.Param)('refundNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RefundController.prototype, "checkRefundStatus", null);
exports.RefundController = RefundController = __decorate([
    (0, swagger_1.ApiTags)('退款管理'),
    (0, common_1.Controller)('v1/payment/refund'),
    __metadata("design:paramtypes", [refund_service_1.RefundService])
], RefundController);
//# sourceMappingURL=refund.controller.js.map