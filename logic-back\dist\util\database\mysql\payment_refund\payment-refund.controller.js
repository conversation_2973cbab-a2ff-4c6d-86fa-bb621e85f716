"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRefundController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_refund_service_1 = require("./payment-refund.service");
const create_payment_refund_dto_1 = require("./dto/create-payment-refund.dto");
const update_payment_refund_dto_1 = require("./dto/update-payment-refund.dto");
let PaymentRefundController = class PaymentRefundController {
    paymentRefundService;
    constructor(paymentRefundService) {
        this.paymentRefundService = paymentRefundService;
    }
    create(createPaymentRefundDto) {
        return this.paymentRefundService.create(createPaymentRefundDto);
    }
    findAll() {
        return this.paymentRefundService.findAll();
    }
    findOne(id) {
        return this.paymentRefundService.findOne(id);
    }
    findByBusinessRefundId(businessRefundId) {
        return this.paymentRefundService.findByBusinessRefundId(businessRefundId);
    }
    findByPaymentOrderId(paymentOrderId) {
        return this.paymentRefundService.findByPaymentOrderId(paymentOrderId);
    }
    findByChannelRefundId(channelRefundId) {
        return this.paymentRefundService.findByChannelRefundId(channelRefundId);
    }
    findByUserId(userId) {
        return this.paymentRefundService.findByUserId(userId);
    }
    findByStatus(status) {
        return this.paymentRefundService.findByStatus(status);
    }
    update(id, updatePaymentRefundDto) {
        return this.paymentRefundService.update(id, updatePaymentRefundDto);
    }
    updateStatus(id, status, result) {
        return this.paymentRefundService.updateStatus(id, status, result);
    }
    updateNotifyData(id, notifyData) {
        return this.paymentRefundService.updateNotifyData(id, notifyData);
    }
    remove(id) {
        return this.paymentRefundService.remove(id);
    }
};
exports.PaymentRefundController = PaymentRefundController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建退款记录' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_refund_dto_1.CreatePaymentRefundDto]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取所有退款记录' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据ID查询退款记录' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据业务退款单号查询退款记录' }),
    (0, common_1.Get)('business-refund-id/:businessRefundId'),
    __param(0, (0, common_1.Param)('businessRefundId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findByBusinessRefundId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据支付订单ID查询退款记录' }),
    (0, common_1.Get)('payment-order/:paymentOrderId'),
    __param(0, (0, common_1.Param)('paymentOrderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findByPaymentOrderId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据渠道退款单号查询退款记录' }),
    (0, common_1.Get)('channel-refund-id/:channelRefundId'),
    __param(0, (0, common_1.Param)('channelRefundId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findByChannelRefundId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID查询退款记录' }),
    (0, common_1.Get)('user/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findByUserId", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '根据退款状态查询退款记录' }),
    (0, common_1.Get)('status/:status'),
    __param(0, (0, common_1.Param)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "findByStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新退款记录信息' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_payment_refund_dto_1.UpdatePaymentRefundDto]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新退款状态' }),
    (0, common_1.Patch)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Body)('result')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "updateStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新通知数据' }),
    (0, common_1.Patch)(':id/notify'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('notifyData')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "updateNotifyData", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除退款记录' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentRefundController.prototype, "remove", null);
exports.PaymentRefundController = PaymentRefundController = __decorate([
    (0, swagger_1.ApiTags)('数据库/mysql/支付退款(payment_refund)'),
    (0, common_1.Controller)('payment-refund'),
    __metadata("design:paramtypes", [payment_refund_service_1.PaymentRefundService])
], PaymentRefundController);
//# sourceMappingURL=payment-refund.controller.js.map