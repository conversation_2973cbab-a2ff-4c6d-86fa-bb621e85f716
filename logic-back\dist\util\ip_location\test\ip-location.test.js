"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const typeorm_1 = require("@nestjs/typeorm");
const ip_location_util_1 = require("../utils/ip-location.util");
const risk_assessment_util_1 = require("../utils/risk-assessment.util");
const ip_location_application_service_1 = require("../application/services/ip-location-application.service");
const user_common_location_entity_1 = require("../domain/entities/user-common-location.entity");
const redis_service_1 = require("../../database/redis/redis.service");
const logger_service_1 = require("../../../common/logger/logger.service");
const mockRedisService = {
    get: jest.fn(),
    set: jest.fn(),
};
const mockLoggerService = {
    logBusiness: jest.fn(),
};
const mockUserCommonLocationRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
};
describe('IpLocationUtil', () => {
    let service;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ip_location_util_1.IpLocationUtil,
                {
                    provide: redis_service_1.RedisService,
                    useValue: mockRedisService,
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
            ],
        }).compile();
        service = module.get(ip_location_util_1.IpLocationUtil);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('getLocationByIP', () => {
        it('应该正确解析有效的IPv4地址', async () => {
            mockRedisService.get.mockResolvedValue(null);
            mockRedisService.set.mockResolvedValue('OK');
            const testIp = '*******';
            const result = await service.getLocationByIP(testIp);
            expect(result).toBeDefined();
            expect(result.dataSource).toBe('ip2region');
            expect(result.displayName).toBeDefined();
            expect(mockRedisService.set).toHaveBeenCalled();
        });
        it('应该从缓存中返回结果', async () => {
            const cachedResult = {
                country: '中国',
                province: '北京市',
                city: '北京市',
                isp: '联通',
                dataSource: 'ip2region',
                confidence: 95,
                displayName: '中国 北京市 北京市'
            };
            mockRedisService.get.mockResolvedValue(JSON.stringify(cachedResult));
            const testIp = '**************';
            const result = await service.getLocationByIP(testIp);
            expect(result).toEqual(cachedResult);
            expect(mockRedisService.get).toHaveBeenCalledWith('ip_location:**************');
        });
        it('应该拒绝无效的IP地址', async () => {
            const invalidIps = [
                'invalid-ip',
                '999.999.999.999',
                '192.168.1',
                'not-an-ip'
            ];
            for (const invalidIp of invalidIps) {
                await expect(service.getLocationByIP(invalidIp))
                    .rejects.toThrow('无效的IP地址格式');
            }
        });
        it('应该正确处理IPv6地址', async () => {
            mockRedisService.get.mockResolvedValue(null);
            mockRedisService.set.mockResolvedValue('OK');
            const ipv6 = '2001:db8::1';
            const result = await service.getLocationByIP(ipv6);
            expect(result).toBeDefined();
            expect(result.dataSource).toBe('ip2region');
        });
    });
    describe('maskIP', () => {
        it('应该正确脱敏IPv4地址', () => {
            expect(ip_location_util_1.IpLocationUtil.maskIP('*************')).toBe('192.168.1.***');
            expect(ip_location_util_1.IpLocationUtil.maskIP('*******')).toBe('8.8.8.***');
        });
        it('应该正确脱敏IPv6地址', () => {
            expect(ip_location_util_1.IpLocationUtil.maskIP('2001:db8:85a3:8d3:1319:8a2e:370:7344'))
                .toBe('2001:db8:85a3:8d3:****');
            expect(ip_location_util_1.IpLocationUtil.maskIP('::1')).toBe('****:****:****:****');
        });
    });
});
describe('RiskAssessmentUtil', () => {
    let service;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                risk_assessment_util_1.RiskAssessmentUtil,
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
            ],
        }).compile();
        service = module.get(risk_assessment_util_1.RiskAssessmentUtil);
    });
    describe('assessLoginRisk', () => {
        it('应该对常用登录地返回低风险', async () => {
            const currentLocation = {
                country: '中国',
                province: '广东省',
                city: '深圳市',
                isp: '电信'
            };
            const userHistory = {
                commonLocations: [{
                        province: '广东省',
                        city: '深圳市',
                        loginCount: 10,
                        isTrusted: true,
                        lastLoginAt: new Date()
                    }],
                riskLoginCount: 0,
                totalLoginCount: 10
            };
            const result = await service.assessLoginRisk(123, currentLocation, userHistory);
            expect(result.level).toBe('LOW');
            expect(result.needVerification).toBe(false);
        });
        it('应该对境外登录返回高风险', async () => {
            const currentLocation = {
                country: '美国',
                province: '加利福尼亚州',
                city: '旧金山',
                isp: 'Google'
            };
            const userHistory = {
                commonLocations: [{
                        province: '广东省',
                        city: '深圳市',
                        loginCount: 10,
                        isTrusted: true,
                        lastLoginAt: new Date()
                    }],
                riskLoginCount: 0,
                totalLoginCount: 10
            };
            const result = await service.assessLoginRisk(123, currentLocation, userHistory);
            expect(result.level).toBe('HIGH');
            expect(result.needVerification).toBe(true);
            expect(result.factors).toContain('境外登录');
        });
        it('应该对跨省登录返回中等风险', async () => {
            const currentLocation = {
                country: '中国',
                province: '上海市',
                city: '上海市',
                isp: '电信'
            };
            const userHistory = {
                commonLocations: [{
                        province: '广东省',
                        city: '深圳市',
                        loginCount: 10,
                        isTrusted: true,
                        lastLoginAt: new Date()
                    }],
                riskLoginCount: 0,
                totalLoginCount: 10
            };
            const result = await service.assessLoginRisk(123, currentLocation, userHistory);
            expect(result.level).toBe('MEDIUM');
            expect(result.factors).toContain('跨省登录至上海市');
        });
    });
});
describe('IpLocationApplicationService', () => {
    let service;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ip_location_application_service_1.IpLocationApplicationService,
                {
                    provide: ip_location_util_1.IpLocationUtil,
                    useValue: {
                        getLocationByIP: jest.fn(),
                        getBasicLocationByIP: jest.fn(),
                    },
                },
                {
                    provide: risk_assessment_util_1.RiskAssessmentUtil,
                    useValue: {
                        assessLoginRisk: jest.fn(),
                        getRecommendedVerificationMethods: jest.fn(),
                    },
                },
                {
                    provide: logger_service_1.LoggerService,
                    useValue: mockLoggerService,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(user_common_location_entity_1.UserCommonLocation),
                    useValue: mockUserCommonLocationRepository,
                },
            ],
        }).compile();
        service = module.get(ip_location_application_service_1.IpLocationApplicationService);
    });
    describe('queryIpLocation', () => {
        it('应该返回格式化的位置信息', async () => {
            const mockLocation = {
                country: '中国',
                province: '北京市',
                city: '北京市',
                isp: '联通',
                dataSource: 'ip2region',
                confidence: 95,
                displayName: '中国 北京市 北京市'
            };
            const ipLocationUtil = service['ipLocationUtil'];
            ipLocationUtil.getLocationByIP.mockResolvedValue(mockLocation);
            const request = { ip: '**************', includeRisk: false };
            const result = await service.queryIpLocation(request);
            expect(result.ip).toBe(request.ip);
            expect(result.country).toBe(mockLocation.country);
            expect(result.province).toBe(mockLocation.province);
            expect(result.isHighQuality).toBe(true);
        });
    });
});
//# sourceMappingURL=ip-location.test.js.map