<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地理位置解析功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .config-section {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .config-section h3 {
            color: #007bff;
        }
        #authToken {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
        }
        #authToken:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 IP地理位置解析功能测试</h1>

        <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
            <h4 style="margin-top: 0; color: #0066cc;">📋 使用说明</h4>
            <ol style="margin-bottom: 0; color: #333;">
                <li><strong>配置认证</strong>：在下方输入你的Bearer Token和API地址</li>
                <li><strong>测试连接</strong>：点击"测试连接"确保API服务正常</li>
                <li><strong>功能测试</strong>：使用各个测试区域验证不同功能</li>
                <li><strong>查看结果</strong>：所有API响应会显示在对应的结果区域</li>
            </ol>
            <p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">
                💾 配置信息会自动保存到浏览器，下次打开时无需重新输入
            </p>
        </div>

        <!-- 认证配置 -->
        <div class="test-section config-section">
            <h3>🔐 认证配置</h3>
            <div class="form-group">
                <label for="authToken">认证Token:</label>
                <input type="text" id="authToken" placeholder="请输入Bearer Token" style="font-family: monospace;">
            </div>
            <div class="form-group">
                <label for="apiBaseUrl">API基础URL:</label>
                <input type="text" id="apiBaseUrl" value="http://localhost:8003/api/v1/ip-location" placeholder="API基础地址">
            </div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="clearConfig()" style="background-color: #dc3545;">清除配置</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                💡 提示：Token和API地址会自动保存到浏览器本地存储
            </div>
        </div>

        <!-- IP地理位置查询测试 -->
        <div class="test-section">
            <h3>1. IP地理位置查询</h3>
            <div class="form-group">
                <label for="queryIp">IP地址:</label>
                <input type="text" id="queryIp" placeholder="例如: **************" value="*******">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeRisk"> 包含风险评估
                </label>
            </div>
            <button onclick="testIpQuery()">查询IP位置</button>
            <button onclick="testCurrentIp()">查询当前IP</button>
            <button onclick="testRealPublicIp()" style="background-color: #28a745;">获取真实公网IP</button>
            <button onclick="testCurrentIpWithMock()" style="background-color: #6c757d;">随机IP测试</button>
            <div id="queryResult" class="result" style="display: none;"></div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                💡 <strong>说明</strong>：
                <br>• <strong>查询当前IP</strong>：获取服务器检测到的客户端IP（本地测试通常是127.0.0.1）
                <br>• <strong>获取真实公网IP</strong>：通过第三方服务获取你的真实公网IP并解析位置
                <br>• <strong>随机IP测试</strong>：使用知名网站IP测试解析效果
            </div>
        </div>

        <!-- 登录风险检查测试 -->
        <div class="test-section">
            <h3>2. 登录风险检查</h3>
            <div class="form-group">
                <label for="riskUserId">用户ID:</label>
                <input type="number" id="riskUserId" placeholder="例如: 12345" value="12345">
            </div>
            <div class="form-group">
                <label for="riskIp">IP地址:</label>
                <input type="text" id="riskIp" placeholder="例如: **************" value="**************">
            </div>
            <div class="form-group">
                <label for="userAgent">用户代理 (可选):</label>
                <input type="text" id="userAgent" placeholder="浏览器信息" value="">
            </div>
            <button onclick="testRiskCheck()">检查登录风险</button>
            <div id="riskResult" class="result" style="display: none;"></div>
        </div>

        <!-- 用户位置统计测试 -->
        <div class="test-section">
            <h3>3. 用户位置统计</h3>
            <div class="form-group">
                <label for="statsUserId">用户ID:</label>
                <input type="number" id="statsUserId" placeholder="例如: 12345" value="12345">
            </div>
            <div class="form-group">
                <label for="statsDays">统计天数:</label>
                <input type="number" id="statsDays" placeholder="例如: 30" value="30">
            </div>
            <button onclick="testUserStats()">获取用户统计</button>
            <div id="statsResult" class="result" style="display: none;"></div>
        </div>

        <!-- 设置可信位置测试 -->
        <div class="test-section">
            <h3>4. 设置可信位置</h3>
            <div class="form-group">
                <label for="trustUserId">用户ID:</label>
                <input type="number" id="trustUserId" placeholder="例如: 12345" value="12345">
            </div>
            <div class="form-group">
                <label for="trustProvince">省份:</label>
                <input type="text" id="trustProvince" placeholder="例如: 广东省" value="广东省">
            </div>
            <div class="form-group">
                <label for="trustCity">城市:</label>
                <input type="text" id="trustCity" placeholder="例如: 深圳市" value="深圳市">
            </div>
            <div class="form-group">
                <label for="trustReason">设置原因 (可选):</label>
                <input type="text" id="trustReason" placeholder="例如: 用户主动设置" value="测试设置">
            </div>
            <button onclick="testSetTrustedLocation()">设置可信位置</button>
            <div id="trustResult" class="result" style="display: none;"></div>
        </div>

        <!-- 健康检查 -->
        <div class="test-section">
            <h3>5. 服务健康检查</h3>
            <button onclick="testHealthCheck()">检查服务状态</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 获取API配置
        function getApiConfig() {
            const token = document.getElementById('authToken').value.trim();
            const baseUrl = document.getElementById('apiBaseUrl').value.trim();
            return { token, baseUrl };
        }

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            try {
                const { token } = getApiConfig();
                const headers = {
                    'Content-Type': 'application/json',
                };

                // 如果有token，添加Authorization头
                if (token) {
                    headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
                }

                const response = await fetch(url, {
                    headers,
                    ...options
                });

                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, isLoading = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';

            if (isLoading) {
                element.className = 'result loading';
                element.textContent = '请求中...';
                return;
            }

            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误 (${result.status || 'N/A'}): ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        // 测试连接
        async function testConnection() {
            const { baseUrl } = getApiConfig();

            if (!baseUrl) {
                alert('请输入API基础URL');
                return;
            }

            showResult('connectionResult', null, true);

            const result = await makeRequest(`${baseUrl}/health`);
            showResult('connectionResult', result);
        }

        // 1. IP地理位置查询
        async function testIpQuery() {
            const { baseUrl } = getApiConfig();
            const ip = document.getElementById('queryIp').value;
            const includeRisk = document.getElementById('includeRisk').checked;

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            if (!ip) {
                alert('请输入IP地址');
                return;
            }

            showResult('queryResult', null, true);

            const url = `${baseUrl}/query?ip=${encodeURIComponent(ip)}&includeRisk=${includeRisk}`;
            const result = await makeRequest(url);
            showResult('queryResult', result);
        }

        // 查询当前IP
        async function testCurrentIp() {
            const { baseUrl } = getApiConfig();

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            showResult('queryResult', null, true);

            const result = await makeRequest(`${baseUrl}/current`);
            showResult('queryResult', result);
        }

        // 获取真实公网IP并测试
        async function testRealPublicIp() {
            const { baseUrl } = getApiConfig();

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            showResult('queryResult', null, true);

            try {
                // 获取真实公网IP
                const publicIP = await getRealPublicIP();

                if (!publicIP) {
                    showResult('queryResult', {
                        success: false,
                        error: '无法获取公网IP地址，请检查网络连接'
                    });
                    return;
                }

                // 使用真实公网IP进行查询
                const url = `${baseUrl}/query?ip=${encodeURIComponent(publicIP)}&includeRisk=false`;
                const result = await makeRequest(url);

                // 在结果中标注这是真实公网IP测试
                if (result.success) {
                    result.data._realPublicIP = true;
                    result.data._detectedIP = publicIP;
                    result.data._note = `这是你的真实公网IP: ${publicIP}`;
                }

                showResult('queryResult', result);

            } catch (error) {
                showResult('queryResult', {
                    success: false,
                    error: `获取公网IP失败: ${error.message}`
                });
            }
        }

        // 获取真实公网IP地址
        async function getRealPublicIP() {
            // 多个公网IP查询服务，提高成功率
            const ipServices = [
                'https://api.ipify.org?format=json',
                'https://ipapi.co/json/',
                'https://httpbin.org/ip',
                'https://api.ip.sb/ip',
                'https://ifconfig.me/ip',
                'https://icanhazip.com',
                'https://ident.me',
                'https://v4.ident.me'
            ];

            for (const service of ipServices) {
                try {
                    console.log(`🔍 尝试获取公网IP: ${service}`);

                    const response = await fetch(service, {
                        method: 'GET',
                        timeout: 5000 // 5秒超时
                    });

                    if (!response.ok) continue;

                    let data;
                    const contentType = response.headers.get('content-type');

                    if (contentType && contentType.includes('application/json')) {
                        data = await response.json();
                        // 处理不同的JSON格式
                        const ip = data.ip || data.origin || data.query;
                        if (ip && isValidIPAddress(ip)) {
                            console.log(`✅ 成功获取公网IP: ${ip} (来源: ${service})`);
                            return ip.trim();
                        }
                    } else {
                        // 纯文本响应
                        const text = await response.text();
                        const ip = text.trim();
                        if (isValidIPAddress(ip)) {
                            console.log(`✅ 成功获取公网IP: ${ip} (来源: ${service})`);
                            return ip;
                        }
                    }
                } catch (error) {
                    console.log(`❌ 获取公网IP失败: ${service} - ${error.message}`);
                    continue;
                }
            }

            console.log('❌ 所有公网IP服务都无法访问');
            return null;
        }

        // 验证IP地址格式
        function isValidIPAddress(ip) {
            // IPv4格式验证
            const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
            // IPv6格式验证（简化版）
            const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

            return ipv4Regex.test(ip) || ipv6Regex.test(ip);
        }

        // 模拟随机IP测试（重命名原函数）
        async function testCurrentIpWithMock() {
            const { baseUrl } = getApiConfig();

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            // 随机选择一个知名网站IP进行测试
            const mockIPs = [
                '**************',  // 百度 - 北京
                '*************',   // 腾讯 - 广州
                '***************', // 百度 - 北京
                '**************',  // 百度 - 北京
                '*********',       // 阿里 - 杭州
                '***************', // 114DNS - 南京
                '*******',         // Google - 美国
                '*******'          // Cloudflare - 澳大利亚
            ];

            const randomIP = mockIPs[Math.floor(Math.random() * mockIPs.length)];

            showResult('queryResult', null, true);

            const url = `${baseUrl}/query?ip=${encodeURIComponent(randomIP)}&includeRisk=false`;
            const result = await makeRequest(url);

            // 在结果中标注这是模拟测试
            if (result.success) {
                result.data._mockTest = true;
                result.data._mockIP = randomIP;
                result.data._note = `随机IP测试，使用知名网站IP: ${randomIP}`;
            }

            showResult('queryResult', result);
        }

        // 2. 登录风险检查
        async function testRiskCheck() {
            const { baseUrl } = getApiConfig();
            const userId = document.getElementById('riskUserId').value;
            const ipAddress = document.getElementById('riskIp').value;
            const userAgent = document.getElementById('userAgent').value;

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            if (!userId || !ipAddress) {
                alert('请输入用户ID和IP地址');
                return;
            }

            showResult('riskResult', null, true);

            const requestBody = {
                userId: parseInt(userId),
                ipAddress,
                userAgent: userAgent || navigator.userAgent
            };

            const result = await makeRequest(`${baseUrl}/check-risk`, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });
            showResult('riskResult', result);
        }

        // 3. 用户位置统计
        async function testUserStats() {
            const { baseUrl } = getApiConfig();
            const userId = document.getElementById('statsUserId').value;
            const days = document.getElementById('statsDays').value;

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            showResult('statsResult', null, true);

            const url = `${baseUrl}/user/${userId}/stats?days=${days || 30}`;
            const result = await makeRequest(url);
            showResult('statsResult', result);
        }

        // 4. 设置可信位置
        async function testSetTrustedLocation() {
            const { baseUrl } = getApiConfig();
            const userId = document.getElementById('trustUserId').value;
            const province = document.getElementById('trustProvince').value;
            const city = document.getElementById('trustCity').value;
            const reason = document.getElementById('trustReason').value;

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            if (!userId || !province || !city) {
                alert('请输入用户ID、省份和城市');
                return;
            }

            showResult('trustResult', null, true);

            const requestBody = {
                province,
                city,
                reason: reason || '测试设置'
            };

            const result = await makeRequest(`${baseUrl}/user/${userId}/trust`, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });
            showResult('trustResult', result);
        }

        // 5. 健康检查
        async function testHealthCheck() {
            const { baseUrl } = getApiConfig();

            if (!baseUrl) {
                alert('请先配置API基础URL');
                return;
            }

            showResult('healthResult', null, true);

            const result = await makeRequest(`${baseUrl}/health`);
            showResult('healthResult', result);
        }

        // 保存配置到本地存储
        function saveConfig() {
            const token = document.getElementById('authToken').value;
            const baseUrl = document.getElementById('apiBaseUrl').value;

            localStorage.setItem('ip_location_test_token', token);
            localStorage.setItem('ip_location_test_base_url', baseUrl);
        }

        // 从本地存储加载配置
        function loadConfig() {
            const savedToken = localStorage.getItem('ip_location_test_token');
            const savedBaseUrl = localStorage.getItem('ip_location_test_base_url');

            if (savedToken) {
                document.getElementById('authToken').value = savedToken;
            }

            if (savedBaseUrl) {
                document.getElementById('apiBaseUrl').value = savedBaseUrl;
            }
        }

        // 清除配置
        function clearConfig() {
            if (confirm('确定要清除保存的配置吗？')) {
                localStorage.removeItem('ip_location_test_token');
                localStorage.removeItem('ip_location_test_base_url');
                document.getElementById('authToken').value = '';
                document.getElementById('apiBaseUrl').value = 'http://localhost:8003/api/v1/ip-location';
                alert('配置已清除');
            }
        }

        // 页面加载时自动填充数据
        document.addEventListener('DOMContentLoaded', function() {
            // 加载保存的配置
            loadConfig();

            // 填充用户代理
            document.getElementById('userAgent').value = navigator.userAgent;

            // 监听配置变化，自动保存
            document.getElementById('authToken').addEventListener('blur', saveConfig);
            document.getElementById('apiBaseUrl').addEventListener('blur', saveConfig);
        });
    </script>
</body>
</html>
