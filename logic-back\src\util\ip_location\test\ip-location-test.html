<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地理位置解析功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 IP地理位置解析功能测试</h1>
        
        <!-- IP地理位置查询测试 -->
        <div class="test-section">
            <h3>1. IP地理位置查询</h3>
            <div class="form-group">
                <label for="queryIp">IP地址:</label>
                <input type="text" id="queryIp" placeholder="例如: **************" value="*******">
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeRisk"> 包含风险评估
                </label>
            </div>
            <button onclick="testIpQuery()">查询IP位置</button>
            <button onclick="testCurrentIp()">查询当前IP</button>
            <div id="queryResult" class="result" style="display: none;"></div>
        </div>

        <!-- 登录风险检查测试 -->
        <div class="test-section">
            <h3>2. 登录风险检查</h3>
            <div class="form-group">
                <label for="riskUserId">用户ID:</label>
                <input type="number" id="riskUserId" placeholder="例如: 12345" value="12345">
            </div>
            <div class="form-group">
                <label for="riskIp">IP地址:</label>
                <input type="text" id="riskIp" placeholder="例如: **************" value="**************">
            </div>
            <div class="form-group">
                <label for="userAgent">用户代理 (可选):</label>
                <input type="text" id="userAgent" placeholder="浏览器信息" value="">
            </div>
            <button onclick="testRiskCheck()">检查登录风险</button>
            <div id="riskResult" class="result" style="display: none;"></div>
        </div>

        <!-- 用户位置统计测试 -->
        <div class="test-section">
            <h3>3. 用户位置统计</h3>
            <div class="form-group">
                <label for="statsUserId">用户ID:</label>
                <input type="number" id="statsUserId" placeholder="例如: 12345" value="12345">
            </div>
            <div class="form-group">
                <label for="statsDays">统计天数:</label>
                <input type="number" id="statsDays" placeholder="例如: 30" value="30">
            </div>
            <button onclick="testUserStats()">获取用户统计</button>
            <div id="statsResult" class="result" style="display: none;"></div>
        </div>

        <!-- 设置可信位置测试 -->
        <div class="test-section">
            <h3>4. 设置可信位置</h3>
            <div class="form-group">
                <label for="trustUserId">用户ID:</label>
                <input type="number" id="trustUserId" placeholder="例如: 12345" value="12345">
            </div>
            <div class="form-group">
                <label for="trustProvince">省份:</label>
                <input type="text" id="trustProvince" placeholder="例如: 广东省" value="广东省">
            </div>
            <div class="form-group">
                <label for="trustCity">城市:</label>
                <input type="text" id="trustCity" placeholder="例如: 深圳市" value="深圳市">
            </div>
            <div class="form-group">
                <label for="trustReason">设置原因 (可选):</label>
                <input type="text" id="trustReason" placeholder="例如: 用户主动设置" value="测试设置">
            </div>
            <button onclick="testSetTrustedLocation()">设置可信位置</button>
            <div id="trustResult" class="result" style="display: none;"></div>
        </div>

        <!-- 健康检查 -->
        <div class="test-section">
            <h3>5. 服务健康检查</h3>
            <button onclick="testHealthCheck()">检查服务状态</button>
            <div id="healthResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8003/api/v1/ip-location';
        
        // 通用请求函数
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        // 这里应该添加实际的认证token
                        // 'Authorization': 'Bearer your-token-here'
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, isLoading = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (isLoading) {
                element.className = 'result loading';
                element.textContent = '请求中...';
                return;
            }
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误: ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        // 1. IP地理位置查询
        async function testIpQuery() {
            const ip = document.getElementById('queryIp').value;
            const includeRisk = document.getElementById('includeRisk').checked;
            
            if (!ip) {
                alert('请输入IP地址');
                return;
            }
            
            showResult('queryResult', null, true);
            
            const url = `${API_BASE_URL}/query?ip=${encodeURIComponent(ip)}&includeRisk=${includeRisk}`;
            const result = await makeRequest(url);
            showResult('queryResult', result);
        }

        // 查询当前IP
        async function testCurrentIp() {
            showResult('queryResult', null, true);
            
            const result = await makeRequest(`${API_BASE_URL}/current`);
            showResult('queryResult', result);
        }

        // 2. 登录风险检查
        async function testRiskCheck() {
            const userId = document.getElementById('riskUserId').value;
            const ipAddress = document.getElementById('riskIp').value;
            const userAgent = document.getElementById('userAgent').value;
            
            if (!userId || !ipAddress) {
                alert('请输入用户ID和IP地址');
                return;
            }
            
            showResult('riskResult', null, true);
            
            const requestBody = {
                userId: parseInt(userId),
                ipAddress,
                userAgent: userAgent || navigator.userAgent
            };
            
            const result = await makeRequest(`${API_BASE_URL}/check-risk`, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });
            showResult('riskResult', result);
        }

        // 3. 用户位置统计
        async function testUserStats() {
            const userId = document.getElementById('statsUserId').value;
            const days = document.getElementById('statsDays').value;
            
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            showResult('statsResult', null, true);
            
            const url = `${API_BASE_URL}/user/${userId}/stats?days=${days || 30}`;
            const result = await makeRequest(url);
            showResult('statsResult', result);
        }

        // 4. 设置可信位置
        async function testSetTrustedLocation() {
            const userId = document.getElementById('trustUserId').value;
            const province = document.getElementById('trustProvince').value;
            const city = document.getElementById('trustCity').value;
            const reason = document.getElementById('trustReason').value;
            
            if (!userId || !province || !city) {
                alert('请输入用户ID、省份和城市');
                return;
            }
            
            showResult('trustResult', null, true);
            
            const requestBody = {
                province,
                city,
                reason: reason || '测试设置'
            };
            
            const result = await makeRequest(`${API_BASE_URL}/user/${userId}/trust`, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });
            showResult('trustResult', result);
        }

        // 5. 健康检查
        async function testHealthCheck() {
            showResult('healthResult', null, true);
            
            const result = await makeRequest(`${API_BASE_URL}/health`);
            showResult('healthResult', result);
        }

        // 页面加载时自动填充用户代理
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('userAgent').value = navigator.userAgent;
        });
    </script>
</body>
</html>
