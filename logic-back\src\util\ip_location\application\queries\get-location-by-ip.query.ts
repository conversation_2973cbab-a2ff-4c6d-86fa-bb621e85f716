import { IpAddress } from '../../domain/value-objects/ip-address.vo';

/**
 * 获取IP位置查询
 * 封装根据IP地址查询地理位置的读操作
 */
export class GetLocationByIpQuery {
  public readonly ipAddress: IpAddress;
  public readonly includeRisk: boolean;
  public readonly cacheEnabled: boolean;
  public readonly timestamp: Date;

  constructor(
    ipAddress: IpAddress,
    includeRisk: boolean = false,
    cacheEnabled: boolean = true
  ) {
    this.ipAddress = ipAddress;
    this.includeRisk = includeRisk;
    this.cacheEnabled = cacheEnabled;
    this.timestamp = new Date();
  }

  /**
   * 创建查询的静态工厂方法
   */
  static create(
    ipAddressString: string,
    includeRisk: boolean = false,
    cacheEnabled: boolean = true
  ): GetLocationByIpQuery {
    const ipAddress = IpAddress.create(ipAddressString);
    return new GetLocationByIpQuery(ipAddress, includeRisk, cacheEnabled);
  }

  /**
   * 创建包含风险评估的查询
   */
  static createWithRisk(
    ipAddressString: string,
    cacheEnabled: boolean = true
  ): GetLocationByIpQuery {
    return GetLocationByIpQuery.create(ipAddressString, true, cacheEnabled);
  }

  /**
   * 创建不使用缓存的查询
   */
  static createWithoutCache(
    ipAddressString: string,
    includeRisk: boolean = false
  ): GetLocationByIpQuery {
    return GetLocationByIpQuery.create(ipAddressString, includeRisk, false);
  }

  /**
   * 验证查询的有效性
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.ipAddress.canGeolocate) {
      errors.push('IP地址不支持地理位置解析');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取缓存键
   */
  getCacheKey(): string {
    const riskSuffix = this.includeRisk ? '_with_risk' : '';
    return `ip_location:${this.ipAddress.value}${riskSuffix}`;
  }

  /**
   * 获取查询摘要信息
   */
  getSummary(): string {
    const riskInfo = this.includeRisk ? '(包含风险评估)' : '';
    const cacheInfo = this.cacheEnabled ? '' : '(不使用缓存)';
    return `查询IP位置: ${this.ipAddress.masked} ${riskInfo}${cacheInfo}`;
  }

  /**
   * 检查是否需要风险评估
   */
  get needsRiskAssessment(): boolean {
    return this.includeRisk && this.ipAddress.isPublic;
  }

  /**
   * 检查是否可以使用缓存
   */
  get canUseCache(): boolean {
    return this.cacheEnabled && this.ipAddress.isPublic;
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      ipAddress: this.ipAddress.toJSON(),
      includeRisk: this.includeRisk,
      cacheEnabled: this.cacheEnabled,
      timestamp: this.timestamp.toISOString(),
      cacheKey: this.getCacheKey(),
      needsRiskAssessment: this.needsRiskAssessment,
      canUseCache: this.canUseCache
    };
  }
}
