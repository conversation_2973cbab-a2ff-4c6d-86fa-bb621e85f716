import { ZwwService } from './zww.service';
import { CreateZwwDto } from './dto/create-zww.dto';
import { UpdateZwwDto } from './dto/update-zww.dto';
export declare class ZwwController {
    private readonly zwwService;
    constructor(zwwService: ZwwService);
    create(createZwwDto: CreateZwwDto): string;
    findAll(): string;
    findOne(id: string): string;
    update(id: string, updateZwwDto: UpdateZwwDto): string;
    remove(id: string): string;
}
