import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

/**
 * 设置可信位置请求DTO
 */
export class TrustLocationRequestDto {
  @ApiProperty({ description: '省份', example: '广东省' })
  @IsNotEmpty({ message: '省份不能为空' })
  @IsString({ message: '省份必须是字符串' })
  province: string;

  @ApiProperty({ description: '城市', example: '深圳市' })
  @IsNotEmpty({ message: '城市不能为空' })
  @IsString({ message: '城市必须是字符串' })
  city: string;

  @ApiProperty({ 
    description: '设置原因', 
    required: false,
    example: '用户主动设置'
  })
  @IsOptional()
  @IsString({ message: '设置原因必须是字符串' })
  reason?: string;
}
