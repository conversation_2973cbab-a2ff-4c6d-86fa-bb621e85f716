"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTagController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_tag_service_1 = require("./user_tag.service");
const tag_entity_1 = require("../../util/database/mysql/tag/entities/tag.entity");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
let UserTagController = class UserTagController {
    userTagService;
    httpResponseResultService;
    constructor(userTagService, httpResponseResultService) {
        this.userTagService = userTagService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async createTag(data) {
        try {
            const result = await this.userTagService.create(data);
            return this.httpResponseResultService.success(result, '创建成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '创建失败', null, 400);
        }
    }
    async updateTag(id, data) {
        try {
            const result = await this.userTagService.updateTag(id, data);
            return this.httpResponseResultService.success(result, '更新成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '更新失败', null, 400);
        }
    }
    async deleteTag(id) {
        try {
            await this.userTagService.deleteTag(id);
            return this.httpResponseResultService.success(true, '删除成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '删除失败', null, 400);
        }
    }
    async getTagInfo(id) {
        try {
            const result = await this.userTagService.getById(id);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
    async getTagList(query) {
        try {
            const result = await this.userTagService.list(query);
            return this.httpResponseResultService.success(result, '获取成功');
        }
        catch (error) {
            return this.httpResponseResultService.error(error.message || '获取失败', null, 400);
        }
    }
};
exports.UserTagController = UserTagController;
__decorate([
    (0, common_1.Post)('/createTag'),
    (0, swagger_1.ApiOperation)({ summary: '创建标签' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['name'],
            properties: {
                name: { type: 'string', description: '标签名称' },
                description: { type: 'string', description: '标签描述' },
                color: { type: 'string', description: '标签颜色' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: tag_entity_1.Tag }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '标签名称已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserTagController.prototype, "createTag", null);
__decorate([
    (0, common_1.Put)('/updateTag/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新标签' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '标签ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', description: '标签名称' },
                description: { type: 'string', description: '标签描述' },
                color: { type: 'string', description: '标签颜色' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: tag_entity_1.Tag }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '标签不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '标签名称已存在' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], UserTagController.prototype, "updateTag", null);
__decorate([
    (0, common_1.Delete)('/deleteTag/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除标签' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '标签ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '标签不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserTagController.prototype, "deleteTag", null);
__decorate([
    (0, common_1.Get)('/infoTag/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取标签详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '标签ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: tag_entity_1.Tag }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '标签不存在' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserTagController.prototype, "getTagInfo", null);
__decorate([
    (0, common_1.Get)('/listTag'),
    (0, swagger_1.ApiOperation)({ summary: '获取标签列表' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: '页码', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'size', required: false, type: Number, description: '每页数量', example: 10 }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', required: false, type: String, description: '搜索关键词' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserTagController.prototype, "getTagList", null);
exports.UserTagController = UserTagController = __decorate([
    (0, swagger_1.ApiTags)('web/用户标签管理(user_tag)'),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    (0, common_1.Controller)('api/v1/tag'),
    __metadata("design:paramtypes", [user_tag_service_1.UserTagService,
        http_response_result_service_1.HttpResponseResultService])
], UserTagController);
//# sourceMappingURL=user_tag.controller.js.map