"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserLoginLogModule = void 0;
const common_1 = require("@nestjs/common");
const user_login_log_module_1 = require("../../util/database/mysql/user_login_log/user_login_log.module");
const login_logger_initializer_service_1 = require("./login-logger-initializer.service");
let UserLoginLogModule = class UserLoginLogModule {
};
exports.UserLoginLogModule = UserLoginLogModule;
exports.UserLoginLogModule = UserLoginLogModule = __decorate([
    (0, common_1.Module)({
        imports: [
            user_login_log_module_1.UserLoginLogModule
        ],
        providers: [login_logger_initializer_service_1.LoginLoggerInitializerService],
        exports: [login_logger_initializer_service_1.LoginLoggerInitializerService]
    })
], UserLoginLogModule);
//# sourceMappingURL=user-login-log.module.js.map