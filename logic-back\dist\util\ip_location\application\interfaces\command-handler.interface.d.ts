export interface ICommandHandler<TCommand, TResult = void> {
    handle(command: TCommand): Promise<TResult>;
    canHandle(command: TCommand): boolean;
    getHandlerName(): string;
}
export interface CommandResult<TData = any> {
    success: boolean;
    data?: TData;
    message?: string;
    errors?: string[];
    timestamp: Date;
    executionTime?: number;
}
export declare function createSuccessResult<TData>(data?: TData, message?: string, executionTime?: number): CommandResult<TData>;
export declare function createFailureResult(errors: string[], message?: string, executionTime?: number): CommandResult;
