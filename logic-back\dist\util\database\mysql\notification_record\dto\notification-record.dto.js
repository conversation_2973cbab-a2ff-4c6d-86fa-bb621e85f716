"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryNotificationRecordDto = exports.UpdateNotificationRecordDto = exports.CreateNotificationRecordDto = exports.NotificationStatus = exports.NotificationType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var NotificationType;
(function (NotificationType) {
    NotificationType["PAYMENT_SUCCESS"] = "payment_success";
    NotificationType["PAYMENT_FAIL"] = "payment_fail";
    NotificationType["REFUND_SUCCESS"] = "refund_success";
    NotificationType["REFUND_FAIL"] = "refund_fail";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationStatus;
(function (NotificationStatus) {
    NotificationStatus["PENDING"] = "pending";
    NotificationStatus["PROCESSING"] = "processing";
    NotificationStatus["SUCCESS"] = "success";
    NotificationStatus["FAILED"] = "failed";
    NotificationStatus["PROCESSED"] = "processed";
})(NotificationStatus || (exports.NotificationStatus = NotificationStatus = {}));
class CreateNotificationRecordDto {
    notificationType;
    targetId;
    userId;
    content;
    maxRetryCount;
    status;
}
exports.CreateNotificationRecordDto = CreateNotificationRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知类型', enum: NotificationType }),
    (0, class_validator_1.IsEnum)(NotificationType),
    __metadata("design:type", String)
], CreateNotificationRecordDto.prototype, "notificationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '目标ID(订单ID/退款ID)' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationRecordDto.prototype, "targetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationRecordDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知内容', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateNotificationRecordDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最大重试次数', default: 3 }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateNotificationRecordDto.prototype, "maxRetryCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知状态', enum: NotificationStatus, required: false }),
    (0, class_validator_1.IsEnum)(NotificationStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateNotificationRecordDto.prototype, "status", void 0);
class UpdateNotificationRecordDto {
    status;
    content;
    errorMessage;
    retryCount;
    nextRetryTime;
}
exports.UpdateNotificationRecordDto = UpdateNotificationRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知状态', enum: NotificationStatus, required: false }),
    (0, class_validator_1.IsEnum)(NotificationStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateNotificationRecordDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知内容', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateNotificationRecordDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误信息', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateNotificationRecordDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '重试次数', required: false }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateNotificationRecordDto.prototype, "retryCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '下次重试时间', required: false }),
    (0, class_validator_1.IsDate)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], UpdateNotificationRecordDto.prototype, "nextRetryTime", void 0);
class QueryNotificationRecordDto {
    notificationType;
    targetId;
    userId;
    status;
}
exports.QueryNotificationRecordDto = QueryNotificationRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知类型', enum: NotificationType, required: false }),
    (0, class_validator_1.IsEnum)(NotificationType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryNotificationRecordDto.prototype, "notificationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '目标ID(订单ID/退款ID)', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryNotificationRecordDto.prototype, "targetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryNotificationRecordDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '通知状态', enum: NotificationStatus, required: false }),
    (0, class_validator_1.IsEnum)(NotificationStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryNotificationRecordDto.prototype, "status", void 0);
//# sourceMappingURL=notification-record.dto.js.map