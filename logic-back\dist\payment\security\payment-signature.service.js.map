{"version": 3, "file": "payment-signature.service.js", "sourceRoot": "", "sources": ["../../../src/payment/security/payment-signature.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAwE;AACxE,iCAAiC;AACjC,yBAAyB;AACzB,6BAA6B;AAC7B,6EAAwE;AACxE,iFAA4E;AAGrE,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAIf;IAEA;IALF,MAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnE,YACmB,aAAmC,EAEnC,0BAAsD;QAFtD,kBAAa,GAAb,aAAa,CAAsB;QAEnC,+BAA0B,GAA1B,0BAA0B,CAA4B;IACtE,CAAC;IAWJ,0BAA0B,CAAC,MAAc,EAAE,GAAW,EAAE,SAAiB,EAAE,KAAa,EAAE,IAAY;QACpG,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YAI7D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,gCAAgC,GAAG,EAAE,CAAC,CAAC;YAChG,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;YAEtD,MAAM,OAAO,GAAG,GAAG,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC;YAC3E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;YAGzC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrB,IAAI,CAAC,GAAG,EAAE,CAAC;YAEX,MAAM,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;YAC7F,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAWD,wBAAwB,CAAC,SAAiB,EAAE,KAAa,EAAE,IAAqB,EAAE,SAAiB,EAAE,QAAiB;QACpH,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAGvE,MAAM,OAAO,GAAG,GAAG,SAAS,KAAK,KAAK,KAAK,OAAO,IAAI,CAAC;YACvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,OAAO,EAAE,CAAC,CAAC;YAG1C,IAAI,kBAAkB,GAA2B,IAAI,CAAC;YACtD,IAAI,OAAO,GAAkB,IAAI,CAAC;YAElC,IAAI,CAAC;gBAEH,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,QAAQ,YAAY,CAAC,CAAC;oBACpD,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAEnE,IAAI,OAAO,EAAE,CAAC;wBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;wBAChD,kBAAkB,GAAG,OAAO,CAAC;oBAC/B,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,QAAQ,gBAAgB,CAAC,CAAC;oBAC/D,CAAC;gBACH,CAAC;gBAGD,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;oBAE7D,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC;wBACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;wBAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;wBAClD,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;oBACxE,CAAC;oBAED,kBAAkB,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC;oBACvG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC;oBAE7E,IAAI,QAAQ,EAAE,CAAC;wBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;wBAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;oBAC/C,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC7D,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACvB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAE5E,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,SAAS,EAAE,CAAC,CAAC;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,OAAO,EAAE,CAAC,CAAC;oBAErC,IAAI,QAAQ,EAAE,CAAC;wBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,QAAQ,EAAE,CAAC,CAAC;wBAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;oBAC5C,CAAC;oBAED,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAChC,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACjE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAOD,uBAAuB,CAAC,MAA2B;QACjD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;YAG1D,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;YAG9C,MAAM,YAAY,GAAG,UAAU;iBAC5B,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,GAAG,KAAK,MAAM,CAAC;iBACxG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;iBACnC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC;YAGhD,IAAI,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;YACzC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAEvC,UAAU,GAAG,oCAAoC,UAAU,iCAAiC,CAAC;gBAG7F,UAAU,GAAG,UAAU,CAAC,OAAO,CAC7B,uEAAuE,EACvE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;oBACT,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;oBACrD,OAAO,oCAAoC,YAAY,iCAAiC,CAAC;gBAC3F,CAAC,CACF,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACvC,CAAC;YAGD,IAAI,IAAY,CAAC;YACjB,IAAI,CAAC;gBACH,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;oBACrC,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;oBAC/C,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACN,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;oBAC7C,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;gBAEvC,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC9D,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOD,qBAAqB,CAAC,MAA2B;QAC/C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,CAAC;YAG1D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnC,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;YAG9C,MAAM,cAAc,GAAG,UAAU;iBAC9B,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,WAAW,CAAC;iBAC/H,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;iBACnC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,cAAc,EAAE,CAAC,CAAC;YAGlD,IAAI,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAEjD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAEtC,SAAS,GAAG,+BAA+B,SAAS,4BAA4B,CAAC;gBAGjF,SAAS,GAAG,SAAS,CAAC,OAAO,CAC3B,6DAA6D,EAC7D,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;oBACT,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;oBACrD,OAAO,+BAA+B,YAAY,4BAA4B,CAAC;gBACjF,CAAC,CACF,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACvC,CAAC;YAGD,IAAI,CAAC;gBACH,IAAI,QAAQ,CAAC;gBACb,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;oBACrC,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;gBAC/C,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAC7C,CAAC;gBAED,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBACxC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAE1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,CAAC,CAAC;gBAE1C,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAOD,wBAAwB,CAAC,QAAwE;QAC/F,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YAC7D,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,QAAQ,CAAC;YAExD,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAGD,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YACrE,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAErE,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CACtC,aAAa,EACb,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CACnB,CAAC;YAEF,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC7B,IAAI,eAAe,EAAE,CAAC;gBACpB,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAChD,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEvE,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AArTY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,eAAM,EAAC,IAAA,mBAAU,EAAC,GAAG,EAAE,CAAC,yDAA0B,CAAC,CAAC,CAAA;qCADrB,6CAAoB;QAEP,yDAA0B;GAN9D,uBAAuB,CAqTnC"}