import { AttachmentService } from './attachment.service';
import { CreateAttachmentDto } from './dto/create-attachment.dto';
import { UpdateAttachmentDto } from './dto/update-attachment.dto';
export declare class AttachmentController {
    private readonly attachmentService;
    constructor(attachmentService: AttachmentService);
    create(createAttachmentDto: CreateAttachmentDto, req: any): Promise<import("./entities/attachment.entity").Attachment>;
    createMany(createAttachmentDtos: CreateAttachmentDto[], req: any): Promise<import("./entities/attachment.entity").Attachment[]>;
    findAll(): Promise<import("./entities/attachment.entity").Attachment[]>;
    findOne(id: string): Promise<import("./entities/attachment.entity").Attachment>;
    update(id: string, updateAttachmentDto: UpdateAttachmentDto, req: any): Promise<import("./entities/attachment.entity").Attachment>;
    remove(id: string): Promise<void>;
}
