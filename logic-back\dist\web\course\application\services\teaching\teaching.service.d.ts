import { Repository, DataSource } from 'typeorm';
import { CourseTeachingRecord } from '../../../domain/entities/teaching/course-teaching-record.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { CourseSettings } from '../../../domain/entities/management/course-settings.entity';
import { TaskTemplate } from '../../../domain/entities/teaching/task-template.entity';
import { OneClickStartDto, OneClickStartDataDto } from '../../dto/teaching/one-click-start.dto';
import { CourseSettingsDataDto } from '../../dto/teaching/course-settings.dto';
import { GetTeachingRecordsQueryDto, TeachingRecordsDataDto } from '../../dto/teaching/teaching-records.dto';
import { LockManager } from '../../../utils/teaching/lock-manager';
import { TaskSelfAssessmentItem } from 'src/util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity';
import { TeacherTask } from 'src/util/database/mysql/teacher_task/entities/teacher_task.entity';
import { TeacherTaskAssignment } from 'src/util/database/mysql/teacher_task_assignment/entities/teacher_task_assignment.entity';
import { UserClassService } from 'src/util/database/mysql/user_class/user_class.service';
import { UserStudentService } from 'src/util/database/mysql/user_student/user_student.service';
import { WebPointPermissionService } from 'src/web/web_point_permission/web_point_permission.service';
export declare class TeachingService {
    private readonly teachingRecordRepository;
    private readonly courseRepository;
    private readonly courseSeriesRepository;
    private readonly courseSettingsRepository;
    private readonly taskTemplateRepository;
    private readonly teacherTaskRepository;
    private readonly teacherTaskAssignmentRepository;
    private readonly taskSelfAssessmentItemRepository;
    private readonly dataSource;
    private readonly lockManager;
    private readonly baseUserClassService;
    private readonly userStudentService;
    private readonly webPointPermissionService;
    private readonly logger;
    private readonly TEST_ROLLBACK_STEP;
    private readonly Allerror;
    constructor(teachingRecordRepository: Repository<CourseTeachingRecord>, courseRepository: Repository<Course>, courseSeriesRepository: Repository<CourseSeries>, courseSettingsRepository: Repository<CourseSettings>, taskTemplateRepository: Repository<TaskTemplate>, teacherTaskRepository: Repository<TeacherTask>, teacherTaskAssignmentRepository: Repository<TeacherTaskAssignment>, taskSelfAssessmentItemRepository: Repository<TaskSelfAssessmentItem>, dataSource: DataSource, lockManager: LockManager, baseUserClassService: UserClassService, userStudentService: UserStudentService, webPointPermissionService: WebPointPermissionService);
    private getTestRollbackStep;
    oneClickStart(dto: OneClickStartDto, teacherId: number): Promise<OneClickStartDataDto>;
    private validateTeachingPermissions;
    private checkCoursePermission;
    private getCourseInfoWithValidation;
    private getClassInfoWithValidation;
    private getClassStudentsWithValidation;
    private getCourseSettingsWithValidation;
    private getTaskTemplatesForCourse;
    private validateCourseSettings;
    private createFailedTeachingRecord;
    private createPartialFailureRecord;
    private createOrReuseTeachingRecord;
    private createTeachingRecordInTransaction;
    private getClassInfo;
    private getClassStudents;
    private executeTeachingFlow;
    private allocatePoints;
    private applyPermissionTemplate;
    private createTasks;
    private updateTeachingRecordSuccess;
    private updateTeachingRecordFailed;
    private buildSuccessResponse;
    getCourseSettings(courseId: number): Promise<CourseSettingsDataDto>;
    private buildCourseSettingsResponse;
    private formatVideoDuration;
    private extractVideoName;
    private calculateResourcesCount;
    private getAttachmentsCount;
    private getAssessmentItemsCount;
    private getFirstAttachmentType;
    getTeachingRecords(query: GetTeachingRecordsQueryDto): Promise<TeachingRecordsDataDto>;
    private applyFilters;
    private buildTeachingRecordList;
    private getClassInfoForRecord;
    private getTeacherInfoForRecord;
}
