"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagePricing = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const package_info_entity_1 = require("../../package_info/entities/package_info.entity");
let PackagePricing = class PackagePricing {
    id;
    packageId;
    packageInfo;
    originalPrice;
    currentPrice;
    discountRate;
    currency;
    priceType;
    startTime;
    endTime;
    status;
    priority;
    description;
    createTime;
    updateTime;
};
exports.PackagePricing = PackagePricing;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_package_id'),
    (0, typeorm_1.Column)({ comment: '套餐ID' }),
    (0, swagger_1.ApiProperty)({ description: '套餐ID' }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "packageId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => package_info_entity_1.PackageInfo),
    (0, typeorm_1.JoinColumn)({ name: 'packageId' }),
    (0, swagger_1.ApiProperty)({ description: '套餐信息', type: () => package_info_entity_1.PackageInfo }),
    __metadata("design:type", package_info_entity_1.PackageInfo)
], PackagePricing.prototype, "packageInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '原价', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '原价' }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "originalPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '现价/售价', type: 'decimal', precision: 10, scale: 2 }),
    (0, swagger_1.ApiProperty)({ description: '现价/售价' }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "currentPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '折扣率(0-1之间)', type: 'decimal', precision: 5, scale: 2, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '折扣率(0-1之间)', required: false }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "discountRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '货币类型', length: 10, default: 'CNY' }),
    (0, swagger_1.ApiProperty)({ description: '货币类型', default: 'CNY' }),
    __metadata("design:type", String)
], PackagePricing.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_price_type'),
    (0, typeorm_1.Column)({ comment: '价格类型(standard-标准价格/vip-会员价格/promotion-促销价格)', length: 20, default: 'standard' }),
    (0, swagger_1.ApiProperty)({ description: '价格类型(standard-标准价格/vip-会员价格/promotion-促销价格)' }),
    __metadata("design:type", String)
], PackagePricing.prototype, "priceType", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_start_time'),
    (0, typeorm_1.Column)({ comment: '价格生效开始时间', type: 'datetime', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '价格生效开始时间', required: false }),
    __metadata("design:type", Date)
], PackagePricing.prototype, "startTime", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_end_time'),
    (0, typeorm_1.Column)({ comment: '价格生效结束时间', type: 'datetime', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '价格生效结束时间', required: false }),
    __metadata("design:type", Date)
], PackagePricing.prototype, "endTime", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Column)({ comment: '状态 0-禁用 1-启用', type: 'tinyint', default: 1 }),
    (0, swagger_1.ApiProperty)({ description: '状态 0-禁用 1-启用' }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_priority'),
    (0, typeorm_1.Column)({ comment: '优先级(数字越大优先级越高)', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '优先级(数字越大优先级越高)' }),
    __metadata("design:type", Number)
], PackagePricing.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '价格描述', length: 255, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '价格描述', required: false }),
    __metadata("design:type", String)
], PackagePricing.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '创建时间', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], PackagePricing.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '更新时间', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], PackagePricing.prototype, "updateTime", void 0);
exports.PackagePricing = PackagePricing = __decorate([
    (0, typeorm_1.Entity)('package_pricing')
], PackagePricing);
//# sourceMappingURL=package-pricing.entity.js.map