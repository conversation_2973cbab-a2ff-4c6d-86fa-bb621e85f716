/**
 * 地理位置值对象
 * 封装地理位置信息和相关业务规则
 */
export class GeographicLocation {
  private readonly _country: string;
  private readonly _province: string;
  private readonly _city: string;
  private readonly _isp: string;
  private readonly _dataSource: 'ip2region' | 'fallback';
  private readonly _confidence: number;

  constructor(
    country: string,
    province: string,
    city: string,
    isp: string,
    dataSource: 'ip2region' | 'fallback' = 'ip2region',
    confidence: number = 100
  ) {
    this._country = this.normalizeLocationField(country);
    this._province = this.normalizeLocationField(province);
    this._city = this.normalizeLocationField(city);
    this._isp = this.normalizeLocationField(isp);
    this._dataSource = dataSource;
    this._confidence = Math.max(0, Math.min(100, confidence));
  }

  /**
   * 获取国家
   */
  get country(): string {
    return this._country;
  }

  /**
   * 获取省份
   */
  get province(): string {
    return this._province;
  }

  /**
   * 获取城市
   */
  get city(): string {
    return this._city;
  }

  /**
   * 获取网络运营商
   */
  get isp(): string {
    return this._isp;
  }

  /**
   * 获取数据来源
   */
  get dataSource(): 'ip2region' | 'fallback' {
    return this._dataSource;
  }

  /**
   * 获取数据置信度
   */
  get confidence(): number {
    return this._confidence;
  }

  /**
   * 是否为中国境内
   */
  get isDomestic(): boolean {
    return this._country === '中国';
  }

  /**
   * 是否为境外位置
   */
  get isForeign(): boolean {
    return !this.isDomestic && this._country !== '未知';
  }

  /**
   * 是否有空字段
   */
  get hasEmptyFields(): boolean {
    return [this._country, this._province, this._city, this._isp]
      .some(field => field === '未知' || field === '');
  }

  /**
   * 是否为高质量数据
   */
  get isHighQuality(): boolean {
    return this._confidence >= 80 && !this.hasEmptyFields;
  }

  /**
   * 获取空字段数量
   */
  get emptyFieldCount(): number {
    return [this._country, this._province, this._city, this._isp]
      .filter(field => field === '未知' || field === '').length;
  }

  /**
   * 获取格式化的显示名称
   */
  get displayName(): string {
    const parts: string[] = [];
    
    if (this._country !== '未知' && this._country !== '') {
      parts.push(this._country);
    }
    
    if (this._province !== '未知' && this._province !== '') {
      parts.push(this._province);
    }
    
    if (this._city !== '未知' && this._city !== '' && this._city !== this._province) {
      parts.push(this._city);
    }
    
    return parts.length > 0 ? parts.join(' ') : '未知位置';
  }

  /**
   * 获取完整的位置描述
   */
  get fullDescription(): string {
    const location = this.displayName;
    const ispInfo = this._isp !== '未知' ? ` (${this._isp})` : '';
    return `${location}${ispInfo}`;
  }

  /**
   * 检查是否与另一个位置在同一省份
   */
  isSameProvince(other: GeographicLocation): boolean {
    return this._province !== '未知' && 
           other._province !== '未知' && 
           this._province === other._province;
  }

  /**
   * 检查是否与另一个位置在同一城市
   */
  isSameCity(other: GeographicLocation): boolean {
    return this.isSameProvince(other) && 
           this._city !== '未知' && 
           other._city !== '未知' && 
           this._city === other._city;
  }

  /**
   * 检查是否使用相同的ISP
   */
  isSameISP(other: GeographicLocation): boolean {
    return this._isp !== '未知' && 
           other._isp !== '未知' && 
           this._isp === other._isp;
  }

  /**
   * 计算与另一个位置的相似度 (0-100)
   */
  calculateSimilarity(other: GeographicLocation): number {
    let score = 0;
    let maxScore = 0;

    // 国家匹配 (权重: 40)
    maxScore += 40;
    if (this._country === other._country && this._country !== '未知') {
      score += 40;
    }

    // 省份匹配 (权重: 30)
    maxScore += 30;
    if (this._province === other._province && this._province !== '未知') {
      score += 30;
    }

    // 城市匹配 (权重: 20)
    maxScore += 20;
    if (this._city === other._city && this._city !== '未知') {
      score += 20;
    }

    // ISP匹配 (权重: 10)
    maxScore += 10;
    if (this._isp === other._isp && this._isp !== '未知') {
      score += 10;
    }

    return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  }

  /**
   * 标准化位置字段
   */
  private normalizeLocationField(field: string): string {
    if (!field || field.trim() === '' || field === '0') {
      return '未知';
    }
    return field.trim();
  }

  /**
   * 创建地理位置值对象的静态工厂方法
   */
  static create(
    country: string,
    province: string,
    city: string,
    isp: string,
    dataSource: 'ip2region' | 'fallback' = 'ip2region',
    confidence: number = 100
  ): GeographicLocation {
    return new GeographicLocation(country, province, city, isp, dataSource, confidence);
  }

  /**
   * 创建未知位置
   */
  static createUnknown(): GeographicLocation {
    return new GeographicLocation('未知', '未知', '未知', '未知', 'fallback', 0);
  }

  /**
   * 从ip2region原始数据创建
   */
  static fromIp2RegionData(rawData: any): GeographicLocation {
    const emptyFields = [rawData.country, rawData.province, rawData.city, rawData.isp]
      .filter(field => field === '0' || field === '' || !field).length;
    
    const confidence = Math.max(100 - (emptyFields * 25), 0);

    return new GeographicLocation(
      rawData.country || '未知',
      rawData.province || '未知',
      rawData.city || '未知',
      rawData.isp || '未知',
      'ip2region',
      confidence
    );
  }

  /**
   * 值对象相等性比较
   */
  equals(other: GeographicLocation): boolean {
    return this._country === other._country &&
           this._province === other._province &&
           this._city === other._city &&
           this._isp === other._isp;
  }

  /**
   * 转换为字符串
   */
  toString(): string {
    return this.displayName;
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      country: this._country,
      province: this._province,
      city: this._city,
      isp: this._isp,
      dataSource: this._dataSource,
      confidence: this._confidence,
      isDomestic: this.isDomestic,
      isForeign: this.isForeign,
      hasEmptyFields: this.hasEmptyFields,
      isHighQuality: this.isHighQuality,
      displayName: this.displayName,
      fullDescription: this.fullDescription
    };
  }
}
