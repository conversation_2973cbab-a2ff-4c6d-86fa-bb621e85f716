import { InvalidIpException } from '../exceptions/invalid-ip.exception';

/**
 * IP地址值对象
 * 封装IP地址的验证逻辑和业务规则
 */
export class IpAddress {
  private readonly _value: string;
  private readonly _type: 'IPv4' | 'IPv6';
  private readonly _isPrivate: boolean;
  private readonly _isLoopback: boolean;

  constructor(value: string) {
    const cleanValue = this.cleanIpAddress(value);
    
    if (!this.isValidIpAddress(cleanValue)) {
      throw new InvalidIpException(`无效的IP地址格式: ${value}`);
    }

    this._value = cleanValue;
    this._type = this.determineIpType(cleanValue);
    this._isPrivate = this.isPrivateIp(cleanValue);
    this._isLoopback = this.isLoopbackIp(cleanValue);
  }

  /**
   * 获取IP地址值
   */
  get value(): string {
    return this._value;
  }

  /**
   * 获取IP类型
   */
  get type(): 'IPv4' | 'IPv6' {
    return this._type;
  }

  /**
   * 是否为私有IP
   */
  get isPrivate(): boolean {
    return this._isPrivate;
  }

  /**
   * 是否为回环IP
   */
  get isLoopback(): boolean {
    return this._isLoopback;
  }

  /**
   * 是否为公网IP
   */
  get isPublic(): boolean {
    return !this._isPrivate && !this._isLoopback;
  }

  /**
   * 是否可以进行地理位置解析
   */
  get canGeolocate(): boolean {
    return this.isPublic;
  }

  /**
   * 获取脱敏后的IP地址
   */
  get masked(): string {
    if (this._type === 'IPv6') {
      const parts = this._value.split(':');
      if (parts.length >= 4) {
        return `${parts.slice(0, 4).join(':')}:****`;
      }
      return '****:****:****:****';
    } else {
      const parts = this._value.split('.');
      if (parts.length === 4) {
        return `${parts[0]}.${parts[1]}.${parts[2]}.***`;
      }
      return '***.***.***.**';
    }
  }

  /**
   * 清理IP地址字符串
   */
  private cleanIpAddress(ip: string): string {
    if (!ip) return '';
    
    let cleanIp = ip.trim();
    
    // 移除IPv6映射的IPv4前缀
    cleanIp = cleanIp.replace('::ffff:', '');
    
    // 处理IPv6本地回环地址
    if (cleanIp === '::1') {
      return cleanIp;
    }
    
    return cleanIp;
  }

  /**
   * 验证IP地址格式
   */
  private isValidIpAddress(ip: string): boolean {
    if (!ip) return false;
    
    // IPv4格式验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    // IPv6格式验证（简化版）
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * 确定IP类型
   */
  private determineIpType(ip: string): 'IPv4' | 'IPv6' {
    return ip.includes(':') ? 'IPv6' : 'IPv4';
  }

  /**
   * 检查是否为私有IP
   */
  private isPrivateIp(ip: string): boolean {
    if (this._type === 'IPv6') {
      // IPv6私有地址范围
      return ip.startsWith('fc') || ip.startsWith('fd') || ip.startsWith('fe80');
    } else {
      // IPv4私有地址范围
      const parts = ip.split('.').map(Number);
      if (parts.length !== 4) return false;
      
      // 10.0.0.0/8
      if (parts[0] === 10) return true;
      
      // **********/12
      if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;
      
      // ***********/16
      if (parts[0] === 192 && parts[1] === 168) return true;
      
      return false;
    }
  }

  /**
   * 检查是否为回环IP
   */
  private isLoopbackIp(ip: string): boolean {
    if (this._type === 'IPv6') {
      return ip === '::1';
    } else {
      return ip.startsWith('127.');
    }
  }

  /**
   * 创建IP地址值对象的静态工厂方法
   */
  static create(value: string): IpAddress {
    return new IpAddress(value);
  }

  /**
   * 尝试创建IP地址值对象，失败时返回null
   */
  static tryCreate(value: string): IpAddress | null {
    try {
      return new IpAddress(value);
    } catch {
      return null;
    }
  }

  /**
   * 值对象相等性比较
   */
  equals(other: IpAddress): boolean {
    return this._value === other._value;
  }

  /**
   * 转换为字符串
   */
  toString(): string {
    return this._value;
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      value: this._value,
      type: this._type,
      isPrivate: this._isPrivate,
      isLoopback: this._isLoopback,
      isPublic: this.isPublic,
      canGeolocate: this.canGeolocate,
      masked: this.masked
    };
  }
}
