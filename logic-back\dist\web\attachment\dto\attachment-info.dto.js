"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AttachmentInfoDto {
    id;
    file_url;
    file_name;
    file_type;
    file_size;
    created_by;
    created_at;
    updated_by;
    updated_at;
}
exports.AttachmentInfoDto = AttachmentInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附件ID' }),
    __metadata("design:type", Number)
], AttachmentInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件URL（存储路径）' }),
    __metadata("design:type", String)
], AttachmentInfoDto.prototype, "file_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始文件名' }),
    __metadata("design:type", String)
], AttachmentInfoDto.prototype, "file_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件类型' }),
    __metadata("design:type", String)
], AttachmentInfoDto.prototype, "file_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文件大小（字节）' }),
    __metadata("design:type", Number)
], AttachmentInfoDto.prototype, "file_size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    __metadata("design:type", Number)
], AttachmentInfoDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], AttachmentInfoDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人ID' }),
    __metadata("design:type", Number)
], AttachmentInfoDto.prototype, "updated_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], AttachmentInfoDto.prototype, "updated_at", void 0);
//# sourceMappingURL=attachment-info.dto.js.map