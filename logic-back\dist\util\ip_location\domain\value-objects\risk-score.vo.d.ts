export declare class RiskScore {
    private readonly _score;
    private readonly _level;
    private readonly _factors;
    private readonly _reason;
    constructor(score: number, factors?: string[]);
    get score(): number;
    get level(): 'LOW' | 'MEDIUM' | 'HIGH';
    get factors(): string[];
    get reason(): string;
    get isLowRisk(): boolean;
    get isMediumRisk(): boolean;
    get isHighRisk(): boolean;
    get needsVerification(): boolean;
    get needsBlocking(): boolean;
    get colorCode(): string;
    get levelDescription(): string;
    get recommendedVerificationMethods(): string[];
    private hasHighRiskFactors;
    private calculateRiskLevel;
    private generateRiskReason;
    addFactor(factor: string): RiskScore;
    removeFactor(factor: string): RiskScore;
    increaseScore(points: number): RiskScore;
    decreaseScore(points: number): RiskScore;
    static create(score: number, factors?: string[]): RiskScore;
    static createZero(): RiskScore;
    static createLow(factors?: string[]): RiskScore;
    static createMedium(factors?: string[]): RiskScore;
    static createHigh(factors?: string[]): RiskScore;
    static fromFactors(factors: string[]): RiskScore;
    equals(other: RiskScore): boolean;
    toString(): string;
    toJSON(): object;
}
