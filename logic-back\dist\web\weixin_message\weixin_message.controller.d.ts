import { Response, Request } from 'express';
import { ReceiveMessageService } from '../utils/receive_message';
import { WeixinMsgUtilService } from '../utils/weixin_msg_util';
import { WeixinConfigService } from '../config/weixin-config.service';
export declare class WeixinMessageController {
    private readonly receiveMessageService;
    private readonly weixinMsgUtilService;
    private readonly weixinConfigService;
    private readonly logger;
    constructor(receiveMessageService: ReceiveMessageService, weixinMsgUtilService: WeixinMsgUtilService, weixinConfigService: WeixinConfigService);
    verifyUrl(signature: string, timestamp: string, nonce: string, echostr: string, encryptType: string, msgSignature: string, res: Response): Promise<Response<any, Record<string, any>>>;
    receiveMessage(req: Request, signature: string, timestamp: string, nonce: string, encryptType: string, msgSignature: string, res: Response): Promise<Response<any, Record<string, any>>>;
}
