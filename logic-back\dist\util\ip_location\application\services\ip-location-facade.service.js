"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationFacadeService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../../../common/logger/logger.service");
const ip_location_command_service_1 = require("./ip-location-command.service");
const ip_location_query_service_1 = require("./ip-location-query.service");
const update_common_location_command_1 = require("../commands/update-common-location.command");
const set_trusted_location_command_1 = require("../commands/set-trusted-location.command");
const record_login_location_command_1 = require("../commands/record-login-location.command");
const get_location_by_ip_query_1 = require("../queries/get-location-by-ip.query");
const get_user_location_stats_query_1 = require("../queries/get-user-location-stats.query");
const assess_login_risk_query_1 = require("../queries/assess-login-risk.query");
const ip_location_domain_service_1 = require("../../domain/services/ip-location-domain.service");
const risk_assessment_domain_service_1 = require("../../domain/services/risk-assessment-domain.service");
const ip_address_vo_1 = require("../../domain/value-objects/ip-address.vo");
const risk_score_vo_1 = require("../../domain/value-objects/risk-score.vo");
let IpLocationFacadeService = class IpLocationFacadeService {
    commandService;
    queryService;
    domainService;
    riskAssessmentService;
    logger;
    constructor(commandService, queryService, domainService, riskAssessmentService, logger) {
        this.commandService = commandService;
        this.queryService = queryService;
        this.domainService = domainService;
        this.riskAssessmentService = riskAssessmentService;
        this.logger = logger;
    }
    async getLocationByIP(ip, includeRisk = false) {
        try {
            const query = get_location_by_ip_query_1.GetLocationByIpQuery.create(ip, includeRisk);
            const result = await this.queryService.handleGetLocationByIp(query);
            if (!result.success) {
                throw new Error(result.errors?.join(', ') || '查询失败');
            }
            return {
                success: true,
                data: result.data,
                message: '查询成功',
                executionTime: result.executionTime,
                fromCache: result.fromCache
            };
        }
        catch (error) {
            this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async assessLoginRisk(userId, ip, userAgent, includeRecommendations = true) {
        try {
            const query = includeRecommendations
                ? assess_login_risk_query_1.AssessLoginRiskQuery.createComplete(userId, ip, userAgent)
                : assess_login_risk_query_1.AssessLoginRiskQuery.createBasic(userId, ip, userAgent);
            const result = await this.queryService.handleAssessLoginRisk(query);
            if (!result.success) {
                throw new Error(result.errors?.join(', ') || '风险评估失败');
            }
            return {
                success: true,
                data: result.data,
                message: '风险评估完成',
                executionTime: result.executionTime
            };
        }
        catch (error) {
            this.logger.error(`登录风险评估失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async getUserLocationStats(userId, days = 30, includeRiskAnalysis = false) {
        try {
            const query = includeRiskAnalysis
                ? get_user_location_stats_query_1.GetUserLocationStatsQuery.createWithRiskAnalysis(userId, days)
                : get_user_location_stats_query_1.GetUserLocationStatsQuery.createBasic(userId, days);
            const result = await this.queryService.handleGetUserLocationStats(query);
            if (!result.success) {
                throw new Error(result.errors?.join(', ') || '统计查询失败');
            }
            return {
                success: true,
                data: result.data,
                message: '统计查询成功',
                executionTime: result.executionTime,
                fromCache: result.fromCache
            };
        }
        catch (error) {
            this.logger.error(`用户位置统计失败: ${userId}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async recordLoginLocation(userId, ip, loginType = record_login_location_command_1.LoginType.PASSWORD, loginStatus = record_login_location_command_1.LoginStatus.SUCCESS, sessionId, userAgent) {
        try {
            const ipAddress = ip_address_vo_1.IpAddress.create(ip);
            const location = await this.domainService.resolveLocation(ipAddress);
            const riskScore = risk_score_vo_1.RiskScore.createLow(['登录记录']);
            let command;
            if (loginStatus === record_login_location_command_1.LoginStatus.SUCCESS) {
                command = record_login_location_command_1.RecordLoginLocationCommand.createSuccessLogin(userId, ip, location, riskScore, loginType, sessionId, userAgent);
            }
            else if (loginStatus === record_login_location_command_1.LoginStatus.FAILED) {
                command = record_login_location_command_1.RecordLoginLocationCommand.createFailedLogin(userId, ip, location, riskScore, loginType, '登录失败', userAgent);
            }
            else {
                command = record_login_location_command_1.RecordLoginLocationCommand.createBlockedLogin(userId, ip, location, riskScore, loginType, '登录被阻止', userAgent);
            }
            const result = await this.commandService.handleRecordLoginLocation(command);
            if (!result.success) {
                throw new Error(result.errors?.join(', ') || '记录失败');
            }
            return {
                success: true,
                data: result.data,
                message: '登录位置记录成功',
                executionTime: result.executionTime
            };
        }
        catch (error) {
            this.logger.error(`登录位置记录失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async setTrustedLocation(userId, province, city, reason = '用户主动设置', setBy = 'USER') {
        try {
            const command = new set_trusted_location_command_1.SetTrustedLocationCommand(userId, province, city, reason, setBy);
            const result = await this.commandService.handleSetTrustedLocation(command);
            if (!result.success) {
                throw new Error(result.errors?.join(', ') || '设置失败');
            }
            return {
                success: true,
                data: result.data,
                message: '可信位置设置成功',
                executionTime: result.executionTime
            };
        }
        catch (error) {
            this.logger.error(`设置可信位置失败: ${userId}-${province}-${city}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async updateUserCommonLocation(userId, ip, sessionId, userAgent) {
        try {
            const ipAddress = ip_address_vo_1.IpAddress.create(ip);
            const location = await this.domainService.resolveLocation(ipAddress);
            const command = new update_common_location_command_1.UpdateCommonLocationCommand(userId, ipAddress, location, sessionId, userAgent);
            const result = await this.commandService.handleUpdateCommonLocation(command);
            if (!result.success) {
                throw new Error(result.errors?.join(', ') || '更新失败');
            }
            return {
                success: true,
                data: result.data,
                message: '常用位置更新成功',
                executionTime: result.executionTime
            };
        }
        catch (error) {
            this.logger.error(`更新常用位置失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
    async testIpResolution(testIp = '*******') {
        try {
            const result = await this.domainService.testResolution(testIp);
            return {
                success: result.success,
                data: result,
                message: result.success ? '测试成功' : '测试失败'
            };
        }
        catch (error) {
            this.logger.error(`IP解析测试失败: ${testIp}`, error, 'IpLocationFacadeService');
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }
};
exports.IpLocationFacadeService = IpLocationFacadeService;
exports.IpLocationFacadeService = IpLocationFacadeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [ip_location_command_service_1.IpLocationCommandService,
        ip_location_query_service_1.IpLocationQueryService,
        ip_location_domain_service_1.IpLocationDomainService,
        risk_assessment_domain_service_1.RiskAssessmentDomainService,
        logger_service_1.LoggerService])
], IpLocationFacadeService);
//# sourceMappingURL=ip-location-facade.service.js.map