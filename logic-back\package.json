{"name": "logic-back", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "cross-env NODE_ENV=dev nest start --watch", "dev": "cross-env NODE_ENV=dev nest start --watch", "debug": "cross-env NODE_ENV=dev nest start --debug --watch --inspect", "start:prod": "cross-env NODE_ENV=prod node dist/main", "prod": "cross-env NODE_ENV=prod node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:db": "node ./test/run-db-tests.js", "test:unit": "jest --config ./jest-unit.config.js"}, "dependencies": {"@alicloud/dysmsapi20170525": "^4.1.0", "@alicloud/facebody20191230": "^5.1.2", "@alicloud/green20220302": "^2.20.4", "@alicloud/imageenhan20190930": "^2.0.9", "@alicloud/imageseg20191230": "^3.0.1", "@alicloud/objectdet20191230": "^2.0.10", "@alicloud/openapi-client": "^0.4.14", "@alicloud/tea-util": "^1.4.10", "@darabonba/number": "^1.2.0", "@nestjs/bull": "^11.0.2", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.0.20", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.4", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.0", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.9", "@types/md5": "^2.3.5", "@types/node-fetch": "^2.6.12", "@types/uuid": "^10.0.0", "ali-oss": "^6.22.0", "alipay-sdk": "^4.14.0", "axios": "^1.9.0", "bull": "^4.16.5", "bullmq": "^5.51.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "handlebars": "^4.7.8", "iconv-lite": "^0.6.3", "install": "^0.13.0", "ioredis": "^5.6.1", "ip2region": "^2.3.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "logic-common": "github:zhul<PERSON>ai/logic-common#master", "md5": "^2.3.0", "mysql2": "^3.14.0", "nest-winston": "^1.10.2", "node-fetch": "^2.7.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/ali-oss": "^6.16.11", "@types/bull": "^3.15.9", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/minimatch": "^5.1.2", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/winston": "^2.4.4", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "iconv-lite": "^0.6.3", "jest": "^29.7.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}}