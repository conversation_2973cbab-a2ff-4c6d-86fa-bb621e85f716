import { Repository } from 'typeorm';
import { CreateAttachmentDto } from './dto/create-attachment.dto';
import { UpdateAttachmentDto } from './dto/update-attachment.dto';
import { Attachment } from './entities/attachment.entity';
export declare class AttachmentService {
    private attachmentRepository;
    constructor(attachmentRepository: Repository<Attachment>);
    create(createAttachmentDto: CreateAttachmentDto): Promise<Attachment>;
    findAll(): Promise<Attachment[]>;
    findOne(id: number): Promise<Attachment>;
    update(id: number, updateAttachmentDto: UpdateAttachmentDto): Promise<Attachment>;
    remove(id: number): Promise<void>;
    createMany(createAttachmentDtos: CreateAttachmentDto[]): Promise<Attachment[]>;
}
