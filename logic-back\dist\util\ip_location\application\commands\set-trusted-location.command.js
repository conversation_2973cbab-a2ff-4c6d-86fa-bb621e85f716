"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetTrustedLocationCommand = void 0;
class SetTrustedLocationCommand {
    userId;
    province;
    city;
    reason;
    setBy;
    timestamp;
    constructor(userId, province, city, reason = '用户主动设置', setBy = 'USER') {
        this.userId = userId;
        this.province = province.trim();
        this.city = city.trim();
        this.reason = reason.trim();
        this.setBy = setBy;
        this.timestamp = new Date();
    }
    static createByUser(userId, province, city, reason) {
        return new SetTrustedLocationCommand(userId, province, city, reason || '用户主动设置', 'USER');
    }
    static createBySystem(userId, province, city, reason) {
        return new SetTrustedLocationCommand(userId, province, city, reason, 'SYSTEM');
    }
    static createByAdmin(userId, province, city, reason) {
        return new SetTrustedLocationCommand(userId, province, city, reason, 'ADMIN');
    }
    static fromLocation(userId, location, reason, setBy = 'USER') {
        return new SetTrustedLocationCommand(userId, location.province, location.city, reason, setBy);
    }
    validate() {
        const errors = [];
        if (this.userId <= 0) {
            errors.push('用户ID必须大于0');
        }
        if (!this.province || this.province === '未知') {
            errors.push('省份不能为空或未知');
        }
        if (!this.city || this.city === '未知') {
            errors.push('城市不能为空或未知');
        }
        if (!this.reason) {
            errors.push('设置原因不能为空');
        }
        if (this.reason.length > 200) {
            errors.push('设置原因不能超过200个字符');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    getLocationDescription() {
        return `${this.province} ${this.city}`;
    }
    getSummary() {
        return `${this.setBy}为用户${this.userId}设置可信位置: ${this.getLocationDescription()} (${this.reason})`;
    }
    isDomesticLocation() {
        const chineseProvinces = [
            '北京市', '天津市', '上海市', '重庆市',
            '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省',
            '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省',
            '河南省', '湖北省', '湖南省', '广东省', '海南省',
            '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省',
            '内蒙古自治区', '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区',
            '香港特别行政区', '澳门特别行政区', '台湾省'
        ];
        return chineseProvinces.some(province => this.province.includes(province.replace(/省|市|自治区|特别行政区/g, '')));
    }
    toJSON() {
        return {
            userId: this.userId,
            province: this.province,
            city: this.city,
            reason: this.reason,
            setBy: this.setBy,
            timestamp: this.timestamp.toISOString(),
            locationDescription: this.getLocationDescription(),
            isDomestic: this.isDomesticLocation()
        };
    }
}
exports.SetTrustedLocationCommand = SetTrustedLocationCommand;
//# sourceMappingURL=set-trusted-location.command.js.map