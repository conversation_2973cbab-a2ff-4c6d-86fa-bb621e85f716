import { PaymentService } from '../services/payment.service';
import { NotifyService } from './notify.service';
import { PaymentRecordService } from '../../util/database/mysql/payment_record/payment-record.service';
import { LockManager } from '../lock/lock.manager';
export declare class PaymentNotifyHandler {
    private readonly paymentService;
    private readonly notifyService;
    private readonly paymentRecordService;
    private readonly lockManager;
    private readonly logger;
    constructor(paymentService: PaymentService, notifyService: NotifyService, paymentRecordService: PaymentRecordService, lockManager: LockManager);
    handlePaymentNotify(channel: string, data: any): Promise<{
        success: boolean;
        message?: string;
    }>;
    private extractOrderNoFromNotify;
    manualTriggerPaymentSuccess(outTradeNo: string, channel: string): Promise<boolean>;
}
