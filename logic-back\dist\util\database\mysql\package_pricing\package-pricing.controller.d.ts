import { PackagePricingService } from './package-pricing.service';
import { CreatePackagePricingDto, UpdatePackagePricingDto, PriceType } from './dto';
import { PackagePricing } from './entities/package-pricing.entity';
export declare class PackagePricingController {
    private readonly packagePricingService;
    constructor(packagePricingService: PackagePricingService);
    create(createPackagePricingDto: CreatePackagePricingDto): Promise<PackagePricing>;
    findAll(): Promise<PackagePricing[]>;
    getCurrentPricings(): Promise<{
        packageId: number;
        packageName: string;
        packageDescription: string;
        points: number;
        validityDays: number;
        originalPrice: number;
        currentPrice: number;
        discountRate: number;
        savings: number;
        priceType: string;
        currency: string;
        promotion?: Record<string, any>;
    }[]>;
    getCurrentPricing(packageId: string, priceType?: PriceType): Promise<PackagePricing | null>;
    getPackagePricings(packageId: string): Promise<PackagePricing[]>;
    findOne(id: string): Promise<PackagePricing>;
    update(id: string, updatePackagePricingDto: UpdatePackagePricingDto): Promise<PackagePricing>;
    remove(id: string): Promise<void>;
}
