/**
 * 获取用户位置统计查询
 * 封装获取用户位置统计信息的读操作
 */
export class GetUserLocationStatsQuery {
  public readonly userId: number;
  public readonly days: number;
  public readonly includeTrusted: boolean;
  public readonly includeRiskAnalysis: boolean;
  public readonly minLoginCount: number;
  public readonly timestamp: Date;

  constructor(
    userId: number,
    days: number = 30,
    includeTrusted: boolean = true,
    includeRiskAnalysis: boolean = false,
    minLoginCount: number = 1
  ) {
    this.userId = userId;
    this.days = Math.max(1, Math.min(365, days)); // 限制在1-365天之间
    this.includeTrusted = includeTrusted;
    this.includeRiskAnalysis = includeRiskAnalysis;
    this.minLoginCount = Math.max(1, minLoginCount);
    this.timestamp = new Date();
  }

  /**
   * 创建基础统计查询
   */
  static createBasic(userId: number, days: number = 30): GetUserLocationStatsQuery {
    return new GetUserLocationStatsQuery(userId, days, true, false, 1);
  }

  /**
   * 创建包含风险分析的查询
   */
  static createWithRiskAnalysis(
    userId: number,
    days: number = 30
  ): GetUserLocationStatsQuery {
    return new GetUserLocationStatsQuery(userId, days, true, true, 1);
  }

  /**
   * 创建只查询可信位置的查询
   */
  static createTrustedOnly(
    userId: number,
    days: number = 30
  ): GetUserLocationStatsQuery {
    return new GetUserLocationStatsQuery(userId, days, true, false, 5);
  }

  /**
   * 创建活跃位置查询（登录次数较多的位置）
   */
  static createActiveLocations(
    userId: number,
    days: number = 30,
    minLoginCount: number = 5
  ): GetUserLocationStatsQuery {
    return new GetUserLocationStatsQuery(userId, days, true, false, minLoginCount);
  }

  /**
   * 验证查询的有效性
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.userId <= 0) {
      errors.push('用户ID必须大于0');
    }

    if (this.days < 1 || this.days > 365) {
      errors.push('查询天数必须在1-365天之间');
    }

    if (this.minLoginCount < 1) {
      errors.push('最小登录次数必须大于0');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取查询的开始时间
   */
  getStartDate(): Date {
    const startDate = new Date(this.timestamp);
    startDate.setDate(startDate.getDate() - this.days);
    startDate.setHours(0, 0, 0, 0);
    return startDate;
  }

  /**
   * 获取查询的结束时间
   */
  getEndDate(): Date {
    const endDate = new Date(this.timestamp);
    endDate.setHours(23, 59, 59, 999);
    return endDate;
  }

  /**
   * 获取缓存键
   */
  getCacheKey(): string {
    const riskSuffix = this.includeRiskAnalysis ? '_risk' : '';
    const trustedSuffix = this.includeTrusted ? '_trusted' : '';
    return `user_location_stats:${this.userId}:${this.days}d:${this.minLoginCount}${trustedSuffix}${riskSuffix}`;
  }

  /**
   * 获取查询摘要信息
   */
  getSummary(): string {
    const riskInfo = this.includeRiskAnalysis ? '(包含风险分析)' : '';
    const trustedInfo = this.includeTrusted ? '(包含可信位置)' : '';
    return `查询用户${this.userId}最近${this.days}天的位置统计 ${trustedInfo}${riskInfo}`;
  }

  /**
   * 检查是否为长期查询
   */
  get isLongTermQuery(): boolean {
    return this.days > 90;
  }

  /**
   * 检查是否为短期查询
   */
  get isShortTermQuery(): boolean {
    return this.days <= 7;
  }

  /**
   * 获取查询类型描述
   */
  getQueryType(): string {
    if (this.isShortTermQuery) {
      return '短期查询';
    } else if (this.isLongTermQuery) {
      return '长期查询';
    } else {
      return '中期查询';
    }
  }

  /**
   * 获取预期的数据量级别
   */
  getExpectedDataVolume(): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (this.days <= 7) {
      return 'LOW';
    } else if (this.days <= 30) {
      return 'MEDIUM';
    } else {
      return 'HIGH';
    }
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      userId: this.userId,
      days: this.days,
      includeTrusted: this.includeTrusted,
      includeRiskAnalysis: this.includeRiskAnalysis,
      minLoginCount: this.minLoginCount,
      timestamp: this.timestamp.toISOString(),
      startDate: this.getStartDate().toISOString(),
      endDate: this.getEndDate().toISOString(),
      cacheKey: this.getCacheKey(),
      queryType: this.getQueryType(),
      expectedDataVolume: this.getExpectedDataVolume()
    };
  }
}
