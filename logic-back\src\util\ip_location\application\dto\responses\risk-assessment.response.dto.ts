import { ApiProperty } from '@nestjs/swagger';

/**
 * 风险评估响应DTO
 */
export class RiskAssessmentResponseDto {
  @ApiProperty({
    description: '风险评估结果',
    type: 'object',
    properties: {
      level: { type: 'string', example: 'HIGH', enum: ['LOW', 'MEDIUM', 'HIGH'] },
      score: { type: 'number', example: 85 },
      reason: { type: 'string', example: '跨省登录' },
      factors: { type: 'array', items: { type: 'string' }, example: ['跨省登录', '新登录地', '运营商变化'] },
      needVerification: { type: 'boolean', example: true },
      recommendedActions: { type: 'array', items: { type: 'string' }, example: ['短信验证', '邮箱验证'] }
    }
  })
  riskAssessment: {
    level: 'LOW' | 'MEDIUM' | 'HIGH';
    score: number;
    reason: string;
    factors: string[];
    needVerification: boolean;
    recommendedActions: string[];
  };

  @ApiProperty({
    description: '位置信息',
    type: 'object',
    properties: {
      country: { type: 'string', example: '中国' },
      province: { type: 'string', example: '上海市' },
      city: { type: 'string', example: '上海市' },
      isp: { type: 'string', example: '电信' },
      displayName: { type: 'string', example: '中国 上海市 上海市' }
    }
  })
  location: {
    country: string;
    province: string;
    city: string;
    isp: string;
    displayName: string;
  };

  @ApiProperty({
    description: '用户历史信息',
    type: 'object',
    properties: {
      lastLoginLocation: { type: 'string', example: '广东省 深圳市' },
      commonLocationCount: { type: 'number', example: 2 },
      isNewLocation: { type: 'boolean', example: true }
    }
  })
  userHistory: {
    lastLoginLocation: string;
    commonLocationCount: number;
    isNewLocation: boolean;
  };
}
