import { ActivityService } from './activity.service';
import { CreateActivityDto } from './dto/create-activity.dto';
import { UpdateActivityDto } from './dto/update-activity.dto';
import { Activity } from './entities/activity.entity';
export declare class ActivityController {
    private readonly activityService;
    constructor(activityService: ActivityService);
    create(createActivityDto: CreateActivityDto): Promise<Activity>;
    findAll(): Promise<Activity[]>;
    findByStatus(status: string): Promise<Activity[]>;
    findActiveActivities(): Promise<Activity[]>;
    findUpcomingActivities(): Promise<Activity[]>;
    findByCreator(creatorId: string): Promise<Activity[]>;
    findByType(activityType: string): Promise<Activity[]>;
    updateStatus(id: string, status: string): Promise<Activity>;
    findOne(id: string): Promise<Activity>;
    update(id: string, updateActivityDto: UpdateActivityDto): Promise<Activity>;
    remove(id: string): Promise<void>;
    hardRemove(id: string): Promise<void>;
}
