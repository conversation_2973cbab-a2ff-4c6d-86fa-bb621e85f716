"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePackagePricingDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const create_package_pricing_dto_1 = require("./create-package-pricing.dto");
class UpdatePackagePricingDto {
    id;
    packageId;
    originalPrice;
    currentPrice;
    discountRate;
    currency;
    priceType;
    startTime;
    endTime;
    status;
    priority;
    description;
}
exports.UpdatePackagePricingDto = UpdatePackagePricingDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '价格ID不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '价格ID必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '价格ID', example: 1 }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '套餐ID必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '套餐ID', required: false, example: 1 }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "packageId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '原价必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '原价不能小于0' }),
    (0, swagger_1.ApiProperty)({ description: '原价', required: false, example: 15.00 }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "originalPrice", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '现价必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '现价不能小于0' }),
    (0, swagger_1.ApiProperty)({ description: '现价/售价', required: false, example: 10.00 }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "currentPrice", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '折扣率必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '折扣率不能小于0' }),
    (0, class_validator_1.Max)(1, { message: '折扣率不能大于1' }),
    (0, swagger_1.ApiProperty)({ description: '折扣率(0-1之间)', required: false, example: 0.67 }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "discountRate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '货币类型必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '货币类型', required: false }),
    __metadata("design:type", String)
], UpdatePackagePricingDto.prototype, "currency", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(create_package_pricing_dto_1.PriceType, { message: '无效的价格类型' }),
    (0, swagger_1.ApiProperty)({
        description: '价格类型',
        enum: create_package_pricing_dto_1.PriceType,
        required: false
    }),
    __metadata("design:type", String)
], UpdatePackagePricingDto.prototype, "priceType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '开始时间格式不正确' }),
    (0, swagger_1.ApiProperty)({ description: '价格生效开始时间', required: false }),
    __metadata("design:type", String)
], UpdatePackagePricingDto.prototype, "startTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '结束时间格式不正确' }),
    (0, swagger_1.ApiProperty)({ description: '价格生效结束时间', required: false }),
    __metadata("design:type", String)
], UpdatePackagePricingDto.prototype, "endTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '状态 0-禁用 1-启用', required: false }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '优先级必须是数字' }),
    (0, swagger_1.ApiProperty)({ description: '优先级(数字越大优先级越高)', required: false }),
    __metadata("design:type", Number)
], UpdatePackagePricingDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '价格描述必须是字符串' }),
    (0, swagger_1.ApiProperty)({ description: '价格描述', required: false }),
    __metadata("design:type", String)
], UpdatePackagePricingDto.prototype, "description", void 0);
//# sourceMappingURL=update-package-pricing.dto.js.map