"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationNotFoundException = void 0;
const ip_location_domain_exception_1 = require("./ip-location-domain.exception");
class LocationNotFoundException extends ip_location_domain_exception_1.IpLocationDomainException {
    constructor(message, ipAddress, context) {
        super(message, 'LOCATION_NOT_FOUND', {
            ipAddress,
            ...context
        });
    }
    static ipResolutionFailed(ipAddress, reason) {
        return new LocationNotFoundException(`无法解析IP地址的地理位置: ${ipAddress}`, ipAddress, {
            reason: reason || 'resolution_failed',
            service: 'ip2region'
        });
    }
    static databaseQueryFailed(ipAddress, error) {
        return new LocationNotFoundException(`地理位置数据库查询失败: ${ipAddress}`, ipAddress, {
            reason: 'database_query_failed',
            originalError: error?.message
        });
    }
    static privateIpAddress(ipAddress) {
        return new LocationNotFoundException(`私有IP地址无法进行地理位置解析: ${ipAddress}`, ipAddress, {
            reason: 'private_ip_address',
            isPrivate: true
        });
    }
    static loopbackIpAddress(ipAddress) {
        return new LocationNotFoundException(`回环IP地址无法进行地理位置解析: ${ipAddress}`, ipAddress, {
            reason: 'loopback_ip_address',
            isLoopback: true
        });
    }
    static serviceUnavailable(ipAddress, service) {
        return new LocationNotFoundException(`地理位置解析服务不可用: ${service}`, ipAddress, {
            reason: 'service_unavailable',
            service
        });
    }
    static lowDataQuality(ipAddress, confidence) {
        return new LocationNotFoundException(`地理位置数据质量过低: ${confidence}%`, ipAddress, {
            reason: 'low_data_quality',
            confidence
        });
    }
}
exports.LocationNotFoundException = LocationNotFoundException;
//# sourceMappingURL=location-not-found.exception.js.map