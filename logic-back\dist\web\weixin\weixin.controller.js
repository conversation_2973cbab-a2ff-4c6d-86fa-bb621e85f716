"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WeixinController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinController = void 0;
const common_1 = require("@nestjs/common");
const user_auth_service_1 = require("../user_auth/user_auth.service");
const weixin_api_util_1 = require("../utils/weixin_api_util");
const weixin_config_service_1 = require("../config/weixin-config.service");
const not_login_decorator_1 = require("../router_guard/not-login.decorator");
const swagger_1 = require("@nestjs/swagger");
let WeixinController = WeixinController_1 = class WeixinController {
    userAuthService;
    weixinApiUtilService;
    weixinConfigService;
    logger = new common_1.Logger(WeixinController_1.name);
    constructor(userAuthService, weixinApiUtilService, weixinConfigService) {
        this.userAuthService = userAuthService;
        this.weixinApiUtilService = weixinApiUtilService;
        this.weixinConfigService = weixinConfigService;
    }
    async bindPhone(bindDto) {
        try {
            const { openid, phone, code } = bindDto;
            if (!openid || !phone || !code) {
                return {
                    success: false,
                    message: '参数不完整',
                    data: null
                };
            }
            const result = await this.userAuthService.bindWeixinWithPhone(openid, phone, code);
            if (result.success) {
                await this.weixinApiUtilService.updateUserRemark(openid, `已绑定-${phone}`);
                return {
                    success: true,
                    message: '绑定成功',
                    data: { userId: result.user?.id }
                };
            }
            else {
                return {
                    success: false,
                    message: result.message || '绑定失败，请稍后重试',
                    data: null
                };
            }
        }
        catch (error) {
            this.logger.error(`绑定手机号失败: ${error.message}`);
            return {
                success: false,
                message: '绑定手机号失败',
                data: null
            };
        }
    }
    async checkBindStatus(queryParams) {
        try {
            const { openid } = queryParams;
            if (!openid) {
                return {
                    success: false,
                    message: '参数不完整',
                    data: { bound: false }
                };
            }
            const user = await this.userAuthService.findUserByWeixinOpenid(openid);
            const bound = !!(user && user.phone);
            return {
                success: true,
                message: '获取绑定状态成功',
                data: {
                    bound,
                    user: bound ? {
                        id: user.id,
                        nickName: user.nickName,
                        phone: user.phone
                    } : null
                }
            };
        }
        catch (error) {
            this.logger.error(`检查绑定状态失败: ${error.message}`);
            return {
                success: false,
                message: '检查绑定状态失败',
                data: { bound: false }
            };
        }
    }
    async bindPage(openid, sceneStr, res) {
        try {
            if (!openid) {
                throw new common_1.BadRequestException('缺少openid参数');
            }
            const weixinUserInfo = await this.weixinApiUtilService.getUserInfo(openid);
            const nickname = weixinUserInfo?.nickname || '微信用户';
            const headimgurl = weixinUserInfo?.headimgurl || '';
            const htmlResponse = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>绑定手机号</title>
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
              text-align: center; 
              padding: 20px; 
              line-height: 1.6;
              color: #333;
              background-color: #f5f5f5;
            }
            .container { 
              max-width: 500px; 
              margin: 0 auto; 
              border-radius: 10px; 
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              padding: 20px;
              background-color: #fff;
            }
            .avatar {
              width: 60px;
              height: 60px;
              border-radius: 50%;
              margin: 0 auto 10px;
              display: block;
              border: 2px solid #eee;
            }
            h1 { color: #333; margin-bottom: 15px; font-size: 18px; }
            p { color: #666; margin-bottom: 20px; font-size: 14px; }
            .form-group {
              margin-bottom: 15px;
              text-align: left;
            }
            label {
              display: block;
              margin-bottom: 5px;
              font-size: 14px;
              color: #555;
            }
            input {
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              box-sizing: border-box;
              font-size: 14px;
            }
            .code-group {
              display: flex;
              gap: 10px;
            }
            .code-input {
              flex: 1;
            }
            .code-btn {
              width: 110px;
              background: #f0f0f0;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 0 5px;
              font-size: 12px;
              cursor: pointer;
            }
            .submit-btn {
              background: #1677ff;
              color: white;
              border: none;
              border-radius: 4px;
              padding: 10px 15px;
              width: 100%;
              font-size: 14px;
              cursor: pointer;
              margin-top: 10px;
            }
            .error-msg {
              color: #f56c6c;
              font-size: 12px;
              text-align: left;
              margin-top: 5px;
              min-height: 18px;
            }
            .accounts-container {
              margin-top: 20px;
              border-top: 1px solid #eee;
              padding-top: 15px;
              display: none;
            }
            .account-item {
              display: flex;
              align-items: center;
              padding: 10px;
              border: 1px solid #eee;
              border-radius: 4px;
              margin-bottom: 10px;
              cursor: pointer;
            }
            .account-item:hover {
              background-color: #f9f9f9;
            }
            .account-info {
              flex: 1;
              text-align: left;
              margin-left: 10px;
            }
            .account-name {
              font-weight: bold;
              margin-bottom: 3px;
            }
            .account-desc {
              font-size: 12px;
              color: #999;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <img src="${headimgurl || '/default-avatar.png'}" alt="头像" class="avatar">
            <h1>绑定手机号</h1>
            <p>您好，${nickname}！请绑定手机号完成登录</p>
            
            <div id="bindForm">
              <div class="form-group">
                <label for="phone">手机号</label>
                <input type="tel" id="phone" placeholder="请输入手机号">
                <div id="phoneError" class="error-msg"></div>
              </div>
              
              <div class="form-group">
                <label for="code">验证码</label>
                <div class="code-group">
                  <input type="text" id="code" placeholder="请输入验证码" class="code-input">
                  <button id="sendCodeBtn" class="code-btn">获取验证码</button>
                </div>
                <div id="codeError" class="error-msg"></div>
              </div>
              
              <button id="submitBtn" class="submit-btn">绑定并登录</button>
              <div id="generalError" class="error-msg" style="text-align: center; margin-top: 10px;"></div>
            </div>
            
            <!-- 多账号选择区域 -->
            <div id="accountsContainer" class="accounts-container">
              <h2 style="font-size: 16px; margin-bottom: 15px;">选择要绑定的账号</h2>
              <div id="accountsList"></div>
              <div id="accountsError" class="error-msg" style="text-align: center; margin-top: 10px;"></div>
            </div>
          </div>
          
          <script>
            // 页面变量
            const openid = "${openid}";
            const sceneStr = "${sceneStr || ''}";
            let countdown = 0;
            let countdownTimer = null;
            let foundAccounts = [];
            
            // 页面元素
            const phoneInput = document.getElementById('phone');
            const codeInput = document.getElementById('code');
            const sendCodeBtn = document.getElementById('sendCodeBtn');
            const submitBtn = document.getElementById('submitBtn');
            const phoneError = document.getElementById('phoneError');
            const codeError = document.getElementById('codeError');
            const generalError = document.getElementById('generalError');
            const bindForm = document.getElementById('bindForm');
            const accountsContainer = document.getElementById('accountsContainer');
            const accountsList = document.getElementById('accountsList');
            
            // 开始倒计时
            function startCountdown() {
              countdown = 60;
              sendCodeBtn.disabled = true;
              
              countdownTimer = setInterval(() => {
                countdown--;
                sendCodeBtn.innerText = \`\${countdown}秒后重试\`;
                
                if (countdown <= 0) {
                  clearInterval(countdownTimer);
                  sendCodeBtn.disabled = false;
                  sendCodeBtn.innerText = '获取验证码';
                }
              }, 1000);
            }
            
            // 发送验证码
            sendCodeBtn.addEventListener('click', async () => {
              // 清除错误提示
              phoneError.innerText = '';
              generalError.innerText = '';
              
              const phone = phoneInput.value.trim();
              
              // 验证手机号
              if (!phone) {
                phoneError.innerText = '请输入手机号';
                return;
              }
              
              if (!/^1[3-9]\\d{9}$/.test(phone)) {
                phoneError.innerText = '手机号格式不正确';
                return;
              }
              
              try {
                sendCodeBtn.disabled = true;
                sendCodeBtn.innerText = '发送中...';
                
                // 调用发送验证码接口
                const response = await fetch('/api/user-auth/smsSend', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({ phone })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                  // 发送成功，开始倒计时
                  startCountdown();
                } else {
                  sendCodeBtn.disabled = false;
                  sendCodeBtn.innerText = '获取验证码';
                  generalError.innerText = result.message || '发送验证码失败，请稍后重试';
                }
              } catch (error) {
                sendCodeBtn.disabled = false;
                sendCodeBtn.innerText = '获取验证码';
                generalError.innerText = '网络错误，请稍后重试';
                console.error('发送验证码错误:', error);
              }
            });
            
            // 渲染账号列表
            function renderAccountsList(accounts) {
              accountsList.innerHTML = '';
              console.log('渲染账号列表:', accounts);
              
              if (!accounts || accounts.length === 0) {
                accountsList.innerHTML = '<div style="text-align:center;padding:20px;">没有找到关联账号</div>';
                return;
              }
              
              accounts.forEach(account => {
                console.log('创建账号项:', account);
                const item = document.createElement('div');
                item.className = 'account-item';
                item.dataset.userid = account.id; // 添加data属性存储userId
                item.innerHTML = \`
                  <div class="account-info">
                    <div class="account-name">\${account.nickName || '用户' + account.id}</div>
                    <div class="account-desc">\${getRoleText(account.roleId)}</div>
                  </div>
                \`;
                
                // 添加点击样式
                item.style.cursor = 'pointer';
                item.style.transition = 'background-color 0.2s';
                
                // 鼠标悬停效果
                item.addEventListener('mouseover', () => {
                  item.style.backgroundColor = '#f0f7ff';
                });
                
                item.addEventListener('mouseout', () => {
                  item.style.backgroundColor = '';
                });
                
                // 点击绑定账号
                item.addEventListener('click', (e) => {
                  console.log('点击账号:', account.id);
                  
                  // 添加选中效果
                  document.querySelectorAll('.account-item').forEach(el => {
                    el.style.borderColor = '#eee';
                    el.style.backgroundColor = '';
                  });
                  
                  item.style.borderColor = '#1677ff';
                  item.style.backgroundColor = '#e6f7ff';
                  
                  // 调用绑定函数
                  bindToAccount(account.id);
                });
                
                accountsList.appendChild(item);
              });
              
              // 添加绑定提示
              const tip = document.createElement('div');
              tip.style.textAlign = 'center';
              tip.style.marginTop = '15px';
              tip.style.fontSize = '13px';
              tip.style.color = '#999';
              tip.innerText = '请点击要绑定的账号';
              accountsList.appendChild(tip);
            }
            
            // 获取角色文本
            function getRoleText(roleId) {
              const roles = {
                1: '学生',
                2: '教师',
                3: '校长',
                4: '管理员',
                5: '运营'
              };
              return roles[roleId] || '普通用户';
            }
            
            // 绑定到指定账号
            async function bindToAccount(userId) {
              try {
                console.log('开始绑定账号:', userId);
                // 清除错误提示
                generalError.innerText = '';
                const accountsError = document.getElementById('accountsError');
                if (accountsError) accountsError.innerText = '';
                
                // 需要获取之前输入的手机号和验证码
                const phone = phoneInput.value.trim();
                const code = codeInput.value.trim();
                
                // 验证手机号和验证码是否有效
                if (!phone || !code) {
                  const errorMessage = '绑定需要有效的手机号和验证码';
                  if (accountsError) {
                    accountsError.innerText = errorMessage;
                    // 滚动到错误信息位置
                    accountsError.scrollIntoView({ behavior: 'smooth' });
                  } else {
                    generalError.innerText = errorMessage;
                  }
                  console.error('绑定失败: 手机号或验证码为空');
                  return;
                }
                
                console.log('绑定参数:', { userId, phone, code, openid, sceneStr });
                
                // 显示加载状态
                const selectedItem = document.querySelector('.account-item[data-userid="' + userId + '"]');
                if (selectedItem) {
                  selectedItem.style.position = 'relative';
                  selectedItem.innerHTML += '<div style="position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(255,255,255,0.7);display:flex;align-items:center;justify-content:center;">处理中...</div>';
                }
                
                // 调用绑定接口
                const response = await fetch('/api/user-auth/bind-weixin', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({ 
                    openid,
                    userId,
                    phone,
                    code,
                    scene_str: sceneStr
                  })
                });
                
                console.log('绑定接口响应状态:', response.status);
                const result = await response.json();
                console.log('绑定接口返回结果:', result);
                
                if (result.code === 200) {
                  // 绑定成功，通知扫码服务
                  await notifyBindSuccess();
                  
                  // 显示成功信息
                  bindForm.innerHTML = \`
                    <div style="text-align: center; padding: 20px;">
                      <div style="font-size: 60px; color: #52c41a; margin-bottom: 20px;">✓</div>
                      <h2 style="margin-bottom: 10px;">绑定成功</h2>
                      <p style="margin-bottom: 20px;">您已成功绑定微信账号，请返回浏览器继续操作。</p>
                      <button onclick="window.close()" class="submit-btn">关闭页面</button>
                    </div>
                  \`;
                  accountsContainer.style.display = 'none';
                } else {
                  // 恢复界面
                  if (selectedItem) {
                    const loadingDiv = selectedItem.querySelector('div[style*="position:absolute"]');
                    if (loadingDiv) loadingDiv.remove();
                  }
                  
                  const errorMessage = result.message || '绑定失败，请稍后重试';
                  if (accountsError) {
                    accountsError.innerText = errorMessage;
                  } else {
                    generalError.innerText = errorMessage;
                  }
                  console.error('绑定失败:', result.message);
                }
              } catch (error) {
                const accountsError = document.getElementById('accountsError');
                const errorMessage = '网络错误，请稍后重试';
                
                if (accountsError) {
                  accountsError.innerText = errorMessage;
                } else {
                  generalError.innerText = errorMessage;
                }
                console.error('绑定错误:', error);
              }
            }
            
            // 提交表单
            submitBtn.addEventListener('click', async () => {
              // 清除错误提示
              phoneError.innerText = '';
              codeError.innerText = '';
              generalError.innerText = '';
              
              const phone = phoneInput.value.trim();
              const code = codeInput.value.trim();
              
              // 验证手机号
              if (!phone) {
                phoneError.innerText = '请输入手机号';
                return;
              }
              
              if (!/^1[3-9]\\d{9}$/.test(phone)) {
                phoneError.innerText = '手机号格式不正确';
                return;
              }
              
              // 验证验证码
              if (!code) {
                codeError.innerText = '请输入验证码';
                return;
              }
              
              try {
                submitBtn.disabled = true;
                submitBtn.innerText = '绑定中...';
                
                // 先检查手机号关联的账号
                const checkResponse = await fetch(\`/api/user-auth/check-phone-accounts?phone=\${phone}\`);
                const checkResult = await checkResponse.json();
                
                if (checkResult.code === 200) {
                  // 找到关联账号
                  const accounts = checkResult.data || [];
                  
                  if (accounts.length > 1) {
                    // 多个账号，显示选择界面
                    foundAccounts = accounts;
                    renderAccountsList(accounts);
                    bindForm.style.display = 'none';
                    accountsContainer.style.display = 'block';
                    return;
                  } else if (accounts.length === 1) {
                    // 只有一个账号，直接绑定
                    const account = accounts[0];
                    
                    // 调用绑定接口
                    const bindResponse = await fetch('/api/user-auth/bind-weixin', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify({ 
                        openid,
                        phone,
                        code,
                        userId: account.id,
                        scene_str: sceneStr
                      })
                    });
                    
                    const bindResult = await bindResponse.json();
                    
                    if (bindResult.code === 200) {
                      // 绑定成功，通知扫码服务
                      await notifyBindSuccess();
                      
                      // 显示成功信息
                      bindForm.innerHTML = \`
                        <div style="text-align: center; padding: 20px;">
                          <div style="font-size: 60px; color: #52c41a; margin-bottom: 20px;">✓</div>
                          <h2 style="margin-bottom: 10px;">绑定成功</h2>
                          <p style="margin-bottom: 20px;">您已成功绑定微信账号，请返回浏览器继续操作。</p>
                          <button onclick="window.close()" class="submit-btn">关闭页面</button>
                        </div>
                      \`;
                    } else {
                      generalError.innerText = bindResult.message || '绑定失败，请稍后重试';
                      submitBtn.disabled = false;
                      submitBtn.innerText = '绑定并登录';
                    }
                  } else {
                    // 没有账号，创建新账号并绑定
                    const registerResponse = await fetch('/api/user-auth/register-and-bind-weixin', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify({ 
                        openid,
                        phone,
                        code,
                        scene_str: sceneStr
                      })
                    });
                    
                    const registerResult = await registerResponse.json();
                    
                    if (registerResult.code === 200) {
                      // 注册并绑定成功，通知扫码服务
                      await notifyBindSuccess();
                      
                      // 显示成功信息
                      bindForm.innerHTML = \`
                        <div style="text-align: center; padding: 20px;">
                          <div style="font-size: 60px; color: #52c41a; margin-bottom: 20px;">✓</div>
                          <h2 style="margin-bottom: 10px;">注册并绑定成功</h2>
                          <p style="margin-bottom: 20px;">您已成功注册并绑定微信账号，请返回浏览器继续操作。</p>
                          <button onclick="window.close()" class="submit-btn">关闭页面</button>
                        </div>
                      \`;
                    } else {
                      generalError.innerText = registerResult.message || '注册失败，请稍后重试';
                      submitBtn.disabled = false;
                      submitBtn.innerText = '绑定并登录';
                    }
                  }
                } else {
                  generalError.innerText = checkResult.message || '验证失败，请稍后重试';
                  submitBtn.disabled = false;
                  submitBtn.innerText = '绑定并登录';
                }
              } catch (error) {
                generalError.innerText = '网络错误，请稍后重试';
                submitBtn.disabled = false;
                submitBtn.innerText = '绑定并登录';
                console.error('提交错误:', error);
              }
            });
            
            // 通知扫码服务绑定成功
            async function notifyBindSuccess() {
              if (!sceneStr) return;
              
              try {
                console.log('通知扫码服务绑定成功:', sceneStr);
                // 调用扫码回调接口，更新状态为bind_success
                await fetch('/api/weixin-scan/callback', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    openid,
                    scene_str: sceneStr,
                    scan_status: 'bind_success'
                  })
                });
                console.log('通知扫码服务完成');
              } catch (error) {
                console.error('通知扫码服务错误:', error);
              }
            }
          </script>
        </body>
      </html>
      `;
            res.setHeader('Content-Type', 'text/html');
            return res.send(htmlResponse);
        }
        catch (error) {
            this.logger.error(`绑定页面错误: ${error.message}`);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).send(`
        <html>
          <body>
            <h1>加载失败</h1>
            <p>${error.message}</p>
          </body>
        </html>
      `);
        }
    }
};
exports.WeixinController = WeixinController;
__decorate([
    (0, common_1.Post)('bindPhone'),
    (0, not_login_decorator_1.NotLogin)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "bindPhone", null);
__decorate([
    (0, common_1.Get)('checkBindStatus'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "checkBindStatus", null);
__decorate([
    (0, common_1.Get)('bindPage'),
    (0, swagger_1.ApiOperation)({ summary: '微信绑定手机号页面' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '返回绑定页面HTML' }),
    __param(0, (0, common_1.Query)('openid')),
    __param(1, (0, common_1.Query)('scene_str')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], WeixinController.prototype, "bindPage", null);
exports.WeixinController = WeixinController = WeixinController_1 = __decorate([
    (0, common_1.Controller)('api/weixin'),
    __metadata("design:paramtypes", [user_auth_service_1.UserAuthService,
        weixin_api_util_1.WeixinApiUtilService,
        weixin_config_service_1.WeixinConfigService])
], WeixinController);
//# sourceMappingURL=weixin.controller.js.map