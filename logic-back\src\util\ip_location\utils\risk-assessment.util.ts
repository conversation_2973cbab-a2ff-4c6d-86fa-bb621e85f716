import { Injectable } from '@nestjs/common';
import { LocationInfo, LocationRisk } from './ip-location.util';
import { LoggerService } from '../../../common/logger/logger.service';

/**
 * 用户位置统计接口
 */
export interface UserLocationStats {
  commonLocations: Array<{
    province: string;
    city: string;
    loginCount: number;
    isTrusted: boolean;
    lastLoginAt: Date;
  }>;
  riskLoginCount: number;
  totalLoginCount: number;
}

/**
 * 风险评估配置
 */
interface RiskConfig {
  // 风险等级阈值
  lowThreshold: number;
  mediumThreshold: number;
  highThreshold: number;
  
  // 风险因子权重
  foreignLoginWeight: number;
  crossProvinceWeight: number;
  newLocationWeight: number;
  ispChangeWeight: number;
  dataQualityWeight: number;
  
  // 用户保护配置
  newUserProtectionDays: number;
  newUserRiskReduction: number;
}

/**
 * 风险评估工具类
 */
@Injectable()
export class RiskAssessmentUtil {
  private readonly riskConfig: RiskConfig = {
    lowThreshold: 30,
    mediumThreshold: 70,
    highThreshold: 100,
    foreignLoginWeight: 60,
    crossProvinceWeight: 40,
    newLocationWeight: 15,
    ispChangeWeight: 5,
    dataQualityWeight: 20,
    newUserProtectionDays: 7,
    newUserRiskReduction: 15
  };

  constructor(private readonly loggerService: LoggerService) {}

  /**
   * 评估登录地风险
   * @param userId 用户ID
   * @param currentLocation 当前登录位置
   * @param userHistory 用户历史位置统计
   * @returns 风险评估结果
   */
  async assessLoginRisk(
    userId: number,
    currentLocation: LocationInfo,
    userHistory: UserLocationStats
  ): Promise<LocationRisk> {
    const startTime = Date.now();
    
    try {
      const riskResult = this.calculateRiskScore(currentLocation, userHistory);
      const riskLevel = this.determineRiskLevel(riskResult.score);
      
      const risk: LocationRisk = {
        level: riskLevel,
        score: riskResult.score,
        reason: this.generateRiskReason(riskLevel, riskResult.factors),
        needVerification: riskLevel === 'HIGH',
        factors: riskResult.factors
      };

      this.loggerService.logBusiness('RiskAssessmentUtil', 'assessLoginRisk', {
        userId,
        location: `${currentLocation.province}${currentLocation.city}`,
        riskLevel: risk.level,
        riskScore: risk.score,
        factors: risk.factors,
        responseTime: Date.now() - startTime
      });

      return risk;
    } catch (error) {
      this.loggerService.logBusiness('RiskAssessmentUtil', 'assessLoginRisk', {
        userId,
        responseTime: Date.now() - startTime
      }, error);
      
      // 返回默认低风险
      return {
        level: 'LOW',
        score: 0,
        reason: '风险评估异常，默认低风险',
        needVerification: false,
        factors: ['评估异常']
      };
    }
  }

  /**
   * 计算风险评分
   * @param currentLocation 当前位置
   * @param userHistory 用户历史
   * @returns 风险评分和因素
   */
  private calculateRiskScore(
    currentLocation: LocationInfo,
    userHistory: UserLocationStats
  ): { score: number; factors: string[] } {
    let score = 0;
    const riskFactors: string[] = [];

    // 境外登录 - 高风险
    if (currentLocation.country !== '中国') {
      score += this.riskConfig.foreignLoginWeight;
      riskFactors.push('境外登录');
    }

    // 数据质量检查
    if (currentLocation.province === '未知' || currentLocation.city === '未知') {
      score += this.riskConfig.dataQualityWeight;
      riskFactors.push('位置信息不完整');
    }

    // 省份级别检查 - 主要风险判断依据
    const hasProvinceHistory = userHistory.commonLocations.some(loc =>
      loc.province === currentLocation.province && currentLocation.province !== '未知'
    );

    if (!hasProvinceHistory && currentLocation.country === '中国' && currentLocation.province !== '未知') {
      score += this.riskConfig.crossProvinceWeight;
      riskFactors.push(`跨省登录至${currentLocation.province}`);
    }

    // 城市级别检查 - 辅助判断
    if (hasProvinceHistory) {
      const isCityMatch = userHistory.commonLocations.some(loc =>
        loc.province === currentLocation.province &&
        loc.city === currentLocation.city &&
        currentLocation.city !== '未知'
      );

      if (!isCityMatch && currentLocation.city !== '未知') {
        score += this.riskConfig.newLocationWeight;
        riskFactors.push(`省内异地登录至${currentLocation.city}`);
      }
    }

    // 运营商变化检查
    const hasISPHistory = userHistory.commonLocations.some(loc =>
      loc.province === currentLocation.province &&
      currentLocation.isp !== '未知'
    );

    if (hasProvinceHistory && !hasISPHistory && currentLocation.isp !== '未知') {
      score += this.riskConfig.ispChangeWeight;
      riskFactors.push('运营商变化');
    }

    // 登录频率影响
    if (userHistory.riskLoginCount > 5) {
      score += 10;
      riskFactors.push('频繁异地登录');
    }

    // 新用户保护
    if (userHistory.totalLoginCount < 3) {
      score = Math.max(score - this.riskConfig.newUserRiskReduction, 0);
      riskFactors.push('新用户保护');
    }

    return {
      score: Math.min(Math.max(score, 0), 100),
      factors: riskFactors
    };
  }

  /**
   * 确定风险等级
   * @param score 风险评分
   * @returns 风险等级
   */
  private determineRiskLevel(score: number): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (score >= this.riskConfig.highThreshold) {
      return 'HIGH';
    } else if (score >= this.riskConfig.mediumThreshold) {
      return 'MEDIUM';
    } else {
      return 'LOW';
    }
  }

  /**
   * 生成风险原因描述
   * @param level 风险等级
   * @param factors 风险因素
   * @returns 风险原因描述
   */
  private generateRiskReason(level: 'LOW' | 'MEDIUM' | 'HIGH', factors: string[]): string {
    if (factors.length === 0) {
      return '常用登录地，风险较低';
    }

    const mainFactor = factors[0];
    
    switch (level) {
      case 'HIGH':
        return `高风险：${mainFactor}`;
      case 'MEDIUM':
        return `中风险：${mainFactor}`;
      case 'LOW':
      default:
        return `低风险：${mainFactor}`;
    }
  }

  /**
   * 检查是否需要额外验证
   * @param risk 风险评估结果
   * @param userHistory 用户历史
   * @returns 是否需要验证
   */
  needAdditionalVerification(risk: LocationRisk, userHistory: UserLocationStats): boolean {
    // 高风险必须验证
    if (risk.level === 'HIGH') {
      return true;
    }

    // 中风险且新用户需要验证
    if (risk.level === 'MEDIUM' && userHistory.totalLoginCount < 5) {
      return true;
    }

    // 包含境外登录因素需要验证
    if (risk.factors.includes('境外登录')) {
      return true;
    }

    return false;
  }

  /**
   * 获取推荐的验证方式
   * @param risk 风险评估结果
   * @returns 推荐的验证方式列表
   */
  getRecommendedVerificationMethods(risk: LocationRisk): string[] {
    const methods: string[] = [];

    if (risk.factors.includes('境外登录')) {
      methods.push('邮箱验证', '短信验证');
    } else if (risk.factors.includes('跨省登录')) {
      methods.push('短信验证');
    } else if (risk.level === 'MEDIUM') {
      methods.push('短信验证');
    }

    return methods.length > 0 ? methods : ['短信验证'];
  }

  /**
   * 更新风险配置
   * @param newConfig 新的配置
   */
  updateRiskConfig(newConfig: Partial<RiskConfig>): void {
    Object.assign(this.riskConfig, newConfig);
    
    this.loggerService.logBusiness('RiskAssessmentUtil', 'updateRiskConfig', {
      updatedFields: Object.keys(newConfig),
      newConfig: this.riskConfig
    });
  }

  /**
   * 获取当前风险配置
   * @returns 当前风险配置
   */
  getRiskConfig(): RiskConfig {
    return { ...this.riskConfig };
  }
}
