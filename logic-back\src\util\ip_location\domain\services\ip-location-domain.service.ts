import { Injectable } from '@nestjs/common';
import { IpAddress } from '../value-objects/ip-address.vo';
import { GeographicLocation } from '../value-objects/geographic-location.vo';
import { InvalidIpException } from '../exceptions/invalid-ip.exception';
import { LocationNotFoundException } from '../exceptions/location-not-found.exception';

// 导入ip2region库
const IP2Region = require('ip2region').default;

/**
 * IP地理位置解析领域服务
 * 封装核心的IP地理位置解析业务逻辑
 */
@Injectable()
export class IpLocationDomainService {
  private ip2region: any;

  constructor() {
    this.initializeIp2Region();
  }

  /**
   * 解析IP地址的地理位置
   * @param ipAddress IP地址值对象
   * @returns 地理位置值对象
   */
  async resolveLocation(ipAddress: IpAddress): Promise<GeographicLocation> {
    // 验证IP地址是否可以进行地理位置解析
    this.validateIpForGeolocation(ipAddress);

    // 在开发环境中，为本地IP返回默认位置
    if (ipAddress.isLoopback && this.isDevelopmentEnvironment()) {
      return this.createDefaultLocationForDevelopment(ipAddress.value);
    }

    try {
      // 使用ip2region进行解析
      const rawResult = this.ip2region.search(ipAddress.value);

      if (!rawResult) {
        throw LocationNotFoundException.ipResolutionFailed(
          ipAddress.value,
          'ip2region返回空结果'
        );
      }

      // 将原始数据转换为地理位置值对象
      const location = GeographicLocation.fromIp2RegionData(rawResult);

      // 验证解析结果的质量
      this.validateLocationQuality(location, ipAddress);

      return location;

    } catch (error) {
      if (error instanceof LocationNotFoundException) {
        throw error;
      }

      throw LocationNotFoundException.ipResolutionFailed(
        ipAddress.value,
        error.message
      );
    }
  }

  /**
   * 批量解析IP地址
   * @param ipAddresses IP地址值对象数组
   * @returns 地理位置值对象数组
   */
  async resolveMultipleLocations(ipAddresses: IpAddress[]): Promise<GeographicLocation[]> {
    const results: GeographicLocation[] = [];

    for (const ipAddress of ipAddresses) {
      try {
        const location = await this.resolveLocation(ipAddress);
        results.push(location);
      } catch (error) {
        // 对于批量解析，失败的IP返回未知位置
        results.push(GeographicLocation.createUnknown());
      }
    }

    return results;
  }

  /**
   * 检查IP地址是否可以进行地理位置解析
   * @param ipAddress IP地址值对象
   * @returns 是否可以解析
   */
  canResolveLocation(ipAddress: IpAddress): boolean {
    return ipAddress.canGeolocate;
  }

  /**
   * 获取IP地址的基本信息（不进行地理位置解析）
   * @param ipAddress IP地址值对象
   * @returns IP地址的基本信息
   */
  getIpBasicInfo(ipAddress: IpAddress): {
    value: string;
    type: 'IPv4' | 'IPv6';
    isPrivate: boolean;
    isLoopback: boolean;
    isPublic: boolean;
    canGeolocate: boolean;
    masked: string;
  } {
    return {
      value: ipAddress.value,
      type: ipAddress.type,
      isPrivate: ipAddress.isPrivate,
      isLoopback: ipAddress.isLoopback,
      isPublic: ipAddress.isPublic,
      canGeolocate: ipAddress.canGeolocate,
      masked: ipAddress.masked
    };
  }

  /**
   * 比较两个IP地址的地理位置
   * @param ipAddress1 第一个IP地址
   * @param ipAddress2 第二个IP地址
   * @returns 位置比较结果
   */
  async compareLocations(
    ipAddress1: IpAddress, 
    ipAddress2: IpAddress
  ): Promise<{
    location1: GeographicLocation;
    location2: GeographicLocation;
    similarity: number;
    isSameCountry: boolean;
    isSameProvince: boolean;
    isSameCity: boolean;
    isSameISP: boolean;
  }> {
    const location1 = await this.resolveLocation(ipAddress1);
    const location2 = await this.resolveLocation(ipAddress2);

    return {
      location1,
      location2,
      similarity: location1.calculateSimilarity(location2),
      isSameCountry: location1.country === location2.country,
      isSameProvince: location1.isSameProvince(location2),
      isSameCity: location1.isSameCity(location2),
      isSameISP: location1.isSameISP(location2)
    };
  }

  /**
   * 初始化ip2region实例
   */
  private initializeIp2Region(): void {
    try {
      this.ip2region = new IP2Region();
    } catch (error) {
      throw new Error(`ip2region初始化失败: ${error.message}`);
    }
  }

  /**
   * 验证IP地址是否可以进行地理位置解析
   */
  private validateIpForGeolocation(ipAddress: IpAddress): void {
    if (ipAddress.isPrivate) {
      throw LocationNotFoundException.privateIpAddress(ipAddress.value);
    }

    if (ipAddress.isLoopback) {
      throw LocationNotFoundException.loopbackIpAddress(ipAddress.value);
    }

    if (!ipAddress.canGeolocate) {
      // 在开发环境中，允许本地IP进行解析
      if (ipAddress.isLoopback && this.isDevelopmentEnvironment()) {
        return; // 允许继续处理
      }

      throw LocationNotFoundException.ipResolutionFailed(
        ipAddress.value,
        'IP地址不支持地理位置解析'
      );
    }
  }

  /**
   * 验证地理位置解析结果的质量
   */
  private validateLocationQuality(
    location: GeographicLocation, 
    ipAddress: IpAddress
  ): void {
    // 如果数据质量过低，可以选择抛出异常或记录警告
    if (location.confidence < 20) {
      throw LocationNotFoundException.lowDataQuality(
        ipAddress.value,
        location.confidence
      );
    }

    // 检查是否所有字段都为空
    if (location.hasEmptyFields && location.confidence === 0) {
      throw LocationNotFoundException.ipResolutionFailed(
        ipAddress.value,
        '解析结果为空'
      );
    }
  }

  /**
   * 获取支持的IP地址类型
   */
  getSupportedIpTypes(): string[] {
    return ['IPv4', 'IPv6'];
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(): {
    isAvailable: boolean;
    version: string;
    supportedTypes: string[];
  } {
    return {
      isAvailable: !!this.ip2region,
      version: 'ip2region-v2.0',
      supportedTypes: this.getSupportedIpTypes()
    };
  }

  /**
   * 测试IP地址解析功能
   * @param testIp 测试用的IP地址字符串
   * @returns 测试结果
   */
  async testResolution(testIp: string = '*******'): Promise<{
    success: boolean;
    ipAddress?: IpAddress;
    location?: GeographicLocation;
    error?: string;
    performanceMs: number;
  }> {
    const startTime = Date.now();

    try {
      const ipAddress = IpAddress.create(testIp);
      const location = await this.resolveLocation(ipAddress);

      return {
        success: true,
        ipAddress,
        location,
        performanceMs: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        performanceMs: Date.now() - startTime
      };
    }
  }

  /**
   * 检查是否为开发环境
   */
  private isDevelopmentEnvironment(): boolean {
    return process.env.NODE_ENV === 'development' ||
           process.env.NODE_ENV === 'dev' ||
           !process.env.NODE_ENV; // 默认为开发环境
  }

  /**
   * 为开发环境创建默认位置信息
   */
  private createDefaultLocationForDevelopment(_ip: string): GeographicLocation {
    return GeographicLocation.create(
      '中国',
      '广东省',
      '广州市',
      '本地开发环境'
    );
  }
}
