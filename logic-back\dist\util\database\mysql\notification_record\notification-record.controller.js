"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationRecordController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationRecordController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const notification_record_service_1 = require("./notification-record.service");
const notification_record_entity_1 = require("./entities/notification-record.entity");
const notification_record_dto_1 = require("./dto/notification-record.dto");
let NotificationRecordController = NotificationRecordController_1 = class NotificationRecordController {
    notificationRecordService;
    logger = new common_1.Logger(NotificationRecordController_1.name);
    constructor(notificationRecordService) {
        this.notificationRecordService = notificationRecordService;
    }
    async create(createDto) {
        this.logger.log(`创建通知记录请求: ${JSON.stringify(createDto)}`);
        const result = await this.notificationRecordService.create(createDto);
        return { code: 0, message: 'success', data: result };
    }
    async findAll(queryDto, page = 1, limit = 10) {
        this.logger.log(`查询通知记录列表: page=${page}, limit=${limit}`);
        const [records, total] = await this.notificationRecordService.findAll(queryDto, page, limit);
        return {
            code: 0,
            message: 'success',
            data: {
                records,
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        };
    }
    async findOne(id) {
        this.logger.log(`查询通知记录详情: ID=${id}`);
        const record = await this.notificationRecordService.findById(id);
        return { code: 0, message: 'success', data: record };
    }
    async update(id, updateDto) {
        this.logger.log(`更新通知记录: ID=${id}`);
        const result = await this.notificationRecordService.update(id, updateDto);
        return { code: 0, message: 'success', data: result };
    }
    async remove(id) {
        this.logger.log(`删除通知记录: ID=${id}`);
        await this.notificationRecordService.remove(id);
        return { code: 0, message: 'success' };
    }
    async markForRetry(id) {
        this.logger.log(`标记通知记录为待重试: ID=${id}`);
        const record = await this.notificationRecordService.findById(id);
        if (!record) {
            return { code: 404, message: '通知记录不存在' };
        }
        const nextRetryTime = new Date(Date.now() + 5 * 60 * 1000);
        const result = await this.notificationRecordService.update(id, {
            status: notification_record_dto_1.NotificationStatus.PENDING,
            nextRetryTime,
            retryCount: record.retryCount + 1,
        });
        return { code: 0, message: 'success', data: result };
    }
    async batchUpdateStatus(payload) {
        this.logger.log(`批量更新通知状态: 数量=${payload.ids.length}, 状态=${payload.status}`);
        await this.notificationRecordService.updateStatus(payload.ids, payload.status);
        return { code: 0, message: 'success' };
    }
};
exports.NotificationRecordController = NotificationRecordController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建通知记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功', type: notification_record_entity_1.NotificationRecord }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_record_dto_1.CreateNotificationRecordDto]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '查询通知记录列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: [notification_record_entity_1.NotificationRecord] }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码', type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量', type: Number }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [notification_record_dto_1.QueryNotificationRecordDto, Object, Object]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '查询通知记录详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功', type: notification_record_entity_1.NotificationRecord }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新通知记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功', type: notification_record_entity_1.NotificationRecord }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, notification_record_dto_1.UpdateNotificationRecordDto]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除通知记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('retry/:id'),
    (0, swagger_1.ApiOperation)({ summary: '标记通知记录为待重试' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '标记成功', type: notification_record_entity_1.NotificationRecord }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "markForRetry", null);
__decorate([
    (0, common_1.Post)('batch/status'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新通知状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationRecordController.prototype, "batchUpdateStatus", null);
exports.NotificationRecordController = NotificationRecordController = NotificationRecordController_1 = __decorate([
    (0, swagger_1.ApiTags)('通知记录'),
    (0, common_1.Controller)('api/v1/notification-records'),
    __metadata("design:paramtypes", [notification_record_service_1.NotificationRecordService])
], NotificationRecordController);
//# sourceMappingURL=notification-record.controller.js.map