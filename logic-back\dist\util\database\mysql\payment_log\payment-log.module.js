"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentLogModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const payment_log_service_1 = require("./payment-log.service");
const payment_log_controller_1 = require("./payment-log.controller");
const payment_log_entity_1 = require("./entities/payment-log.entity");
let PaymentLogModule = class PaymentLogModule {
};
exports.PaymentLogModule = PaymentLogModule;
exports.PaymentLogModule = PaymentLogModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([payment_log_entity_1.PaymentLog])],
        providers: [payment_log_service_1.PaymentLogService],
        controllers: [payment_log_controller_1.PaymentLogController],
        exports: [payment_log_service_1.PaymentLogService],
    })
], PaymentLogModule);
//# sourceMappingURL=payment-log.module.js.map