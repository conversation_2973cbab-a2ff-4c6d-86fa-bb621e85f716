"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PackageOrderBusinessService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrderBusinessService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const moment = require("moment");
const package_order_service_1 = require("../../util/database/mysql/package_order/package-order.service");
const package_info_service_1 = require("../../util/database/mysql/package_info/package_info.service");
const user_info_service_1 = require("../user_info/user_info.service");
const user_package_service_1 = require("../../util/database/mysql/user_package/user_package.service");
const user_points_service_1 = require("../../util/database/mysql/user_points/user_points.service");
const payment_service_1 = require("../../payment/services/payment.service");
const package_pricing_service_1 = require("../../util/database/mysql/package_pricing/package-pricing.service");
const payment_order_service_1 = require("../../util/database/mysql/payment_order/payment-order.service");
const package_order_entity_1 = require("../../util/database/mysql/package_order/entities/package-order.entity");
const user_package_entity_1 = require("../../util/database/mysql/user_package/entities/user_package.entity");
const user_info_entity_1 = require("../../util/database/mysql/user_info/entities/user_info.entity");
const dto_1 = require("./dto");
const dto_2 = require("../../util/database/mysql/package_order/dto");
const payment_request_dto_1 = require("../../payment/dto/payment-request.dto");
let PackageOrderBusinessService = PackageOrderBusinessService_1 = class PackageOrderBusinessService {
    dataSource;
    packageOrderService;
    packageInfoService;
    userInfoService;
    userPackageService;
    userPointsService;
    paymentService;
    packagePricingService;
    paymentOrderService;
    packageOrderRepository;
    userPackageRepository;
    userInfoRepository;
    logger = new common_1.Logger(PackageOrderBusinessService_1.name);
    constructor(dataSource, packageOrderService, packageInfoService, userInfoService, userPackageService, userPointsService, paymentService, packagePricingService, paymentOrderService, packageOrderRepository, userPackageRepository, userInfoRepository) {
        this.dataSource = dataSource;
        this.packageOrderService = packageOrderService;
        this.packageInfoService = packageInfoService;
        this.userInfoService = userInfoService;
        this.userPackageService = userPackageService;
        this.userPointsService = userPointsService;
        this.paymentService = paymentService;
        this.packagePricingService = packagePricingService;
        this.paymentOrderService = paymentOrderService;
        this.packageOrderRepository = packageOrderRepository;
        this.userPackageRepository = userPackageRepository;
        this.userInfoRepository = userInfoRepository;
    }
    async getAvailablePackages() {
        return await this.packagePricingService.getAllCurrentPricings();
    }
    async purchasePackage(userId, purchaseDto) {
        const { packageId, channel, paymentMode = dto_1.PaymentMode.REDIRECT, clientIp, returnUrl } = purchaseDto;
        this.logger.log(`用户 ${userId} 开始购买套餐 ${packageId}`);
        const packageInfo = await this.packageInfoService.findOne(packageId);
        if (!packageInfo) {
            throw new common_1.NotFoundException(`套餐ID为${packageId}的信息不存在`);
        }
        if (packageInfo.status !== 1) {
            throw new common_1.BadRequestException(`套餐"${packageInfo.name}"已下架或不可用`);
        }
        const userInfo = await this.userInfoService.findOne(parseInt(userId));
        if (!userInfo) {
            throw new common_1.NotFoundException(`用户ID为${userId}的信息不存在`);
        }
        const pricing = await this.packagePricingService.getCurrentPricing(packageId);
        if (!pricing) {
            throw new common_1.NotFoundException(`套餐ID为${packageId}的价格信息不存在或已过期`);
        }
        const originalPrice = Number(pricing.originalPrice);
        const price = Number(pricing.currentPrice);
        const discountRate = Number(pricing.discountRate || (price / originalPrice).toFixed(2));
        const orderNo = this.generateOrderNo();
        return await this.dataSource.transaction(async (manager) => {
            try {
                const packageOrder = manager.create(package_order_entity_1.PackageOrder, {
                    orderNo,
                    userId,
                    packageId,
                    packageName: packageInfo.name,
                    points: packageInfo.points,
                    validityDays: packageInfo.validityDays,
                    price,
                    originalPrice,
                    discountRate,
                    status: dto_2.PackageOrderStatus.PENDING,
                    promotion: packageInfo.promotion,
                });
                await manager.save(package_order_entity_1.PackageOrder, packageOrder);
                this.logger.log(`创建套餐订单成功: ${orderNo}`);
                const paymentChannel = this.mapPaymentChannel(channel);
                const paymentModeEnum = this.mapPaymentMode(paymentMode);
                const paymentResult = await this.paymentService.createPayment({
                    userId,
                    amount: price,
                    subject: `购买套餐-${packageInfo.name}`,
                    description: `用户购买套餐：${packageInfo.name}，点数：${packageInfo.points}，有效期：${packageInfo.validityDays}天`,
                    channel: paymentChannel,
                    paymentMode: paymentModeEnum,
                    clientIp,
                    returnUrl,
                    extraData: {
                        packageOrderNo: orderNo,
                        packageId,
                        packageName: packageInfo.name,
                    },
                });
                this.logger.log(`创建支付订单成功: ${paymentResult.orderNo}`);
                const expireTime = moment().add(30, 'minutes').toDate();
                return {
                    orderNo,
                    paymentUrl: paymentResult.paymentUrl,
                    qrCode: paymentResult.qrCode,
                    amount: price,
                    packageName: packageInfo.name,
                    expireTime,
                };
            }
            catch (error) {
                this.logger.error(`购买套餐失败: ${error.message}`, error.stack);
                throw new common_1.BadRequestException(`购买套餐失败: ${error.message}`);
            }
        });
    }
    async handlePaymentSuccess(callbackDto) {
        const { orderNo, paymentId } = callbackDto;
        this.logger.log(`处理支付成功回调: 订单=${orderNo}, 支付ID=${paymentId}`);
        await this.dataSource.transaction(async (manager) => {
            try {
                const packageOrder = await manager.findOne(package_order_entity_1.PackageOrder, {
                    where: { orderNo }
                });
                if (!packageOrder) {
                    throw new common_1.NotFoundException(`订单编号为${orderNo}的信息不存在`);
                }
                if (packageOrder.status === dto_2.PackageOrderStatus.PAID) {
                    this.logger.warn(`订单 ${orderNo} 已经是已支付状态，跳过处理`);
                    return;
                }
                await manager.update(package_order_entity_1.PackageOrder, { orderNo }, {
                    status: dto_2.PackageOrderStatus.PAID,
                    paymentId,
                    paidTime: new Date(),
                });
                this.logger.log(`更新订单状态成功: ${orderNo}`);
                await this.assignPackageToUser(manager, packageOrder);
                this.logger.log(`支付成功处理完成: ${orderNo}`);
            }
            catch (error) {
                this.logger.error(`处理支付成功回调失败: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    async assignPackageToUser(manager, packageOrder) {
        const { userId, packageId, packageName, points, validityDays } = packageOrder;
        this.logger.log(`开始分配套餐给用户: 用户=${userId}, 套餐=${packageName}`);
        const now = new Date();
        const expireTime = moment(now).add(validityDays, 'days').toDate();
        const userPackage = manager.create(user_package_entity_1.UserPackage, {
            userId: parseInt(userId),
            packageId,
            points,
            startTime: now,
            expireTime,
            status: 1,
            assignType: 1,
            remark: `购买套餐获得：${packageName}`,
        });
        await manager.save(user_package_entity_1.UserPackage, userPackage);
        this.logger.log(`创建用户套餐记录成功`);
        await manager.update(user_info_entity_1.UserInfo, { id: parseInt(userId) }, {
            points: () => `points + ${points}`,
            pointsExpireTime: expireTime,
        });
        await this.userPointsService.create({
            userId: parseInt(userId),
            pointsValue: points,
            totalPoints: Number((await manager.findOne(user_info_entity_1.UserInfo, { where: { id: parseInt(userId) } }))?.points || 0),
            type: 1,
            source: 1,
            remark: `购买套餐获得积分：${packageName}`,
            operator: 'system',
        });
        this.logger.log(`分配套餐完成: 用户=${userId}, 套餐=${packageName}, 积分=${points}`);
    }
    async getUserOrders(userId, page = 1, limit = 10) {
        return await this.packageOrderService.findWithPagination({
            userId,
            page,
            limit,
            sortBy: 'createTime',
            sortOrder: 'DESC',
        });
    }
    async cancelOrder(userId, orderNo) {
        try {
            const order = await this.packageOrderService.findByOrderNo(orderNo);
            if (order.userId !== userId) {
                throw new common_1.BadRequestException('无权操作此订单');
            }
            if (order.status === dto_2.PackageOrderStatus.PAID) {
                throw new common_1.BadRequestException('已支付的订单无法取消');
            }
            if (order.status === dto_2.PackageOrderStatus.CANCELLED) {
                this.logger.warn(`订单已经是取消状态: ${orderNo}`);
                return order;
            }
            this.logger.log(`开始取消订单: ${orderNo}, 用户: ${userId}`);
            const cancelledOrder = await this.packageOrderService.cancelOrder(orderNo);
            this.logger.log(`套餐订单取消成功: ${orderNo}`);
            try {
                const paymentOrders = await this.paymentOrderService.findAll();
                const matchingPaymentOrder = paymentOrders.find(paymentOrder => paymentOrder.parameters &&
                    paymentOrder.parameters.packageOrderNo === orderNo);
                if (matchingPaymentOrder) {
                    this.logger.log(`找到对应的支付订单: ${matchingPaymentOrder.businessOrderId}, 开始关闭支付订单`);
                    const closeResult = await this.paymentService.closePayment(matchingPaymentOrder.businessOrderId, `用户取消套餐订单: ${orderNo}`);
                    if (closeResult) {
                        this.logger.log(`支付订单关闭成功: ${matchingPaymentOrder.businessOrderId}`);
                    }
                    else {
                        this.logger.warn(`支付订单关闭失败: ${matchingPaymentOrder.businessOrderId}`);
                    }
                }
                else {
                    this.logger.warn(`未找到对应的支付订单: 套餐订单=${orderNo}`);
                }
            }
            catch (paymentError) {
                this.logger.error(`关闭支付订单时发生错误: ${paymentError.message}`, paymentError.stack);
                this.logger.warn(`支付订单关闭失败，但套餐订单已成功取消: ${orderNo}`);
            }
            this.logger.log(`订单取消完成: ${orderNo}`);
            return cancelledOrder;
        }
        catch (error) {
            this.logger.error(`取消订单失败: ${orderNo}, 错误: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getOrderDetail(userId, orderNo) {
        const order = await this.packageOrderService.findByOrderNo(orderNo);
        if (order.userId !== userId) {
            throw new common_1.BadRequestException('无权查看此订单');
        }
        return order;
    }
    async getOrderStatus(orderNo) {
        try {
            const packageOrder = await this.dataSource.getRepository(package_order_entity_1.PackageOrder).findOne({
                where: { orderNo }
            });
            if (!packageOrder) {
                throw new common_1.NotFoundException(`套餐订单 ${orderNo} 不存在`);
            }
            return {
                orderNo: packageOrder.orderNo,
                userId: packageOrder.userId,
                status: packageOrder.status,
                packageName: packageOrder.packageName,
                points: Number(packageOrder.points),
                amount: Number(packageOrder.price),
                paidTime: packageOrder.paidTime,
                createTime: packageOrder.createTime,
            };
        }
        catch (error) {
            this.logger.error(`查询套餐订单状态失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    mapPaymentChannel(channel) {
        switch (channel) {
            case dto_1.PaymentChannel.ALIPAY:
                return payment_request_dto_1.PaymentChannel.ALIPAY;
            case dto_1.PaymentChannel.WECHATPAY:
                return payment_request_dto_1.PaymentChannel.WECHATPAY;
            default:
                throw new common_1.BadRequestException(`不支持的支付渠道: ${channel}`);
        }
    }
    mapPaymentMode(mode) {
        switch (mode) {
            case dto_1.PaymentMode.REDIRECT:
                return payment_request_dto_1.PaymentMode.REDIRECT;
            case dto_1.PaymentMode.QR_CODE:
                return payment_request_dto_1.PaymentMode.QR_CODE;
            default:
                throw new common_1.BadRequestException(`不支持的支付方式: ${mode}`);
        }
    }
    generateOrderNo() {
        const timestamp = moment().format('YYYYMMDDHHmmss');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `PKG${timestamp}${random}`;
    }
};
exports.PackageOrderBusinessService = PackageOrderBusinessService;
exports.PackageOrderBusinessService = PackageOrderBusinessService = PackageOrderBusinessService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(9, (0, typeorm_1.InjectRepository)(package_order_entity_1.PackageOrder)),
    __param(10, (0, typeorm_1.InjectRepository)(user_package_entity_1.UserPackage)),
    __param(11, (0, typeorm_1.InjectRepository)(user_info_entity_1.UserInfo)),
    __metadata("design:paramtypes", [typeorm_2.DataSource,
        package_order_service_1.PackageOrderService,
        package_info_service_1.PackageInfoService,
        user_info_service_1.UserInfoService,
        user_package_service_1.UserPackageService,
        user_points_service_1.UserPointsService,
        payment_service_1.PaymentService,
        package_pricing_service_1.PackagePricingService,
        payment_order_service_1.PaymentOrderService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], PackageOrderBusinessService);
//# sourceMappingURL=package-order-business.service.js.map