"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SelfAssessmentItemModule = void 0;
const common_1 = require("@nestjs/common");
const self_assessment_item_controller_1 = require("./self_assessment_item.controller");
const task_self_assessment_item_service_1 = require("../../util/database/mysql/task_self_assessment_item/task_self_assessment_item.service");
const typeorm_1 = require("@nestjs/typeorm");
const task_self_assessment_item_entity_1 = require("../../util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
let SelfAssessmentItemModule = class SelfAssessmentItemModule {
};
exports.SelfAssessmentItemModule = SelfAssessmentItemModule;
exports.SelfAssessmentItemModule = SelfAssessmentItemModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([task_self_assessment_item_entity_1.TaskSelfAssessmentItem]),
        ],
        controllers: [self_assessment_item_controller_1.SelfAssessmentItemController],
        providers: [task_self_assessment_item_service_1.TaskSelfAssessmentItemService, http_response_result_service_1.HttpResponseResultService],
        exports: [task_self_assessment_item_service_1.TaskSelfAssessmentItemService]
    })
], SelfAssessmentItemModule);
//# sourceMappingURL=self_assessment_item.module.js.map