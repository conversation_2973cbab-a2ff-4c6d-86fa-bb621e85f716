import { TeacherAuditService } from './teacher_audit.service';
import { CreateTeacherAuditDto } from './dto/create-teacher_audit.dto';
import { UpdateTeacherAuditDto } from './dto/update-teacher_audit.dto';
import { AttachmentService } from '../attachment/attachment.service';
import { Attachment } from '../attachment/entities/attachment.entity';
import { TeacherAuditAttachmentService } from '../teacher_audit_attachment/teacher_audit_attachment.service';
import { AuditResult, TeacherAudit } from './entities/teacher_audit.entity';
import { UserSchoolRelationService } from '../user_school_relation/user_school_relation.service';
import { UserSchoolService } from '../user_school/user_school.service';
import { UserInfoService } from '../user_info/user_info.service';
export declare class TeacherAuditController {
    private readonly teacherAuditService;
    private readonly attachmentService;
    private readonly teacherAuditAttachmentService;
    private readonly userSchoolRelationService;
    private readonly userSchoolService;
    private readonly userInfoService;
    constructor(teacherAuditService: TeacherAuditService, attachmentService: AttachmentService, teacherAuditAttachmentService: TeacherAuditAttachmentService, userSchoolRelationService: UserSchoolRelationService, userSchoolService: UserSchoolService, userInfoService: UserInfoService);
    create(createTeacherAuditDto: CreateTeacherAuditDto, req: any): Promise<{
        code: number;
        message: string;
        data: {
            teacherAudit: TeacherAudit;
            attachments: Attachment[];
        };
    }>;
    findAll(): Promise<TeacherAudit[]>;
    findTeacherAudit(req: any, status?: number, userId?: number): Promise<{
        attachments: Attachment[];
        id: number;
        teacherId: number;
        teacherName: string;
        auditorId: number;
        schoolInfo: string;
        auditorName: string;
        result: AuditResult;
        reason: string;
        beforeStatus: string;
        afterStatus: string;
        createTime: Date;
        updateTime: Date;
        operationIp: string;
        deviceInfo: string;
        isDelete: number;
        teacher: import("../../util/database/mysql/user_info/entities/user_info.entity").UserInfo;
        auditor: import("../../util/database/mysql/user_info/entities/user_info.entity").UserInfo;
    }[]>;
    findCurrentTeacherAudit(req: any): Promise<TeacherAudit | null>;
    findOne(id: string): Promise<{
        attachments: Attachment[];
        teacher: {
            id: number;
            nickName: string;
            avatarUrl: string;
            phone: string;
        } | null;
        auditor: {
            id: number;
            nickName: string;
            avatarUrl: string;
            phone: string;
        } | null;
        id: number;
        teacherId: number;
        teacherName: string;
        auditorId: number;
        schoolInfo: string;
        auditorName: string;
        result: AuditResult;
        reason: string;
        beforeStatus: string;
        afterStatus: string;
        createTime: Date;
        updateTime: Date;
        operationIp: string;
        deviceInfo: string;
        isDelete: number;
    }>;
    update(id: string, updateTeacherAuditDto: UpdateTeacherAuditDto, req: any): Promise<TeacherAudit>;
    remove(id: string): Promise<void>;
    getTeacherAuditDetail(id: string): Promise<{
        attachments: Attachment[];
        teacher: {
            id: number;
            nickName: string;
            avatarUrl: string;
            phone: string;
        } | null;
        auditor: {
            id: number;
            nickName: string;
            avatarUrl: string;
            phone: string;
        } | null;
        id: number;
        teacherId: number;
        teacherName: string;
        auditorId: number;
        schoolInfo: string;
        auditorName: string;
        result: AuditResult;
        reason: string;
        beforeStatus: string;
        afterStatus: string;
        createTime: Date;
        updateTime: Date;
        operationIp: string;
        deviceInfo: string;
        isDelete: number;
    }>;
    findByName(name: string): Promise<TeacherAudit[]>;
    findByUserId(userId: string): Promise<TeacherAudit[]>;
    findByNamePattern(pattern: string): Promise<TeacherAudit[]>;
    reviewTeacherAudit(reviewDto: {
        id: number;
        userId: number;
        result: number;
        reason?: string;
    }, req: any): Promise<{
        message: string;
        id: number;
        teacherId: number;
        teacherName: string;
        auditorId: number;
        schoolInfo: string;
        auditorName: string;
        result: AuditResult;
        reason: string;
        beforeStatus: string;
        afterStatus: string;
        createTime: Date;
        updateTime: Date;
        operationIp: string;
        deviceInfo: string;
        isDelete: number;
        teacher: import("../../util/database/mysql/user_info/entities/user_info.entity").UserInfo;
        auditor: import("../../util/database/mysql/user_info/entities/user_info.entity").UserInfo;
    }>;
}
