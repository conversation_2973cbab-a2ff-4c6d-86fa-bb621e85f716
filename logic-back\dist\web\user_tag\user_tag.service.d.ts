import { TagService } from 'src/util/database/mysql/tag/tag.service';
import { Tag } from 'src/util/database/mysql/tag/entities/tag.entity';
export declare class UserTagService {
    private readonly tagService;
    constructor(tagService: TagService);
    create(data: {
        name: string;
        description?: string;
        color?: string;
    }): Promise<Tag>;
    updateTag(id: number, data: {
        name?: string;
        description?: string;
        color?: string;
    }): Promise<Tag>;
    deleteTag(id: number): Promise<void>;
    getById(id: number): Promise<Tag>;
    list(params: {
        page?: number;
        size?: number;
        keyword?: string;
    }): Promise<{
        list: Tag[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
}
