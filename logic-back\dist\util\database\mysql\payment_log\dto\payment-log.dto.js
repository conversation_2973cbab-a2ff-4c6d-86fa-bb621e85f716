"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryPaymentLogDto = exports.CreatePaymentLogDto = exports.OperationType = exports.LogStatus = exports.LogType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var LogType;
(function (LogType) {
    LogType["PAYMENT"] = "payment";
    LogType["REFUND"] = "refund";
    LogType["NOTIFICATION"] = "notification";
    LogType["SYSTEM"] = "system";
})(LogType || (exports.LogType = LogType = {}));
var LogStatus;
(function (LogStatus) {
    LogStatus["SUCCESS"] = "success";
    LogStatus["FAIL"] = "fail";
})(LogStatus || (exports.LogStatus = LogStatus = {}));
var OperationType;
(function (OperationType) {
    OperationType["CREATE"] = "create";
    OperationType["QUERY"] = "query";
    OperationType["UPDATE"] = "update";
    OperationType["DELETE"] = "delete";
    OperationType["NOTIFY"] = "notify";
    OperationType["CALLBACK"] = "callback";
})(OperationType || (exports.OperationType = OperationType = {}));
class CreatePaymentLogDto {
    logType;
    orderNo;
    refundNo;
    paymentChannel;
    operation;
    operatorId;
    clientIp;
    requestData;
    responseData;
    status = LogStatus.SUCCESS;
    errorMessage;
    executionTime;
}
exports.CreatePaymentLogDto = CreatePaymentLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '日志类型', enum: LogType }),
    (0, class_validator_1.IsEnum)(LogType),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "logType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单编号', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "orderNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款编号', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "refundNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "paymentChannel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作类型', enum: OperationType }),
    (0, class_validator_1.IsEnum)(OperationType),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "operation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作人ID', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "operatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '客户端IP', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "clientIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '请求数据', required: false }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreatePaymentLogDto.prototype, "requestData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应数据', required: false }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreatePaymentLogDto.prototype, "responseData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态', enum: LogStatus, default: LogStatus.SUCCESS }),
    (0, class_validator_1.IsEnum)(LogStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '错误信息', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePaymentLogDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '执行时间(ms)', required: false }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreatePaymentLogDto.prototype, "executionTime", void 0);
class QueryPaymentLogDto {
    logType;
    orderNo;
    refundNo;
    paymentChannel;
    operation;
    operatorId;
    status;
    startTime;
    endTime;
}
exports.QueryPaymentLogDto = QueryPaymentLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '日志类型', enum: LogType, required: false }),
    (0, class_validator_1.IsEnum)(LogType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "logType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单编号', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "orderNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款编号', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "refundNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "paymentChannel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作类型', enum: OperationType, required: false }),
    (0, class_validator_1.IsEnum)(OperationType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "operation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作人ID', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "operatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态', enum: LogStatus, required: false }),
    (0, class_validator_1.IsEnum)(LogStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开始时间', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '结束时间', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QueryPaymentLogDto.prototype, "endTime", void 0);
//# sourceMappingURL=payment-log.dto.js.map