{"version": 3, "file": "geographic-location.vo.js", "sourceRoot": "", "sources": ["../../../../../src/util/ip_location/domain/value-objects/geographic-location.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,kBAAkB;IACZ,QAAQ,CAAS;IACjB,SAAS,CAAS;IAClB,KAAK,CAAS;IACd,IAAI,CAAS;IACb,WAAW,CAA2B;IACtC,WAAW,CAAS;IAErC,YACE,OAAe,EACf,QAAgB,EAChB,IAAY,EACZ,GAAW,EACX,aAAuC,WAAW,EAClD,aAAqB,GAAG;QAExB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;IAC5D,CAAC;IAKD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAKD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAKD,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAKD,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAKD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;IAChC,CAAC;IAKD,IAAI,SAAS;QACX,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;IACpD,CAAC;IAKD,IAAI,cAAc;QAChB,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;aAC1D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;IACnD,CAAC;IAKD,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;IACxD,CAAC;IAKD,IAAI,eAAe;QACjB,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC;aAC1D,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;IAC5D,CAAC;IAKD,IAAI,WAAW;QACb,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;YACnD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE,CAAC;YACrD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9E,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrD,CAAC;IAKD,IAAI,eAAe;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,OAAO,GAAG,QAAQ,GAAG,OAAO,EAAE,CAAC;IACjC,CAAC;IAKD,cAAc,CAAC,KAAyB;QACtC,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI;YACvB,KAAK,CAAC,SAAS,KAAK,IAAI;YACxB,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC;IAC5C,CAAC;IAKD,UAAU,CAAC,KAAyB;QAClC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,KAAK,KAAK,IAAI;YACnB,KAAK,CAAC,KAAK,KAAK,IAAI;YACpB,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;IAKD,SAAS,CAAC,KAAyB;QACjC,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;YAClB,KAAK,CAAC,IAAI,KAAK,IAAI;YACnB,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;IAClC,CAAC;IAKD,mBAAmB,CAAC,KAAyB;QAC3C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,QAAQ,GAAG,CAAC,CAAC;QAGjB,QAAQ,IAAI,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC/D,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAGD,QAAQ,IAAI,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YAClE,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAGD,QAAQ,IAAI,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YACtD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAGD,QAAQ,IAAI,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YACnD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAKO,sBAAsB,CAAC,KAAa;QAC1C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAKD,MAAM,CAAC,MAAM,CACX,OAAe,EACf,QAAgB,EAChB,IAAY,EACZ,GAAW,EACX,aAAuC,WAAW,EAClD,aAAqB,GAAG;QAExB,OAAO,IAAI,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACtF,CAAC;IAKD,MAAM,CAAC,aAAa;QAClB,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IACvE,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,OAAY;QACnC,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC;aAC/E,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QAEnE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,OAAO,IAAI,kBAAkB,CAC3B,OAAO,CAAC,OAAO,IAAI,IAAI,EACvB,OAAO,CAAC,QAAQ,IAAI,IAAI,EACxB,OAAO,CAAC,IAAI,IAAI,IAAI,EACpB,OAAO,CAAC,GAAG,IAAI,IAAI,EACnB,WAAW,EACX,UAAU,CACX,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,KAAyB;QAC9B,OAAO,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAChC,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS;YAClC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK;YAC1B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;IAClC,CAAC;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAKD,MAAM;QACJ,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,UAAU,EAAE,IAAI,CAAC,WAAW;YAC5B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;CACF;AAzRD,gDAyRC"}