import { Injectable } from '@nestjs/common';
import { IpAddress } from '../value-objects/ip-address.vo';
import { GeographicLocation } from '../value-objects/geographic-location.vo';
import { RiskScore } from '../value-objects/risk-score.vo';

/**
 * 用户位置历史接口
 */
export interface UserLocationHistory {
  commonLocations: Array<{
    location: GeographicLocation;
    loginCount: number;
    isTrusted: boolean;
    lastLoginAt: Date;
    firstLoginAt: Date;
  }>;
  totalLoginCount: number;
  riskLoginCount: number;
  accountAge: number; // 账户年龄（天）
}

/**
 * 风险评估配置接口
 */
export interface RiskAssessmentConfig {
  // 风险等级阈值
  lowThreshold: number;
  mediumThreshold: number;
  highThreshold: number;
  
  // 风险因子权重
  foreignLoginWeight: number;
  crossProvinceWeight: number;
  newLocationWeight: number;
  ispChangeWeight: number;
  dataQualityWeight: number;
  
  // 用户保护配置
  newUserProtectionDays: number;
  newUserRiskReduction: number;
  
  // 数据质量要求
  minDataQuality: number;
}

/**
 * 风险评估领域服务
 * 封装登录风险评估的核心业务逻辑
 */
@Injectable()
export class RiskAssessmentDomainService {
  private readonly defaultConfig: RiskAssessmentConfig = {
    lowThreshold: 30,
    mediumThreshold: 70,
    highThreshold: 100,
    foreignLoginWeight: 60,
    crossProvinceWeight: 40,
    newLocationWeight: 15,
    ispChangeWeight: 5,
    dataQualityWeight: 20,
    newUserProtectionDays: 7,
    newUserRiskReduction: 15,
    minDataQuality: 20
  };

  private config: RiskAssessmentConfig;

  constructor(config?: Partial<RiskAssessmentConfig>) {
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 评估登录风险
   * @param currentLocation 当前登录位置
   * @param userHistory 用户历史位置信息
   * @param ipAddress 当前IP地址
   * @returns 风险评分值对象
   */
  assessLoginRisk(
    currentLocation: GeographicLocation,
    userHistory: UserLocationHistory,
    ipAddress: IpAddress
  ): RiskScore {
    const riskFactors: string[] = [];
    let riskScore = 0;

    // 1. 境外登录检查 - 最高风险
    if (currentLocation.isForeign) {
      riskScore += this.config.foreignLoginWeight;
      riskFactors.push('境外登录');
    }

    // 2. 数据质量检查
    if (currentLocation.confidence < this.config.minDataQuality) {
      riskScore += this.config.dataQualityWeight;
      riskFactors.push('位置信息不完整');
    }

    // 3. 省份级别检查 - 主要风险判断依据
    const hasProvinceHistory = this.hasLocationInHistory(
      currentLocation, 
      userHistory, 
      'province'
    );

    if (!hasProvinceHistory && currentLocation.isDomestic && currentLocation.province !== '未知') {
      riskScore += this.config.crossProvinceWeight;
      riskFactors.push(`跨省登录至${currentLocation.province}`);
    }

    // 4. 城市级别检查 - 辅助判断
    if (hasProvinceHistory) {
      const hasCityHistory = this.hasLocationInHistory(
        currentLocation, 
        userHistory, 
        'city'
      );

      if (!hasCityHistory && currentLocation.city !== '未知') {
        riskScore += this.config.newLocationWeight;
        riskFactors.push(`省内异地登录至${currentLocation.city}`);
      }
    }

    // 5. 运营商变化检查
    if (hasProvinceHistory && !this.hasISPInHistory(currentLocation, userHistory)) {
      riskScore += this.config.ispChangeWeight;
      riskFactors.push('运营商变化');
    }

    // 6. 登录频率影响
    if (userHistory.riskLoginCount > 5) {
      riskScore += 10;
      riskFactors.push('频繁异地登录');
    }

    // 7. 新用户保护
    if (this.isNewUser(userHistory)) {
      riskScore = Math.max(riskScore - this.config.newUserRiskReduction, 0);
      riskFactors.push('新用户保护');
    }

    // 8. IP地址类型风险
    if (ipAddress.type === 'IPv6' && !hasProvinceHistory) {
      riskScore += 5;
      riskFactors.push('IPv6新位置');
    }

    // 确保评分在有效范围内
    const finalScore = Math.min(Math.max(riskScore, 0), 100);

    return RiskScore.create(finalScore, riskFactors);
  }

  /**
   * 评估位置变化风险
   * @param previousLocation 上次登录位置
   * @param currentLocation 当前登录位置
   * @returns 位置变化风险评分
   */
  assessLocationChangeRisk(
    previousLocation: GeographicLocation,
    currentLocation: GeographicLocation
  ): RiskScore {
    const riskFactors: string[] = [];
    let riskScore = 0;

    // 计算位置相似度
    const similarity = currentLocation.calculateSimilarity(previousLocation);

    if (similarity < 20) {
      riskScore += 50;
      riskFactors.push('位置完全不同');
    } else if (similarity < 50) {
      riskScore += 30;
      riskFactors.push('位置差异较大');
    } else if (similarity < 80) {
      riskScore += 10;
      riskFactors.push('位置有所变化');
    }

    // 国家变化
    if (previousLocation.country !== currentLocation.country) {
      riskScore += 40;
      riskFactors.push('跨国登录');
    }

    // 省份变化
    if (!currentLocation.isSameProvince(previousLocation)) {
      riskScore += 25;
      riskFactors.push('跨省登录');
    }

    // 城市变化
    if (!currentLocation.isSameCity(previousLocation)) {
      riskScore += 10;
      riskFactors.push('跨城市登录');
    }

    return RiskScore.create(riskScore, riskFactors);
  }

  /**
   * 检查是否需要额外验证
   * @param riskScore 风险评分
   * @param userHistory 用户历史
   * @returns 验证需求分析
   */
  analyzeVerificationNeeds(
    riskScore: RiskScore,
    userHistory: UserLocationHistory
  ): {
    needsVerification: boolean;
    verificationMethods: string[];
    reason: string;
    urgency: 'LOW' | 'MEDIUM' | 'HIGH';
  } {
    let needsVerification = riskScore.needsVerification;
    let urgency: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';

    // 高风险必须验证
    if (riskScore.isHighRisk) {
      needsVerification = true;
      urgency = 'HIGH';
    }

    // 中风险且新用户需要验证
    if (riskScore.isMediumRisk && this.isNewUser(userHistory)) {
      needsVerification = true;
      urgency = 'MEDIUM';
    }

    // 包含境外登录因素需要验证
    if (riskScore.factors.includes('境外登录')) {
      needsVerification = true;
      urgency = 'HIGH';
    }

    return {
      needsVerification,
      verificationMethods: riskScore.recommendedVerificationMethods,
      reason: riskScore.reason,
      urgency
    };
  }

  /**
   * 计算用户的整体风险档案
   * @param userHistory 用户历史
   * @returns 用户风险档案
   */
  calculateUserRiskProfile(userHistory: UserLocationHistory): {
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    trustScore: number;
    characteristics: string[];
    recommendations: string[];
  } {
    const characteristics: string[] = [];
    const recommendations: string[] = [];
    let trustScore = 50; // 基础信任分

    // 账户年龄影响
    if (userHistory.accountAge > 365) {
      trustScore += 20;
      characteristics.push('老用户');
    } else if (userHistory.accountAge < 30) {
      trustScore -= 10;
      characteristics.push('新用户');
      recommendations.push('建议增强验证');
    }

    // 登录频率影响
    const riskRate = userHistory.totalLoginCount > 0 
      ? (userHistory.riskLoginCount / userHistory.totalLoginCount) * 100 
      : 0;

    if (riskRate > 20) {
      trustScore -= 15;
      characteristics.push('频繁异地登录');
      recommendations.push('建议设置常用登录地');
    } else if (riskRate < 5) {
      trustScore += 10;
      characteristics.push('登录位置稳定');
    }

    // 可信位置数量
    const trustedLocationCount = userHistory.commonLocations
      .filter(loc => loc.isTrusted).length;

    if (trustedLocationCount > 0) {
      trustScore += trustedLocationCount * 5;
      characteristics.push(`${trustedLocationCount}个可信位置`);
    } else {
      recommendations.push('建议设置可信登录地');
    }

    // 确定风险等级
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    if (trustScore >= 70) {
      riskLevel = 'LOW';
    } else if (trustScore >= 40) {
      riskLevel = 'MEDIUM';
    } else {
      riskLevel = 'HIGH';
      recommendations.push('建议完善账户安全设置');
    }

    return {
      riskLevel,
      trustScore: Math.max(0, Math.min(100, trustScore)),
      characteristics,
      recommendations
    };
  }

  /**
   * 检查位置是否在用户历史中
   */
  private hasLocationInHistory(
    location: GeographicLocation,
    userHistory: UserLocationHistory,
    level: 'country' | 'province' | 'city'
  ): boolean {
    return userHistory.commonLocations.some(historyItem => {
      switch (level) {
        case 'country':
          return historyItem.location.country === location.country && 
                 location.country !== '未知';
        case 'province':
          return historyItem.location.isSameProvince(location);
        case 'city':
          return historyItem.location.isSameCity(location);
        default:
          return false;
      }
    });
  }

  /**
   * 检查ISP是否在用户历史中
   */
  private hasISPInHistory(
    location: GeographicLocation,
    userHistory: UserLocationHistory
  ): boolean {
    return userHistory.commonLocations.some(historyItem =>
      historyItem.location.isSameProvince(location) &&
      historyItem.location.isSameISP(location)
    );
  }

  /**
   * 检查是否为新用户
   */
  private isNewUser(userHistory: UserLocationHistory): boolean {
    return userHistory.totalLoginCount < 3 || 
           userHistory.accountAge < this.config.newUserProtectionDays;
  }

  /**
   * 更新风险评估配置
   */
  updateConfig(newConfig: Partial<RiskAssessmentConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): RiskAssessmentConfig {
    return { ...this.config };
  }
}
