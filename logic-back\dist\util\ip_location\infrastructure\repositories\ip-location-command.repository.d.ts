import { Repository } from 'typeorm';
import { IIpLocationCommandRepository } from './ip-location.repository.interface';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';
export declare class IpLocationCommandRepository implements IIpLocationCommandRepository {
    private readonly repository;
    constructor(repository: Repository<UserCommonLocation>);
    findUserCommonLocation(userId: number, location: GeographicLocation): Promise<UserCommonLocation | null>;
    findUserCommonLocations(userId: number, options?: {
        limit?: number;
        onlyTrusted?: boolean;
        minLoginCount?: number;
        orderBy?: 'loginCount' | 'lastLoginAt' | 'trustScore';
        orderDirection?: 'ASC' | 'DESC';
    }): Promise<UserCommonLocation[]>;
    findUserLocationsByDateRange(userId: number, startDate: Date, endDate: Date, options?: {
        minLoginCount?: number;
        includeTrusted?: boolean;
    }): Promise<UserCommonLocation[]>;
    saveUserCommonLocation(userLocation: UserCommonLocation): Promise<UserCommonLocation>;
    createUserCommonLocation(userId: number, location: GeographicLocation, initialData?: Partial<UserCommonLocation>): Promise<UserCommonLocation>;
    incrementLoginCount(userLocationId: number, incrementBy?: number): Promise<UserCommonLocation>;
    updateTrustedLocations(userId: number, province: string, city: string, isTrusted: boolean, trustReason?: string): Promise<number>;
    updateTrustScore(userLocationId: number, newTrustScore: number): Promise<UserCommonLocation>;
    deleteUserCommonLocation(userLocationId: number): Promise<boolean>;
    getUserLocationStats(userId: number, days: number): Promise<{
        totalLocations: number;
        trustedLocations: number;
        totalLogins: number;
        riskLogins: number;
        uniqueProvinces: number;
        uniqueCities: number;
    }>;
    hasLocationInHistory(userId: number, location: GeographicLocation, level: 'country' | 'province' | 'city'): Promise<boolean>;
    private calculateInitialTrustScore;
}
