"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentRefundDto = exports.RefundStatus = exports.PaymentChannel = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var PaymentChannel;
(function (PaymentChannel) {
    PaymentChannel["ALIPAY"] = "alipay";
    PaymentChannel["WECHAT"] = "wechat";
    PaymentChannel["UNIONPAY"] = "unionpay";
})(PaymentChannel || (exports.PaymentChannel = PaymentChannel = {}));
var RefundStatus;
(function (RefundStatus) {
    RefundStatus["PENDING"] = "pending";
    RefundStatus["PROCESSING"] = "processing";
    RefundStatus["SUCCESS"] = "success";
    RefundStatus["FAILED"] = "failed";
})(RefundStatus || (exports.RefundStatus = RefundStatus = {}));
class CreatePaymentRefundDto {
    paymentOrderId;
    businessRefundId;
    amount;
    channel;
    channelRefundId;
    reason;
    status;
    parameters;
    userId;
    operatorId;
    notifyUrl;
}
exports.CreatePaymentRefundDto = CreatePaymentRefundDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付订单ID', example: 'payment_order_uuid' }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付订单ID不能为空' }),
    (0, class_validator_1.IsString)({ message: '支付订单ID必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "paymentOrderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '业务退款单号', example: 'REFUND_2023101010001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '业务退款单号不能为空' }),
    (0, class_validator_1.IsString)({ message: '业务退款单号必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "businessRefundId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款金额', example: 99.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '退款金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '退款金额必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '退款金额必须大于0' }),
    __metadata("design:type", Number)
], CreatePaymentRefundDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付渠道不能为空' }),
    (0, class_validator_1.IsEnum)(PaymentChannel, { message: '无效的支付渠道' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '渠道退款单号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '渠道退款单号必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "channelRefundId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款原因', required: false, example: '客户取消订单' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '退款原因必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款状态', enum: RefundStatus, default: RefundStatus.PENDING, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(RefundStatus, { message: '无效的退款状态' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款参数', required: false, type: Object }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: '退款参数必须是对象' }),
    __metadata("design:type", Object)
], CreatePaymentRefundDto.prototype, "parameters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户ID必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作人ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '操作人ID必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "operatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '退款通知回调URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: '退款通知回调URL格式不正确' }),
    __metadata("design:type", String)
], CreatePaymentRefundDto.prototype, "notifyUrl", void 0);
//# sourceMappingURL=create-payment-refund.dto.js.map