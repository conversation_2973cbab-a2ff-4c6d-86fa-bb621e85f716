"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackageOrderBusinessController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const package_order_business_service_1 = require("./package-order-business.service");
const dto_1 = require("./dto");
const not_login_decorator_1 = require("../router_guard/not-login.decorator");
const encrypt_decorator_1 = require("../../util/encrypt/encrypt.decorator");
let PackageOrderBusinessController = class PackageOrderBusinessController {
    packageOrderBusinessService;
    constructor(packageOrderBusinessService) {
        this.packageOrderBusinessService = packageOrderBusinessService;
    }
    async purchasePackage(req, purchaseDto) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId) {
            return {
                code: 401,
                message: '用户未登录',
                data: null
            };
        }
        try {
            const result = await this.packageOrderBusinessService.purchasePackage(userId.toString(), purchaseDto);
            return {
                code: 200,
                message: '购买成功',
                data: result
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '购买失败',
                data: null
            };
        }
    }
    async paymentCallback(callbackDto) {
        try {
            await this.packageOrderBusinessService.handlePaymentSuccess(callbackDto);
            return {
                code: 200,
                message: '支付回调处理成功',
                data: null
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '支付回调处理失败',
                data: null
            };
        }
    }
    async getOrderStatus(orderNo) {
        try {
            const orderStatus = await this.packageOrderBusinessService.getOrderStatus(orderNo);
            return {
                code: 200,
                message: '查询成功',
                data: orderStatus
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '查询失败',
                data: null
            };
        }
    }
    async getMyOrders(req, page = 1, limit = 10) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId) {
            return {
                code: 401,
                message: '用户未登录',
                data: null
            };
        }
        try {
            const result = await this.packageOrderBusinessService.getUserOrders(userId.toString(), Number(page), Number(limit));
            return {
                code: 200,
                message: '获取成功',
                data: result
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '获取失败',
                data: null
            };
        }
    }
    async cancelOrder(req, orderNo) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId) {
            return {
                code: 401,
                message: '用户未登录',
                data: null
            };
        }
        try {
            const result = await this.packageOrderBusinessService.cancelOrder(userId.toString(), orderNo);
            return {
                code: 200,
                message: '取消成功',
                data: result
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '取消失败',
                data: null
            };
        }
    }
    async getPackages() {
        try {
            const result = await this.packageOrderBusinessService.getAvailablePackages();
            return {
                code: 200,
                message: '获取成功',
                data: result
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '获取失败',
                data: null
            };
        }
    }
    async getOrderDetail(req, orderNo) {
        const userId = req.user?.id || req.user?.userId;
        if (!userId) {
            return {
                code: 401,
                message: '用户未登录',
                data: null
            };
        }
        try {
            const result = await this.packageOrderBusinessService.getOrderDetail(userId.toString(), orderNo);
            return {
                code: 200,
                message: '获取成功',
                data: result
            };
        }
        catch (error) {
            return {
                code: 400,
                message: error.message || '获取失败',
                data: null
            };
        }
    }
};
exports.PackageOrderBusinessController = PackageOrderBusinessController;
__decorate([
    (0, common_1.Post)('purchase'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '购买套餐' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '购买成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '购买成功' },
                data: {
                    type: 'object',
                    properties: {
                        orderNo: { type: 'string', description: '订单编号' },
                        paymentUrl: { type: 'string', description: '支付链接' },
                        qrCode: { type: 'string', description: '支付二维码' },
                        amount: { type: 'number', description: '支付金额' },
                        packageName: { type: 'string', description: '套餐名称' },
                        expireTime: { type: 'string', description: '订单过期时间' }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '请求参数错误' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '套餐不存在' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.PurchasePackageDto]),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "purchasePackage", null);
__decorate([
    (0, common_1.Post)('payment-callback'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, not_login_decorator_1.NotLogin)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '支付成功回调' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '回调处理成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '回调处理失败' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.PaymentCallbackDto]),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "paymentCallback", null);
__decorate([
    (0, common_1.Get)('order-status/:orderNo'),
    (0, encrypt_decorator_1.SecureEncrypt)(),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiOperation)({ summary: '查询套餐订单状态' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '套餐订单号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '查询成功' }),
    __param(0, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "getOrderStatus", null);
__decorate([
    (0, common_1.Get)('my-orders'),
    (0, encrypt_decorator_1.SecureEncrypt)(),
    (0, swagger_1.ApiOperation)({ summary: '获取我的套餐订单列表' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: '页码', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '每页数量', example: 10 }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '获取成功' },
                data: {
                    type: 'object',
                    properties: {
                        data: { type: 'array', description: '订单列表' },
                        total: { type: 'number', description: '总数量' },
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "getMyOrders", null);
__decorate([
    (0, common_1.Post)('cancel/:orderNo'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '取消订单' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '取消成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '取消失败' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "cancelOrder", null);
__decorate([
    (0, common_1.Get)('packages'),
    (0, encrypt_decorator_1.SecureEncrypt)(),
    (0, swagger_1.ApiOperation)({ summary: '获取套餐价格表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '获取成功' },
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            packageId: { type: 'number', description: '套餐ID' },
                            packageName: { type: 'string', description: '套餐名称' },
                            packageDescription: { type: 'string', description: '套餐描述' },
                            points: { type: 'number', description: '点数' },
                            validityDays: { type: 'number', description: '有效期(天)' },
                            originalPrice: { type: 'number', description: '原价' },
                            currentPrice: { type: 'number', description: '现价' },
                            discountRate: { type: 'number', description: '折扣率' },
                            savings: { type: 'number', description: '节省金额' },
                            currency: { type: 'string', description: '货币类型' },
                            promotion: { type: 'object', description: '宣传话语' }
                        }
                    }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "getPackages", null);
__decorate([
    (0, common_1.Get)('order/:orderNo'),
    (0, encrypt_decorator_1.SecureEncrypt)(),
    (0, swagger_1.ApiOperation)({ summary: '获取订单详情' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '订单不存在' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], PackageOrderBusinessController.prototype, "getOrderDetail", null);
exports.PackageOrderBusinessController = PackageOrderBusinessController = __decorate([
    (0, swagger_1.ApiTags)('套餐购买业务'),
    (0, common_1.Controller)('api/v1/package-order'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [package_order_business_service_1.PackageOrderBusinessService])
], PackageOrderBusinessController);
//# sourceMappingURL=package-order-business.controller.js.map