2025-07-30 11:19:45.762 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 11:19:45.950 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.951 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.952 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.953 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.954 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.955 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.955 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.956 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.956 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.957 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.958 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.959 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.962 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.963 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.967 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.968 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.969 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.971 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.972 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.973 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.728 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 11:59:43.808 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.809 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.809 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.810 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.810 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.810 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.811 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.811 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.811 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.812 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.813 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.813 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.814 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.814 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.815 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.815 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.815 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.816 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.817 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:59:43.817 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82028,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.462 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 12:00:13.533 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.533 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.534 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.534 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.534 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.534 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.535 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.535 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.535 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.536 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.536 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.536 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.537 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.537 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.537 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.537 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.538 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.538 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.538 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:00:13.539 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80964,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.387 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 12:03:27.485 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.486 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.486 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.487 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.487 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.488 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.488 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.489 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.489 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.490 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.490 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.491 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.491 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.492 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.492 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.493 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.493 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.494 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.494 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:27.498 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81388,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.564 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 12:03:56.678 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.678 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.679 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.679 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.680 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.680 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.681 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.681 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.681 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.682 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.682 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.683 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.683 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.684 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.684 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.685 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.685 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.685 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.686 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:03:56.686 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":50920,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:19.942 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 12:04:20.039 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.040 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.040 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.041 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.041 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.042 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.042 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.042 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.043 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.043 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.044 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.045 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.045 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.045 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.046 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.046 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.047 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.047 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.048 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:04:20.048 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":79460,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.758 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 12:07:49.835 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.835 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.835 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.835 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.836 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.836 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.836 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.837 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.837 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.838 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.838 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.838 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.839 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.839 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.839 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.840 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.840 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.841 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.841 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 12:07:49.841 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:06:57.738 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*******&includeRisk=false - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-30 14:06:57.739 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*******&includeRisk=false","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:06:57.739Z"}
2025-07-30 14:06:58.641 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-30 14:06:58.642 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:06:58.641Z"}
2025-07-30 14:07:04.049 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-30 14:07:04.050 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:07:04.049Z"}
2025-07-30 14:23:08.752 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*******&includeRisk=false - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:23:08.753 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*******&includeRisk=false","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:23:08.752Z"}
2025-07-30 14:23:14.818 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*******&includeRisk=true - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:23:14.818 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*******&includeRisk=true","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:23:14.818Z"}
2025-07-30 14:25:40.255 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*******&includeRisk=true - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:25:40.256 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86708,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*******&includeRisk=true","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:25:40.256Z"}
2025-07-30 14:29:21.861 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 14:29:21.937 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.938 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.938 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.939 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.939 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.943 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.944 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.944 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.944 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.945 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.945 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.945 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.946 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.946 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.947 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.947 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.947 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.948 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.948 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:21.948 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87668,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.352 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 14:29:36.436 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.436 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.437 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.439 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.439 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.439 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.440 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.440 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.441 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.441 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.441 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.442 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.442 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.442 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.443 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.443 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.443 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.444 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.444 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:29:36.445 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:30:09.458 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*******&includeRisk=false - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:30:09.459 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*******&includeRisk=false","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:30:09.459Z"}
2025-07-30 14:30:13.909 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=***************&includeRisk=false - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:30:13.910 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=***************&includeRisk=false","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:30:13.909Z"}
2025-07-30 14:30:19.634 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*************&includeRisk=false - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:30:19.635 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*************&includeRisk=false","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:30:19.635Z"}
2025-07-30 14:30:24.530 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=*******&includeRisk=false - 包含风险评估必须是布尔值 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
BadRequestException: Bad Request Exception
    at ValidationPipe.exceptionFactory (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:107:20)
    at ValidationPipe.transform (F:\logicleap2\logic-back\node_modules\@nestjs\common\pipes\validation.pipe.js:74:30)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async resolveParamValue (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:148:23)
    at async Promise.all (index 0)
    at async pipesFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:151:13)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:37:30
2025-07-30 14:30:24.531 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86776,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=*******&includeRisk=false","method":"GET","statusCode":400,"message":["包含风险评估必须是布尔值"],"details":{"message":["包含风险评估必须是布尔值"],"error":"Bad Request","statusCode":400},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T06:30:24.531Z"}
2025-07-30 14:37:01.413 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 14:37:01.498 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.498 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.499 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.499 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.500 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.500 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.500 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.501 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.501 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.502 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.502 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.502 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.503 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.503 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.503 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.504 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.504 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.504 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.505 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:01.505 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":81120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.199 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 14:37:59.285 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.285 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.285 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.286 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.286 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.287 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.287 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.288 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.288 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.289 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.289 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.289 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.290 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.290 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.290 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.291 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.291 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.292 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.292 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:37:59.292 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78988,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.194 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 14:38:32.280 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.280 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.281 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.283 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.284 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.284 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.285 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.285 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.286 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.287 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.287 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.288 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.291 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.292 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.293 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.293 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.294 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.294 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.295 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 14:38:32.295 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82112,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.487 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 16:15:58.568 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.569 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.569 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.569 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.570 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.570 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.570 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.570 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.571 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.571 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.571 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.572 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.572 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.573 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.573 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.574 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.574 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.575 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.576 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:15:58.576 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46236,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.395 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 16:16:29.496 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.497 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.497 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.498 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.498 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.498 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.499 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.499 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.499 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.500 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.501 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.501 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.502 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.502 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.502 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.503 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.503 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.504 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.504 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:29.504 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:16:40.286 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:16:40.287 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:16:40.288 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationApplicationService.queryIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\application\\services\\ip-location-application.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:240:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:16:40.288Z"}
2025-07-30 16:16:47.775 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:16:47.776 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:16:47.776 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationApplicationService.queryIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\application\\services\\ip-location-application.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:240:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:16:47.776Z"}
2025-07-30 16:17:11.623 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:17:11.624 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:17:11.625 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74744,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationApplicationService.queryIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\application\\services\\ip-location-application.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:240:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:17:11.624Z"}
2025-07-30 16:23:58.099 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 16:23:58.194 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.194 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.195 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.195 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.195 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.196 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.197 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.197 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.198 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.198 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.199 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.199 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.199 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.200 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.200 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.200 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.201 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.201 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.202 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:23:58.202 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":90040,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.818 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 16:25:18.970 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.971 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.971 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.972 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.972 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.973 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.973 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.974 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.974 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.975 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.975 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.976 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.976 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.976 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.977 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.977 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.978 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.978 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.979 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:18.979 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80540,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:37.910 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 16:25:38.001 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.001 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.002 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.002 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.002 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.003 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.003 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.004 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.004 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.004 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.004 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.005 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.005 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.006 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.006 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.007 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.007 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.008 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.008 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:38.008 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 16:25:40.282 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:25:40.284 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:25:40.285 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationApplicationService.queryIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\application\\services\\ip-location-application.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:240:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:25:40.284Z"}
2025-07-30 16:25:41.212 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-30 16:25:41.213 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:25:41.213Z"}
2025-07-30 16:25:42.064 [ERROR] [GlobalExceptionFilter] GET /favicon.ico - Cannot GET /favicon.ico {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
NotFoundException: Cannot GET /favicon.ico
    at callback (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (F:\logicleap2\logic-back\node_modules\router\index.js:342:13)
    at F:\logicleap2\logic-back\node_modules\router\index.js:297:9
    at processParams (F:\logicleap2\logic-back\node_modules\router\index.js:582:12)
    at next (F:\logicleap2\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (F:\logicleap2\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-30 16:25:42.065 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/favicon.ico","method":"GET","statusCode":404,"message":"Cannot GET /favicon.ico","details":{"message":"Cannot GET /favicon.ico","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:25:42.065Z"}
2025-07-30 16:25:45.822 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-30 16:25:45.822 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:25:45.822Z"}
2025-07-30 16:25:48.580 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-30 16:25:48.581 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:25:48.580Z"}
2025-07-30 16:25:56.877 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:25:56.878 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:25:56.879 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationApplicationService.queryIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\application\\services\\ip-location-application.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:240:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:25:56.878Z"}
2025-07-30 16:26:17.306 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:26:17.306 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:240:12)
2025-07-30 16:26:17.307 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78868,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationApplicationService.queryIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\application\\services\\ip-location-application.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:240:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-30T08:26:17.307Z"}
