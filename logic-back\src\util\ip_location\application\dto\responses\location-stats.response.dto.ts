import { ApiProperty } from '@nestjs/swagger';

/**
 * 用户位置统计响应DTO
 */
export class LocationStatsResponseDto {
  @ApiProperty({ description: '用户ID', example: 12345 })
  userId: number;

  @ApiProperty({
    description: '统计周期',
    type: 'object',
    properties: {
      days: { type: 'number', example: 30 },
      startDate: { type: 'string', example: '2024-12-23T00:00:00Z' },
      endDate: { type: 'string', example: '2025-01-22T23:59:59Z' }
    }
  })
  statisticsPeriod: {
    days: number;
    startDate: string;
    endDate: string;
  };

  @ApiProperty({
    description: '常用登录地列表',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        province: { type: 'string', example: '广东省' },
        city: { type: 'string', example: '深圳市' },
        loginCount: { type: 'number', example: 45 },
        isTrusted: { type: 'boolean', example: true },
        trustScore: { type: 'number', example: 95.5 },
        firstLoginAt: { type: 'string', example: '2024-11-15T09:20:00Z' },
        lastLoginAt: { type: 'string', example: '2025-01-22T10:30:00Z' }
      }
    }
  })
  commonLocations: Array<{
    province: string;
    city: string;
    loginCount: number;
    isTrusted: boolean;
    trustScore: number;
    firstLoginAt: string;
    lastLoginAt: string;
  }>;

  @ApiProperty({
    description: '统计摘要',
    type: 'object',
    properties: {
      totalLocations: { type: 'number', example: 3 },
      trustedLocations: { type: 'number', example: 2 },
      riskLoginCount: { type: 'number', example: 2 },
      totalLoginCount: { type: 'number', example: 47 },
      riskRate: { type: 'number', example: 4.26 }
    }
  })
  summary: {
    totalLocations: number;
    trustedLocations: number;
    riskLoginCount: number;
    totalLoginCount: number;
    riskRate: number;
  };
}
