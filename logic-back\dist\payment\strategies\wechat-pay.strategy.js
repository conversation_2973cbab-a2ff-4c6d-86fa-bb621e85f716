"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WechatPayStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatPayStrategy = void 0;
const common_1 = require("@nestjs/common");
const payment_config_service_1 = require("../config/payment-config.service");
const payment_signature_service_1 = require("../security/payment-signature.service");
const fs = require("fs");
const path = require("path");
const moment = require("moment");
const axios_1 = require("axios");
const crypto = require("crypto");
let WechatPayStrategy = WechatPayStrategy_1 = class WechatPayStrategy {
    configService;
    signatureService;
    logger = new common_1.Logger(WechatPayStrategy_1.name);
    wechatpayClient;
    constructor(configService, signatureService) {
        this.configService = configService;
        this.signatureService = signatureService;
        this.initWechatPayClient();
    }
    async initWechatPayClient() {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            let privateKey;
            try {
                privateKey = fs.readFileSync(path.resolve(process.cwd(), wechatConfig.privateKeyPath));
            }
            catch (err) {
                this.logger.error(`读取微信支付私钥失败: ${err.message}`, err.stack);
                throw new Error('无法读取微信支付私钥');
            }
            this.wechatpayClient = {
                appId: wechatConfig.appId,
                mchId: wechatConfig.mchId,
                privateKey,
                serialNo: wechatConfig.serialNo,
                apiV3Key: wechatConfig.apiV3Key
            };
            this.logger.log('微信支付客户端初始化成功');
        }
        catch (error) {
            this.logger.error(`微信支付客户端初始化失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    getChannel() {
        return 'wechatpay';
    }
    generateSignature(method, url, timestamp, nonce, body) {
        try {
            return this.signatureService.generateWechatPaySignature(method, url, timestamp, nonce, body);
        }
        catch (error) {
            this.logger.error(`生成签名失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async createPayment(params) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const payParams = {
                appid: wechatConfig.appId,
                mchid: wechatConfig.mchId,
                description: params.subject,
                out_trade_no: params.outTradeNo,
                notify_url: params.notifyUrl || this.configService.getNotifyUrl('wechatpay'),
                amount: {
                    total: Math.round(params.totalAmount * 100),
                    currency: 'CNY'
                }
            };
            if (params.clientIp) {
                payParams['scene_info'] = {
                    payer_client_ip: params.clientIp
                };
            }
            if (params.timeExpire) {
                payParams['time_expire'] = moment(params.timeExpire).format('YYYY-MM-DDTHH:mm:ss+08:00');
            }
            const path = '/v3/pay/transactions/native';
            const url = `https://api.mch.weixin.qq.com${path}`;
            const nonce = crypto.randomBytes(16).toString('hex');
            const timestamp = Math.floor(Date.now() / 1000);
            const signature = this.generateSignature('POST', path, timestamp, nonce, JSON.stringify(payParams));
            try {
                this.logger.debug(`创建支付请求URL: ${url}`);
                this.logger.debug(`创建支付请求体: ${JSON.stringify(payParams)}`);
                this.logger.debug(`创建支付请求头: mchid=${wechatConfig.mchId}, nonce_str=${nonce}, timestamp=${timestamp}, serial_no=${wechatConfig.serialNo}`);
                const response = await axios_1.default.post(url, payParams, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${wechatConfig.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${wechatConfig.serialNo}"`
                    }
                });
                if (response.status === 200 && response.data.code_url) {
                    this.logger.debug(`微信支付创建成功: ${JSON.stringify(response.data)}`);
                    return {
                        success: true,
                        qrCode: response.data.code_url,
                        rawResponse: response.data,
                    };
                }
                else {
                    throw new Error(`微信支付创建响应异常: ${JSON.stringify(response.data)}`);
                }
            }
            catch (error) {
                if (error.response) {
                    this.logger.error(`微信支付API错误: ${JSON.stringify(error.response.data)}`);
                    throw new Error(`微信支付API错误: ${error.response.data.message || '未知错误'}`);
                }
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`微信支付创建失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async queryPayment(params) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const path = `/v3/pay/transactions/out-trade-no/${params.outTradeNo}`;
            const queryString = `?mchid=${wechatConfig.mchId}`;
            const fullPath = path + queryString;
            const url = `https://api.mch.weixin.qq.com${fullPath}`;
            const nonce = crypto.randomBytes(16).toString('hex');
            const timestamp = Math.floor(Date.now() / 1000);
            const signature = this.generateSignature('GET', fullPath, timestamp, nonce, '');
            try {
                this.logger.debug(`查询支付状态请求URL: ${url}`);
                this.logger.debug(`查询支付状态请求头: mchid=${wechatConfig.mchId}, nonce_str=${nonce}, timestamp=${timestamp}, serial_no=${wechatConfig.serialNo}`);
                const response = await axios_1.default.get(url, {
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${wechatConfig.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${wechatConfig.serialNo}"`
                    }
                });
                const result = response.data;
                this.logger.debug(`微信支付查询响应: ${JSON.stringify(result)}`);
                const isPaid = result.trade_state === 'SUCCESS';
                let amount = 0;
                if (result.amount && typeof result.amount.total === 'number') {
                    amount = result.amount.total / 100;
                }
                else {
                    this.logger.warn(`微信支付查询响应中缺少金额信息: ${JSON.stringify(result)}`);
                }
                return {
                    success: true,
                    paid: isPaid,
                    paymentId: result.transaction_id,
                    amount: amount,
                    status: this.mapTradeState(result.trade_state),
                    paymentTime: result.success_time ? new Date(result.success_time) : undefined,
                    rawResponse: result,
                };
            }
            catch (error) {
                if (error.response && error.response.status === 404) {
                    return {
                        success: true,
                        paid: false,
                        status: 'not_found',
                    };
                }
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`微信支付查询失败: ${error.message}`, error.stack);
            return {
                success: false,
                paid: false,
                status: 'error',
                errorMessage: error.message,
            };
        }
    }
    async closePayment(params) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const path = `/v3/pay/transactions/out-trade-no/${params.outTradeNo}/close`;
            const url = `https://api.mch.weixin.qq.com${path}`;
            const nonce = crypto.randomBytes(16).toString('hex');
            const timestamp = Math.floor(Date.now() / 1000);
            const requestBody = JSON.stringify({ mchid: wechatConfig.mchId });
            const signature = this.generateSignature('POST', path, timestamp, nonce, requestBody);
            try {
                this.logger.debug(`关闭订单请求URL: ${url}`);
                this.logger.debug(`关闭订单请求体: ${requestBody}`);
                this.logger.debug(`关闭订单请求头: mchid=${wechatConfig.mchId}, nonce_str=${nonce}, timestamp=${timestamp}, serial_no=${wechatConfig.serialNo}`);
                const response = await axios_1.default.post(url, { mchid: wechatConfig.mchId }, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${wechatConfig.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${wechatConfig.serialNo}"`
                    }
                });
                if (response.status === 204) {
                    return {
                        success: true,
                    };
                }
                else {
                    throw new Error(`关闭订单响应异常: ${response.status}`);
                }
            }
            catch (error) {
                if (error.response) {
                    throw new Error(`关闭订单失败: ${JSON.stringify(error.response.data)}`);
                }
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`微信支付关闭失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    decryptResource(resource) {
        try {
            return this.signatureService.decryptWechatPayResource(resource);
        }
        catch (error) {
            this.logger.error(`解密资源数据失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async verifyNotify(data) {
        try {
            const headers = data.headers || {};
            const body = data.body || {};
            const isRefundNotify = body && body.event_type && body.event_type === 'REFUND.SUCCESS';
            const signature = headers['wechatpay-signature'];
            const timestamp = headers['wechatpay-timestamp'];
            const nonce = headers['wechatpay-nonce'];
            const serialNo = headers['wechatpay-serial'];
            this.logger.debug(`微信支付${isRefundNotify ? '退款' : '支付'}通知处理: signature=${signature ? '已提供' : '未提供'}, timestamp=${timestamp}, nonce=${nonce}, serialNo=${serialNo}`);
            if (!signature || !timestamp || !nonce) {
                this.logger.warn(`微信支付${isRefundNotify ? '退款' : '支付'}通知缺少必要的头信息`);
                return { verified: false };
            }
            let bodyStr = '';
            try {
                bodyStr = typeof body === 'string' ? body : JSON.stringify(body);
                this.logger.debug(`原始通知body字符串: ${bodyStr}`);
            }
            catch (err) {
                this.logger.error(`处理通知body失败: ${err.message}`);
            }
            const signVerified = this.signatureService.verifyWechatPaySignature(timestamp, nonce, bodyStr, signature, serialNo);
            if (!signVerified) {
                this.logger.error(`微信支付${isRefundNotify ? '退款' : '支付'}通知签名验证失败`);
                return { verified: false };
            }
            else {
                this.logger.log(`微信支付${isRefundNotify ? '退款' : '支付'}通知签名验证成功`);
            }
            let decryptedData;
            try {
                decryptedData = this.decryptResource(body.resource);
                this.logger.debug(`解密后的通知数据: ${JSON.stringify(decryptedData)}`);
            }
            catch (error) {
                this.logger.error(`解密通知数据失败: ${error.message}`, error.stack);
                return { verified: false };
            }
            if (isRefundNotify) {
                return {
                    verified: true,
                    outTradeNo: decryptedData.out_trade_no,
                    outRefundNo: decryptedData.out_refund_no,
                    refundId: decryptedData.refund_id,
                    refundStatus: decryptedData.refund_status,
                    successTime: decryptedData.success_time,
                    refundAmount: decryptedData.amount?.refund ? decryptedData.amount.refund / 100 : 0,
                    totalAmount: decryptedData.amount?.total ? decryptedData.amount.total / 100 : 0,
                    rawNotify: decryptedData,
                };
            }
            else {
                if (decryptedData.trade_state !== 'SUCCESS') {
                    this.logger.warn(`微信支付通知交易状态不是成功状态: ${decryptedData.trade_state}`);
                    return { verified: false };
                }
                return {
                    verified: true,
                    outTradeNo: decryptedData.out_trade_no,
                    paymentId: decryptedData.transaction_id,
                    totalAmount: decryptedData.amount.total / 100,
                    paymentTime: decryptedData.success_time ? new Date(decryptedData.success_time) : undefined,
                    tradeState: decryptedData.trade_state,
                    openid: decryptedData.payer?.openid,
                    rawNotify: decryptedData,
                };
            }
        }
        catch (error) {
            this.logger.error(`微信支付通知验证失败: ${error.message}`, error.stack);
            return { verified: false };
        }
    }
    mapTradeState(tradeState) {
        switch (tradeState) {
            case 'SUCCESS':
                return 'success';
            case 'REFUND':
                return 'refund';
            case 'NOTPAY':
            case 'USERPAYING':
                return 'pending';
            case 'CLOSED':
                return 'closed';
            case 'REVOKED':
                return 'cancelled';
            case 'PAYERROR':
                return 'failed';
            default:
                return 'unknown';
        }
    }
    async refund(params) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            try {
                const orderQuery = await this.queryPayment({ outTradeNo: params.outTradeNo });
                if (!orderQuery.success) {
                    throw new Error(`无法查询原订单信息: ${orderQuery.errorMessage || '未知错误'}`);
                }
                if (!orderQuery.paid) {
                    throw new Error('订单未支付，不能退款');
                }
                const totalAmount = orderQuery.amount || params.refundAmount;
                const refundParams = {
                    out_trade_no: params.outTradeNo,
                    out_refund_no: params.outRefundNo,
                    reason: params.reason || '用户申请退款',
                    notify_url: this.configService.getNotifyUrl('wechatpay_refund'),
                    amount: {
                        refund: Math.round(params.refundAmount * 100),
                        total: Math.round(totalAmount * 100),
                        currency: 'CNY'
                    }
                };
                const path = '/v3/refund/domestic/refunds';
                const url = `https://api.mch.weixin.qq.com${path}`;
                const nonce = crypto.randomBytes(16).toString('hex');
                const timestamp = Math.floor(Date.now() / 1000);
                const signature = this.generateSignature('POST', path, timestamp, nonce, JSON.stringify(refundParams));
                this.logger.debug(`微信支付退款请求URL: ${url}`);
                this.logger.debug(`微信支付退款请求体: ${JSON.stringify(refundParams)}`);
                this.logger.debug(`微信支付退款请求头: mchid=${wechatConfig.mchId}, nonce_str=${nonce}, timestamp=${timestamp}, serial_no=${wechatConfig.serialNo}`);
                const response = await axios_1.default.post(url, refundParams, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${wechatConfig.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${wechatConfig.serialNo}"`
                    }
                });
                if (response.status === 200 && response.data) {
                    this.logger.debug(`微信支付退款成功: ${JSON.stringify(response.data)}`);
                    return {
                        success: true,
                        refundId: response.data.refund_id,
                        rawResult: response.data,
                    };
                }
                else {
                    throw new Error(`微信支付退款响应异常: ${JSON.stringify(response.data)}`);
                }
            }
            catch (error) {
                if (error.response) {
                    const errorResponse = error.response;
                    const errorData = errorResponse.data || {};
                    let errorMessage = `微信支付退款API错误: ${errorData.message || '未知错误'}`;
                    if (errorResponse.status === 403 && errorData.code === 'NOT_ENOUGH') {
                        errorMessage = '商户余额不足，无法退款';
                    }
                    else if (errorResponse.status === 404 && errorData.code === 'RESOURCE_NOT_EXISTS') {
                        errorMessage = '订单不存在或未支付，无法退款';
                    }
                    else if (errorResponse.status === 429 && errorData.code === 'FREQUENCY_LIMITED') {
                        errorMessage = '退款请求频率过高，请稍后再试';
                    }
                    this.logger.error(`${errorMessage}: ${JSON.stringify(errorData)}`);
                    throw new Error(errorMessage);
                }
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`微信支付退款失败: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message,
                rawResult: { error: error.message },
            };
        }
    }
    async queryRefundStatus(outRefundNo) {
        try {
            const wechatConfig = this.configService.getWechatPayConfig();
            const path = `/v3/refund/domestic/refunds/out-refund-no/${outRefundNo}`;
            const url = `https://api.mch.weixin.qq.com${path}`;
            const nonce = crypto.randomBytes(16).toString('hex');
            const timestamp = Math.floor(Date.now() / 1000);
            const signature = this.generateSignature('GET', path, timestamp, nonce, '');
            this.logger.debug(`微信支付退款查询请求URL: ${url}`);
            this.logger.debug(`微信支付退款查询请求头: mchid=${wechatConfig.mchId}, nonce_str=${nonce}, timestamp=${timestamp}, serial_no=${wechatConfig.serialNo}`);
            try {
                const response = await axios_1.default.get(url, {
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${wechatConfig.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${wechatConfig.serialNo}"`
                    }
                });
                if (response.status === 200 && response.data) {
                    this.logger.debug(`微信支付退款查询成功: ${JSON.stringify(response.data)}`);
                    const refundStatus = response.data.status;
                    let status;
                    let errorMessage;
                    switch (refundStatus) {
                        case 'SUCCESS':
                            status = 'success';
                            break;
                        case 'PROCESSING':
                            status = 'processing';
                            break;
                        case 'ABNORMAL':
                            status = 'processing';
                            errorMessage = '退款异常，请联系商户处理';
                            break;
                        case 'CLOSED':
                            status = 'closed';
                            errorMessage = '退款已关闭';
                            break;
                        default:
                            status = 'failed';
                            errorMessage = '退款处理失败';
                    }
                    const result = {
                        status,
                        refundId: response.data.refund_id,
                        outTradeNo: response.data.out_trade_no,
                        transactionId: response.data.transaction_id,
                        refundAmount: response.data.amount?.refund ? response.data.amount.refund / 100 : 0,
                        totalAmount: response.data.amount?.total ? response.data.amount.total / 100 : 0,
                        successTime: response.data.success_time,
                        createTime: response.data.create_time,
                        errorMessage,
                        rawResult: response.data,
                    };
                    return result;
                }
                else {
                    throw new Error(`微信支付退款查询响应异常: ${JSON.stringify(response.data)}`);
                }
            }
            catch (error) {
                if (error.response) {
                    const errorResponse = error.response;
                    const errorData = errorResponse.data || {};
                    if (errorResponse.status === 404) {
                        return {
                            status: 'failed',
                            errorMessage: '退款单号不存在',
                            rawResult: errorData,
                        };
                    }
                    else {
                        const errorMessage = `微信支付退款查询API错误: ${errorData.code || ''} - ${errorData.message || '未知错误'}`;
                        this.logger.error(errorMessage);
                        throw new Error(errorMessage);
                    }
                }
                throw error;
            }
        }
        catch (error) {
            this.logger.error(`微信支付退款查询失败: ${error.message}`, error.stack);
            return {
                status: 'failed',
                errorMessage: error.message,
                rawResult: { error: error.message },
            };
        }
    }
};
exports.WechatPayStrategy = WechatPayStrategy;
exports.WechatPayStrategy = WechatPayStrategy = WechatPayStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_config_service_1.PaymentConfigService,
        payment_signature_service_1.PaymentSignatureService])
], WechatPayStrategy);
//# sourceMappingURL=wechat-pay.strategy.js.map