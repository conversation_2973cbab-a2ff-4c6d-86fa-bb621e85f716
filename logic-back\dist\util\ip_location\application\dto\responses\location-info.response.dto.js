"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationInfoResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class LocationInfoResponseDto {
    ip;
    country;
    province;
    city;
    isp;
    dataSource;
    confidence;
    isHighQuality;
    displayName;
    risk;
}
exports.LocationInfoResponseDto = LocationInfoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'IP地址', example: '**************' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "ip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '国家', example: '中国' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '省份', example: '北京市' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "province", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '城市', example: '北京市' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '网络运营商', example: '联通' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "isp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '数据来源', example: 'ip2region' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "dataSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '数据置信度', example: 95 }),
    __metadata("design:type", Number)
], LocationInfoResponseDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否为高质量数据', example: true }),
    __metadata("design:type", Boolean)
], LocationInfoResponseDto.prototype, "isHighQuality", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '格式化显示名称', example: '中国 北京市 北京市' }),
    __metadata("design:type", String)
], LocationInfoResponseDto.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '风险评估信息',
        required: false,
        type: 'object',
        properties: {
            level: { type: 'string', example: 'LOW' },
            score: { type: 'number', example: 15 },
            reason: { type: 'string', example: '常用登录地' },
            needVerification: { type: 'boolean', example: false }
        }
    }),
    __metadata("design:type", Object)
], LocationInfoResponseDto.prototype, "risk", void 0);
//# sourceMappingURL=location-info.response.dto.js.map