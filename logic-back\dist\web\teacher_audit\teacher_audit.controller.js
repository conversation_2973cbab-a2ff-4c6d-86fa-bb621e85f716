"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherAuditController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const teacher_audit_service_1 = require("./teacher_audit.service");
const create_teacher_audit_dto_1 = require("./dto/create-teacher_audit.dto");
const update_teacher_audit_dto_1 = require("./dto/update-teacher_audit.dto");
const teacher_audit_info_dto_1 = require("./dto/teacher_audit_info.dto");
const attachment_service_1 = require("../attachment/attachment.service");
const teacher_audit_attachment_service_1 = require("../teacher_audit_attachment/teacher_audit_attachment.service");
const teacher_audit_entity_1 = require("./entities/teacher_audit.entity");
const user_school_relation_service_1 = require("../user_school_relation/user_school_relation.service");
const user_school_service_1 = require("../user_school/user_school.service");
const user_info_service_1 = require("../user_info/user_info.service");
let TeacherAuditController = class TeacherAuditController {
    teacherAuditService;
    attachmentService;
    teacherAuditAttachmentService;
    userSchoolRelationService;
    userSchoolService;
    userInfoService;
    constructor(teacherAuditService, attachmentService, teacherAuditAttachmentService, userSchoolRelationService, userSchoolService, userInfoService) {
        this.teacherAuditService = teacherAuditService;
        this.attachmentService = attachmentService;
        this.teacherAuditAttachmentService = teacherAuditAttachmentService;
        this.userSchoolRelationService = userSchoolRelationService;
        this.userSchoolService = userSchoolService;
        this.userInfoService = userInfoService;
    }
    async create(createTeacherAuditDto, req) {
        try {
            if (!createTeacherAuditDto.teacherId) {
                createTeacherAuditDto.teacherId = req.user.id;
            }
            console.log("zww：当前申请人的id为", req.user.id);
            const userAudits = await this.teacherAuditService.findByUserId(createTeacherAuditDto.teacherId);
            if (userAudits.length > 0) {
                const hasPending = userAudits.some(audit => audit.result === teacher_audit_entity_1.AuditResult.PENDING);
                const hasApproved = userAudits.some(audit => audit.result === teacher_audit_entity_1.AuditResult.APPROVED);
                if (hasPending) {
                    throw new common_1.HttpException({
                        code: 400,
                        message: '您已提交过认证申请，正在审核中，请耐心等待',
                        data: null
                    }, common_1.HttpStatus.BAD_REQUEST);
                }
                else if (hasApproved) {
                    throw new common_1.HttpException({
                        code: 400,
                        message: '您已通过教师认证，无需重复认证',
                        data: null
                    }, common_1.HttpStatus.BAD_REQUEST);
                }
            }
            if (!createTeacherAuditDto.operationIp) {
                createTeacherAuditDto.operationIp = req.ip;
            }
            if (!createTeacherAuditDto.deviceInfo) {
                createTeacherAuditDto.deviceInfo = req.headers['user-agent'] || '未知设备';
            }
            const teacherAudit = await this.teacherAuditService.create(createTeacherAuditDto);
            const attachments = createTeacherAuditDto.attachments || [];
            let savedAttachments = [];
            if (attachments.length > 0) {
                const attachmentDtos = attachments.map(attachment => ({
                    file_url: attachment.url,
                    file_name: attachment.name,
                    file_type: attachment.type,
                    file_size: attachment.size,
                    created_by: req.user.id
                }));
                savedAttachments = await this.attachmentService.createMany(attachmentDtos);
                const teacherAuditAttachments = savedAttachments.map(attachment => ({
                    teacher_audit_id: teacherAudit.id,
                    attachment_id: attachment.id
                }));
                await this.teacherAuditAttachmentService.createMany(teacherAuditAttachments);
            }
            try {
                const userInfo = await this.userInfoService.findOne(createTeacherAuditDto.teacherId);
                if (userInfo && userInfo.roleId === 999) {
                    await this.userInfoService.updateRole(createTeacherAuditDto.teacherId, 3);
                    console.log(`已将用户(ID: ${createTeacherAuditDto.teacherId})的角色临时更新为普通用户角色`);
                }
            }
            catch (error) {
                console.error('更新用户角色失败:', error);
            }
            return {
                code: 200,
                message: '申请提交成功',
                data: {
                    teacherAudit,
                    attachments: savedAttachments
                }
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            console.error('提交教师认证申请失败:', error);
            throw new common_1.HttpException({
                code: 500,
                message: `提交失败: ${error.message || '服务器内部错误'}`,
                data: null
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    findAll() {
        return this.teacherAuditService.findAll();
    }
    async findTeacherAudit(req, status, userId) {
        try {
            if (req.user.roleId !== 4) {
                throw new common_1.HttpException({
                    code: 403,
                    message: '无权限查看',
                    data: null
                }, common_1.HttpStatus.FORBIDDEN);
            }
            let audits;
            if (userId) {
                audits = await this.teacherAuditService.findByUserId(userId);
                if (status !== undefined) {
                    audits = audits.filter(audit => audit.result === status);
                }
            }
            else if (status !== undefined) {
                audits = await this.teacherAuditService.findByTeacherNameAndStatus('', status);
            }
            else {
                audits = await this.teacherAuditService.findAll();
            }
            const result = await Promise.all(audits.map(async (audit) => {
                const attachments = await this.teacherAuditAttachmentService.findAttachmentsByAuditId(audit.id);
                return {
                    ...audit,
                    attachments
                };
            }));
            return result;
        }
        catch (error) {
            console.error('获取教师认证列表失败:', error);
            throw error;
        }
    }
    async findCurrentTeacherAudit(req) {
        console.log("req的user的userid:", req.user.userId);
        console.log("req的user的id:", req.user.id);
        const teacherAudit = await this.teacherAuditService.findByTeacherId(req.user.id);
        console.log("zww：根据教师id找到的认证记录为", teacherAudit);
        return teacherAudit;
    }
    async findOne(id) {
        const teacherAudit = await this.teacherAuditService.getTeacherAuditDetail(+id);
        const attachments = await this.teacherAuditAttachmentService.findAttachmentsByAuditId(+id);
        return {
            ...teacherAudit,
            attachments
        };
    }
    update(id, updateTeacherAuditDto, req) {
        if (updateTeacherAuditDto.result !== undefined) {
            updateTeacherAuditDto.auditorId = req.user.id;
            updateTeacherAuditDto.auditorName = req.user.nickName || '管理员';
        }
        updateTeacherAuditDto['operationIp'] = req.ip;
        return this.teacherAuditService.update(+id, updateTeacherAuditDto);
    }
    async remove(id) {
        await this.teacherAuditAttachmentService.removeByTeacherAuditId(+id);
        return this.teacherAuditService.remove(+id);
    }
    async getTeacherAuditDetail(id) {
        const teacherAudit = await this.teacherAuditService.getTeacherAuditDetail(+id);
        const attachments = await this.teacherAuditAttachmentService.findAttachmentsByAuditId(+id);
        return {
            ...teacherAudit,
            attachments
        };
    }
    async findByName(name) {
        return this.teacherAuditService.findByTeacherName(name);
    }
    async findByUserId(userId) {
        return this.teacherAuditService.findByUserId(+userId);
    }
    async findByNamePattern(pattern) {
        return this.teacherAuditService.findByTeacherNameLike(pattern);
    }
    async reviewTeacherAudit(reviewDto, req) {
        const audit = await this.teacherAuditService.findOne(reviewDto.id);
        console.log("审核教师认证参数:", reviewDto);
        if (audit.result !== teacher_audit_entity_1.AuditResult.PENDING) {
            throw new common_1.NotFoundException(`ID为 ${reviewDto.id} 的教师认证不在待审核状态`);
        }
        const updateTeacherAuditDto = {
            result: reviewDto.result,
            reason: reviewDto.reason,
            auditorId: req.user.id,
            teacherId: reviewDto.userId,
            auditorName: req.user.nickName || '管理员',
            beforeStatus: audit.result,
            afterStatus: reviewDto.result,
        };
        console.log("zww：更新教师记录dto", updateTeacherAuditDto);
        const updatedAudit = await this.teacherAuditService.update(reviewDto.id, updateTeacherAuditDto);
        if (reviewDto.result === teacher_audit_entity_1.AuditResult.APPROVED && reviewDto.userId && audit.schoolInfo) {
            try {
                await this.userInfoService.updateRole(reviewDto.userId, 2);
                console.log(`已将用户(ID: ${reviewDto.userId})的角色更新为教师角色`);
                const schoolInfoArr = audit.schoolInfo.split('|');
                let schoolId = 0;
                if (schoolInfoArr.length >= 4) {
                    const schoolName = schoolInfoArr[0];
                    const province = schoolInfoArr[1];
                    const city = schoolInfoArr[2];
                    const district = schoolInfoArr[3];
                    const school = await this.userSchoolService.findExactSchool(province, city, district, schoolName);
                    if (school) {
                        schoolId = school.id;
                    }
                    else {
                        console.log(`未找到学校: ${schoolName}, 省: ${province}, 市: ${city}, 区: ${district}`);
                        try {
                            const newSchool = await this.userSchoolService.create({
                                schoolName,
                                province,
                                city,
                                district
                            });
                            if (newSchool && newSchool.id) {
                                schoolId = newSchool.id;
                                console.log(`已创建新学校: ${schoolName}, ID: ${schoolId}`);
                            }
                        }
                        catch (error) {
                            console.error('创建学校记录失败:', error);
                        }
                    }
                }
                else {
                    console.error('学校信息格式不正确:', audit.schoolInfo);
                }
                if (schoolId) {
                    const teacherSchoolRelation = {
                        userId: reviewDto.userId,
                        schoolId: schoolId,
                        roleType: 2
                    };
                    await this.userSchoolRelationService.create(teacherSchoolRelation);
                    console.log(`已创建教师(ID: ${reviewDto.userId})与学校(ID: ${schoolId})的关联记录`);
                }
            }
            catch (error) {
                console.error('创建教师-学校关联记录失败:', error);
            }
        }
        return {
            ...updatedAudit,
            message: reviewDto.result === teacher_audit_entity_1.AuditResult.APPROVED ? '审核通过成功' : '审核拒绝成功'
        };
    }
};
exports.TeacherAuditController = TeacherAuditController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '提交教师认证', description: '上传教师认证资料进行审核' }),
    (0, swagger_1.ApiBody)({
        description: '教师认证申请数据',
        schema: {
            type: 'object',
            required: ['teacherId', 'teacherName', 'schoolInfo'],
            properties: {
                teacherId: {
                    type: 'number',
                    example: 15459,
                    description: '教师ID'
                },
                teacherName: {
                    type: 'string',
                    example: 'zww',
                    description: '教师姓名'
                },
                schoolInfo: {
                    type: 'string',
                    example: '洛基小学|广东省|佛山市|禅城区',
                    description: '学校信息（格式：学校名称|省份|城市|区县）'
                },
                attachments: {
                    type: 'array',
                    description: '附件信息列表',
                    items: {
                        type: 'object',
                        properties: {
                            url: {
                                type: 'string',
                                example: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/images/jpg/1751958145337.jpg',
                                description: '文件URL地址'
                            },
                            name: {
                                type: 'string',
                                example: 'zww.jpg',
                                description: '文件名称'
                            },
                            type: {
                                type: 'string',
                                example: 'image/jpeg',
                                description: '文件MIME类型'
                            },
                            size: {
                                type: 'number',
                                example: 102400,
                                description: '文件大小（字节）'
                            }
                        }
                    },
                    example: [
                        {
                            url: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/images/jpg/1751958145337.jpg',
                            name: 'zww.jpg',
                            type: 'image/jpeg',
                            size: 102400
                        },
                        {
                            url: 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/other/1751958145620.docx',
                            name: '工作证明模板.docx',
                            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            size: 25600
                        }
                    ]
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '提交成功，等待审核' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '提交失败' }),
    (0, common_1.Post)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '提交教师认证申请' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: '申请提交成功', type: create_teacher_audit_dto_1.CreateTeacherAuditDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_teacher_audit_dto_1.CreateTeacherAuditDto, Object]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取所有教师认证' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [teacher_audit_info_dto_1.TeacherAuditInfoDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TeacherAuditController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('getTeacherAuthByCondition'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取教师认证' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: '认证状态' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: [teacher_audit_info_dto_1.TeacherAuditInfoDto] }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "findTeacherAudit", null);
__decorate([
    (0, common_1.Get)('teacher'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取当前教师的认证' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: teacher_audit_info_dto_1.TeacherAuditInfoDto }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "findCurrentTeacherAudit", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取教师认证详情' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: teacher_audit_info_dto_1.TeacherAuditInfoDto }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '更新教师认证信息' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '更新成功', type: teacher_audit_info_dto_1.TeacherAuditInfoDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_teacher_audit_dto_1.UpdateTeacherAuditDto, Object]),
    __metadata("design:returntype", void 0)
], TeacherAuditController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '软删除教师认证' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('info/:id'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取教师认证详情，包含学校信息和教师信息' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: teacher_audit_info_dto_1.TeacherAuditInfoDto }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "getTeacherAuditDetail", null);
__decorate([
    (0, common_1.Get)('search/name/:name'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '根据教师名称搜索认证记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '搜索成功', type: [teacher_audit_info_dto_1.TeacherAuditInfoDto] }),
    __param(0, (0, common_1.Param)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "findByName", null);
__decorate([
    (0, common_1.Get)('search/user/:userId'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '根据用户ID搜索认证记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '搜索成功', type: [teacher_audit_info_dto_1.TeacherAuditInfoDto] }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "findByUserId", null);
__decorate([
    (0, common_1.Get)('search/name-like/:pattern'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '根据教师名称模糊搜索认证记录' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '搜索成功', type: [teacher_audit_info_dto_1.TeacherAuditInfoDto] }),
    __param(0, (0, common_1.Param)('pattern')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "findByNamePattern", null);
__decorate([
    (0, common_1.Post)('review'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '审核教师认证' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '审核成功', type: teacher_audit_info_dto_1.TeacherAuditInfoDto }),
    (0, swagger_1.ApiOperation)({ summary: '审核教师认证', description: '处理教师认证申请' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['id', 'userId', 'result'],
            properties: {
                id: {
                    type: 'number',
                    example: 123,
                    description: '教师认证记录ID'
                },
                userId: {
                    type: 'number',
                    example: 15459,
                    description: '用户ID'
                },
                result: {
                    type: 'number',
                    example: 1,
                    description: '审核结果（1=通过，2=拒绝）'
                },
                reason: {
                    type: 'string',
                    example: '证件信息不清晰，请重新上传',
                    description: '拒绝理由（拒绝时必填）'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '审核成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '认证记录不存在或不在待审核状态' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TeacherAuditController.prototype, "reviewTeacherAudit", null);
exports.TeacherAuditController = TeacherAuditController = __decorate([
    (0, swagger_1.ApiTags)('web/教师认证'),
    (0, common_1.Controller)('api/teacher-audit'),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __metadata("design:paramtypes", [teacher_audit_service_1.TeacherAuditService,
        attachment_service_1.AttachmentService,
        teacher_audit_attachment_service_1.TeacherAuditAttachmentService,
        user_school_relation_service_1.UserSchoolRelationService,
        user_school_service_1.UserSchoolService,
        user_info_service_1.UserInfoService])
], TeacherAuditController);
//# sourceMappingURL=teacher_audit.controller.js.map