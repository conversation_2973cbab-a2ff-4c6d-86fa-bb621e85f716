/**
 * 命令处理器接口
 * 定义命令处理的统一规范
 */
export interface ICommandHandler<TCommand, TResult = void> {
  /**
   * 处理命令
   * @param command 要处理的命令
   * @returns 处理结果
   */
  handle(command: TCommand): Promise<TResult>;

  /**
   * 验证命令是否可以处理
   * @param command 要验证的命令
   * @returns 验证结果
   */
  canHandle(command: TCommand): boolean;

  /**
   * 获取处理器名称
   */
  getHandlerName(): string;
}

/**
 * 命令处理结果接口
 */
export interface CommandResult<TData = any> {
  success: boolean;
  data?: TData;
  message?: string;
  errors?: string[];
  timestamp: Date;
  executionTime?: number;
}

/**
 * 创建成功的命令结果
 */
export function createSuccessResult<TData>(
  data?: TData,
  message?: string,
  executionTime?: number
): CommandResult<TData> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date(),
    executionTime
  };
}

/**
 * 创建失败的命令结果
 */
export function createFailureResult(
  errors: string[],
  message?: string,
  executionTime?: number
): CommandResult {
  return {
    success: false,
    message,
    errors,
    timestamp: new Date(),
    executionTime
  };
}
