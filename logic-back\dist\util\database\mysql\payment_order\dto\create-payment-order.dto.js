"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentOrderDto = exports.PaymentStatus = exports.PaymentChannel = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var PaymentChannel;
(function (PaymentChannel) {
    PaymentChannel["ALIPAY"] = "alipay";
    PaymentChannel["WECHAT"] = "wechat";
    PaymentChannel["UNIONPAY"] = "unionpay";
})(PaymentChannel || (exports.PaymentChannel = PaymentChannel = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "pending";
    PaymentStatus["PROCESSING"] = "processing";
    PaymentStatus["SUCCESS"] = "success";
    PaymentStatus["FAILED"] = "failed";
    PaymentStatus["CLOSED"] = "closed";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
class CreatePaymentOrderDto {
    businessOrderId;
    amount;
    description;
    channel;
    channelOrderId;
    status;
    parameters;
    userId;
    clientIp;
    notifyUrl;
    returnUrl;
    cancelUrl;
    expiredAt;
}
exports.CreatePaymentOrderDto = CreatePaymentOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '业务订单号', example: 'ORDER_2023101010001' }),
    (0, class_validator_1.IsNotEmpty)({ message: '业务订单号不能为空' }),
    (0, class_validator_1.IsString)({ message: '业务订单号必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "businessOrderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 99.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '支付金额必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '支付金额必须大于0' }),
    __metadata("design:type", Number)
], CreatePaymentOrderDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '商品描述', example: '测试商品' }),
    (0, class_validator_1.IsNotEmpty)({ message: '商品描述不能为空' }),
    (0, class_validator_1.IsString)({ message: '商品描述必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付渠道不能为空' }),
    (0, class_validator_1.IsEnum)(PaymentChannel, { message: '无效的支付渠道' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道订单号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '支付渠道订单号必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "channelOrderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', enum: PaymentStatus, default: PaymentStatus.PENDING, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PaymentStatus, { message: '无效的支付状态' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付参数', required: false, type: Object }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: '支付参数必须是对象' }),
    __metadata("design:type", Object)
], CreatePaymentOrderDto.prototype, "parameters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '用户ID必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '客户端IP', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '客户端IP必须是字符串' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "clientIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付通知回调URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: '支付通知回调URL格式不正确' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "notifyUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付成功跳转URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: '支付成功跳转URL格式不正确' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "returnUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付取消跳转URL', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: '支付取消跳转URL格式不正确' }),
    __metadata("design:type", String)
], CreatePaymentOrderDto.prototype, "cancelUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], CreatePaymentOrderDto.prototype, "expiredAt", void 0);
//# sourceMappingURL=create-payment-order.dto.js.map