import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

// 查询对象
import { GetLocationByIpQuery } from '../queries/get-location-by-ip.query';
import { GetUserLocationStatsQuery } from '../queries/get-user-location-stats.query';
import { AssessLoginRiskQuery } from '../queries/assess-login-risk.query';

// 接口
import { IQueryHandler, QueryResult, createSuccessQueryResult, createFailureQueryResult } from '../interfaces/query-handler.interface';

// 领域服务
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService, UserLocationHistory } from '../../domain/services/risk-assessment-domain.service';
import { LocationComparisonService } from '../../domain/services/location-comparison.service';

// 值对象
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { RiskScore } from '../../domain/value-objects/risk-score.vo';

// 实体
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';

// 异常
import { IpLocationDomainException } from '../../domain/exceptions/ip-location-domain.exception';

/**
 * 位置查询结果接口
 */
export interface LocationQueryResult {
  ip: string;
  country: string;
  province: string;
  city: string;
  isp: string;
  dataSource: string;
  confidence: number;
  isHighQuality: boolean;
  displayName: string;
  riskAssessment?: {
    level: string;
    score: number;
    reason: string;
    factors: string[];
    needVerification: boolean;
    recommendedActions: string[];
  };
}

/**
 * 用户位置统计结果接口
 */
export interface UserLocationStatsResult {
  userId: number;
  queryPeriod: {
    days: number;
    startDate: string;
    endDate: string;
  };
  summary: {
    totalLocations: number;
    trustedLocations: number;
    totalLogins: number;
    riskLogins: number;
    uniqueProvinces: number;
    uniqueCities: number;
  };
  locations: Array<{
    country: string;
    province: string;
    city: string;
    isp: string;
    loginCount: number;
    isTrusted: boolean;
    trustScore: number;
    lastLoginAt: string;
    firstLoginAt: string;
  }>;
  riskAnalysis?: {
    overallRiskLevel: string;
    riskFactors: string[];
    recommendations: string[];
  };
}

/**
 * 风险评估结果接口
 */
export interface RiskAssessmentResult {
  riskAssessment: {
    level: string;
    score: number;
    reason: string;
    factors: string[];
    needVerification: boolean;
    recommendedActions: string[];
  };
  location: {
    country: string;
    province: string;
    city: string;
    isp: string;
    displayName: string;
  };
  userHistory: {
    lastLoginLocation?: string;
    commonLocationCount: number;
    isNewLocation: boolean;
  };
  recommendations?: {
    verificationMethods: string[];
    urgency: string;
    reason: string;
  };
  userProfile?: {
    riskLevel: string;
    trustScore: number;
    characteristics: string[];
    recommendations: string[];
  };
}

/**
 * IP地理位置查询处理服务
 * 处理所有与IP地理位置相关的读操作查询
 */
@Injectable()
export class IpLocationQueryService {
  constructor(
    @InjectRepository(UserCommonLocation)
    private readonly userLocationRepository: Repository<UserCommonLocation>,
    private readonly ipLocationDomainService: IpLocationDomainService,
    private readonly riskAssessmentService: RiskAssessmentDomainService,
    private readonly locationComparisonService: LocationComparisonService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  /**
   * 处理IP位置查询
   */
  async handleGetLocationByIp(
    query: GetLocationByIpQuery
  ): Promise<QueryResult<LocationQueryResult>> {
    const startTime = Date.now();

    try {
      // 1. 验证查询
      const validation = query.validate();
      if (!validation.isValid) {
        return createFailureQueryResult(
          validation.errors,
          '查询验证失败',
          Date.now() - startTime
        );
      }

      // 2. 检查缓存
      let fromCache = false;
      if (query.canUseCache) {
        const cached = await this.cacheManager.get<LocationQueryResult>(query.getCacheKey());
        if (cached) {
          return createSuccessQueryResult(
            cached,
            '从缓存获取位置信息',
            Date.now() - startTime,
            true,
            query.getCacheKey()
          );
        }
      }

      // 3. 解析IP地理位置
      const location = await this.ipLocationDomainService.resolveLocation(query.ipAddress);

      // 4. 构建基础结果
      const result: LocationQueryResult = {
        ip: query.ipAddress.value,
        country: location.country,
        province: location.province,
        city: location.city,
        isp: location.isp,
        dataSource: location.dataSource,
        confidence: location.confidence,
        isHighQuality: location.isHighQuality,
        displayName: location.displayName
      };

      // 5. 如果需要风险评估
      if (query.needsRiskAssessment) {
        // 创建简单的用户历史（用于基础风险评估）
        const basicUserHistory: UserLocationHistory = {
          commonLocations: [],
          totalLoginCount: 0,
          riskLoginCount: 0,
          accountAge: 0
        };

        const riskScore = this.riskAssessmentService.assessLoginRisk(
          location,
          basicUserHistory,
          query.ipAddress
        );

        result.riskAssessment = {
          level: riskScore.level,
          score: riskScore.score,
          reason: riskScore.reason,
          factors: riskScore.factors,
          needVerification: riskScore.needsVerification,
          recommendedActions: riskScore.recommendedVerificationMethods
        };
      }

      // 6. 缓存结果
      if (query.canUseCache) {
        await this.cacheManager.set(query.getCacheKey(), result, 24 * 60 * 60 * 1000); // 24小时
      }

      return createSuccessQueryResult(
        result,
        'IP位置查询成功',
        Date.now() - startTime,
        fromCache,
        query.getCacheKey()
      );

    } catch (error) {
      if (error instanceof IpLocationDomainException) {
        return createFailureQueryResult(
          [error.message],
          '领域业务错误',
          Date.now() - startTime
        );
      }

      return createFailureQueryResult(
        ['IP位置查询时发生未知错误'],
        error.message,
        Date.now() - startTime
      );
    }
  }

  /**
   * 处理用户位置统计查询
   */
  async handleGetUserLocationStats(
    query: GetUserLocationStatsQuery
  ): Promise<QueryResult<UserLocationStatsResult>> {
    const startTime = Date.now();

    try {
      // 1. 验证查询
      const validation = query.validate();
      if (!validation.isValid) {
        return createFailureQueryResult(
          validation.errors,
          '查询验证失败',
          Date.now() - startTime
        );
      }

      // 2. 检查缓存
      const cacheKey = query.getCacheKey();
      const cached = await this.cacheManager.get<UserLocationStatsResult>(cacheKey);
      if (cached) {
        return createSuccessQueryResult(
          cached,
          '从缓存获取用户位置统计',
          Date.now() - startTime,
          true,
          cacheKey
        );
      }

      // 3. 查询用户位置数据
      const queryBuilder = this.userLocationRepository.createQueryBuilder('location')
        .where('location.userId = :userId', { userId: query.userId })
        .andWhere('location.lastLoginAt >= :startDate', { startDate: query.getStartDate() })
        .andWhere('location.lastLoginAt <= :endDate', { endDate: query.getEndDate() });

      if (query.minLoginCount > 1) {
        queryBuilder.andWhere('location.loginCount >= :minLoginCount', { 
          minLoginCount: query.minLoginCount 
        });
      }

      const locations = await queryBuilder.getMany();

      // 4. 构建统计结果
      const result: UserLocationStatsResult = {
        userId: query.userId,
        queryPeriod: {
          days: query.days,
          startDate: query.getStartDate().toISOString(),
          endDate: query.getEndDate().toISOString()
        },
        summary: {
          totalLocations: locations.length,
          trustedLocations: locations.filter(l => l.isTrusted).length,
          totalLogins: locations.reduce((sum, l) => sum + l.loginCount, 0),
          riskLogins: locations.filter(l => l.trustScore < 50).reduce((sum, l) => sum + l.loginCount, 0),
          uniqueProvinces: new Set(locations.map(l => l.province)).size,
          uniqueCities: new Set(locations.map(l => l.city)).size
        },
        locations: locations.map(location => ({
          country: location.country,
          province: location.province,
          city: location.city,
          isp: location.isp,
          loginCount: location.loginCount,
          isTrusted: location.isTrusted,
          trustScore: location.trustScore,
          lastLoginAt: location.lastLoginAt.toISOString(),
          firstLoginAt: location.firstLoginAt.toISOString()
        }))
      };

      // 5. 如果需要风险分析
      if (query.includeRiskAnalysis) {
        const userHistory: UserLocationHistory = {
          commonLocations: locations.map(l => ({
            location: GeographicLocation.create(l.country, l.province, l.city, l.isp),
            loginCount: l.loginCount,
            isTrusted: l.isTrusted,
            lastLoginAt: l.lastLoginAt,
            firstLoginAt: l.firstLoginAt
          })),
          totalLoginCount: result.summary.totalLogins,
          riskLoginCount: result.summary.riskLogins,
          accountAge: 365 // 假设账户年龄，实际应该从用户表获取
        };

        const userProfile = this.riskAssessmentService.calculateUserRiskProfile(userHistory);

        result.riskAnalysis = {
          overallRiskLevel: userProfile.riskLevel,
          riskFactors: userProfile.characteristics,
          recommendations: userProfile.recommendations
        };
      }

      // 6. 缓存结果
      const cacheTTL = query.isShortTermQuery ? 5 * 60 * 1000 : 60 * 60 * 1000; // 短期5分钟，其他1小时
      await this.cacheManager.set(cacheKey, result, cacheTTL);

      return createSuccessQueryResult(
        result,
        '用户位置统计查询成功',
        Date.now() - startTime,
        false,
        cacheKey
      );

    } catch (error) {
      if (error instanceof IpLocationDomainException) {
        return createFailureQueryResult(
          [error.message],
          '领域业务错误',
          Date.now() - startTime
        );
      }

      return createFailureQueryResult(
        ['用户位置统计查询时发生未知错误'],
        error.message,
        Date.now() - startTime
      );
    }
  }

  /**
   * 处理登录风险评估查询
   */
  async handleAssessLoginRisk(
    query: AssessLoginRiskQuery
  ): Promise<QueryResult<RiskAssessmentResult>> {
    const startTime = Date.now();

    try {
      // 1. 验证查询
      const validation = query.validate();
      if (!validation.isValid) {
        return createFailureQueryResult(
          validation.errors,
          '查询验证失败',
          Date.now() - startTime
        );
      }

      // 2. 检查缓存（高优先级查询不使用缓存）
      let fromCache = false;
      if (!query.isHighPriority) {
        const cached = await this.cacheManager.get<RiskAssessmentResult>(query.getCacheKey());
        if (cached) {
          return createSuccessQueryResult(
            cached,
            '从缓存获取风险评估结果',
            Date.now() - startTime,
            true,
            query.getCacheKey()
          );
        }
      }

      // 3. 解析当前位置
      const currentLocation = await this.ipLocationDomainService.resolveLocation(query.ipAddress);

      // 4. 获取用户历史位置
      const userLocations = await this.userLocationRepository.find({
        where: { userId: query.userId },
        order: { lastLoginAt: 'DESC' },
        take: 50 // 最近50个位置
      });

      const userHistory: UserLocationHistory = {
        commonLocations: userLocations.map(l => ({
          location: GeographicLocation.create(l.country, l.province, l.city, l.isp),
          loginCount: l.loginCount,
          isTrusted: l.isTrusted,
          lastLoginAt: l.lastLoginAt,
          firstLoginAt: l.firstLoginAt
        })),
        totalLoginCount: userLocations.reduce((sum, l) => sum + l.loginCount, 0),
        riskLoginCount: userLocations.filter(l => l.trustScore < 50).reduce((sum, l) => sum + l.loginCount, 0),
        accountAge: 365 // 假设账户年龄
      };

      // 5. 评估风险
      const riskScore = this.riskAssessmentService.assessLoginRisk(
        currentLocation,
        userHistory,
        query.ipAddress
      );

      // 6. 构建结果
      const result: RiskAssessmentResult = {
        riskAssessment: {
          level: riskScore.level,
          score: riskScore.score,
          reason: riskScore.reason,
          factors: riskScore.factors,
          needVerification: riskScore.needsVerification,
          recommendedActions: riskScore.recommendedVerificationMethods
        },
        location: {
          country: currentLocation.country,
          province: currentLocation.province,
          city: currentLocation.city,
          isp: currentLocation.isp,
          displayName: currentLocation.displayName
        },
        userHistory: {
          lastLoginLocation: userLocations.length > 0 ? userLocations[0].displayName : undefined,
          commonLocationCount: userLocations.length,
          isNewLocation: !userLocations.some(l => 
            l.province === currentLocation.province && l.city === currentLocation.city
          )
        }
      };

      // 7. 如果需要验证建议
      if (query.includeRecommendations) {
        const verificationNeeds = this.riskAssessmentService.analyzeVerificationNeeds(
          riskScore,
          userHistory
        );

        result.recommendations = {
          verificationMethods: verificationNeeds.verificationMethods,
          urgency: verificationNeeds.urgency,
          reason: verificationNeeds.reason
        };
      }

      // 8. 如果需要用户档案
      if (query.includeUserProfile) {
        const userProfile = this.riskAssessmentService.calculateUserRiskProfile(userHistory);
        result.userProfile = userProfile;
      }

      // 9. 缓存结果（低优先级查询缓存5分钟）
      if (!query.isHighPriority) {
        await this.cacheManager.set(query.getCacheKey(), result, 5 * 60 * 1000);
      }

      return createSuccessQueryResult(
        result,
        '登录风险评估完成',
        Date.now() - startTime,
        fromCache,
        query.getCacheKey()
      );

    } catch (error) {
      if (error instanceof IpLocationDomainException) {
        return createFailureQueryResult(
          [error.message],
          '领域业务错误',
          Date.now() - startTime
        );
      }

      return createFailureQueryResult(
        ['登录风险评估时发生未知错误'],
        error.message,
        Date.now() - startTime
      );
    }
  }
}
