"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherAuditModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const teacher_audit_service_1 = require("./teacher_audit.service");
const teacher_audit_controller_1 = require("./teacher_audit.controller");
const teacher_audit_entity_1 = require("./entities/teacher_audit.entity");
const user_info_entity_1 = require("../../util/database/mysql/user_info/entities/user_info.entity");
const attachment_module_1 = require("../attachment/attachment.module");
const teacher_audit_attachment_module_1 = require("../teacher_audit_attachment/teacher_audit_attachment.module");
const user_school_relation_entity_1 = require("../../util/database/mysql/user_school_relation/entities/user_school_relation.entity");
const user_school_relation_module_1 = require("../user_school_relation/user_school_relation.module");
const user_school_module_1 = require("../user_school/user_school.module");
const user_school_entity_1 = require("../../util/database/mysql/user_school/entities/user_school.entity");
const user_info_module_1 = require("../user_info/user_info.module");
const user_role_entity_1 = require("../../util/database/mysql/user_role/entities/user_role.entity");
const user_role_relation_entity_1 = require("../../util/database/mysql/user_role_relation/entities/user_role_relation.entity");
let TeacherAuditModule = class TeacherAuditModule {
};
exports.TeacherAuditModule = TeacherAuditModule;
exports.TeacherAuditModule = TeacherAuditModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                teacher_audit_entity_1.TeacherAudit,
                user_info_entity_1.UserInfo,
                user_school_relation_entity_1.UserSchoolRelation,
                user_school_entity_1.UserSchool,
                user_role_entity_1.UserRole,
                user_role_relation_entity_1.UserRoleRelation
            ]),
            attachment_module_1.AttachmentModule,
            teacher_audit_attachment_module_1.TeacherAuditAttachmentModule,
            user_school_relation_module_1.UserSchoolRelationModule,
            user_school_module_1.UserSchoolModule,
            user_info_module_1.UserInfoModule
        ],
        controllers: [teacher_audit_controller_1.TeacherAuditController],
        providers: [teacher_audit_service_1.TeacherAuditService],
        exports: [teacher_audit_service_1.TeacherAuditService],
    })
], TeacherAuditModule);
//# sourceMappingURL=teacher_audit.module.js.map