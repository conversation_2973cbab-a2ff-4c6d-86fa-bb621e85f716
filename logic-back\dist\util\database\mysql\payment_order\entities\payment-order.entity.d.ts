export declare class PaymentOrder {
    id: string;
    businessOrderId: string;
    channelOrderId: string;
    channel: string;
    amount: number;
    description: string;
    status: string;
    parameters: Record<string, any>;
    result: Record<string, any>;
    notifyData: Record<string, any>;
    userId: string;
    clientIp: string;
    notifyUrl: string;
    returnUrl: string;
    cancelUrl: string;
    expiredAt: Date;
    paidAt: Date;
    closedAt: Date;
    version: number;
    createdAt: Date;
    updatedAt: Date;
}
