"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RefundService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundService = void 0;
const common_1 = require("@nestjs/common");
const payment_order_service_1 = require("../../util/database/mysql/payment_order/payment-order.service");
const payment_refund_service_1 = require("../../util/database/mysql/payment_refund/payment-refund.service");
const payment_log_service_1 = require("../../util/database/mysql/payment_log/payment-log.service");
const alipay_strategy_1 = require("../strategies/alipay.strategy");
const wechat_pay_strategy_1 = require("../strategies/wechat-pay.strategy");
const notify_service_1 = require("../notification/notify.service");
const lock_manager_1 = require("../lock/lock.manager");
const create_payment_refund_dto_1 = require("../../util/database/mysql/payment_refund/dto/create-payment-refund.dto");
const payment_log_dto_1 = require("../../util/database/mysql/payment_log/dto/payment-log.dto");
const moment = require("moment");
let RefundService = RefundService_1 = class RefundService {
    paymentOrderService;
    paymentRefundService;
    paymentLogService;
    alipayStrategy;
    wechatPayStrategy;
    notifyService;
    lockManager;
    logger = new common_1.Logger(RefundService_1.name);
    constructor(paymentOrderService, paymentRefundService, paymentLogService, alipayStrategy, wechatPayStrategy, notifyService, lockManager) {
        this.paymentOrderService = paymentOrderService;
        this.paymentRefundService = paymentRefundService;
        this.paymentLogService = paymentLogService;
        this.alipayStrategy = alipayStrategy;
        this.wechatPayStrategy = wechatPayStrategy;
        this.notifyService = notifyService;
        this.lockManager = lockManager;
    }
    normalizePaymentChannel(channel) {
        if (channel === 'wechatpay' || channel === 'wxpay') {
            return create_payment_refund_dto_1.PaymentChannel.WECHAT;
        }
        if (channel === 'alipay' || channel === 'zhifubao') {
            return create_payment_refund_dto_1.PaymentChannel.ALIPAY;
        }
        return channel;
    }
    generateRefundNo() {
        const date = moment().format('YYYYMMDDHHmmss');
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        return `R${date}${random}`;
    }
    async createRefund(userId, refundRequestDto) {
        const refundNo = this.generateRefundNo();
        return this.lockManager.withDistributedLock(`refund:create:${refundRequestDto.businessOrderId}`, async () => {
            try {
                const startTime = Date.now();
                this.logger.log(`创建退款申请: ${refundNo}, 订单号: ${refundRequestDto.businessOrderId}`);
                const orderInfo = await this.paymentOrderService.findByBusinessOrderId(refundRequestDto.businessOrderId);
                if (!orderInfo) {
                    throw new common_1.NotFoundException(`订单 ${refundRequestDto.businessOrderId} 不存在`);
                }
                if (orderInfo.status !== 'success') {
                    throw new common_1.BadRequestException(`订单 ${refundRequestDto.businessOrderId} 状态为 ${orderInfo.status}，不支持退款`);
                }
                if (refundRequestDto.amount > orderInfo.amount) {
                    throw new common_1.BadRequestException(`退款金额 ${refundRequestDto.amount} 超过订单支付金额 ${orderInfo.amount}`);
                }
                const pendingRefunds = await this.paymentRefundService.findByPaymentOrderId(orderInfo.id);
                const hasPendingRefund = pendingRefunds.some(refund => refund.status === create_payment_refund_dto_1.RefundStatus.PENDING || refund.status === create_payment_refund_dto_1.RefundStatus.PROCESSING);
                if (hasPendingRefund) {
                    throw new common_1.BadRequestException(`订单 ${refundRequestDto.businessOrderId} 存在处理中的退款申请，请等待处理完成后再申请`);
                }
                const refundRecord = await this.paymentRefundService.create({
                    businessRefundId: refundNo,
                    paymentOrderId: orderInfo.id,
                    amount: refundRequestDto.amount,
                    reason: refundRequestDto.description || refundRequestDto.reason,
                    channel: orderInfo.channel,
                    status: create_payment_refund_dto_1.RefundStatus.PENDING,
                    userId,
                    operatorId: refundRequestDto.operatorId,
                    notifyUrl: refundRequestDto.notifyUrl,
                    parameters: {
                        originalOrderNo: refundRequestDto.businessOrderId,
                        ...refundRequestDto.extraData
                    }
                });
                await this.paymentLogService.create({
                    logType: payment_log_dto_1.LogType.REFUND,
                    operation: payment_log_dto_1.OperationType.CREATE,
                    orderNo: refundRequestDto.businessOrderId,
                    refundNo: refundNo,
                    requestData: refundRequestDto,
                    status: payment_log_dto_1.LogStatus.SUCCESS,
                    executionTime: Date.now() - startTime,
                    operatorId: userId
                });
                try {
                    let refundResult;
                    const normalizedChannel = this.normalizePaymentChannel(orderInfo.channel);
                    if (normalizedChannel === create_payment_refund_dto_1.PaymentChannel.ALIPAY) {
                        refundResult = await this.alipayStrategy.refund({
                            outTradeNo: refundRequestDto.businessOrderId,
                            outRefundNo: refundNo,
                            refundAmount: refundRequestDto.amount,
                            reason: refundRequestDto.description || refundRequestDto.reason,
                        });
                    }
                    else if (normalizedChannel === create_payment_refund_dto_1.PaymentChannel.WECHAT) {
                        refundResult = await this.wechatPayStrategy.refund({
                            outTradeNo: refundRequestDto.businessOrderId,
                            outRefundNo: refundNo,
                            refundAmount: refundRequestDto.amount,
                            reason: refundRequestDto.description || refundRequestDto.reason,
                        });
                    }
                    else {
                        throw new common_1.BadRequestException(`不支持的支付渠道: ${orderInfo.channel}`);
                    }
                    const newStatus = refundResult.success ? create_payment_refund_dto_1.RefundStatus.SUCCESS : create_payment_refund_dto_1.RefundStatus.FAILED;
                    await this.paymentRefundService.updateStatus(refundRecord.id, newStatus, refundResult);
                    await this.paymentLogService.create({
                        logType: payment_log_dto_1.LogType.REFUND,
                        operation: payment_log_dto_1.OperationType.UPDATE,
                        orderNo: refundRequestDto.businessOrderId,
                        refundNo: refundNo,
                        paymentChannel: orderInfo.channel,
                        requestData: { refundNo, businessOrderId: refundRequestDto.businessOrderId },
                        responseData: refundResult,
                        status: refundResult.success ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                        errorMessage: refundResult.success ? undefined : refundResult.errorMessage,
                        executionTime: Date.now() - startTime,
                        operatorId: userId
                    });
                    if (refundResult.success) {
                        await this.notifyService.handleRefundSuccess(refundNo, refundResult.refundId || '', refundRequestDto.amount, orderInfo.channel, {
                            businessOrderId: refundRequestDto.businessOrderId,
                            userId,
                            operatorId: refundRequestDto.operatorId,
                        });
                    }
                    else {
                        await this.notifyService.handleRefundFail(refundNo, refundResult.errorMessage || '退款处理失败', orderInfo.channel, {
                            businessOrderId: refundRequestDto.businessOrderId,
                            userId,
                            operatorId: refundRequestDto.operatorId,
                        });
                    }
                    return {
                        refundNo,
                        businessOrderId: refundRequestDto.businessOrderId,
                        amount: refundRequestDto.amount,
                        status: newStatus,
                        createTime: refundRecord.createdAt,
                        finishTime: refundResult.success ? new Date() : undefined,
                        channelRefundNo: refundResult.refundId,
                        failReason: refundResult.success ? undefined : refundResult.errorMessage,
                        notifyUrl: refundRequestDto.notifyUrl
                    };
                }
                catch (error) {
                    await this.paymentRefundService.updateStatus(refundRecord.id, create_payment_refund_dto_1.RefundStatus.FAILED, { error: error.message });
                    await this.paymentLogService.create({
                        logType: payment_log_dto_1.LogType.REFUND,
                        operation: payment_log_dto_1.OperationType.UPDATE,
                        orderNo: refundRequestDto.businessOrderId,
                        refundNo: refundNo,
                        paymentChannel: orderInfo.channel,
                        requestData: { refundNo, businessOrderId: refundRequestDto.businessOrderId },
                        responseData: { error: error.message },
                        status: payment_log_dto_1.LogStatus.FAIL,
                        errorMessage: error.message,
                        executionTime: Date.now() - startTime,
                        operatorId: userId
                    });
                    throw error;
                }
            }
            catch (error) {
                this.logger.error(`创建退款申请失败: ${error.message}`, error.stack);
                throw error;
            }
        }, 10000);
    }
    async queryRefundStatus(refundQueryDto) {
        try {
            const startTime = Date.now();
            this.logger.log(`查询退款状态: ${refundQueryDto.refundNo}`);
            const refundRecord = await this.paymentRefundService.findByBusinessRefundId(refundQueryDto.refundNo);
            if (!refundRecord) {
                throw new common_1.NotFoundException(`退款记录 ${refundQueryDto.refundNo} 不存在`);
            }
            const paymentOrder = await this.paymentOrderService.findOne(refundRecord.paymentOrderId);
            if (refundRecord.status === create_payment_refund_dto_1.RefundStatus.SUCCESS ||
                refundRecord.status === create_payment_refund_dto_1.RefundStatus.FAILED) {
                return {
                    refundNo: refundRecord.businessRefundId,
                    businessOrderId: paymentOrder.businessOrderId,
                    amount: refundRecord.amount,
                    status: refundRecord.status,
                    createTime: refundRecord.createdAt,
                    finishTime: refundRecord.refundedAt,
                    channelRefundNo: refundRecord.channelRefundId,
                    failReason: refundRecord.result?.errorMessage,
                    notifyUrl: refundRecord.notifyUrl
                };
            }
            try {
                const channel = refundQueryDto.channel || refundRecord.channel;
                const normalizedChannel = this.normalizePaymentChannel(channel);
                let refundResult;
                if (normalizedChannel === create_payment_refund_dto_1.PaymentChannel.ALIPAY) {
                    refundResult = await this.alipayStrategy.queryRefundStatus(refundRecord.businessRefundId);
                }
                else if (normalizedChannel === create_payment_refund_dto_1.PaymentChannel.WECHAT) {
                    refundResult = await this.wechatPayStrategy.queryRefundStatus(refundRecord.businessRefundId);
                }
                else {
                    throw new common_1.BadRequestException(`不支持的支付渠道: ${channel}`);
                }
                if (refundResult.status !== refundRecord.status) {
                    await this.paymentRefundService.updateStatus(refundRecord.id, refundResult.status, refundResult.rawResult || refundResult);
                    await this.paymentLogService.create({
                        logType: payment_log_dto_1.LogType.REFUND,
                        operation: payment_log_dto_1.OperationType.QUERY,
                        orderNo: paymentOrder.businessOrderId,
                        refundNo: refundQueryDto.refundNo,
                        paymentChannel: channel,
                        requestData: refundQueryDto,
                        responseData: refundResult,
                        status: payment_log_dto_1.LogStatus.SUCCESS,
                        executionTime: Date.now() - startTime
                    });
                    if (refundResult.status === create_payment_refund_dto_1.RefundStatus.SUCCESS) {
                        await this.notifyService.handleRefundSuccess(refundRecord.businessRefundId, refundResult.refundId || '', refundRecord.amount, channel, {
                            businessOrderId: paymentOrder.businessOrderId,
                            userId: refundRecord.userId,
                        });
                    }
                    else if (refundResult.status === create_payment_refund_dto_1.RefundStatus.FAILED) {
                        await this.notifyService.handleRefundFail(refundRecord.businessRefundId, refundResult.errorMessage || '退款失败', channel, {
                            businessOrderId: paymentOrder.businessOrderId,
                            userId: refundRecord.userId,
                        });
                    }
                }
                return {
                    refundNo: refundRecord.businessRefundId,
                    businessOrderId: paymentOrder.businessOrderId,
                    amount: refundRecord.amount,
                    status: refundResult.status,
                    createTime: refundRecord.createdAt,
                    finishTime: refundResult.status === create_payment_refund_dto_1.RefundStatus.SUCCESS ? new Date() : undefined,
                    channelRefundNo: refundResult.refundId,
                    failReason: refundResult.status === create_payment_refund_dto_1.RefundStatus.FAILED ? refundResult.errorMessage : undefined,
                    notifyUrl: refundRecord.notifyUrl
                };
            }
            catch (error) {
                this.logger.error(`查询第三方退款状态失败: ${error.message}`, error.stack);
                await this.paymentLogService.create({
                    logType: payment_log_dto_1.LogType.REFUND,
                    operation: payment_log_dto_1.OperationType.QUERY,
                    orderNo: paymentOrder.businessOrderId,
                    refundNo: refundQueryDto.refundNo,
                    paymentChannel: refundRecord.channel,
                    requestData: refundQueryDto,
                    responseData: { error: error.message },
                    status: payment_log_dto_1.LogStatus.FAIL,
                    errorMessage: error.message,
                    executionTime: Date.now() - startTime
                });
                return {
                    refundNo: refundRecord.businessRefundId,
                    businessOrderId: paymentOrder.businessOrderId,
                    amount: refundRecord.amount,
                    status: refundRecord.status,
                    createTime: refundRecord.createdAt,
                    finishTime: refundRecord.refundedAt,
                    channelRefundNo: refundRecord.channelRefundId,
                    failReason: `查询异常: ${error.message}`,
                    notifyUrl: refundRecord.notifyUrl
                };
            }
        }
        catch (error) {
            this.logger.error(`查询退款状态失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async checkRefundStatus(refundNo) {
        return this.queryRefundStatus({ refundNo });
    }
};
exports.RefundService = RefundService;
exports.RefundService = RefundService = RefundService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_order_service_1.PaymentOrderService,
        payment_refund_service_1.PaymentRefundService,
        payment_log_service_1.PaymentLogService,
        alipay_strategy_1.AlipayStrategy,
        wechat_pay_strategy_1.WechatPayStrategy,
        notify_service_1.NotifyService,
        lock_manager_1.LockManager])
], RefundService);
//# sourceMappingURL=refund.service.js.map