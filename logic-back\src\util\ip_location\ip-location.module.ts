import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// 实体
import { UserCommonLocation } from './domain/entities/user-common-location.entity';

// 控制器
import { IpLocationController } from './controllers/ip-location.controller';

// 服务
import { IpLocationApplicationService } from './application/services/ip-location-application.service';

// 工具类
import { IpLocationUtil } from './utils/ip-location.util';
import { RiskAssessmentUtil } from './utils/risk-assessment.util';

// 基础设施依赖
import { RedisModule } from '../database/redis/redis.module';
import { LoggerService } from '../../common/logger/logger.service';

/**
 * IP地理位置模块
 * 提供IP地理位置解析、风险评估、用户位置统计等功能
 */
@Module({
  imports: [
    // 注册实体
    TypeOrmModule.forFeature([UserCommonLocation]),
    
    // 导入Redis模块
    RedisModule,
  ],
  controllers: [
    IpLocationController,
  ],
  providers: [
    // 应用服务
    IpLocationApplicationService,
    
    // 工具类
    IpLocationUtil,
    RiskAssessmentUtil,
    
    // 日志服务
    LoggerService,
  ],
  exports: [
    // 导出核心服务供其他模块使用
    IpLocationApplicationService,
    IpLocationUtil,
    RiskAssessmentUtil,
  ],
})
export class IpLocationModule {}
