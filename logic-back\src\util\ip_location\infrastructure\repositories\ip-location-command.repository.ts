import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { IIpLocationCommandRepository } from './ip-location.repository.interface';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';

/**
 * IP地理位置命令仓储实现
 * 基于现有TypeORM实现写操作
 */
@Injectable()
export class IpLocationCommandRepository implements IIpLocationCommandRepository {
  constructor(
    @InjectRepository(UserCommonLocation)
    private readonly repository: Repository<UserCommonLocation>
  ) {}

  /**
   * 查找用户的常用位置
   */
  async findUserCommonLocation(
    userId: number,
    location: GeographicLocation
  ): Promise<UserCommonLocation | null> {
    return await this.repository.findOne({
      where: {
        userId,
        country: location.country,
        province: location.province,
        city: location.city
      }
    });
  }

  /**
   * 获取用户所有常用位置
   */
  async findUserCommonLocations(
    userId: number,
    options: {
      limit?: number;
      onlyTrusted?: boolean;
      minLoginCount?: number;
      orderBy?: 'loginCount' | 'lastLoginAt' | 'trustScore';
      orderDirection?: 'ASC' | 'DESC';
    } = {}
  ): Promise<UserCommonLocation[]> {
    const queryBuilder = this.repository.createQueryBuilder('location')
      .where('location.userId = :userId', { userId });

    if (options.onlyTrusted) {
      queryBuilder.andWhere('location.isTrusted = :isTrusted', { isTrusted: true });
    }

    if (options.minLoginCount) {
      queryBuilder.andWhere('location.loginCount >= :minLoginCount', { 
        minLoginCount: options.minLoginCount 
      });
    }

    if (options.orderBy) {
      queryBuilder.orderBy(
        `location.${options.orderBy}`, 
        options.orderDirection || 'DESC'
      );
    }

    if (options.limit) {
      queryBuilder.limit(options.limit);
    }

    return await queryBuilder.getMany();
  }

  /**
   * 获取用户在指定时间范围内的位置记录
   */
  async findUserLocationsByDateRange(
    userId: number,
    startDate: Date,
    endDate: Date,
    options: {
      minLoginCount?: number;
      includeTrusted?: boolean;
    } = {}
  ): Promise<UserCommonLocation[]> {
    const queryBuilder = this.repository.createQueryBuilder('location')
      .where('location.userId = :userId', { userId })
      .andWhere('location.lastLoginAt >= :startDate', { startDate })
      .andWhere('location.lastLoginAt <= :endDate', { endDate });

    if (options.minLoginCount) {
      queryBuilder.andWhere('location.loginCount >= :minLoginCount', { 
        minLoginCount: options.minLoginCount 
      });
    }

    if (options.includeTrusted === false) {
      queryBuilder.andWhere('location.isTrusted = :isTrusted', { isTrusted: false });
    }

    return await queryBuilder.getMany();
  }

  /**
   * 保存或更新用户常用位置
   */
  async saveUserCommonLocation(userLocation: UserCommonLocation): Promise<UserCommonLocation> {
    return await this.repository.save(userLocation);
  }

  /**
   * 创建新的用户常用位置
   */
  async createUserCommonLocation(
    userId: number,
    location: GeographicLocation,
    initialData: Partial<UserCommonLocation> = {}
  ): Promise<UserCommonLocation> {
    const now = new Date();
    
    const userLocation = this.repository.create({
      userId,
      country: location.country,
      province: location.province,
      city: location.city,
      isp: location.isp,
      dataSource: location.dataSource,
      hasEmptyFields: location.hasEmptyFields,
      confidence: location.confidence,
      displayName: location.displayName,
      loginCount: 1,
      lastLoginAt: now,
      firstLoginAt: now,
      isTrusted: false,
      trustScore: this.calculateInitialTrustScore(location),
      ...initialData
    });

    return await this.repository.save(userLocation);
  }

  /**
   * 增加位置登录次数
   */
  async incrementLoginCount(
    userLocationId: number,
    incrementBy: number = 1
  ): Promise<UserCommonLocation> {
    await this.repository.increment(
      { id: userLocationId },
      'loginCount',
      incrementBy
    );

    // 更新最后登录时间
    await this.repository.update(userLocationId, {
      lastLoginAt: new Date()
    });

    const updated = await this.repository.findOne({
      where: { id: userLocationId }
    });

    if (!updated) {
      throw new Error(`用户位置记录不存在: ${userLocationId}`);
    }

    return updated;
  }

  /**
   * 批量更新可信位置
   */
  async updateTrustedLocations(
    userId: number,
    province: string,
    city: string,
    isTrusted: boolean,
    trustReason?: string
  ): Promise<number> {
    const updateData: any = {
      isTrusted,
      trustedAt: new Date()
    };

    if (trustReason) {
      updateData.trustReason = trustReason;
    }

    if (isTrusted) {
      // 如果设置为可信，提升信任分数
      updateData.trustScore = () => 'LEAST(trust_score + 20, 100)';
    }

    const result = await this.repository.update(
      {
        userId,
        province,
        city
      },
      updateData
    );

    return result.affected || 0;
  }

  /**
   * 更新位置信任分数
   */
  async updateTrustScore(
    userLocationId: number,
    newTrustScore: number
  ): Promise<UserCommonLocation> {
    await this.repository.update(userLocationId, {
      trustScore: Math.max(0, Math.min(100, newTrustScore))
    });

    const updated = await this.repository.findOne({
      where: { id: userLocationId }
    });

    if (!updated) {
      throw new Error(`用户位置记录不存在: ${userLocationId}`);
    }

    return updated;
  }

  /**
   * 删除用户位置记录
   */
  async deleteUserCommonLocation(userLocationId: number): Promise<boolean> {
    const result = await this.repository.delete(userLocationId);
    return (result.affected || 0) > 0;
  }

  /**
   * 获取用户位置统计信息
   */
  async getUserLocationStats(
    userId: number,
    days: number
  ): Promise<{
    totalLocations: number;
    trustedLocations: number;
    totalLogins: number;
    riskLogins: number;
    uniqueProvinces: number;
    uniqueCities: number;
  }> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const locations = await this.findUserLocationsByDateRange(
      userId,
      startDate,
      new Date()
    );

    return {
      totalLocations: locations.length,
      trustedLocations: locations.filter(l => l.isTrusted).length,
      totalLogins: locations.reduce((sum, l) => sum + l.loginCount, 0),
      riskLogins: locations.filter(l => l.trustScore < 50).reduce((sum, l) => sum + l.loginCount, 0),
      uniqueProvinces: new Set(locations.map(l => l.province)).size,
      uniqueCities: new Set(locations.map(l => l.city)).size
    };
  }

  /**
   * 检查位置是否存在于用户历史中
   */
  async hasLocationInHistory(
    userId: number,
    location: GeographicLocation,
    level: 'country' | 'province' | 'city'
  ): Promise<boolean> {
    const queryBuilder = this.repository.createQueryBuilder('location')
      .where('location.userId = :userId', { userId });

    switch (level) {
      case 'country':
        queryBuilder.andWhere('location.country = :country', { 
          country: location.country 
        });
        break;
      case 'province':
        queryBuilder.andWhere('location.country = :country', { 
          country: location.country 
        })
        .andWhere('location.province = :province', { 
          province: location.province 
        });
        break;
      case 'city':
        queryBuilder.andWhere('location.country = :country', { 
          country: location.country 
        })
        .andWhere('location.province = :province', { 
          province: location.province 
        })
        .andWhere('location.city = :city', { 
          city: location.city 
        });
        break;
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  /**
   * 计算初始信任分数
   */
  private calculateInitialTrustScore(location: GeographicLocation): number {
    let score = 50; // 基础分数

    // 数据质量影响
    if (location.isHighQuality) {
      score += 20;
    } else if (location.confidence > 50) {
      score += 10;
    }

    // 境内位置加分
    if (location.isDomestic) {
      score += 10;
    }

    return Math.min(score, 100);
  }
}
