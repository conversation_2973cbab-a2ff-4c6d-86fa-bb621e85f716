"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_common_location_entity_1 = require("./domain/entities/user-common-location.entity");
const ip_location_controller_1 = require("./controllers/ip-location.controller");
const ip_location_application_service_1 = require("./application/services/ip-location-application.service");
const ip_location_util_1 = require("./utils/ip-location.util");
const risk_assessment_util_1 = require("./utils/risk-assessment.util");
const redis_module_1 = require("../database/redis/redis.module");
const logger_service_1 = require("../../common/logger/logger.service");
let IpLocationModule = class IpLocationModule {
};
exports.IpLocationModule = IpLocationModule;
exports.IpLocationModule = IpLocationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_common_location_entity_1.UserCommonLocation]),
            redis_module_1.RedisModule,
        ],
        controllers: [
            ip_location_controller_1.IpLocationController,
        ],
        providers: [
            ip_location_application_service_1.IpLocationApplicationService,
            ip_location_util_1.IpLocationUtil,
            risk_assessment_util_1.RiskAssessmentUtil,
            logger_service_1.LoggerService,
        ],
        exports: [
            ip_location_application_service_1.IpLocationApplicationService,
            ip_location_util_1.IpLocationUtil,
            risk_assessment_util_1.RiskAssessmentUtil,
        ],
    })
], IpLocationModule);
//# sourceMappingURL=ip-location.module.js.map