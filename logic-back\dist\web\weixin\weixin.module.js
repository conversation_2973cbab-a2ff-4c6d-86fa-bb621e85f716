"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeixinModule = void 0;
const common_1 = require("@nestjs/common");
const weixin_controller_1 = require("./weixin.controller");
const user_auth_module_1 = require("../user_auth/user_auth.module");
const weixin_utils_module_1 = require("../utils/weixin_utils.module");
let WeixinModule = class WeixinModule {
};
exports.WeixinModule = WeixinModule;
exports.WeixinModule = WeixinModule = __decorate([
    (0, common_1.Module)({
        imports: [
            user_auth_module_1.UserAuthModule,
            weixin_utils_module_1.WeixinUtilsModule,
        ],
        controllers: [
            weixin_controller_1.WeixinController,
        ],
        providers: [],
        exports: [],
    })
], WeixinModule);
//# sourceMappingURL=weixin.module.js.map