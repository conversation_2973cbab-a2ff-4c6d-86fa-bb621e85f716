{"version": 3, "file": "user_tag.service.js", "sourceRoot": "", "sources": ["../../../src/web/user_tag/user_tag.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2EAAqE;AAM9D,IAAM,cAAc,GAApB,MAAM,cAAc;IAEN;IADnB,YACmB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IACtC,CAAC;IAKJ,KAAK,CAAC,MAAM,CAAC,IAIZ;QACC,MAAM,YAAY,GAAiB;YACjC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IACpD,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,IAI3B;QACC,MAAM,YAAY,GAAiB;YACjC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;IACxD,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,EAAU;QACxB,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC3C,CAAC;IAKD,KAAK,CAAC,IAAI,CAAC,MAIV;QACC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;CACF,CAAA;AA7DY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAGoB,wBAAU;GAF9B,cAAc,CA6D1B"}