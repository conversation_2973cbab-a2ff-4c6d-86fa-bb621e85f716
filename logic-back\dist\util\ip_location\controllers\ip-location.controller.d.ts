import { Request } from 'express';
import { IpLocationApplicationService } from '../application/services/ip-location-application.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../application/dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../application/dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../application/dto/responses/location-stats.response.dto';
export declare class IpLocationController {
    private readonly ipLocationApplicationService;
    constructor(ipLocationApplicationService: IpLocationApplicationService);
    queryIpLocation(query: IpQueryRequestDto): Promise<LocationInfoResponseDto>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<RiskAssessmentResponseDto>;
    getUserLocationStats(userId: number, days?: number, includeTrusted?: boolean): Promise<LocationStatsResponseDto>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<{
        message: string;
        data: any;
    }>;
    getCurrentIpLocation(request: Request): Promise<LocationInfoResponseDto>;
    healthCheck(): Promise<{
        status: string;
        timestamp: string;
        service: string;
    }>;
    private extractClientIP;
    private cleanAndValidateIP;
    private isValidIP;
}
