"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessmentResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class RiskAssessmentResponseDto {
    riskAssessment;
    location;
    userHistory;
}
exports.RiskAssessmentResponseDto = RiskAssessmentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '风险评估结果',
        type: 'object',
        properties: {
            level: { type: 'string', example: 'HIGH', enum: ['LOW', 'MEDIUM', 'HIGH'] },
            score: { type: 'number', example: 85 },
            reason: { type: 'string', example: '跨省登录' },
            factors: { type: 'array', items: { type: 'string' }, example: ['跨省登录', '新登录地', '运营商变化'] },
            needVerification: { type: 'boolean', example: true },
            recommendedActions: { type: 'array', items: { type: 'string' }, example: ['短信验证', '邮箱验证'] }
        }
    }),
    __metadata("design:type", Object)
], RiskAssessmentResponseDto.prototype, "riskAssessment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '位置信息',
        type: 'object',
        properties: {
            country: { type: 'string', example: '中国' },
            province: { type: 'string', example: '上海市' },
            city: { type: 'string', example: '上海市' },
            isp: { type: 'string', example: '电信' },
            displayName: { type: 'string', example: '中国 上海市 上海市' }
        }
    }),
    __metadata("design:type", Object)
], RiskAssessmentResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '用户历史信息',
        type: 'object',
        properties: {
            lastLoginLocation: { type: 'string', example: '广东省 深圳市' },
            commonLocationCount: { type: 'number', example: 2 },
            isNewLocation: { type: 'boolean', example: true }
        }
    }),
    __metadata("design:type", Object)
], RiskAssessmentResponseDto.prototype, "userHistory", void 0);
//# sourceMappingURL=risk-assessment.response.dto.js.map