import { Repository } from 'typeorm';
import { NotificationRecord } from './entities/notification-record.entity';
import { CreateNotificationRecordDto, UpdateNotificationRecordDto, QueryNotificationRecordDto, NotificationStatus } from './dto/notification-record.dto';
export declare class NotificationRecordService {
    private readonly notificationRecordRepository;
    private readonly logger;
    constructor(notificationRecordRepository: Repository<NotificationRecord>);
    create(createDto: CreateNotificationRecordDto): Promise<NotificationRecord>;
    update(id: string, updateDto: UpdateNotificationRecordDto): Promise<NotificationRecord>;
    findById(id: string): Promise<NotificationRecord | null>;
    findAll(queryDto: QueryNotificationRecordDto, page?: number, limit?: number): Promise<[NotificationRecord[], number]>;
    findPendingRetryNotifications(limit?: number): Promise<NotificationRecord[]>;
    updateStatus(ids: string[], status: NotificationStatus): Promise<void>;
    remove(id: string): Promise<void>;
    findByTargetIdAndType(targetId: string, notificationType: string, status?: string): Promise<NotificationRecord[]>;
}
