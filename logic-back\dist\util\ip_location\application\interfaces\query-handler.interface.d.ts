export interface IQueryHandler<TQuery, TResult> {
    handle(query: TQuery): Promise<TResult>;
    canHandle(query: TQuery): boolean;
    getHandlerName(): string;
    getCacheTTL?(query: TQuery): number;
}
export interface QueryResult<TData> {
    success: boolean;
    data?: TData;
    message?: string;
    errors?: string[];
    timestamp: Date;
    executionTime?: number;
    fromCache?: boolean;
    cacheKey?: string;
}
export declare function createSuccessQueryResult<TData>(data: TData, message?: string, executionTime?: number, fromCache?: boolean, cacheKey?: string): QueryResult<TData>;
export declare function createFailureQueryResult<TData>(errors: string[], message?: string, executionTime?: number): QueryResult<TData>;
