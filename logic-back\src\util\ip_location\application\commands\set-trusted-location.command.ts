import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';

/**
 * 设置可信位置命令
 * 封装设置用户可信登录地的写操作
 */
export class SetTrustedLocationCommand {
  public readonly userId: number;
  public readonly province: string;
  public readonly city: string;
  public readonly reason: string;
  public readonly setBy: 'USER' | 'SYSTEM' | 'ADMIN';
  public readonly timestamp: Date;

  constructor(
    userId: number,
    province: string,
    city: string,
    reason: string = '用户主动设置',
    setBy: 'USER' | 'SYSTEM' | 'ADMIN' = 'USER'
  ) {
    this.userId = userId;
    this.province = province.trim();
    this.city = city.trim();
    this.reason = reason.trim();
    this.setBy = setBy;
    this.timestamp = new Date();
  }

  /**
   * 创建用户设置的可信位置命令
   */
  static createByUser(
    userId: number,
    province: string,
    city: string,
    reason?: string
  ): SetTrustedLocationCommand {
    return new SetTrustedLocationCommand(
      userId,
      province,
      city,
      reason || '用户主动设置',
      'USER'
    );
  }

  /**
   * 创建系统自动设置的可信位置命令
   */
  static createBySystem(
    userId: number,
    province: string,
    city: string,
    reason: string
  ): SetTrustedLocationCommand {
    return new SetTrustedLocationCommand(
      userId,
      province,
      city,
      reason,
      'SYSTEM'
    );
  }

  /**
   * 创建管理员设置的可信位置命令
   */
  static createByAdmin(
    userId: number,
    province: string,
    city: string,
    reason: string
  ): SetTrustedLocationCommand {
    return new SetTrustedLocationCommand(
      userId,
      province,
      city,
      reason,
      'ADMIN'
    );
  }

  /**
   * 从地理位置值对象创建命令
   */
  static fromLocation(
    userId: number,
    location: GeographicLocation,
    reason: string,
    setBy: 'USER' | 'SYSTEM' | 'ADMIN' = 'USER'
  ): SetTrustedLocationCommand {
    return new SetTrustedLocationCommand(
      userId,
      location.province,
      location.city,
      reason,
      setBy
    );
  }

  /**
   * 验证命令的有效性
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (this.userId <= 0) {
      errors.push('用户ID必须大于0');
    }

    if (!this.province || this.province === '未知') {
      errors.push('省份不能为空或未知');
    }

    if (!this.city || this.city === '未知') {
      errors.push('城市不能为空或未知');
    }

    if (!this.reason) {
      errors.push('设置原因不能为空');
    }

    if (this.reason.length > 200) {
      errors.push('设置原因不能超过200个字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取位置的完整描述
   */
  getLocationDescription(): string {
    return `${this.province} ${this.city}`;
  }

  /**
   * 获取命令摘要信息
   */
  getSummary(): string {
    return `${this.setBy}为用户${this.userId}设置可信位置: ${this.getLocationDescription()} (${this.reason})`;
  }

  /**
   * 检查是否为有效的中国境内位置
   */
  isDomesticLocation(): boolean {
    // 简单的中国省份验证
    const chineseProvinces = [
      '北京市', '天津市', '上海市', '重庆市',
      '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省',
      '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省',
      '河南省', '湖北省', '湖南省', '广东省', '海南省',
      '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省',
      '内蒙古自治区', '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区',
      '香港特别行政区', '澳门特别行政区', '台湾省'
    ];

    return chineseProvinces.some(province => 
      this.province.includes(province.replace(/省|市|自治区|特别行政区/g, ''))
    );
  }

  /**
   * 转换为JSON
   */
  toJSON(): object {
    return {
      userId: this.userId,
      province: this.province,
      city: this.city,
      reason: this.reason,
      setBy: this.setBy,
      timestamp: this.timestamp.toISOString(),
      locationDescription: this.getLocationDescription(),
      isDomestic: this.isDomesticLocation()
    };
  }
}
