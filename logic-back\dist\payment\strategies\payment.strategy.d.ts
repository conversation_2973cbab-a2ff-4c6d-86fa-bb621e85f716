export interface PaymentResult {
    success: boolean;
    paymentId?: string;
    qrCode?: string;
    paymentUrl?: string;
    redirectUrl?: string;
    errorMessage?: string;
    rawResponse?: any;
}
export interface PaymentQueryResult {
    success: boolean;
    paid: boolean;
    paymentId?: string;
    paymentTime?: Date;
    amount?: number;
    status: string;
    errorMessage?: string;
    rawResponse?: any;
}
export interface PaymentStrategy {
    getChannel(): string;
    createPayment(params: {
        outTradeNo: string;
        subject: string;
        totalAmount: number;
        returnUrl?: string;
        notifyUrl?: string;
        clientIp?: string;
        timeExpire?: Date;
        [key: string]: any;
    }): Promise<PaymentResult>;
    queryPayment(params: {
        outTradeNo: string;
        paymentId?: string;
    }): Promise<PaymentQueryResult>;
    closePayment(params: {
        outTradeNo: string;
        paymentId?: string;
    }): Promise<{
        success: boolean;
        errorMessage?: string;
        rawResponse?: any;
    }>;
    verifyNotify(data: any): Promise<{
        verified: boolean;
        outTradeNo?: string;
        paymentId?: string;
        totalAmount?: number;
        paymentTime?: Date;
        [key: string]: any;
    }>;
    refund(params: {
        outTradeNo: string;
        outRefundNo: string;
        refundAmount: number;
        reason?: string;
    }): Promise<{
        success: boolean;
        refundId?: string;
        errorMessage?: string;
        rawResult?: any;
    }>;
    queryRefundStatus(outRefundNo: string): Promise<{
        status: string;
        refundId?: string;
        errorMessage?: string;
        rawResult?: any;
    }>;
}
