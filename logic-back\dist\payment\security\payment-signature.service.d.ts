import { PaymentConfigService } from '../config/payment-config.service';
import { PlatformCertificateService } from './platform-certificate.service';
export declare class PaymentSignatureService {
    private readonly configService;
    private readonly platformCertificateService;
    private readonly logger;
    constructor(configService: PaymentConfigService, platformCertificateService: PlatformCertificateService);
    generateWechatPaySignature(method: string, url: string, timestamp: number, nonce: string, body: string): string;
    verifyWechatPaySignature(timestamp: string, nonce: string, body: string | object, signature: string, serialNo?: string): boolean;
    generateAlipaySignature(params: Record<string, any>): string;
    verifyAlipaySignature(params: Record<string, any>): boolean;
    decryptWechatPayResource(resource: {
        ciphertext: string;
        nonce: string;
        associated_data: string;
    }): any;
}
