import { Injectable } from '@nestjs/common';
import { GeographicLocation } from '../value-objects/geographic-location.vo';
import { IpAddress } from '../value-objects/ip-address.vo';

/**
 * 位置比较结果接口
 */
export interface LocationComparisonResult {
  similarity: number;
  differences: string[];
  commonalities: string[];
  riskFactors: string[];
  isSignificantChange: boolean;
  changeLevel: 'NONE' | 'MINOR' | 'MODERATE' | 'MAJOR' | 'EXTREME';
}

/**
 * 位置聚类结果接口
 */
export interface LocationCluster {
  centroid: GeographicLocation;
  locations: GeographicLocation[];
  radius: number;
  confidence: number;
}

/**
 * 位置比较领域服务
 * 提供地理位置比较、分析和聚类功能
 */
@Injectable()
export class LocationComparisonService {
  
  /**
   * 比较两个地理位置
   * @param location1 第一个位置
   * @param location2 第二个位置
   * @returns 详细的比较结果
   */
  compareLocations(
    location1: GeographicLocation,
    location2: GeographicLocation
  ): LocationComparisonResult {
    const differences: string[] = [];
    const commonalities: string[] = [];
    const riskFactors: string[] = [];

    // 国家比较
    if (location1.country !== location2.country) {
      differences.push(`国家: ${location1.country} → ${location2.country}`);
      if (location1.isDomestic !== location2.isDomestic) {
        riskFactors.push('跨境变化');
      }
    } else if (location1.country !== '未知') {
      commonalities.push(`相同国家: ${location1.country}`);
    }

    // 省份比较
    if (!location1.isSameProvince(location2)) {
      differences.push(`省份: ${location1.province} → ${location2.province}`);
      if (location1.isDomestic && location2.isDomestic) {
        riskFactors.push('跨省变化');
      }
    } else if (location1.province !== '未知') {
      commonalities.push(`相同省份: ${location1.province}`);
    }

    // 城市比较
    if (!location1.isSameCity(location2)) {
      differences.push(`城市: ${location1.city} → ${location2.city}`);
      if (location1.isSameProvince(location2)) {
        riskFactors.push('省内城市变化');
      }
    } else if (location1.city !== '未知') {
      commonalities.push(`相同城市: ${location1.city}`);
    }

    // ISP比较
    if (!location1.isSameISP(location2)) {
      differences.push(`运营商: ${location1.isp} → ${location2.isp}`);
      if (location1.isSameCity(location2)) {
        riskFactors.push('运营商变化');
      }
    } else if (location1.isp !== '未知') {
      commonalities.push(`相同运营商: ${location1.isp}`);
    }

    // 计算相似度
    const similarity = location1.calculateSimilarity(location2);

    // 确定变化级别
    const changeLevel = this.determineChangeLevel(similarity, riskFactors);

    // 判断是否为显著变化
    const isSignificantChange = changeLevel === 'MAJOR' || changeLevel === 'EXTREME';

    return {
      similarity,
      differences,
      commonalities,
      riskFactors,
      isSignificantChange,
      changeLevel
    };
  }

  /**
   * 批量比较位置列表
   * @param locations 位置列表
   * @returns 位置相似度矩阵
   */
  compareMultipleLocations(locations: GeographicLocation[]): number[][] {
    const matrix: number[][] = [];

    for (let i = 0; i < locations.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < locations.length; j++) {
        if (i === j) {
          matrix[i][j] = 100; // 自己与自己100%相似
        } else {
          matrix[i][j] = locations[i].calculateSimilarity(locations[j]);
        }
      }
    }

    return matrix;
  }

  /**
   * 查找最相似的位置
   * @param targetLocation 目标位置
   * @param candidateLocations 候选位置列表
   * @returns 最相似的位置和相似度
   */
  findMostSimilarLocation(
    targetLocation: GeographicLocation,
    candidateLocations: GeographicLocation[]
  ): { location: GeographicLocation; similarity: number } | null {
    if (candidateLocations.length === 0) {
      return null;
    }

    let mostSimilar = candidateLocations[0];
    let highestSimilarity = targetLocation.calculateSimilarity(mostSimilar);

    for (let i = 1; i < candidateLocations.length; i++) {
      const similarity = targetLocation.calculateSimilarity(candidateLocations[i]);
      if (similarity > highestSimilarity) {
        highestSimilarity = similarity;
        mostSimilar = candidateLocations[i];
      }
    }

    return {
      location: mostSimilar,
      similarity: highestSimilarity
    };
  }

  /**
   * 对位置进行聚类分析
   * @param locations 位置列表
   * @param similarityThreshold 相似度阈值
   * @returns 位置聚类结果
   */
  clusterLocations(
    locations: GeographicLocation[],
    similarityThreshold: number = 70
  ): LocationCluster[] {
    const clusters: LocationCluster[] = [];
    const processed = new Set<number>();

    for (let i = 0; i < locations.length; i++) {
      if (processed.has(i)) continue;

      const cluster: GeographicLocation[] = [locations[i]];
      processed.add(i);

      // 查找相似的位置
      for (let j = i + 1; j < locations.length; j++) {
        if (processed.has(j)) continue;

        const similarity = locations[i].calculateSimilarity(locations[j]);
        if (similarity >= similarityThreshold) {
          cluster.push(locations[j]);
          processed.add(j);
        }
      }

      // 计算聚类中心
      const centroid = this.calculateCentroid(cluster);
      const radius = this.calculateClusterRadius(cluster, centroid);
      const confidence = this.calculateClusterConfidence(cluster);

      clusters.push({
        centroid,
        locations: cluster,
        radius,
        confidence
      });
    }

    return clusters.sort((a, b) => b.locations.length - a.locations.length);
  }

  /**
   * 分析位置变化趋势
   * @param locationHistory 位置历史记录（按时间排序）
   * @returns 变化趋势分析
   */
  analyzeLocationTrend(locationHistory: GeographicLocation[]): {
    stability: number;
    mainLocations: GeographicLocation[];
    changeFrequency: number;
    trendDirection: 'STABLE' | 'EXPANDING' | 'CONTRACTING' | 'CHAOTIC';
  } {
    if (locationHistory.length < 2) {
      return {
        stability: 100,
        mainLocations: locationHistory,
        changeFrequency: 0,
        trendDirection: 'STABLE'
      };
    }

    // 计算稳定性
    let totalSimilarity = 0;
    let changeCount = 0;

    for (let i = 1; i < locationHistory.length; i++) {
      const similarity = locationHistory[i-1].calculateSimilarity(locationHistory[i]);
      totalSimilarity += similarity;
      
      if (similarity < 80) {
        changeCount++;
      }
    }

    const stability = totalSimilarity / (locationHistory.length - 1);
    const changeFrequency = (changeCount / (locationHistory.length - 1)) * 100;

    // 聚类分析找出主要位置
    const clusters = this.clusterLocations(locationHistory, 60);
    const mainLocations = clusters.slice(0, 3).map(cluster => cluster.centroid);

    // 确定趋势方向
    let trendDirection: 'STABLE' | 'EXPANDING' | 'CONTRACTING' | 'CHAOTIC';
    
    if (stability > 80) {
      trendDirection = 'STABLE';
    } else if (changeFrequency > 50) {
      trendDirection = 'CHAOTIC';
    } else if (clusters.length > locationHistory.length * 0.7) {
      trendDirection = 'EXPANDING';
    } else {
      trendDirection = 'CONTRACTING';
    }

    return {
      stability,
      mainLocations,
      changeFrequency,
      trendDirection
    };
  }

  /**
   * 确定位置变化级别
   */
  private determineChangeLevel(
    similarity: number,
    riskFactors: string[]
  ): 'NONE' | 'MINOR' | 'MODERATE' | 'MAJOR' | 'EXTREME' {
    if (similarity >= 95) return 'NONE';
    if (similarity >= 80) return 'MINOR';
    if (similarity >= 60) return 'MODERATE';
    if (similarity >= 30) return 'MAJOR';
    
    // 特殊情况检查
    if (riskFactors.includes('跨境变化')) return 'EXTREME';
    
    return 'EXTREME';
  }

  /**
   * 计算聚类中心
   */
  private calculateCentroid(locations: GeographicLocation[]): GeographicLocation {
    if (locations.length === 1) {
      return locations[0];
    }

    // 简化实现：选择出现频率最高的位置作为中心
    const locationCounts = new Map<string, { location: GeographicLocation; count: number }>();

    locations.forEach(location => {
      const key = `${location.country}-${location.province}-${location.city}`;
      const existing = locationCounts.get(key);
      
      if (existing) {
        existing.count++;
      } else {
        locationCounts.set(key, { location, count: 1 });
      }
    });

    let maxCount = 0;
    let centroid = locations[0];

    locationCounts.forEach(({ location, count }) => {
      if (count > maxCount) {
        maxCount = count;
        centroid = location;
      }
    });

    return centroid;
  }

  /**
   * 计算聚类半径
   */
  private calculateClusterRadius(
    locations: GeographicLocation[],
    centroid: GeographicLocation
  ): number {
    if (locations.length === 1) return 0;

    const similarities = locations.map(location => 
      centroid.calculateSimilarity(location)
    );

    return 100 - Math.min(...similarities);
  }

  /**
   * 计算聚类置信度
   */
  private calculateClusterConfidence(locations: GeographicLocation[]): number {
    if (locations.length === 1) return locations[0].confidence;

    const avgConfidence = locations.reduce((sum, location) => 
      sum + location.confidence, 0
    ) / locations.length;

    // 位置数量越多，置信度越高
    const sizeBonus = Math.min(locations.length * 5, 20);

    return Math.min(avgConfidence + sizeBonus, 100);
  }
}
