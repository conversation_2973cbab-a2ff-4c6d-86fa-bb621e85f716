import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { IpLocationUtil } from '../utils/ip-location.util';
import { RiskAssessmentUtil } from '../utils/risk-assessment.util';
import { IpLocationApplicationService } from '../application/services/ip-location-application.service';
import { UserCommonLocation } from '../domain/entities/user-common-location.entity';
import { RedisService } from '../../database/redis/redis.service';
import { LoggerService } from '../../../common/logger/logger.service';

// Mock数据
const mockRedisService = {
  get: jest.fn(),
  set: jest.fn(),
};

const mockLoggerService = {
  logBusiness: jest.fn(),
};

const mockUserCommonLocationRepository = {
  find: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
};

describe('IpLocationUtil', () => {
  let service: IpLocationUtil;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IpLocationUtil,
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<IpLocationUtil>(IpLocationUtil);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getLocationByIP', () => {
    it('应该正确解析有效的IPv4地址', async () => {
      // 模拟Redis缓存未命中
      mockRedisService.get.mockResolvedValue(null);
      mockRedisService.set.mockResolvedValue('OK');

      const testIp = '*******';
      const result = await service.getLocationByIP(testIp);

      expect(result).toBeDefined();
      expect(result.dataSource).toBe('ip2region');
      expect(result.displayName).toBeDefined();
      expect(mockRedisService.set).toHaveBeenCalled();
    });

    it('应该从缓存中返回结果', async () => {
      const cachedResult = {
        country: '中国',
        province: '北京市',
        city: '北京市',
        isp: '联通',
        dataSource: 'ip2region',
        confidence: 95,
        displayName: '中国 北京市 北京市'
      };

      mockRedisService.get.mockResolvedValue(JSON.stringify(cachedResult));

      const testIp = '**************';
      const result = await service.getLocationByIP(testIp);

      expect(result).toEqual(cachedResult);
      expect(mockRedisService.get).toHaveBeenCalledWith('ip_location:**************');
    });

    it('应该拒绝无效的IP地址', async () => {
      const invalidIps = [
        'invalid-ip',
        '999.999.999.999',
        '192.168.1',
        'not-an-ip'
      ];

      for (const invalidIp of invalidIps) {
        await expect(service.getLocationByIP(invalidIp))
          .rejects.toThrow('无效的IP地址格式');
      }
    });

    it('应该正确处理IPv6地址', async () => {
      mockRedisService.get.mockResolvedValue(null);
      mockRedisService.set.mockResolvedValue('OK');

      const ipv6 = '2001:db8::1';
      const result = await service.getLocationByIP(ipv6);

      expect(result).toBeDefined();
      expect(result.dataSource).toBe('ip2region');
    });
  });

  describe('maskIP', () => {
    it('应该正确脱敏IPv4地址', () => {
      expect(IpLocationUtil.maskIP('*************')).toBe('192.168.1.***');
      expect(IpLocationUtil.maskIP('*******')).toBe('8.8.8.***');
    });

    it('应该正确脱敏IPv6地址', () => {
      expect(IpLocationUtil.maskIP('2001:db8:85a3:8d3:1319:8a2e:370:7344'))
        .toBe('2001:db8:85a3:8d3:****');
      expect(IpLocationUtil.maskIP('::1')).toBe('****:****:****:****');
    });
  });
});

describe('RiskAssessmentUtil', () => {
  let service: RiskAssessmentUtil;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskAssessmentUtil,
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    service = module.get<RiskAssessmentUtil>(RiskAssessmentUtil);
  });

  describe('assessLoginRisk', () => {
    it('应该对常用登录地返回低风险', async () => {
      const currentLocation = {
        country: '中国',
        province: '广东省',
        city: '深圳市',
        isp: '电信'
      };

      const userHistory = {
        commonLocations: [{
          province: '广东省',
          city: '深圳市',
          loginCount: 10,
          isTrusted: true,
          lastLoginAt: new Date()
        }],
        riskLoginCount: 0,
        totalLoginCount: 10
      };

      const result = await service.assessLoginRisk(123, currentLocation, userHistory);

      expect(result.level).toBe('LOW');
      expect(result.needVerification).toBe(false);
    });

    it('应该对境外登录返回高风险', async () => {
      const currentLocation = {
        country: '美国',
        province: '加利福尼亚州',
        city: '旧金山',
        isp: 'Google'
      };

      const userHistory = {
        commonLocations: [{
          province: '广东省',
          city: '深圳市',
          loginCount: 10,
          isTrusted: true,
          lastLoginAt: new Date()
        }],
        riskLoginCount: 0,
        totalLoginCount: 10
      };

      const result = await service.assessLoginRisk(123, currentLocation, userHistory);

      expect(result.level).toBe('HIGH');
      expect(result.needVerification).toBe(true);
      expect(result.factors).toContain('境外登录');
    });

    it('应该对跨省登录返回中等风险', async () => {
      const currentLocation = {
        country: '中国',
        province: '上海市',
        city: '上海市',
        isp: '电信'
      };

      const userHistory = {
        commonLocations: [{
          province: '广东省',
          city: '深圳市',
          loginCount: 10,
          isTrusted: true,
          lastLoginAt: new Date()
        }],
        riskLoginCount: 0,
        totalLoginCount: 10
      };

      const result = await service.assessLoginRisk(123, currentLocation, userHistory);

      expect(result.level).toBe('MEDIUM');
      expect(result.factors).toContain('跨省登录至上海市');
    });
  });
});

describe('IpLocationApplicationService', () => {
  let service: IpLocationApplicationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IpLocationApplicationService,
        {
          provide: IpLocationUtil,
          useValue: {
            getLocationByIP: jest.fn(),
            getBasicLocationByIP: jest.fn(),
          },
        },
        {
          provide: RiskAssessmentUtil,
          useValue: {
            assessLoginRisk: jest.fn(),
            getRecommendedVerificationMethods: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: getRepositoryToken(UserCommonLocation),
          useValue: mockUserCommonLocationRepository,
        },
      ],
    }).compile();

    service = module.get<IpLocationApplicationService>(IpLocationApplicationService);
  });

  describe('queryIpLocation', () => {
    it('应该返回格式化的位置信息', async () => {
      const mockLocation = {
        country: '中国',
        province: '北京市',
        city: '北京市',
        isp: '联通',
        dataSource: 'ip2region',
        confidence: 95,
        displayName: '中国 北京市 北京市'
      };

      const ipLocationUtil = service['ipLocationUtil'];
      (ipLocationUtil.getLocationByIP as jest.Mock).mockResolvedValue(mockLocation);

      const request = { ip: '**************', includeRisk: false };
      const result = await service.queryIpLocation(request);

      expect(result.ip).toBe(request.ip);
      expect(result.country).toBe(mockLocation.country);
      expect(result.province).toBe(mockLocation.province);
      expect(result.isHighQuality).toBe(true);
    });
  });
});
