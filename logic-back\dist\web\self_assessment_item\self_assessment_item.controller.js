"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SelfAssessmentItemController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const task_self_assessment_item_service_1 = require("../../util/database/mysql/task_self_assessment_item/task_self_assessment_item.service");
const task_self_assessment_item_entity_1 = require("../../util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
let SelfAssessmentItemController = class SelfAssessmentItemController {
    taskSelfAssessmentItemService;
    httpResponseResultService;
    constructor(taskSelfAssessmentItemService, httpResponseResultService) {
        this.taskSelfAssessmentItemService = taskSelfAssessmentItemService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async getByTaskId(taskId) {
        try {
            const items = await this.taskSelfAssessmentItemService.findByTaskId(+taskId);
            return this.httpResponseResultService.success(items);
        }
        catch (error) {
            return this.httpResponseResultService.error('获取自评项失败', error.message);
        }
    }
    async searchItems(keyword) {
        try {
            if (!keyword) {
                return this.httpResponseResultService.success([]);
            }
            const items = await this.taskSelfAssessmentItemService.searchItems(keyword);
            return this.httpResponseResultService.success(items);
        }
        catch (error) {
            return this.httpResponseResultService.error('搜索自评项失败', error.message);
        }
    }
};
exports.SelfAssessmentItemController = SelfAssessmentItemController;
__decorate([
    (0, common_1.Get)('/task/:taskId'),
    (0, swagger_1.ApiOperation)({ summary: '根据任务ID获取自评项' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '获取成功', type: task_self_assessment_item_entity_1.TaskSelfAssessmentItem, isArray: true }),
    __param(0, (0, common_1.Param)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SelfAssessmentItemController.prototype, "getByTaskId", null);
__decorate([
    (0, common_1.Get)('/search'),
    (0, swagger_1.ApiOperation)({ summary: '搜索自评项' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: '搜索成功', type: task_self_assessment_item_entity_1.TaskSelfAssessmentItem, isArray: true }),
    __param(0, (0, common_1.Query)('keyword')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SelfAssessmentItemController.prototype, "searchItems", null);
exports.SelfAssessmentItemController = SelfAssessmentItemController = __decorate([
    (0, swagger_1.ApiTags)('web/任务自评项(self_assessment_item)'),
    (0, common_1.Controller)('api/self-assessment-item'),
    __metadata("design:paramtypes", [task_self_assessment_item_service_1.TaskSelfAssessmentItemService,
        http_response_result_service_1.HttpResponseResultService])
], SelfAssessmentItemController);
//# sourceMappingURL=self_assessment_item.controller.js.map