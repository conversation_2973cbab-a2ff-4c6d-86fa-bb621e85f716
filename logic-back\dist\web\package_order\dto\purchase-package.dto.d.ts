export declare enum PaymentChannel {
    ALIPAY = "alipay",
    WECHATPAY = "wechatpay"
}
export declare enum PaymentMode {
    REDIRECT = "redirect",
    QR_CODE = "qrcode"
}
export declare class PurchasePackageDto {
    packageId: number;
    channel: PaymentChannel;
    paymentMode?: PaymentMode;
    clientIp?: string;
    returnUrl?: string;
}
export declare class PaymentCallbackDto {
    orderNo: string;
    paymentId: string;
}
