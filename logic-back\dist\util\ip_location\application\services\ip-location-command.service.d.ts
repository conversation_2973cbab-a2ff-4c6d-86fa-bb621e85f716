import { Repository } from 'typeorm';
import { UpdateCommonLocationCommand } from '../commands/update-common-location.command';
import { SetTrustedLocationCommand } from '../commands/set-trusted-location.command';
import { RecordLoginLocationCommand } from '../commands/record-login-location.command';
import { CommandResult } from '../interfaces/command-handler.interface';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { UserCommonLocation } from '../../domain/entities/user-common-location.entity';
export declare class IpLocationCommandService {
    private readonly userLocationRepository;
    private readonly ipLocationDomainService;
    constructor(userLocationRepository: Repository<UserCommonLocation>, ipLocationDomainService: IpLocationDomainService);
    handleUpdateCommonLocation(command: UpdateCommonLocationCommand): Promise<CommandResult<UserCommonLocation>>;
    handleSetTrustedLocation(command: SetTrustedLocationCommand): Promise<CommandResult<UserCommonLocation[]>>;
    handleRecordLoginLocation(command: RecordLoginLocationCommand): Promise<CommandResult<{
        locationUpdated: boolean;
        loginRecorded: boolean;
    }>>;
    private calculateInitialTrustScore;
    private calculateUpdatedTrustScore;
}
