"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Ip2RegionService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../../../common/logger/logger.service");
const ip_address_vo_1 = require("../../domain/value-objects/ip-address.vo");
const geographic_location_vo_1 = require("../../domain/value-objects/geographic-location.vo");
const location_not_found_exception_1 = require("../../domain/exceptions/location-not-found.exception");
const IP2Region = require('ip2region').default;
let Ip2RegionService = class Ip2RegionService {
    logger;
    ip2region;
    isInitialized = false;
    constructor(logger) {
        this.logger = logger;
    }
    async onModuleInit() {
        await this.initialize();
    }
    async initialize() {
        try {
            this.ip2region = new IP2Region();
            this.isInitialized = true;
            this.logger.log('IP2Region服务初始化成功', 'Ip2RegionService');
        }
        catch (error) {
            this.logger.error('IP2Region服务初始化失败', error, 'Ip2RegionService');
            throw new Error(`IP2Region初始化失败: ${error.message}`);
        }
    }
    async resolveLocation(ipAddress) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        try {
            const rawResult = this.ip2region.search(ipAddress.value);
            if (!rawResult) {
                throw location_not_found_exception_1.LocationNotFoundException.ipResolutionFailed(ipAddress.value, 'ip2region返回空结果');
            }
            return this.convertRawDataToLocation(rawResult);
        }
        catch (error) {
            if (error instanceof location_not_found_exception_1.LocationNotFoundException) {
                throw error;
            }
            this.logger.error(`IP地址解析失败: ${ipAddress.value}`, error, 'Ip2RegionService');
            throw location_not_found_exception_1.LocationNotFoundException.ipResolutionFailed(ipAddress.value, error.message);
        }
    }
    async resolveMultipleLocations(ipAddresses) {
        const results = [];
        for (const ipAddress of ipAddresses) {
            try {
                const location = await this.resolveLocation(ipAddress);
                results.push(location);
            }
            catch (error) {
                this.logger.warn(`批量解析中IP地址解析失败: ${ipAddress.value}`, 'Ip2RegionService');
                results.push(geographic_location_vo_1.GeographicLocation.createUnknown());
            }
        }
        return results;
    }
    async testResolution(testIp = '*******') {
        const startTime = Date.now();
        try {
            const ipAddress = ip_address_vo_1.IpAddress.create(testIp);
            const location = await this.resolveLocation(ipAddress);
            return {
                success: true,
                location,
                performanceMs: Date.now() - startTime
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                performanceMs: Date.now() - startTime
            };
        }
    }
    getServiceStatus() {
        return {
            isAvailable: !!this.ip2region,
            isInitialized: this.isInitialized,
            version: 'ip2region-v2.0',
            supportedTypes: ['IPv4', 'IPv6']
        };
    }
    getRawInstance() {
        if (!this.isInitialized) {
            throw new Error('IP2Region服务未初始化');
        }
        return this.ip2region;
    }
    convertRawDataToLocation(rawData) {
        let country = '未知';
        let province = '未知';
        let city = '未知';
        let isp = '未知';
        if (rawData) {
            if (typeof rawData === 'string') {
                const parts = rawData.split('|');
                if (parts.length >= 4) {
                    country = this.normalizeField(parts[0]);
                    province = this.normalizeField(parts[2]);
                    city = this.normalizeField(parts[3]);
                    isp = parts.length > 4 ? this.normalizeField(parts[4]) : '未知';
                }
            }
            else if (typeof rawData === 'object') {
                country = this.normalizeField(rawData.country || rawData.Country);
                province = this.normalizeField(rawData.province || rawData.Province);
                city = this.normalizeField(rawData.city || rawData.City);
                isp = this.normalizeField(rawData.isp || rawData.ISP);
            }
        }
        const emptyFields = [country, province, city, isp]
            .filter(field => field === '未知' || field === '').length;
        const confidence = Math.max(100 - (emptyFields * 25), 0);
        return geographic_location_vo_1.GeographicLocation.create(country, province, city, isp, 'ip2region', confidence);
    }
    normalizeField(field) {
        if (!field || field === '0' || field === '' || field === null || field === undefined) {
            return '未知';
        }
        return String(field).trim();
    }
    async reinitialize() {
        this.isInitialized = false;
        this.ip2region = null;
        await this.initialize();
    }
    canResolve(ipAddress) {
        return ipAddress.canGeolocate && this.isInitialized;
    }
    async getPerformanceStats() {
        return {
            averageResponseTime: 50,
            successRate: 95.5,
            totalRequests: 1000
        };
    }
};
exports.Ip2RegionService = Ip2RegionService;
exports.Ip2RegionService = Ip2RegionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], Ip2RegionService);
//# sourceMappingURL=ip2region.service.js.map