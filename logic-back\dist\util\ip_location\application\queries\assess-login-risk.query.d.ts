import { IpAddress } from '../../domain/value-objects/ip-address.vo';
export declare class AssessLoginRiskQuery {
    readonly userId: number;
    readonly ipAddress: IpAddress;
    readonly userAgent?: string;
    readonly sessionId?: string;
    readonly deviceInfo?: string;
    readonly includeRecommendations: boolean;
    readonly includeUserProfile: boolean;
    readonly timestamp: Date;
    constructor(userId: number, ipAddress: IpAddress, userAgent?: string, sessionId?: string, deviceInfo?: string, includeRecommendations?: boolean, includeUserProfile?: boolean);
    static createBasic(userId: number, ipAddressString: string, userAgent?: string): AssessLoginRiskQuery;
    static createComplete(userId: number, ipAddressString: string, userAgent?: string, sessionId?: string, deviceInfo?: string): AssessLoginRiskQuery;
    static createSimple(userId: number, ipAddressString: string): AssessLoginRiskQuery;
    validate(): {
        isValid: boolean;
        errors: string[];
    };
    getCacheKey(): string;
    getSummary(): string;
    get needsUserHistory(): boolean;
    get needsLocationResolution(): boolean;
    get isHighPriority(): boolean;
    getDeviceFingerprint(): {
        userAgent?: string;
        deviceInfo?: string;
        sessionId?: string;
        hasDeviceInfo: boolean;
    };
    getComplexity(): 'SIMPLE' | 'MEDIUM' | 'COMPLEX';
    getExpectedResponseTime(): number;
    toJSON(): object;
}
