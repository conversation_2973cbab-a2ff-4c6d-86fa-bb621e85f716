import { Repository } from 'typeorm';
import { PaymentOrder } from '../../util/database/mysql/payment_order/entities/payment-order.entity';
import { CreatePaymentDto } from '../dto/payment-request.dto';
import { PaymentStrategy } from '../strategies/payment.strategy';
import { AlipayStrategy } from '../strategies/alipay.strategy';
import { WechatPayStrategy } from '../strategies/wechat-pay.strategy';
import { LockManager } from '../lock/lock.manager';
import { PaymentConfigService } from '../config/payment-config.service';
import { ModuleRef } from '@nestjs/core';
import { RecordHelperService } from './record-helper.service';
import { PaymentOrderService } from '../../util/database/mysql/payment_order/payment-order.service';
export declare class PaymentService {
    private readonly paymentOrderRepository;
    private readonly alipayStrategy;
    private readonly wechatPayStrategy;
    private readonly lockManager;
    private readonly configService;
    private readonly moduleRef;
    private readonly recordHelper;
    private readonly paymentOrderService;
    private readonly logger;
    private readonly strategies;
    constructor(paymentOrderRepository: Repository<PaymentOrder>, alipayStrategy: AlipayStrategy, wechatPayStrategy: WechatPayStrategy, lockManager: LockManager, configService: PaymentConfigService, moduleRef: ModuleRef, recordHelper: RecordHelperService, paymentOrderService: PaymentOrderService);
    private registerStrategy;
    getPaymentStrategy(channel: string): PaymentStrategy;
    private getStrategy;
    private generateOrderNo;
    createPayment(createPaymentDto: CreatePaymentDto): Promise<{
        orderNo: string;
        paymentUrl?: string;
        qrCode?: string;
        redirectUrl?: string;
        extraData?: any;
    }>;
    queryPaymentStatus(orderNo: string): Promise<{
        orderNo: string;
        status: string;
        paymentTime?: Date;
        paymentId?: string;
        amount: number;
        channel: string;
    }>;
    closePayment(orderNo: string, reason?: string): Promise<boolean>;
    handlePaymentNotify(channel: string, notifyData: any): Promise<string>;
    private getNotifySuccessResponse;
    private getNotifyFailResponse;
    getOrderDetail(orderNo: string): Promise<any>;
}
