import { Repository } from 'typeorm';
import { CreateActivityEventsTaskDto } from './dto/create-activity-events-task.dto';
import { UpdateActivityEventsTaskDto } from './dto/update-activity-events-task.dto';
import { ActivityEventsTask } from './entities/activity_events_task.entity';
export declare class ActivityEventsTaskService {
    private readonly activityEventsTaskRepository;
    constructor(activityEventsTaskRepository: Repository<ActivityEventsTask>);
    create(createActivityEventsTaskDto: CreateActivityEventsTaskDto): Promise<ActivityEventsTask>;
    findAll(): Promise<ActivityEventsTask[]>;
    findOne(id: number): Promise<ActivityEventsTask>;
    findByStatus(status: number): Promise<ActivityEventsTask[]>;
    findByUser(userId: number): Promise<ActivityEventsTask[]>;
    findByCreator(creatorId: number): Promise<ActivityEventsTask[]>;
    findBySchool(schoolName: string): Promise<ActivityEventsTask[]>;
    findByUserAndEvent(userId: number, eventName: string): Promise<ActivityEventsTask | null>;
    findByUserAndActivity(userId: number, activityId: number): Promise<ActivityEventsTask | null>;
    findByActivityId(activityId: number): Promise<ActivityEventsTask[]>;
    updateStatusByUserAndEvent(userId: number, eventName: string, status: number): Promise<ActivityEventsTask | null>;
    updateStatusByUserAndActivity(userId: number, activityId: number, status: number): Promise<ActivityEventsTask | null>;
    update(id: number, updateActivityEventsTaskDto: UpdateActivityEventsTaskDto): Promise<ActivityEventsTask>;
    updateStatus(id: number, status: number): Promise<ActivityEventsTask>;
    remove(id: number): Promise<void>;
    getStatistics(): Promise<any>;
}
