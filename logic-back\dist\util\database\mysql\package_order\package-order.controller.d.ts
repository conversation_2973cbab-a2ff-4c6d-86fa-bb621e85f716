import { PackageOrderService } from './package-order.service';
import { CreatePackageOrderDto, UpdatePackageOrderDto, QueryPackageOrderDto, PackageOrderStatus } from './dto';
import { PackageOrder } from './entities/package-order.entity';
export declare class PackageOrderController {
    private readonly packageOrderService;
    constructor(packageOrderService: PackageOrderService);
    create(createPackageOrderDto: CreatePackageOrderDto): Promise<PackageOrder>;
    findAll(): Promise<PackageOrder[]>;
    search(queryDto: QueryPackageOrderDto): Promise<{
        data: PackageOrder[];
        total: number;
        page: number;
        limit: number;
    }>;
    findByUserId(userId: string): Promise<PackageOrder[]>;
    getUserStats(userId: string): Promise<{
        total: number;
        pending: number;
        paid: number;
        cancelled: number;
    }>;
    findByOrderNo(orderNo: string): Promise<PackageOrder>;
    findOne(id: string): Promise<PackageOrder>;
    update(id: string, updatePackageOrderDto: UpdatePackageOrderDto): Promise<PackageOrder>;
    remove(id: string): Promise<void>;
    updatePaymentStatus(orderNo: string, body: {
        paymentId: string;
        status?: PackageOrderStatus;
    }): Promise<PackageOrder>;
    cancelOrder(orderNo: string): Promise<PackageOrder>;
}
