"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSuccessQueryResult = createSuccessQueryResult;
exports.createFailureQueryResult = createFailureQueryResult;
function createSuccessQueryResult(data, message, executionTime, fromCache, cacheKey) {
    return {
        success: true,
        data,
        message,
        timestamp: new Date(),
        executionTime,
        fromCache,
        cacheKey
    };
}
function createFailureQueryResult(errors, message, executionTime) {
    return {
        success: false,
        message,
        errors,
        timestamp: new Date(),
        executionTime
    };
}
//# sourceMappingURL=query-handler.interface.js.map