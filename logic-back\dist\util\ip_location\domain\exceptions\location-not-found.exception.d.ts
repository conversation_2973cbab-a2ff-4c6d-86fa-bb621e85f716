import { IpLocationDomainException } from './ip-location-domain.exception';
export declare class LocationNotFoundException extends IpLocationDomainException {
    constructor(message: string, ipAddress?: string, context?: Record<string, any>);
    static ipResolutionFailed(ipAddress: string, reason?: string): LocationNotFoundException;
    static databaseQueryFailed(ipAddress: string, error?: Error): LocationNotFoundException;
    static privateIpAddress(ipAddress: string): LocationNotFoundException;
    static loopbackIpAddress(ipAddress: string): LocationNotFoundException;
    static serviceUnavailable(ipAddress: string, service: string): LocationNotFoundException;
    static lowDataQuality(ipAddress: string, confidence: number): LocationNotFoundException;
}
