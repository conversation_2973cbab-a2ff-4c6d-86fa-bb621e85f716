2025-07-30 11:19:41.120 [INFO] [Startup] Application is starting... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","nodeVersion":"v20.17.0","platform":"win32","port":8003}
2025-07-30 11:19:45.660 [INFO] [NestFactory] Starting Nest application... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.661 [INFO] [PaymentTemplateService] 已加载模板: payment_fail {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.663 [INFO] [PaymentTemplateService] 已加载模板: payment_success {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.666 [INFO] [PaymentTemplateService] 已加载模板: refund_fail {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.668 [INFO] [PaymentTemplateService] 已加载模板: refund_success {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.670 [INFO] [PaymentTemplateService] 已加载 4 个模板 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.670 [INFO] [InstanceLoader] WebModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.671 [INFO] [InstanceLoader] YamlModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.672 [INFO] [InstanceLoader] AliServiceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.673 [INFO] [InstanceLoader] ScratchModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.675 [INFO] [InstanceLoader] UtilModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.676 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.677 [INFO] [InstanceLoader] WebWeixinScanModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.678 [INFO] [InstanceLoader] CommonServicesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.679 [INFO] [InstanceLoader] WinstonModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.682 [INFO] [InstanceLoader] ConfigHostModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.683 [INFO] [InstanceLoader] HttpResponseResultModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.684 [INFO] [InstanceLoader] WebTemplateFolderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.690 [INFO] [InstanceLoader] ZwwModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.695 [INFO] [InstanceLoader] DiscoveryModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.704 [INFO] [InstanceLoader] LoggerModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.707 [INFO] [InstanceLoader] ScratchConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.708 [INFO] [InstanceLoader] AiProvidersConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.709 [INFO] [InstanceLoader] JwtModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.709 [INFO] [InstanceLoader] AliConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.711 [INFO] [InstanceLoader] DatabaseConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.712 [INFO] [XunfeiSpeechRecognitionService] 初始化讯飞语音识别服务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.713 [INFO] [XunfeiSpeechRecognitionService] AppID: 0292**** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.714 [INFO] [XunfeiSpeechRecognitionService] WebSocket URL: ws://iat.xf-yun.com/v1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.715 [INFO] [XunfeiVoiceprintRecognitionService] 初始化讯飞声纹识别服务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.716 [INFO] [XunfeiVoiceprintRecognitionService] AppID: 4f01**** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.717 [INFO] [XunfeiVoiceprintRecognitionService] API URL: https://api.xf-yun.com/v1/private/s782b4996 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.717 [INFO] [InstanceLoader] ConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.718 [INFO] [InstanceLoader] ConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.719 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.720 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.720 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.721 [INFO] [RedisSessionService] Redis会话存储服务已初始化 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.721 [INFO] [InstanceLoader] AliGreenModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.723 [INFO] [InstanceLoader] BullModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.726 [INFO] [InstanceLoader] MinimaxImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.735 [INFO] [InstanceLoader] ZhipuLlmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.737 [INFO] [InstanceLoader] BaiduImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.738 [INFO] [InstanceLoader] AliyunImageScoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.739 [INFO] [InstanceLoader] AliyunSegmentImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.741 [INFO] [InstanceLoader] MinimaxTtsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.741 [INFO] [InstanceLoader] AliSmsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.742 [INFO] [AlipayStrategy] 初始化支付宝支付策略... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.744 [INFO] [AlipayStrategy] 支付宝支付策略初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.745 [INFO] [WechatPayStrategy] 微信支付客户端初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.745 [INFO] [InstanceLoader] RedisModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.747 [INFO] [InstanceLoader] AliQwenVisionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.749 [INFO] [InstanceLoader] AliyunExpressionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.750 [INFO] [InstanceLoader] AliyunFaceCompareModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.751 [INFO] [InstanceLoader] AliyunFaceRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.752 [INFO] [InstanceLoader] AliyunObjectDetectionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.754 [INFO] [InstanceLoader] XunfeiVoiceprintRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.755 [INFO] [InstanceLoader] SessionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.755 [INFO] [InstanceLoader] KeyManagementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.756 [INFO] [InstanceLoader] AliyunStaticGestureRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.757 [INFO] [InstanceLoader] AliQwenTurboModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.757 [INFO] [InstanceLoader] QueueModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.757 [INFO] [EncryptionService] 【初始化】加密服务开始初始化... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.759 [WARN] [EncryptionService] 【初始化】获取RSA密钥对失败: RSA密钥对未初始化，请稍后重试，服务将在首次请求时重试 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.760 [INFO] [EncryptionService] 【初始化】加密服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.762 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-30 11:19:45.762 [INFO] [KeyManagementService] 生成了新的RSA密钥对，ID: key-c1ba6e97，指纹: c1ba6e97 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.763 [INFO] [InstanceLoader] WebSocketModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.763 [INFO] [EncryptionCleanupTask] 【初始化】会话清理任务已启动 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.764 [INFO] [InstanceLoader] OssModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.769 [INFO] [InstanceLoader] MysqlModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.769 [INFO] [InstanceLoader] XunfeiSpeechRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.771 [INFO] [InstanceLoader] RouterGuardModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.772 [INFO] [InstanceLoader] AliOssModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.772 [INFO] [InstanceLoader] EncryptModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.773 [INFO] [InstanceLoader] TypeOrmCoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.775 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.775 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.776 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.776 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.777 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.778 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.778 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.779 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.782 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.788 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.793 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.793 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.794 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.795 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.796 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.796 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.796 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.797 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.797 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.798 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.798 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.799 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.799 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.799 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.800 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.803 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.805 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.806 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.809 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.810 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.810 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.812 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.812 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.813 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.814 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.814 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.815 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.815 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.816 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.817 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.817 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.821 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.822 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.822 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.823 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.823 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.823 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.824 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.824 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.824 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.825 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.825 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.826 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.826 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.827 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.827 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.827 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.828 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.828 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.828 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.829 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.829 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.830 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.830 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.830 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.831 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.831 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.831 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.833 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.834 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.834 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.835 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.835 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.835 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.836 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.836 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.836 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.837 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.837 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.839 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.839 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.840 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.840 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.841 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.841 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.842 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.842 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.842 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.844 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.846 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.847 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.847 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.848 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.848 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.849 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.849 [INFO] [InstanceLoader] TeacherAuditAttachmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.850 [INFO] [InstanceLoader] UserSrchImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.851 [INFO] [InstanceLoader] UserSrchEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.851 [INFO] [InstanceLoader] UserSrchAudioModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.852 [INFO] [InstanceLoader] UserSrchImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.852 [INFO] [InstanceLoader] TrainImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.852 [INFO] [InstanceLoader] TrainPoseModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.853 [INFO] [InstanceLoader] TrainSoundModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.853 [INFO] [InstanceLoader] WebWorkAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.853 [INFO] [InstanceLoader] UserRoleRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.854 [INFO] [InstanceLoader] UserJoinRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.854 [INFO] [InstanceLoader] TeacherTaskAssignmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.855 [INFO] [InstanceLoader] RolePermissionTemplatesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.855 [INFO] [InstanceLoader] UserPointsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.855 [INFO] [InstanceLoader] UserPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.856 [INFO] [InstanceLoader] PackageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.857 [INFO] [InstanceLoader] UserPointsPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.857 [INFO] [InstanceLoader] UserClassModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.859 [INFO] [InstanceLoader] UserSchoolModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.860 [INFO] [InstanceLoader] UserStudentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.861 [INFO] [InstanceLoader] KeyPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.861 [INFO] [InstanceLoader] UserRoleRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.862 [INFO] [InstanceLoader] UserJoinRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.865 [INFO] [InstanceLoader] RoleTemplateExtensionPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.866 [INFO] [InstanceLoader] RoleTemplateBlockPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.866 [INFO] [InstanceLoader] RoleTemplateFolderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.866 [INFO] [InstanceLoader] RoleTemplateFolderJoinTemplateModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.867 [INFO] [InstanceLoader] TableJoingModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.868 [INFO] [InstanceLoader] UserWorkInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.869 [INFO] [InstanceLoader] WorkModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.871 [INFO] [InstanceLoader] TeacherTaskAssignmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.872 [INFO] [InstanceLoader] TeacherTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.874 [INFO] [InstanceLoader] UserImageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.876 [INFO] [InstanceLoader] TaskSelfAssessmentItemModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.877 [INFO] [InstanceLoader] UserPasswordResetRequestModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.879 [INFO] [InstanceLoader] UserImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.880 [INFO] [InstanceLoader] UserVoiceInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.881 [INFO] [InstanceLoader] UserSchoolRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.882 [INFO] [InstanceLoader] AnnouncementReadRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.883 [INFO] [InstanceLoader] AnnouncementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.883 [INFO] [InstanceLoader] AnnouncementAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.883 [INFO] [InstanceLoader] SpaceCarouselMapModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.884 [INFO] [InstanceLoader] CarouselAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.885 [INFO] [InstanceLoader] WebRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.887 [INFO] [InstanceLoader] BlockModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.888 [INFO] [InstanceLoader] ExtensionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.889 [INFO] [InstanceLoader] ExtensionPermissionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.889 [INFO] [InstanceLoader] BlockPermissionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.890 [INFO] [InstanceLoader] UserReportModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.891 [INFO] [InstanceLoader] DocModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.892 [INFO] [InstanceLoader] KeyPackageRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.892 [INFO] [InstanceLoader] UserImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.892 [INFO] [InstanceLoader] AttachmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.895 [INFO] [InstanceLoader] SelfAssessmentItemModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.897 [INFO] [InstanceLoader] PackageOrderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.898 [INFO] [InstanceLoader] PackagePricingModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.898 [INFO] [InstanceLoader] PaymentRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.899 [INFO] [InstanceLoader] NotificationRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.899 [INFO] [InstanceLoader] PaymentLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.900 [INFO] [InstanceLoader] PaymentOrderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.901 [INFO] [InstanceLoader] PaymentRefundModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.901 [INFO] [InstanceLoader] UserLoginLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.901 [INFO] [InstanceLoader] TagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.902 [INFO] [InstanceLoader] ActivityModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.902 [INFO] [InstanceLoader] ActivityTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.903 [INFO] [InstanceLoader] ActivityWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.903 [INFO] [InstanceLoader] ImageTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.903 [INFO] [InstanceLoader] PoseTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.904 [INFO] [InstanceLoader] AudioTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.905 [INFO] [InstanceLoader] UserPointsOfflineMessageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.905 [INFO] [InstanceLoader] UserRolePermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.905 [INFO] [InstanceLoader] UserRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.906 [INFO] [InstanceLoader] UserSchoolRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.906 [INFO] [InstanceLoader] UserWorkLikeModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.907 [INFO] [InstanceLoader] ActivityAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.907 [INFO] [InstanceLoader] ParticipationAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.908 [INFO] [InstanceLoader] WorkAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.908 [INFO] [InstanceLoader] StudentSelfAssessmentSubmissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.908 [INFO] [InstanceLoader] VoiceprintGroupModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.909 [INFO] [InstanceLoader] VoiceprintFeatureModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.909 [INFO] [InstanceLoader] UserRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.910 [INFO] [InstanceLoader] UserLoginLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.910 [INFO] [InstanceLoader] WebUserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.911 [INFO] [InstanceLoader] UserSrchWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.911 [INFO] [InstanceLoader] UserSrchTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.912 [INFO] [InstanceLoader] UserSchoolModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.912 [INFO] [InstanceLoader] UserImageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.913 [INFO] [InstanceLoader] UserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.913 [INFO] [InstanceLoader] AppModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.913 [INFO] [InstanceLoader] TeacherAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.914 [INFO] [InstanceLoader] WebPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.914 [INFO] [InstanceLoader] ActivitySubmitModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.914 [INFO] [InstanceLoader] ActivityEventsTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.915 [INFO] [InstanceLoader] UserPointPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.917 [INFO] [InstanceLoader] UserStudentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.919 [INFO] [InstanceLoader] WeixinModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.920 [INFO] [InstanceLoader] WebAnnouncementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.921 [INFO] [InstanceLoader] WebCarouselModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.921 [INFO] [InstanceLoader] WebDocModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.921 [INFO] [InstanceLoader] WebKeyPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.922 [INFO] [InstanceLoader] UserClassModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.923 [INFO] [InstanceLoader] UserTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.924 [INFO] [InstanceLoader] WebActivityTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.926 [INFO] [InstanceLoader] WebActivityWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.927 [INFO] [InstanceLoader] UserWorkLikeModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.929 [INFO] [InstanceLoader] TPSModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.931 [INFO] [InstanceLoader] UserWorkInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.931 [INFO] [InstanceLoader] TeacherTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.932 [INFO] [InstanceLoader] WebPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.932 [INFO] [InstanceLoader] UserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.933 [INFO] [InstanceLoader] UserSrchTemplatesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.933 [INFO] [InstanceLoader] WebReportModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.934 [INFO] [InstanceLoader] UserPasswordResetModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.935 [INFO] [InstanceLoader] WebWeixinScanModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.936 [INFO] [InstanceLoader] WeixinUtilsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.936 [INFO] [InstanceLoader] PackageOrderBusinessModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.936 [INFO] [InstanceLoader] UserPointModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.937 [INFO] [InstanceLoader] WebActivityModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.937 [INFO] [InstanceLoader] UserAuthModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.937 [INFO] [InstanceLoader] WebEventsTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.938 [INFO] [InstanceLoader] WeixinMessageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.938 [INFO] [InstanceLoader] WebPointPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.938 [INFO] [InstanceLoader] WebPointModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.939 [INFO] [InstanceLoader] PaymentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.939 [INFO] [InstanceLoader] CourseModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.940 [INFO] [InstanceLoader] AiVisualRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.940 [INFO] [InstanceLoader] AiImageScoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.941 [INFO] [InstanceLoader] AiStaticGestureRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.943 [INFO] [InstanceLoader] AiExpressionRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.944 [INFO] [InstanceLoader] AiFaceCompareModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.944 [INFO] [InstanceLoader] AiFaceRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.945 [INFO] [InstanceLoader] AiSpeechRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.945 [INFO] [InstanceLoader] AiObjectDetectionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.946 [INFO] [InstanceLoader] AiTextDialogueModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.946 [INFO] [InstanceLoader] AiImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.947 [INFO] [InstanceLoader] AiVoiceprintRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.948 [INFO] [InstanceLoader] AiSpeechSynthesisModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.949 [INFO] [InstanceLoader] AiImageGenerateModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.949 [INFO] [InstanceLoader] AiImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.950 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.951 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.952 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.953 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.954 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.955 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.956 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.956 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.956 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.957 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.958 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.959 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.962 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.963 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.967 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.968 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.969 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.971 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.972 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.973 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-30 11:19:45.974 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "add_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.975 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "check_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.976 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "message_to_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.976 [INFO] [RoutesResolver] AppController {/}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.977 [INFO] [RouterExplorer] Mapped {/, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.979 [INFO] [RoutesResolver] WorkAuditController {/api/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.981 [INFO] [RouterExplorer] Mapped {/api/work-audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.982 [INFO] [RouterExplorer] Mapped {/api/work-audit/listByWork/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.982 [INFO] [RouterExplorer] Mapped {/api/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.984 [INFO] [RouterExplorer] Mapped {/api/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.984 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.985 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.985 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.985 [INFO] [RoutesResolver] UserRoleRelationController {/api/user-role-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.986 [INFO] [RouterExplorer] Mapped {/api/user-role-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.986 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/batch-assign-roles, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.986 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/batch-assign-users, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.986 [INFO] [RouterExplorer] Mapped {/api/user-role-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.987 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.994 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:45.999 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.008 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.010 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.012 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.016 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.019 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.020 [INFO] [RoutesResolver] UserJoinRoleController {/api/user-join-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.021 [INFO] [RouterExplorer] Mapped {/api/user-join-role/createUserJoinRole, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.021 [INFO] [RouterExplorer] Mapped {/api/user-join-role/batchCreateUserJoinRole, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.022 [INFO] [RouterExplorer] Mapped {/api/user-join-role/getUserRoleAndTemplateId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.022 [INFO] [RouterExplorer] Mapped {/api/user-join-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.023 [INFO] [RouterExplorer] Mapped {/api/user-join-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.023 [INFO] [RouterExplorer] Mapped {/api/user-join-role/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.024 [INFO] [RouterExplorer] Mapped {/api/user-join-role/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.024 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.025 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.026 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.027 [INFO] [RouterExplorer] Mapped {/api/user-join-role/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.030 [INFO] [RoutesResolver] TeacherTaskAssignmentController {/api/teacher-task-assignment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.032 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/mark-read, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.033 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/return-revision, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.035 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/studentSubmitedWork, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.036 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/publish-to-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.037 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/grade, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.037 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/studentCommitWork, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.042 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/submitWork, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.047 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/workSubmissionsRecord, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.060 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.072 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.077 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.080 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.086 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.090 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.094 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.095 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/update-status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.096 [INFO] [RoutesResolver] UserWorkInfoController {/api/user-work-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.096 [INFO] [RouterExplorer] Mapped {/api/user-work-info/reviewList, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.097 [INFO] [RouterExplorer] Mapped {/api/user-work-info/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.098 [INFO] [RouterExplorer] Mapped {/api/user-work-info/searchClassprojects, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.098 [INFO] [RouterExplorer] Mapped {/api/user-work-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.099 [INFO] [RouterExplorer] Mapped {/api/user-work-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.100 [INFO] [RouterExplorer] Mapped {/api/user-work-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.101 [INFO] [RouterExplorer] Mapped {/api/user-work-info/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.101 [INFO] [RouterExplorer] Mapped {/api/user-work-info/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.102 [INFO] [RouterExplorer] Mapped {/api/user-work-info/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.102 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.102 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.103 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.103 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.104 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.104 [INFO] [RouterExplorer] Mapped {/api/user-work-info/like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.105 [INFO] [RouterExplorer] Mapped {/api/user-work-info/class/projects, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.105 [INFO] [RoutesResolver] UserWorkLikeController {/api/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.106 [INFO] [RouterExplorer] Mapped {/api/user-work-like/likeList, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.106 [INFO] [RouterExplorer] Mapped {/api/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.107 [INFO] [RouterExplorer] Mapped {/api/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.107 [INFO] [RouterExplorer] Mapped {/api/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.107 [INFO] [RouterExplorer] Mapped {/api/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.108 [INFO] [RouterExplorer] Mapped {/api/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.108 [INFO] [RouterExplorer] Mapped {/api/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.109 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.109 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.109 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.110 [INFO] [RoutesResolver] UserImageInfoController {/api/user-image-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.111 [INFO] [RouterExplorer] Mapped {/api/user-image-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.112 [INFO] [RouterExplorer] Mapped {/api/user-image-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.113 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.114 [INFO] [RouterExplorer] Mapped {/api/user-image-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.114 [INFO] [RouterExplorer] Mapped {/api/user-image-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.115 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.115 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.117 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.118 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.119 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.121 [INFO] [RouterExplorer] Mapped {/api/user-image-info/like/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.121 [INFO] [RoutesResolver] UserInfoController {/user-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.123 [INFO] [RouterExplorer] Mapped {/user-info/condition/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.124 [INFO] [RouterExplorer] Mapped {/user-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.124 [INFO] [RouterExplorer] Mapped {/user-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.125 [INFO] [RouterExplorer] Mapped {/user-info/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.125 [INFO] [RouterExplorer] Mapped {/user-info/phone/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.126 [INFO] [RouterExplorer] Mapped {/user-info/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.126 [INFO] [RouterExplorer] Mapped {/user-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.129 [INFO] [RouterExplorer] Mapped {/user-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.131 [INFO] [RouterExplorer] Mapped {/user-info/:id/points/:points, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.132 [INFO] [RouterExplorer] Mapped {/user-info/:id/role/:roleId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.132 [INFO] [RouterExplorer] Mapped {/user-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.133 [INFO] [RoutesResolver] RolePermissionTemplatesController {/role-permission-templates}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.134 [INFO] [RouterExplorer] Mapped {/role-permission-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.135 [INFO] [RouterExplorer] Mapped {/role-permission-templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.135 [INFO] [RouterExplorer] Mapped {/role-permission-templates/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.136 [INFO] [RouterExplorer] Mapped {/role-permission-templates/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.137 [INFO] [RouterExplorer] Mapped {/role-permission-templates/role/:roleId/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.137 [INFO] [RouterExplorer] Mapped {/role-permission-templates/official, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.138 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.139 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.139 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.140 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.141 [INFO] [RoutesResolver] UserStudentController {/api/user-student}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.141 [INFO] [RouterExplorer] Mapped {/api/user-student/export, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.141 [INFO] [RouterExplorer] Mapped {/api/user-student/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.141 [INFO] [RouterExplorer] Mapped {/api/user-student/match-by-ids, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.142 [INFO] [RouterExplorer] Mapped {/api/user-student/batch-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.142 [INFO] [RouterExplorer] Mapped {/api/user-student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.142 [INFO] [RouterExplorer] Mapped {/api/user-student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.143 [INFO] [RouterExplorer] Mapped {/api/user-student/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.143 [INFO] [RouterExplorer] Mapped {/api/user-student/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.143 [INFO] [RouterExplorer] Mapped {/api/user-student/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.144 [INFO] [RouterExplorer] Mapped {/api/user-student/school/:schoolId/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.144 [INFO] [RouterExplorer] Mapped {/api/user-student/number, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.145 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.145 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.146 [INFO] [RouterExplorer] Mapped {/api/user-student/:id/class/:classId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.146 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.156 [INFO] [RouterExplorer] Mapped {/api/user-student/user/:userId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.161 [INFO] [RoutesResolver] UserPointController {/api/user-point}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.162 [INFO] [RouterExplorer] Mapped {/api/user-point/total, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.162 [INFO] [RouterExplorer] Mapped {/api/user-point/:userId/total, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.163 [INFO] [RouterExplorer] Mapped {/api/user-point/details, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.163 [INFO] [RouterExplorer] Mapped {/api/user-point/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.163 [INFO] [RouterExplorer] Mapped {/api/user-point/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.164 [INFO] [RouterExplorer] Mapped {/api/user-point/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.166 [INFO] [RouterExplorer] Mapped {/api/user-point/assignPoints, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.169 [INFO] [RouterExplorer] Mapped {/api/user-point/batch-total, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.174 [INFO] [RouterExplorer] Mapped {/api/user-point/student/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.180 [INFO] [RouterExplorer] Mapped {/api/user-point/student/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.181 [INFO] [RouterExplorer] Mapped {/api/user-point/teacher/student/:studentId/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.183 [INFO] [RouterExplorer] Mapped {/api/user-point/packages, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.185 [INFO] [RoutesResolver] UserPointPackageController {/user-point-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.185 [INFO] [RouterExplorer] Mapped {/user-point-package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.186 [INFO] [RouterExplorer] Mapped {/user-point-package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.186 [INFO] [RouterExplorer] Mapped {/user-point-package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.187 [INFO] [RouterExplorer] Mapped {/user-point-package/clean-expired, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.187 [INFO] [RouterExplorer] Mapped {/user-point-package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.189 [INFO] [RoutesResolver] UserPointsController {/user-points}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.190 [INFO] [RouterExplorer] Mapped {/user-points, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.191 [INFO] [RouterExplorer] Mapped {/user-points, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.192 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.192 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/date-range, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.193 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.198 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/source/:source, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.198 [INFO] [RouterExplorer] Mapped {/user-points/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.199 [INFO] [RouterExplorer] Mapped {/user-points/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.199 [INFO] [RouterExplorer] Mapped {/user-points/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.199 [INFO] [RoutesResolver] HttpResponseResultController {/http-response}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.200 [INFO] [RouterExplorer] Mapped {/http-response/success, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.200 [INFO] [RouterExplorer] Mapped {/http-response/error, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.201 [INFO] [RouterExplorer] Mapped {/http-response/custom, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.201 [INFO] [RoutesResolver] UserPackageController {/user-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.202 [INFO] [RouterExplorer] Mapped {/user-package, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.202 [INFO] [RouterExplorer] Mapped {/user-package, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.202 [INFO] [RouterExplorer] Mapped {/user-package/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.203 [INFO] [RouterExplorer] Mapped {/user-package/user/:userId/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.203 [INFO] [RouterExplorer] Mapped {/user-package/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.203 [INFO] [RouterExplorer] Mapped {/user-package/check-expired, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.204 [INFO] [RouterExplorer] Mapped {/user-package/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.204 [INFO] [RouterExplorer] Mapped {/user-package/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.204 [INFO] [RouterExplorer] Mapped {/user-package/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.205 [INFO] [RoutesResolver] PackageInfoController {/package-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.205 [INFO] [RouterExplorer] Mapped {/package-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.206 [INFO] [RouterExplorer] Mapped {/package-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.206 [INFO] [RouterExplorer] Mapped {/package-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.207 [INFO] [RouterExplorer] Mapped {/package-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.207 [INFO] [RouterExplorer] Mapped {/package-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.208 [INFO] [RoutesResolver] UserPointsPermissionController {/user-points-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.209 [INFO] [RouterExplorer] Mapped {/user-points-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.210 [INFO] [RouterExplorer] Mapped {/user-points-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.210 [INFO] [RouterExplorer] Mapped {/user-points-permission/student/:studentUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.211 [INFO] [RouterExplorer] Mapped {/user-points-permission/teacher/:teacherUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.211 [INFO] [RouterExplorer] Mapped {/user-points-permission/valid, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.212 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.212 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.212 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.213 [INFO] [RoutesResolver] UserClassController {/api/user/class}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.214 [INFO] [RouterExplorer] Mapped {/api/user/class, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.214 [INFO] [RouterExplorer] Mapped {/api/user/class/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.215 [INFO] [RouterExplorer] Mapped {/api/user/class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.216 [INFO] [RouterExplorer] Mapped {/api/user/class/update/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.216 [INFO] [RouterExplorer] Mapped {/api/user/class/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.217 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.217 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/:teacherId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.218 [INFO] [RouterExplorer] Mapped {/api/user/class/assistant/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.218 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.220 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/export, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.222 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/invite, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.223 [INFO] [RouterExplorer] Mapped {/api/user/class/student/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.223 [INFO] [RouterExplorer] Mapped {/api/user/class/invite_join, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.223 [INFO] [RouterExplorer] Mapped {/api/user/class/transfer, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.224 [INFO] [RouterExplorer] Mapped {/api/user/class/import, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.224 [INFO] [RouterExplorer] Mapped {/api/user/class/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.224 [INFO] [RouterExplorer] Mapped {/api/user/class/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.225 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/classes/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.225 [INFO] [RouterExplorer] Mapped {/api/user/class/student/remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.226 [INFO] [RouterExplorer] Mapped {/api/user/class/assistant/remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.227 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/search/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.227 [INFO] [RouterExplorer] Mapped {/api/user/class/school/:schoolId/all, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.228 [INFO] [RoutesResolver] UserClassController {/user-class}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.228 [INFO] [RouterExplorer] Mapped {/user-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.229 [INFO] [RouterExplorer] Mapped {/user-class, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.229 [INFO] [RouterExplorer] Mapped {/user-class/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.229 [INFO] [RouterExplorer] Mapped {/user-class/school/:schoolId/grade/:grade, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.230 [INFO] [RouterExplorer] Mapped {/user-class/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.230 [INFO] [RouterExplorer] Mapped {/user-class/assistant-teacher/:assistantTeacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.236 [INFO] [RouterExplorer] Mapped {/user-class/invite-code/:inviteCode, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.236 [INFO] [RouterExplorer] Mapped {/user-class/:id/regenerate-invite-code, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.237 [INFO] [RouterExplorer] Mapped {/user-class/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.238 [INFO] [RouterExplorer] Mapped {/user-class/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.238 [INFO] [RouterExplorer] Mapped {/user-class/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.240 [INFO] [RoutesResolver] UserSchoolController {/user-school}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.241 [INFO] [RouterExplorer] Mapped {/user-school, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.241 [INFO] [RouterExplorer] Mapped {/user-school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.242 [INFO] [RouterExplorer] Mapped {/user-school/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.243 [INFO] [RouterExplorer] Mapped {/user-school/area, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.246 [INFO] [RouterExplorer] Mapped {/user-school/provinces, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.247 [INFO] [RouterExplorer] Mapped {/user-school/cities/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.247 [INFO] [RouterExplorer] Mapped {/user-school/districts/:province/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.247 [INFO] [RouterExplorer] Mapped {/user-school/province/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.248 [INFO] [RouterExplorer] Mapped {/user-school/province/:province/city/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.249 [INFO] [RouterExplorer] Mapped {/user-school/province/:province/city/:city/district/:district, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.251 [INFO] [RouterExplorer] Mapped {/user-school/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.252 [INFO] [RouterExplorer] Mapped {/user-school/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.252 [INFO] [RouterExplorer] Mapped {/user-school/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.252 [INFO] [RoutesResolver] UserStudentController {/user-student}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.253 [INFO] [RouterExplorer] Mapped {/user-student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.253 [INFO] [RouterExplorer] Mapped {/user-student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.253 [INFO] [RouterExplorer] Mapped {/user-student/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.253 [INFO] [RouterExplorer] Mapped {/user-student/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.254 [INFO] [RouterExplorer] Mapped {/user-student/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.254 [INFO] [RouterExplorer] Mapped {/user-student/school/:schoolId/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.254 [INFO] [RouterExplorer] Mapped {/user-student/number, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.255 [INFO] [RouterExplorer] Mapped {/user-student/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.255 [INFO] [RouterExplorer] Mapped {/user-student/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.255 [INFO] [RouterExplorer] Mapped {/user-student/:id/class/:classId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.256 [INFO] [RouterExplorer] Mapped {/user-student/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.256 [INFO] [RouterExplorer] Mapped {/user-student/user/:userId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.256 [INFO] [RoutesResolver] RedisController {/redis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.257 [INFO] [RouterExplorer] Mapped {/redis/set, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.258 [INFO] [RouterExplorer] Mapped {/redis/get, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.259 [INFO] [RouterExplorer] Mapped {/redis/del, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.259 [INFO] [RouterExplorer] Mapped {/redis/exists, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.259 [INFO] [RouterExplorer] Mapped {/redis/expire, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.260 [INFO] [RoutesResolver] KeyPackageController {/key-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.260 [INFO] [RouterExplorer] Mapped {/key-package, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.260 [INFO] [RouterExplorer] Mapped {/key-package, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.261 [INFO] [RouterExplorer] Mapped {/key-package/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.262 [INFO] [RouterExplorer] Mapped {/key-package/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.262 [INFO] [RouterExplorer] Mapped {/key-package/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.263 [INFO] [RoutesResolver] UserAuthController {/api/user-auth}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.263 [INFO] [RouterExplorer] Mapped {/api/user-auth/bindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.264 [INFO] [RouterExplorer] Mapped {/api/user-auth/setPasswordByPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.264 [INFO] [RouterExplorer] Mapped {/api/user-auth/setPasswordByUserId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.265 [INFO] [RouterExplorer] Mapped {/api/user-auth/password, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.265 [INFO] [RouterExplorer] Mapped {/api/user-auth/select-identity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.265 [INFO] [RouterExplorer] Mapped {/api/user-auth/refreshToken, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.266 [INFO] [RouterExplorer] Mapped {/api/user-auth/updatePassword, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.266 [INFO] [RouterExplorer] Mapped {/api/user-auth/logout, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.267 [INFO] [RouterExplorer] Mapped {/api/user-auth/student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.267 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsSend, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.268 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsSendUpdatePhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.271 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsLogin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.273 [INFO] [RouterExplorer] Mapped {/api/user-auth/resetPasswordByCode, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.275 [INFO] [RouterExplorer] Mapped {/api/user-auth/student/reset-password, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.275 [INFO] [RouterExplorer] Mapped {/api/user-auth/check-phone-accounts, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.276 [INFO] [RouterExplorer] Mapped {/api/user-auth/findAllBindByPhone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.276 [INFO] [RouterExplorer] Mapped {/api/user-auth/bind-weixin-to-user, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.277 [INFO] [RouterExplorer] Mapped {/api/user-auth/register-and-bind-weixin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.278 [INFO] [RouterExplorer] Mapped {/api/user-auth/bind-weixin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.279 [INFO] [RouterExplorer] Mapped {/api/user-auth/reset-password-by-phone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.280 [INFO] [RouterExplorer] Mapped {/api/user-auth/switch-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.283 [INFO] [RouterExplorer] Mapped {/api/user-auth/verify-sms-code, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.284 [INFO] [RouterExplorer] Mapped {/api/user-auth/transfer-weixin-openid, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.285 [INFO] [RouterExplorer] Mapped {/api/user-auth/auto-login-by-userid, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.286 [INFO] [RoutesResolver] AliSmsController {/ali-sms}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.286 [INFO] [RouterExplorer] Mapped {/ali-sms/send-code, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.287 [INFO] [RoutesResolver] UserInfoController {/api/user-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.287 [INFO] [RouterExplorer] Mapped {/api/user-info/updatePhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.287 [INFO] [RouterExplorer] Mapped {/api/user-info/condition/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.288 [INFO] [RouterExplorer] Mapped {/api/user-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.288 [INFO] [RouterExplorer] Mapped {/api/user-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.288 [INFO] [RouterExplorer] Mapped {/api/user-info/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.289 [INFO] [RouterExplorer] Mapped {/api/user-info/phone/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.289 [INFO] [RouterExplorer] Mapped {/api/user-info/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.290 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.291 [INFO] [RouterExplorer] Mapped {/api/user-info/detail/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.295 [INFO] [RouterExplorer] Mapped {/api/user-info/person/info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.296 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.297 [INFO] [RouterExplorer] Mapped {/api/user-info/:id/points/:points, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.298 [INFO] [RouterExplorer] Mapped {/api/user-info/:id/role/:roleId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.299 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.299 [INFO] [RoutesResolver] UserRoleRelationController {/user-role-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.299 [INFO] [RouterExplorer] Mapped {/user-role-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.300 [INFO] [RouterExplorer] Mapped {/user-role-relation/batch-assign-roles, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.301 [INFO] [RouterExplorer] Mapped {/user-role-relation/batch-assign-users, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.302 [INFO] [RouterExplorer] Mapped {/user-role-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.302 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.302 [INFO] [RouterExplorer] Mapped {/user-role-relation/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.303 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.303 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.304 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.304 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.304 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.305 [INFO] [RouterExplorer] Mapped {/user-role-relation/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.305 [INFO] [RoutesResolver] WebWeixinScanController {/weixin-scan}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.305 [INFO] [RouterExplorer] Mapped {/weixin-scan/qrcode, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.305 [INFO] [RouterExplorer] Mapped {/weixin-scan/check-status/:scene_str, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.306 [INFO] [RouterExplorer] Mapped {/weixin-scan/confirm, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.306 [INFO] [RouterExplorer] Mapped {/weixin-scan/callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.306 [INFO] [RoutesResolver] AliOssController {/ali-oss}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.307 [INFO] [RouterExplorer] Mapped {/ali-oss/upload, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.307 [INFO] [RouterExplorer] Mapped {/ali-oss/upload-form, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.307 [INFO] [RouterExplorer] Mapped {/ali-oss/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.307 [INFO] [RouterExplorer] Mapped {/ali-oss/buckets, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.308 [INFO] [RouterExplorer] Mapped {/ali-oss/check-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.308 [INFO] [RoutesResolver] UserJoinRoleController {/user-join-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.308 [INFO] [RouterExplorer] Mapped {/user-join-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.308 [INFO] [RouterExplorer] Mapped {/user-join-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.309 [INFO] [RouterExplorer] Mapped {/user-join-role/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.309 [INFO] [RouterExplorer] Mapped {/user-join-role/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.309 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.309 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.310 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.310 [INFO] [RouterExplorer] Mapped {/user-join-role/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.311 [INFO] [RoutesResolver] UserSrchTemplatesController {/api/user/srch/templates}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.311 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.311 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.311 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.312 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/current/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.312 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/batch-current, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.312 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.313 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/list/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.313 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.313 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id/default, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.314 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/official/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.314 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/scratch/permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.315 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/scratchs/permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.315 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/tree, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.315 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.316 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.316 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.316 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.316 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.316 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/addTemplate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.317 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/removeTemplate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.317 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/addTemplates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.317 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/teacher_students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.317 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/:folderId/templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.317 [INFO] [RoutesResolver] RoleTemplateExtensionPermissionController {/role-template-extension-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.318 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.318 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.318 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.319 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.319 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.319 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.319 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.320 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.320 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId/extension/:extensionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.320 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.321 [INFO] [RoutesResolver] RoleTemplateBlockPermissionController {/role-template-block-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.321 [INFO] [RouterExplorer] Mapped {/role-template-block-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.321 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.321 [INFO] [RouterExplorer] Mapped {/role-template-block-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.322 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.322 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.322 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.323 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.323 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.323 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.324 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/extension/:extensionId/block/:blockId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.324 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.325 [INFO] [RoutesResolver] RoleTemplateFolderController {/role-template-folder}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.328 [INFO] [RouterExplorer] Mapped {/role-template-folder, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.330 [INFO] [RouterExplorer] Mapped {/role-template-folder, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.331 [INFO] [RouterExplorer] Mapped {/role-template-folder/root, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.331 [INFO] [RouterExplorer] Mapped {/role-template-folder/tree, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.331 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.332 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/children, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.332 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.332 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/sort/:sort, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.333 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/move, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.333 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.333 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/with-children, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.333 [INFO] [RoutesResolver] RoleTemplateFolderJoinTemplateController {/role-template-folder-join-template}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.334 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.334 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/batch-templates-to-folder, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.334 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/batch-folders-to-template, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.335 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.335 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.335 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.336 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.336 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.336 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.336 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId/template/:templateId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.337 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.337 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.337 [INFO] [RoutesResolver] TableJoingController {/table-joing}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.338 [INFO] [RouterExplorer] Mapped {/table-joing/student-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.338 [INFO] [RoutesResolver] RouterGuardController {/api/router-guard}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.338 [INFO] [RouterExplorer] Mapped {/api/router-guard/protected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.339 [INFO] [RouterExplorer] Mapped {/api/router-guard/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.339 [INFO] [RouterExplorer] Mapped {/api/router-guard/login-check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.339 [INFO] [RouterExplorer] Mapped {/api/router-guard/refresh-token, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.339 [INFO] [RouterExplorer] Mapped {/api/router-guard/protected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.340 [INFO] [RoutesResolver] UserSrchWorkController {/api/user/srch/work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.340 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.340 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.341 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.341 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.341 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.341 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/class/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.341 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/stats/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.342 [INFO] [RoutesResolver] UserWorkInfoController {/user-work-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.342 [INFO] [RouterExplorer] Mapped {/user-work-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.343 [INFO] [RouterExplorer] Mapped {/user-work-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.343 [INFO] [RouterExplorer] Mapped {/user-work-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.344 [INFO] [RouterExplorer] Mapped {/user-work-info/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.344 [INFO] [RouterExplorer] Mapped {/user-work-info/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.345 [INFO] [RouterExplorer] Mapped {/user-work-info/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.345 [INFO] [RouterExplorer] Mapped {/user-work-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.345 [INFO] [RouterExplorer] Mapped {/user-work-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.346 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.346 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.346 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.347 [INFO] [RoutesResolver] WorkModelController {/work-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.348 [INFO] [RouterExplorer] Mapped {/work-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.349 [INFO] [RouterExplorer] Mapped {/work-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.349 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.349 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.350 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId/number/:modelNumber, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.350 [INFO] [RouterExplorer] Mapped {/work-model/:id/toggle-active, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.350 [INFO] [RouterExplorer] Mapped {/work-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.350 [INFO] [RouterExplorer] Mapped {/work-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.351 [INFO] [RouterExplorer] Mapped {/work-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.351 [INFO] [RoutesResolver] UserSrchTaskController {/api/user/srch/task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.351 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:workId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.351 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/stats/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.352 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.352 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.352 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/grade, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.353 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:assignmentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.353 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/return, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.353 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/status/:assignmentId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.353 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/publish-to-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.354 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/mark-as-read, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.354 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/task/stats/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.354 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.355 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.355 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.355 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/updata, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.355 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/remove/:taskId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.356 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.356 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.356 [INFO] [RoutesResolver] TeacherTaskAssignmentController {/teacher-task-assignment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.356 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.357 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.357 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.357 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.357 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.358 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.359 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.359 [INFO] [RoutesResolver] TeacherTaskController {/teacher-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.359 [INFO] [RouterExplorer] Mapped {/teacher-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.359 [INFO] [RouterExplorer] Mapped {/teacher-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.360 [INFO] [RouterExplorer] Mapped {/teacher-task/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.360 [INFO] [RouterExplorer] Mapped {/teacher-task/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.360 [INFO] [RouterExplorer] Mapped {/teacher-task/check-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.361 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.361 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.361 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.361 [INFO] [RoutesResolver] UserImageInfoController {/user-image-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.362 [INFO] [RouterExplorer] Mapped {/user-image-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.362 [INFO] [RouterExplorer] Mapped {/user-image-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.362 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.362 [INFO] [RouterExplorer] Mapped {/user-image-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.363 [INFO] [RouterExplorer] Mapped {/user-image-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.363 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.363 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.364 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.364 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.365 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.365 [INFO] [RoutesResolver] TaskSelfAssessmentItemController {/api/task-self-assessment-item}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.365 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.366 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.366 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.367 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.367 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.367 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.368 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.368 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/check-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.368 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/update-by-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.368 [INFO] [RoutesResolver] WebUserInfoController {/api/web/user/info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.369 [INFO] [RouterExplorer] Mapped {/api/web/user/info/ping, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.369 [INFO] [RouterExplorer] Mapped {/api/web/user/info/checkHasBindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.369 [INFO] [RouterExplorer] Mapped {/api/web/user/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.369 [INFO] [RouterExplorer] Mapped {/api/web/user/info/update, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.370 [INFO] [RouterExplorer] Mapped {/api/web/user/info/update/password, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.370 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset_password/request, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.371 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset/password/handle, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.371 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset/password/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.371 [INFO] [RoutesResolver] UserPasswordResetRequestController {/user-password-reset-request}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.372 [INFO] [RouterExplorer] Mapped {/user-password-reset-request, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.372 [INFO] [RouterExplorer] Mapped {/user-password-reset-request, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.372 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.373 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.373 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.374 [INFO] [RoutesResolver] UserSrchImageController {/api/user-srch-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.374 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.375 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.375 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.375 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.376 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/task-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.376 [INFO] [RoutesResolver] UserSrchEnhanceController {/api/user-srch-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.377 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.378 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.378 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/byImageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.379 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/task-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.379 [INFO] [RoutesResolver] UserImageEnhanceController {/user-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.380 [INFO] [RouterExplorer] Mapped {/user-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.380 [INFO] [RouterExplorer] Mapped {/user-image-enhance, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.381 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.381 [INFO] [RouterExplorer] Mapped {/user-image-enhance/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.382 [INFO] [RouterExplorer] Mapped {/user-image-enhance/image/:imageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.382 [INFO] [RouterExplorer] Mapped {/user-image-enhance/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.383 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.383 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.383 [INFO] [RoutesResolver] UserSrchAudioController {/api/user-srch-audio}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.384 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.384 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.384 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.385 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/task-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.385 [INFO] [RoutesResolver] UserVoiceInfoController {/user-voice-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.385 [INFO] [RouterExplorer] Mapped {/user-voice-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.386 [INFO] [RouterExplorer] Mapped {/user-voice-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.386 [INFO] [RouterExplorer] Mapped {/user-voice-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.386 [INFO] [RouterExplorer] Mapped {/user-voice-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.386 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.387 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.387 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.388 [INFO] [RoutesResolver] UserPointPackageController {/user-point-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.389 [INFO] [RouterExplorer] Mapped {/user-point-package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.390 [INFO] [RouterExplorer] Mapped {/user-point-package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.391 [INFO] [RouterExplorer] Mapped {/user-point-package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.391 [INFO] [RouterExplorer] Mapped {/user-point-package/clean-expired, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.392 [INFO] [RouterExplorer] Mapped {/user-point-package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.392 [INFO] [RoutesResolver] WebPointController {/web/point}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.392 [INFO] [RouterExplorer] Mapped {/web/point/package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.392 [INFO] [RouterExplorer] Mapped {/web/point/package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.393 [INFO] [RouterExplorer] Mapped {/web/point/package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.393 [INFO] [RouterExplorer] Mapped {/web/point/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.393 [INFO] [RouterExplorer] Mapped {/web/point/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.393 [INFO] [RouterExplorer] Mapped {/web/point/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.394 [INFO] [RouterExplorer] Mapped {/web/point/assign/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.394 [INFO] [RouterExplorer] Mapped {/web/point/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.394 [INFO] [RouterExplorer] Mapped {/web/point/permission/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.394 [INFO] [RouterExplorer] Mapped {/web/point/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.395 [INFO] [RouterExplorer] Mapped {/web/point/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.395 [INFO] [RouterExplorer] Mapped {/web/point/clean-expired, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.395 [INFO] [RouterExplorer] Mapped {/web/point/clean-expired-permissions, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.396 [INFO] [RoutesResolver] UserSchoolController {/api/user-school}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.396 [INFO] [RouterExplorer] Mapped {/api/user-school, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.396 [INFO] [RouterExplorer] Mapped {/api/user-school/listByUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.397 [INFO] [RouterExplorer] Mapped {/api/user-school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.398 [INFO] [RouterExplorer] Mapped {/api/user-school/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.398 [INFO] [RouterExplorer] Mapped {/api/user-school/area, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.398 [INFO] [RouterExplorer] Mapped {/api/user-school/provinces, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.398 [INFO] [RouterExplorer] Mapped {/api/user-school/cities/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.399 [INFO] [RouterExplorer] Mapped {/api/user-school/districts/:province/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.399 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.399 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province/city/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.399 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province/city/:city/district/:district, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.400 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.400 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.400 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.401 [INFO] [RoutesResolver] UserSchoolRelationController {/api/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.401 [INFO] [RouterExplorer] Mapped {/api/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.401 [INFO] [RouterExplorer] Mapped {/api/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.401 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.402 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.402 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.402 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.403 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.404 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.404 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.404 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.405 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.405 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.405 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.405 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.406 [INFO] [RoutesResolver] WebAnnouncementController {/api/web/announcement}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.406 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/deleteByUser, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.406 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/deleteByAnnouncement, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.407 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/unread, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.407 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/mark, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.408 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.408 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/delete/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.408 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/update/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.409 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.409 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.410 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/page, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.410 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.410 [INFO] [RouterExplorer] Mapped {/api/web/announcement/list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.411 [INFO] [RouterExplorer] Mapped {/api/web/announcement/recall, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.411 [INFO] [RouterExplorer] Mapped {/api/web/announcement/allIds, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.411 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publishedIds, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.411 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publishedIdsByTarget, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.412 [INFO] [RouterExplorer] Mapped {/api/web/announcement/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.412 [INFO] [RouterExplorer] Mapped {/api/web/announcement/increaseReadCount, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.412 [INFO] [RouterExplorer] Mapped {/api/web/announcement/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.412 [INFO] [RouterExplorer] Mapped {/api/web/announcement/review/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.412 [INFO] [RouterExplorer] Mapped {/api/web/announcement/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.413 [INFO] [RouterExplorer] Mapped {/api/web/announcement/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.413 [INFO] [RouterExplorer] Mapped {/api/web/announcement/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.413 [INFO] [RouterExplorer] Mapped {/api/web/announcement/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.413 [INFO] [RouterExplorer] Mapped {/api/web/announcement/page, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.414 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/listByAnnouncement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.414 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.414 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.414 [INFO] [RoutesResolver] AnnouncementReadRecordController {/announcement-read-record}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.415 [INFO] [RouterExplorer] Mapped {/announcement-read-record, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.415 [INFO] [RouterExplorer] Mapped {/announcement-read-record, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.415 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.415 [INFO] [RouterExplorer] Mapped {/announcement-read-record/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.415 [INFO] [RouterExplorer] Mapped {/announcement-read-record/announcement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.416 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.416 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.416 [INFO] [RoutesResolver] AnnouncementController {/announcement}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.416 [INFO] [RouterExplorer] Mapped {/announcement, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.417 [INFO] [RouterExplorer] Mapped {/announcement, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.417 [INFO] [RouterExplorer] Mapped {/announcement/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.417 [INFO] [RouterExplorer] Mapped {/announcement/publisher/:publisherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.417 [INFO] [RouterExplorer] Mapped {/announcement/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.417 [INFO] [RouterExplorer] Mapped {/announcement/top/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.418 [INFO] [RouterExplorer] Mapped {/announcement/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.418 [INFO] [RouterExplorer] Mapped {/announcement/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.419 [INFO] [RouterExplorer] Mapped {/announcement/:id/read, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.419 [INFO] [RouterExplorer] Mapped {/announcement/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.419 [INFO] [RoutesResolver] AnnouncementAuditController {/announcement-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.420 [INFO] [RouterExplorer] Mapped {/announcement-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.420 [INFO] [RouterExplorer] Mapped {/announcement-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.420 [INFO] [RouterExplorer] Mapped {/announcement-audit/announcement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.421 [INFO] [RouterExplorer] Mapped {/announcement-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.421 [INFO] [RouterExplorer] Mapped {/announcement-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.421 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.421 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.422 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.422 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.422 [INFO] [RoutesResolver] WebCarouselController {/api/web-carousel}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.422 [INFO] [RouterExplorer] Mapped {/api/web-carousel/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.425 [INFO] [RouterExplorer] Mapped {/api/web-carousel, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.425 [INFO] [RouterExplorer] Mapped {/api/web-carousel/review, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.426 [INFO] [RouterExplorer] Mapped {/api/web-carousel/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.426 [INFO] [RouterExplorer] Mapped {/api/web-carousel/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.426 [INFO] [RouterExplorer] Mapped {/api/web-carousel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.426 [INFO] [RouterExplorer] Mapped {/api/web-carousel/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.427 [INFO] [RouterExplorer] Mapped {/api/web-carousel/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.427 [INFO] [RoutesResolver] WebCarouselAuditController {/api/web-carousel/audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.427 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/listByCarousel/:carouselId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.427 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.427 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.428 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.428 [INFO] [RoutesResolver] SpaceCarouselMapController {/space-carousel-map}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.428 [INFO] [RouterExplorer] Mapped {/space-carousel-map, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.429 [INFO] [RouterExplorer] Mapped {/space-carousel-map, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.429 [INFO] [RouterExplorer] Mapped {/space-carousel-map/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.429 [INFO] [RouterExplorer] Mapped {/space-carousel-map/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.429 [INFO] [RouterExplorer] Mapped {/space-carousel-map/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.429 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.430 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id/sort/:sort, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.430 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.430 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.430 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.431 [INFO] [RoutesResolver] CarouselAuditController {/carousel-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.431 [INFO] [RouterExplorer] Mapped {/carousel-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.431 [INFO] [RouterExplorer] Mapped {/carousel-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.432 [INFO] [RouterExplorer] Mapped {/carousel-audit/carousel/:carouselId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.432 [INFO] [RouterExplorer] Mapped {/carousel-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.432 [INFO] [RouterExplorer] Mapped {/carousel-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.432 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.433 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.433 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.434 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.434 [INFO] [RoutesResolver] TeacherTaskController {/api/teacher-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.434 [INFO] [RouterExplorer] Mapped {/api/teacher-task/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.435 [INFO] [RouterExplorer] Mapped {/api/teacher-task/delete-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.435 [INFO] [RouterExplorer] Mapped {/api/teacher-task/update-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.435 [INFO] [RouterExplorer] Mapped {/api/teacher-task/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.436 [INFO] [RouterExplorer] Mapped {/api/teacher-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.436 [INFO] [RouterExplorer] Mapped {/api/teacher-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.436 [INFO] [RouterExplorer] Mapped {/api/teacher-task/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.436 [INFO] [RouterExplorer] Mapped {/api/teacher-task/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.437 [INFO] [RouterExplorer] Mapped {/api/teacher-task/check-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.437 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.437 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.437 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.438 [INFO] [RouterExplorer] Mapped {/api/teacher-task/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.438 [INFO] [RoutesResolver] WebRoleController {/api/web-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.438 [INFO] [RouterExplorer] Mapped {/api/web-role/user/join/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.438 [INFO] [RouterExplorer] Mapped {/api/web-role/template/teacher/default, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.439 [INFO] [RouterExplorer] Mapped {/api/web-role/template/official/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.439 [INFO] [RouterExplorer] Mapped {/api/web-role/template/official/list/page, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.439 [INFO] [RoutesResolver] UserPasswordResetController {/api/user-password-reset}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.439 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.440 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.440 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/handle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.440 [INFO] [RoutesResolver] WebPermissionController {/api/web-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.440 [INFO] [RouterExplorer] Mapped {/api/web-permission/extension/available, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.441 [INFO] [RouterExplorer] Mapped {/api/web-permission/block/byExtension, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.441 [INFO] [RouterExplorer] Mapped {/api/web-permission/getUserPermissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.441 [INFO] [RouterExplorer] Mapped {/api/web-permission/updateExtensionPermission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.441 [INFO] [RouterExplorer] Mapped {/api/web-permission/updateBlockPermission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.441 [INFO] [RouterExplorer] Mapped {/api/web-permission/role-templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.442 [INFO] [RouterExplorer] Mapped {/api/web-permission/user/:userId/role/:roleId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.442 [INFO] [RouterExplorer] Mapped {/api/web-permission/template-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.443 [INFO] [RoutesResolver] BlockController {/block}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.443 [INFO] [RouterExplorer] Mapped {/block, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.443 [INFO] [RouterExplorer] Mapped {/block, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.444 [INFO] [RouterExplorer] Mapped {/block/block-id/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.444 [INFO] [RouterExplorer] Mapped {/block/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.444 [INFO] [RouterExplorer] Mapped {/block/type/:blockType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.445 [INFO] [RouterExplorer] Mapped {/block/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.445 [INFO] [RouterExplorer] Mapped {/block/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.445 [INFO] [RouterExplorer] Mapped {/block/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.445 [INFO] [RoutesResolver] ExtensionsController {/extensions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.446 [INFO] [RouterExplorer] Mapped {/extensions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.446 [INFO] [RouterExplorer] Mapped {/extensions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.446 [INFO] [RouterExplorer] Mapped {/extensions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.446 [INFO] [RouterExplorer] Mapped {/extensions/extensionId/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.446 [INFO] [RouterExplorer] Mapped {/extensions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RouterExplorer] Mapped {/extensions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RoutesResolver] ExtensionPermissionsController {/extension-permissions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RouterExplorer] Mapped {/extension-permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RouterExplorer] Mapped {/extension-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RouterExplorer] Mapped {/extension-permissions/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RouterExplorer] Mapped {/extension-permissions/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.447 [INFO] [RouterExplorer] Mapped {/extension-permissions/active/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.448 [INFO] [RouterExplorer] Mapped {/extension-permissions/check/:userId/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.448 [INFO] [RouterExplorer] Mapped {/extension-permissions/batch/:extensionId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.448 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/enable, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.448 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/disable, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.449 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/expire, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.449 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.449 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.450 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.450 [INFO] [RoutesResolver] BlockPermissionsController {/block-permissions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.450 [INFO] [RouterExplorer] Mapped {/block-permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.450 [INFO] [RouterExplorer] Mapped {/block-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.451 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.451 [INFO] [RouterExplorer] Mapped {/block-permissions/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.451 [INFO] [RouterExplorer] Mapped {/block-permissions/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.451 [INFO] [RouterExplorer] Mapped {/block-permissions/block/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.451 [INFO] [RouterExplorer] Mapped {/block-permissions/check/:userId/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.452 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.452 [INFO] [RouterExplorer] Mapped {/block-permissions/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.452 [INFO] [RouterExplorer] Mapped {/block-permissions/:id/expire, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.452 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.452 [INFO] [RoutesResolver] WebReportController {/api/web/report}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.453 [INFO] [RouterExplorer] Mapped {/api/web/report/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.453 [INFO] [RouterExplorer] Mapped {/api/web/report/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.453 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.453 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/:id/handle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.454 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/batchHandle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.454 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.454 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.454 [INFO] [RoutesResolver] UserReportController {/user-report}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.454 [INFO] [RouterExplorer] Mapped {/user-report, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.455 [INFO] [RouterExplorer] Mapped {/user-report, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.455 [INFO] [RouterExplorer] Mapped {/user-report/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.455 [INFO] [RouterExplorer] Mapped {/user-report/reporter/:reporterId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.456 [INFO] [RouterExplorer] Mapped {/user-report/target/:targetId/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.456 [INFO] [RouterExplorer] Mapped {/user-report/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.456 [INFO] [RouterExplorer] Mapped {/user-report/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.457 [INFO] [RouterExplorer] Mapped {/user-report/:id/handle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.457 [INFO] [RouterExplorer] Mapped {/user-report/:id/reject, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.457 [INFO] [RouterExplorer] Mapped {/user-report/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.458 [INFO] [RoutesResolver] WebTemplateFolderController {/web-template-folder}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.458 [INFO] [RoutesResolver] WebDocController {/api/web/doc}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.458 [INFO] [RouterExplorer] Mapped {/api/web/doc/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.458 [INFO] [RouterExplorer] Mapped {/api/web/doc/getByDocId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.459 [INFO] [RouterExplorer] Mapped {/api/web/doc/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.459 [INFO] [RouterExplorer] Mapped {/api/web/doc/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.459 [INFO] [RouterExplorer] Mapped {/api/web/doc/getUserDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.459 [INFO] [RouterExplorer] Mapped {/api/web/doc/getDocById, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.460 [INFO] [RouterExplorer] Mapped {/api/web/doc/getAllDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.460 [INFO] [RouterExplorer] Mapped {/api/web/doc/findByTitle, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.460 [INFO] [RouterExplorer] Mapped {/api/web/doc/findByTag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.460 [INFO] [RouterExplorer] Mapped {/api/web/doc/findPublicDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.461 [INFO] [RoutesResolver] DocController {/doc}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.461 [INFO] [RouterExplorer] Mapped {/doc, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.461 [INFO] [RouterExplorer] Mapped {/doc, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.461 [INFO] [RouterExplorer] Mapped {/doc/doc-id/:docId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.462 [INFO] [RouterExplorer] Mapped {/doc/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.462 [INFO] [RouterExplorer] Mapped {/doc/search/title, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.462 [INFO] [RouterExplorer] Mapped {/doc/tag/:tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.462 [INFO] [RouterExplorer] Mapped {/doc/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.463 [INFO] [RouterExplorer] Mapped {/doc/visible/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.463 [INFO] [RouterExplorer] Mapped {/doc/:id/soft-remove, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.463 [INFO] [RouterExplorer] Mapped {/doc/:id/restore, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.463 [INFO] [RouterExplorer] Mapped {/doc/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.464 [INFO] [RouterExplorer] Mapped {/doc/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.464 [INFO] [RouterExplorer] Mapped {/doc/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.464 [INFO] [RoutesResolver] WebKeyPackageController {/api/web/key_package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.465 [INFO] [RouterExplorer] Mapped {/api/web/key_package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.465 [INFO] [RouterExplorer] Mapped {/api/web/key_package/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.465 [INFO] [RouterExplorer] Mapped {/api/web/key_package/validate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.465 [INFO] [RouterExplorer] Mapped {/api/web/key_package/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.466 [INFO] [RouterExplorer] Mapped {/api/web/key_package/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.466 [INFO] [RouterExplorer] Mapped {/api/web/key_package/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.466 [INFO] [RouterExplorer] Mapped {/api/web/key_package/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.466 [INFO] [RouterExplorer] Mapped {/api/web/key_package/info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.466 [INFO] [RouterExplorer] Mapped {/api/web/key_package/batch-create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.467 [INFO] [RoutesResolver] KeyPackageRecordController {/key-package-record}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.467 [INFO] [RouterExplorer] Mapped {/key-package-record, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.467 [INFO] [RouterExplorer] Mapped {/key-package-record, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.467 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.467 [INFO] [RouterExplorer] Mapped {/key-package-record/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.468 [INFO] [RouterExplorer] Mapped {/key-package-record/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.468 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.468 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.468 [INFO] [RoutesResolver] WebPackageController {/api/web/package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.469 [INFO] [RouterExplorer] Mapped {/api/web/package/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.469 [INFO] [RouterExplorer] Mapped {/api/web/package/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.469 [INFO] [RouterExplorer] Mapped {/api/web/package/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.469 [INFO] [RouterExplorer] Mapped {/api/web/package/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.470 [INFO] [RouterExplorer] Mapped {/api/web/package/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.470 [INFO] [RouterExplorer] Mapped {/api/web/package/available, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.470 [INFO] [RouterExplorer] Mapped {/api/web/package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.470 [INFO] [RouterExplorer] Mapped {/api/web/package/user/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.470 [INFO] [RouterExplorer] Mapped {/api/web/package/user/current, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.471 [INFO] [RouterExplorer] Mapped {/api/web/package/user/message-center, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.471 [INFO] [RouterExplorer] Mapped {/api/web/package/user/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.471 [INFO] [RouterExplorer] Mapped {/api/web/package/user/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.471 [INFO] [RoutesResolver] WebPointPermissionController {/api/web-point-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.471 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.472 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/batchAssign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.472 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.472 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/teacher, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.472 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.473 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.473 [INFO] [RoutesResolver] OssController {/api/oss}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.473 [INFO] [RouterExplorer] Mapped {/api/oss/upload, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.474 [INFO] [RouterExplorer] Mapped {/api/oss/upload-form, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.474 [INFO] [RouterExplorer] Mapped {/api/oss/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.474 [INFO] [RoutesResolver] UserSrchImageSegmentController {/api/user-srch-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.474 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.475 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.475 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/status/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.475 [INFO] [RoutesResolver] UserImageSegmentController {/user-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.476 [INFO] [RouterExplorer] Mapped {/user-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.476 [INFO] [RouterExplorer] Mapped {/user-image-segment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.476 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.476 [INFO] [RouterExplorer] Mapped {/user-image-segment/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.477 [INFO] [RouterExplorer] Mapped {/user-image-segment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.477 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.477 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.477 [INFO] [RoutesResolver] WeixinMessageController {/weixin}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.478 [INFO] [RouterExplorer] Mapped {/weixin/message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.478 [INFO] [RouterExplorer] Mapped {/weixin/message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.479 [INFO] [RoutesResolver] WeixinController {/api/weixin}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.481 [INFO] [RouterExplorer] Mapped {/api/weixin/bindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.486 [INFO] [RouterExplorer] Mapped {/api/weixin/checkBindStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.487 [INFO] [RouterExplorer] Mapped {/api/weixin/bindPage, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.487 [INFO] [RoutesResolver] TeacherAuditController {/api/teacher-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.488 [INFO] [RouterExplorer] Mapped {/api/teacher-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.488 [INFO] [RouterExplorer] Mapped {/api/teacher-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.489 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/getTeacherAuthByCondition, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.489 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/teacher, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.489 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.490 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.490 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.490 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.491 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/name/:name, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.491 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.492 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/name-like/:pattern, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.492 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.492 [INFO] [RoutesResolver] AttachmentController {/api/attachment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.493 [INFO] [RouterExplorer] Mapped {/api/attachment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.493 [INFO] [RouterExplorer] Mapped {/api/attachment/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.493 [INFO] [RouterExplorer] Mapped {/api/attachment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.494 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.494 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.494 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.495 [INFO] [RoutesResolver] SelfAssessmentItemController {/api/self-assessment-item}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.495 [INFO] [RouterExplorer] Mapped {/api/self-assessment-item/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.496 [INFO] [RouterExplorer] Mapped {/api/self-assessment-item/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.496 [INFO] [RoutesResolver] ZwwController {/zww}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.496 [INFO] [RouterExplorer] Mapped {/zww, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.496 [INFO] [RouterExplorer] Mapped {/zww, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.497 [INFO] [RouterExplorer] Mapped {/zww/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.497 [INFO] [RouterExplorer] Mapped {/zww/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.497 [INFO] [RouterExplorer] Mapped {/zww/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.497 [INFO] [RoutesResolver] PackageOrderBusinessController {/api/v1/package-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.498 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/purchase, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.499 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/payment-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.500 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/order-status/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.500 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/my-orders, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.500 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/cancel/:orderNo, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.501 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/packages, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.501 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.501 [INFO] [RoutesResolver] PackageOrderController {/package-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.501 [INFO] [RouterExplorer] Mapped {/package-order, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.502 [INFO] [RouterExplorer] Mapped {/package-order, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.502 [INFO] [RouterExplorer] Mapped {/package-order/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.502 [INFO] [RouterExplorer] Mapped {/package-order/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.502 [INFO] [RouterExplorer] Mapped {/package-order/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.503 [INFO] [RouterExplorer] Mapped {/package-order/order-no/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.503 [INFO] [RouterExplorer] Mapped {/package-order/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.503 [INFO] [RouterExplorer] Mapped {/package-order/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.504 [INFO] [RouterExplorer] Mapped {/package-order/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.504 [INFO] [RouterExplorer] Mapped {/package-order/payment/:orderNo, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.504 [INFO] [RouterExplorer] Mapped {/package-order/cancel/:orderNo, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.505 [INFO] [RoutesResolver] PackagePricingController {/package-pricing}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.505 [INFO] [RouterExplorer] Mapped {/package-pricing, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.505 [INFO] [RouterExplorer] Mapped {/package-pricing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.506 [INFO] [RouterExplorer] Mapped {/package-pricing/current-pricings, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.506 [INFO] [RouterExplorer] Mapped {/package-pricing/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.507 [INFO] [RouterExplorer] Mapped {/package-pricing/package/:packageId/all, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.508 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.508 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.508 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.509 [INFO] [RoutesResolver] PaymentController {/v1/payment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.509 [INFO] [RouterExplorer] Mapped {/v1/payment/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.509 [INFO] [RouterExplorer] Mapped {/v1/payment/query/:outTradeNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.510 [INFO] [RouterExplorer] Mapped {/v1/payment/close, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.512 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/:channel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.513 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/notify/:channel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.514 [INFO] [RouterExplorer] Mapped {/v1/payment/return/:channel, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.514 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/wechatpay, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.514 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/alipay/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.514 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/wechatpay/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.515 [INFO] [RouterExplorer] Mapped {/v1/payment/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.515 [INFO] [RouterExplorer] Mapped {/v1/payment/test/wechat/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.515 [INFO] [RoutesResolver] RefundController {/v1/payment/refund}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.516 [INFO] [RouterExplorer] Mapped {/v1/payment/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.518 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/query, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.518 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/:refundNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.519 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/:refundNo/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.519 [INFO] [RoutesResolver] PaymentRecordController {/api/v1/payment-records}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.519 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.519 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.520 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.520 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.520 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.521 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.521 [INFO] [RoutesResolver] NotificationRecordController {/api/v1/notification-records}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.521 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.522 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.522 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.523 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.524 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.524 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/retry/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.525 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.525 [INFO] [RoutesResolver] PaymentLogController {/api/v1/payment-logs}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.525 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.525 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.526 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.526 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.526 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/refund/:refundNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.526 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/cleanup, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.527 [INFO] [RoutesResolver] PaymentOrderController {/payment-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.527 [INFO] [RouterExplorer] Mapped {/payment-order, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.527 [INFO] [RouterExplorer] Mapped {/payment-order, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.527 [INFO] [RouterExplorer] Mapped {/payment-order/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.528 [INFO] [RouterExplorer] Mapped {/payment-order/business-order-id/:businessOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.529 [INFO] [RouterExplorer] Mapped {/payment-order/channel-order-id/:channelOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.529 [INFO] [RouterExplorer] Mapped {/payment-order/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.530 [INFO] [RouterExplorer] Mapped {/payment-order/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.530 [INFO] [RouterExplorer] Mapped {/payment-order/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.531 [INFO] [RouterExplorer] Mapped {/payment-order/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.531 [INFO] [RouterExplorer] Mapped {/payment-order/:id/notify, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.531 [INFO] [RouterExplorer] Mapped {/payment-order/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.532 [INFO] [RoutesResolver] PaymentRefundController {/payment-refund}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.532 [INFO] [RouterExplorer] Mapped {/payment-refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.532 [INFO] [RouterExplorer] Mapped {/payment-refund, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.533 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.533 [INFO] [RouterExplorer] Mapped {/payment-refund/business-refund-id/:businessRefundId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.533 [INFO] [RouterExplorer] Mapped {/payment-refund/payment-order/:paymentOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.534 [INFO] [RouterExplorer] Mapped {/payment-refund/channel-refund-id/:channelRefundId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.535 [INFO] [RouterExplorer] Mapped {/payment-refund/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.536 [INFO] [RouterExplorer] Mapped {/payment-refund/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.536 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.537 [INFO] [RouterExplorer] Mapped {/payment-refund/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.537 [INFO] [RouterExplorer] Mapped {/payment-refund/:id/notify, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.537 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.538 [INFO] [RoutesResolver] UserLoginLogController {/api/user-login-log}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.538 [INFO] [RouterExplorer] Mapped {/api/user-login-log, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.538 [INFO] [RouterExplorer] Mapped {/api/user-login-log, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.539 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/history, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.539 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/last, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.539 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.539 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/check-abnormal, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.540 [INFO] [RouterExplorer] Mapped {/api/user-login-log/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.540 [INFO] [RouterExplorer] Mapped {/api/user-login-log/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.541 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/logout, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.541 [INFO] [RouterExplorer] Mapped {/api/user-login-log/debug/user/:userId/recent, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.541 [INFO] [RoutesResolver] UserTagController {/api/v1/tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.541 [INFO] [RouterExplorer] Mapped {/api/v1/tag/createTag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.542 [INFO] [RouterExplorer] Mapped {/api/v1/tag/updateTag/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.542 [INFO] [RouterExplorer] Mapped {/api/v1/tag/deleteTag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.542 [INFO] [RouterExplorer] Mapped {/api/v1/tag/infoTag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.542 [INFO] [RouterExplorer] Mapped {/api/v1/tag/listTag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.543 [INFO] [RoutesResolver] TagController {/tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.543 [INFO] [RouterExplorer] Mapped {/tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.543 [INFO] [RouterExplorer] Mapped {/tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.543 [INFO] [RouterExplorer] Mapped {/tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.543 [INFO] [RouterExplorer] Mapped {/tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.544 [INFO] [RouterExplorer] Mapped {/tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.544 [INFO] [RoutesResolver] WebActivityController {/api/v1/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.544 [INFO] [RouterExplorer] Mapped {/api/v1/activity/createActivity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.544 [INFO] [RouterExplorer] Mapped {/api/v1/activity/updateActivity/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.545 [INFO] [RouterExplorer] Mapped {/api/v1/activity/deleteActivity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.545 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.545 [INFO] [RouterExplorer] Mapped {/api/v1/activity/listActivity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.545 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivityWithWorks/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.546 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivityContent/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.546 [INFO] [RouterExplorer] Mapped {/api/v1/activity/addWorks/:activityId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.546 [INFO] [RouterExplorer] Mapped {/api/v1/activity/setAwardedWorks/:activityId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.547 [INFO] [RouterExplorer] Mapped {/api/v1/activity/uploadSignature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.547 [INFO] [RouterExplorer] Mapped {/api/v1/activity/submitRegistration, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.547 [INFO] [RouterExplorer] Mapped {/api/v1/activity/checkRegistration/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.547 [INFO] [RouterExplorer] Mapped {/api/v1/activity/cancelRegistration/:activityId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.547 [INFO] [RouterExplorer] Mapped {/api/v1/activity/myRegistrations, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.548 [INFO] [RouterExplorer] Mapped {/api/v1/activity/registrationStatistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.548 [INFO] [RoutesResolver] ActivityController {/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.548 [INFO] [RouterExplorer] Mapped {/activity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.549 [INFO] [RouterExplorer] Mapped {/activity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.549 [INFO] [RouterExplorer] Mapped {/activity/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.549 [INFO] [RouterExplorer] Mapped {/activity/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.550 [INFO] [RouterExplorer] Mapped {/activity/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.550 [INFO] [RouterExplorer] Mapped {/activity/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.550 [INFO] [RouterExplorer] Mapped {/activity/type/:activityType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.550 [INFO] [RouterExplorer] Mapped {/activity/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.551 [INFO] [RouterExplorer] Mapped {/activity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.551 [INFO] [RouterExplorer] Mapped {/activity/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.551 [INFO] [RouterExplorer] Mapped {/activity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.551 [INFO] [RouterExplorer] Mapped {/activity/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.552 [INFO] [RoutesResolver] ActivitySubmitController {/activity-submit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.552 [INFO] [RouterExplorer] Mapped {/activity-submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.552 [INFO] [RouterExplorer] Mapped {/activity-submit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.552 [INFO] [RouterExplorer] Mapped {/activity-submit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.553 [INFO] [RouterExplorer] Mapped {/activity-submit/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.553 [INFO] [RouterExplorer] Mapped {/activity-submit/check/:activityId/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.553 [INFO] [RouterExplorer] Mapped {/activity-submit/statistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.554 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.554 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.554 [INFO] [RouterExplorer] Mapped {/activity-submit/:id/cancel, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.554 [INFO] [RouterExplorer] Mapped {/activity-submit/cancel/:activityId/:userId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.554 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.555 [INFO] [RoutesResolver] ActivityEventsTaskController {/activity-events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.555 [INFO] [RouterExplorer] Mapped {/activity-events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.555 [INFO] [RouterExplorer] Mapped {/activity-events-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.555 [INFO] [RouterExplorer] Mapped {/activity-events-task/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.555 [INFO] [RouterExplorer] Mapped {/activity-events-task/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.556 [INFO] [RouterExplorer] Mapped {/activity-events-task/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.556 [INFO] [RouterExplorer] Mapped {/activity-events-task/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.556 [INFO] [RouterExplorer] Mapped {/activity-events-task/school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.556 [INFO] [RouterExplorer] Mapped {/activity-events-task/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.556 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.557 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.557 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.557 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.557 [INFO] [RoutesResolver] WebActivityTagController {/api/v1/activity_tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.557 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/add-tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.558 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/edit-tags/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.558 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/remove-tags/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.559 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-activity/:activityId/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.559 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-tag/:tagId/activities, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.559 [INFO] [RoutesResolver] ActivityTagController {/activity-tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.560 [INFO] [RouterExplorer] Mapped {/activity-tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.560 [INFO] [RouterExplorer] Mapped {/activity-tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.560 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.561 [INFO] [RouterExplorer] Mapped {/activity-tag/tag/:tagId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.562 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.562 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.563 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.563 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.563 [INFO] [RouterExplorer] Mapped {/activity-tag/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.563 [INFO] [RoutesResolver] WebActivityWorkController {/api/v1/activity_work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.564 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-works, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.564 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.564 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-works/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.565 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/check-submitted/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.565 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/list/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.565 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.566 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/batch-remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.566 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/set-awarded/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.566 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-category/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.566 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-status/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.567 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/cancel/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.567 [INFO] [RoutesResolver] ActivityWorkController {/activity-work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.567 [INFO] [RouterExplorer] Mapped {/activity-work, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.567 [INFO] [RouterExplorer] Mapped {/activity-work, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.567 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.568 [INFO] [RouterExplorer] Mapped {/activity-work/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.568 [INFO] [RouterExplorer] Mapped {/activity-work/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.568 [INFO] [RouterExplorer] Mapped {/activity-work/selected/:isSelected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.568 [INFO] [RouterExplorer] Mapped {/activity-work/winners, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.573 [INFO] [RouterExplorer] Mapped {/activity-work/:id/selected/:isSelected, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.574 [INFO] [RouterExplorer] Mapped {/activity-work/:id/winner/:isWinner, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.574 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.574 [INFO] [RouterExplorer] Mapped {/activity-work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.574 [INFO] [RouterExplorer] Mapped {/activity-work/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.575 [INFO] [RouterExplorer] Mapped {/activity-work/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.575 [INFO] [RouterExplorer] Mapped {/activity-work/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.575 [INFO] [RoutesResolver] WebEventsTaskController {/api/v1/web/events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.575 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.576 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.576 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/activity/:activityId/tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.576 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.577 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.577 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/ongoing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.577 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.577 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.578 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.578 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.578 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.579 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/admin-review, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.579 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.579 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.579 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.580 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.580 [INFO] [RoutesResolver] ManagementController {/api/v1/course-management}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.581 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/my-series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.581 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/courses, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.581 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.582 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.582 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.582 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.582 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.583 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.583 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/settings, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.583 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/task-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.583 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.584 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.584 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.584 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/course-orders, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.584 [INFO] [RoutesResolver] TeachingController {/api/v1/course-teaching}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.585 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/one-click-start, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.585 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/course-settings/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.585 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.585 [INFO] [RoutesResolver] MarketplaceController {/api/v1/course-marketplace}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.586 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.586 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.586 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId/courses/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.586 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.587 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.587 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.587 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.587 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.588 [INFO] [RoutesResolver] AiImageGenerateController {/api/ai-image-generate}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.588 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.588 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.588 [INFO] [RoutesResolver] QueueController {/api/queue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.589 [INFO] [RouterExplorer] Mapped {/api/queue/addJob, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.589 [INFO] [RouterExplorer] Mapped {/api/queue/clearAllQueues, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.589 [INFO] [RoutesResolver] MinimaxImageController {/api/minimax-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.590 [INFO] [RouterExplorer] Mapped {/api/minimax-image/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.590 [INFO] [RoutesResolver] AiTextDialogueController {/api/ai-text-dialogue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.590 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.590 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.591 [INFO] [RoutesResolver] ZhipuLlmController {/zhipu-llm}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.591 [INFO] [RouterExplorer] Mapped {/zhipu-llm/chat, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.591 [INFO] [RoutesResolver] AliQwenTurboController {/ali-qwen-turbo}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.592 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/stream, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.592 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/sse-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.592 [INFO] [RoutesResolver] AiVisualRecognitionController {/api/ai-visual-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.593 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.593 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.593 [INFO] [RoutesResolver] AliQwenVisionController {/ali-qwen-vision}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.593 [INFO] [RouterExplorer] Mapped {/ali-qwen-vision/analyze, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.594 [INFO] [RoutesResolver] AiExpressionRecognitionController {/api/ai-expression-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.594 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.594 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.595 [INFO] [RoutesResolver] AliyunExpressionController {/api/aliyun-expression}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.595 [INFO] [RouterExplorer] Mapped {/api/aliyun-expression/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.596 [INFO] [RoutesResolver] AiFaceCompareController {/ai-face-compare}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.596 [INFO] [RouterExplorer] Mapped {/ai-face-compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.597 [INFO] [RouterExplorer] Mapped {/ai-face-compare/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.599 [INFO] [RoutesResolver] AliyunFaceCompareController {/aliyun-face-one-contrast-one}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.599 [INFO] [RouterExplorer] Mapped {/aliyun-face-one-contrast-one/compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.599 [INFO] [RoutesResolver] AiFaceRecognitionController {/api/ai-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.600 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.600 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.601 [INFO] [RoutesResolver] AliyunFaceRecognitionController {/api/aliyun-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.601 [INFO] [RouterExplorer] Mapped {/api/aliyun-face-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.602 [INFO] [RoutesResolver] AiImageEnhanceController {/api/ai-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.602 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.602 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.603 [INFO] [RoutesResolver] BaiduImageEnhanceController {/api/baidu-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.604 [INFO] [RouterExplorer] Mapped {/api/baidu-image-enhance/enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.604 [INFO] [RoutesResolver] AiImageScoreController {/api/ai-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.604 [INFO] [RouterExplorer] Mapped {/api/ai-image-score, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.605 [INFO] [RouterExplorer] Mapped {/api/ai-image-score/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.605 [INFO] [RoutesResolver] AliyunImageScoreController {/api/aliyun-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.605 [INFO] [RouterExplorer] Mapped {/api/aliyun-image-score/assess, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.606 [INFO] [RoutesResolver] AiImageSegmentController {/api/ai-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.606 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.606 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.607 [INFO] [RoutesResolver] AliyunSegmentImageController {/api/aliyun-segment-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.607 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.607 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/download, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.607 [INFO] [RoutesResolver] AiSpeechSynthesisController {/api/ai-speech-synthesis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.608 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.608 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.608 [INFO] [RoutesResolver] MinimaxTtsController {/api/minimax-tts}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.608 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/config, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.609 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.609 [INFO] [RoutesResolver] AiSpeechRecognitionController {/api/ai-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.609 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.609 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.610 [INFO] [RoutesResolver] XunfeiSpeechRecognitionController {/xunfei-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.610 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-url, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.610 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-base64, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.610 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-file, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.611 [INFO] [RoutesResolver] TrainImageController {/api/scratch/train/image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.611 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.611 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.611 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.612 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.612 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.612 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.613 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.613 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.613 [INFO] [RoutesResolver] ImageTrainModelController {/image-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.614 [INFO] [RouterExplorer] Mapped {/image-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.614 [INFO] [RouterExplorer] Mapped {/image-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.614 [INFO] [RouterExplorer] Mapped {/image-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.615 [INFO] [RouterExplorer] Mapped {/image-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.615 [INFO] [RouterExplorer] Mapped {/image-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.615 [INFO] [RouterExplorer] Mapped {/image-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.616 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.616 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.616 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.616 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.617 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.617 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.617 [INFO] [RoutesResolver] TrainPoseController {/api/scratch/train/pose}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.617 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.618 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.618 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.619 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.619 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.620 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.621 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.621 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.621 [INFO] [RoutesResolver] PoseTrainModelController {/pose-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.622 [INFO] [RouterExplorer] Mapped {/pose-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.622 [INFO] [RouterExplorer] Mapped {/pose-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.622 [INFO] [RouterExplorer] Mapped {/pose-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.623 [INFO] [RouterExplorer] Mapped {/pose-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.623 [INFO] [RouterExplorer] Mapped {/pose-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.623 [INFO] [RouterExplorer] Mapped {/pose-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.623 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.623 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.624 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id/increment-use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.624 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.624 [INFO] [RoutesResolver] TrainSoundController {/api/scratch/train/sound}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.624 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.625 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.625 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.625 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.626 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.626 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.626 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.627 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.627 [INFO] [RoutesResolver] AudioTrainModelController {/audio-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.627 [INFO] [RouterExplorer] Mapped {/audio-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.627 [INFO] [RouterExplorer] Mapped {/audio-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.627 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.628 [INFO] [RouterExplorer] Mapped {/audio-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.628 [INFO] [RouterExplorer] Mapped {/audio-train-model/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.628 [INFO] [RouterExplorer] Mapped {/audio-train-model/public/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.628 [INFO] [RouterExplorer] Mapped {/audio-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.629 [INFO] [RouterExplorer] Mapped {/audio-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.629 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.629 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.629 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.629 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.630 [INFO] [RoutesResolver] AiObjectDetectionController {/api/ai-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.630 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.630 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.630 [INFO] [RoutesResolver] AliyunObjectDetectionController {/api/aliyun-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.630 [INFO] [RouterExplorer] Mapped {/api/aliyun-object-detection/detect, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.631 [INFO] [RoutesResolver] AiStaticGestureRecognitionController {/api/scratch/ai-static-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.631 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.631 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.631 [INFO] [RoutesResolver] AliyunStaticGestureRecognitionController {/api/aliyun-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.632 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.632 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.632 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.632 [INFO] [RoutesResolver] UserPointsOfflineMessageController {/user-points-offline-message}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.632 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.633 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.633 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.633 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/pending, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.633 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.633 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.634 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id/mark-as-sent, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.634 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.634 [INFO] [RoutesResolver] UserRolePermissionController {/user-role-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.634 [INFO] [RouterExplorer] Mapped {/user-role-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.634 [INFO] [RouterExplorer] Mapped {/user-role-permission/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.634 [INFO] [RouterExplorer] Mapped {/user-role-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.635 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.635 [INFO] [RouterExplorer] Mapped {/user-role-permission/permission/:permissionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.635 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.635 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.636 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.636 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/permission/:permissionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.636 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.636 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.636 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.637 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.637 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.638 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.638 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.638 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.639 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.639 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.639 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.639 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.640 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.640 [INFO] [RoutesResolver] UserSchoolRelationController {/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.640 [INFO] [RouterExplorer] Mapped {/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.640 [INFO] [RouterExplorer] Mapped {/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.641 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.641 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.642 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.642 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.642 [INFO] [RouterExplorer] Mapped {/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.643 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.643 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.643 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.644 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.644 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.644 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.644 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.645 [INFO] [RoutesResolver] UserWorkLikeController {/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.645 [INFO] [RouterExplorer] Mapped {/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.646 [INFO] [RouterExplorer] Mapped {/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.647 [INFO] [RouterExplorer] Mapped {/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.647 [INFO] [RouterExplorer] Mapped {/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.648 [INFO] [RouterExplorer] Mapped {/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.648 [INFO] [RouterExplorer] Mapped {/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.648 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.648 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.649 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.649 [INFO] [RoutesResolver] ActivityAuditController {/activity-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.649 [INFO] [RouterExplorer] Mapped {/activity-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.650 [INFO] [RouterExplorer] Mapped {/activity-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.650 [INFO] [RouterExplorer] Mapped {/activity-audit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.650 [INFO] [RouterExplorer] Mapped {/activity-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.650 [INFO] [RouterExplorer] Mapped {/activity-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.651 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.651 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.651 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.652 [INFO] [RouterExplorer] Mapped {/activity-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.652 [INFO] [RoutesResolver] ParticipationAuditController {/participation-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.652 [INFO] [RouterExplorer] Mapped {/participation-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.652 [INFO] [RouterExplorer] Mapped {/participation-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.653 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.653 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.653 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.653 [INFO] [RoutesResolver] WorkAuditController {/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.653 [INFO] [RouterExplorer] Mapped {/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.654 [INFO] [RouterExplorer] Mapped {/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.654 [INFO] [RouterExplorer] Mapped {/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.654 [INFO] [RouterExplorer] Mapped {/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.654 [INFO] [RouterExplorer] Mapped {/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.655 [INFO] [RoutesResolver] StudentSelfAssessmentSubmissionController {/student-self-assessment-submission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.655 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/bulk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.655 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/assignment/:assignmentId/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.656 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/item/:itemId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.656 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.656 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.656 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.657 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.657 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.657 [INFO] [RoutesResolver] EncryptionController {/api/encryption}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.658 [INFO] [RouterExplorer] Mapped {/api/encryption/publicKey, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.658 [INFO] [RouterExplorer] Mapped {/api/encryption/keyStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.659 [INFO] [RouterExplorer] Mapped {/api/encryption/session, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.659 [INFO] [RouterExplorer] Mapped {/api/encryption/secureSession, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.659 [INFO] [RouterExplorer] Mapped {/api/encryption/renew-session/:sessionId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.659 [INFO] [RouterExplorer] Mapped {/api/encryption/session/:sessionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.659 [INFO] [RouterExplorer] Mapped {/api/encryption/session-stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.660 [INFO] [RouterExplorer] Mapped {/api/encryption/cleanup-sessions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.660 [INFO] [RouterExplorer] Mapped {/api/encryption/create-session-debug, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.660 [INFO] [RoutesResolver] EncryptExampleController {/encrypt-example}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.660 [INFO] [RouterExplorer] Mapped {/encrypt-example/public-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.660 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.661 [INFO] [RouterExplorer] Mapped {/encrypt-example/partial-secure/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.661 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-with-decrypt, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.661 [INFO] [RouterExplorer] Mapped {/encrypt-example/test-request-body-encryption, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.662 [INFO] [RouterExplorer] Mapped {/encrypt-example/simple-partial/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.662 [INFO] [RoutesResolver] SecureExampleController {/api/secure-examples}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.662 [INFO] [RouterExplorer] Mapped {/api/secure-examples/standard, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.663 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.663 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure-partial, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.663 [INFO] [RouterExplorer] Mapped {/api/secure-examples/sessions/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.664 [INFO] [RoutesResolver] TPSController {/tps}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.664 [INFO] [RouterExplorer] Mapped {/tps/create-test-students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.665 [INFO] [RouterExplorer] Mapped {/tps/delete-by-prefix, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.665 [INFO] [RouterExplorer] Mapped {/tps/delete-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.665 [INFO] [RouterExplorer] Mapped {/tps/create-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.666 [INFO] [RouterExplorer] Mapped {/tps/create-test-teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.666 [INFO] [RouterExplorer] Mapped {/tps/export-test-data-csv, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.666 [INFO] [RouterExplorer] Mapped {/tps/assign-special-package-to-all-students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.666 [INFO] [RoutesResolver] AiVoiceprintRecognitionController {/api/ai-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.666 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.667 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.667 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.667 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.668 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.668 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-libraries, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.669 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.669 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.669 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.669 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.670 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-features/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.670 [INFO] [RoutesResolver] XunfeiVoiceprintRecognitionController {/xunfei-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.670 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.670 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.673 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.673 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.674 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.674 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.675 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.675 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.675 [INFO] [RoutesResolver] VoiceprintGroupController {/voiceprint-group}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.675 [INFO] [RouterExplorer] Mapped {/voiceprint-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.676 [INFO] [RouterExplorer] Mapped {/voiceprint-group, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.676 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.676 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.676 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.677 [INFO] [RoutesResolver] VoiceprintFeatureController {/voiceprint-feature}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.677 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.677 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.677 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.678 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/group/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.678 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.678 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.678 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.679 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.679 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.679 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.679 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.680 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.680 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.681 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.681 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.681 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.682 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.682 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.682 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.684 [INFO] [PlatformCertificateService] 已加载微信支付平台证书: 604B78292D99D739AA8F4D437065F4D6A87F4057, 过期时间: 2030-06-30T10:22:39+08:00 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.687 [INFO] [PlatformCertificateService] 已加载 1 个微信支付平台证书 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.687 [INFO] [KeyManagementService] 密钥管理选项已加载: algorithm=aes-256-cbc, useRedis=true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.687 [INFO] [KeyManagementService] 从Redis加载了密钥: b0381a63-e168-4575-97d7-a67b68462d98 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.688 [INFO] [KeyManagementService] 从Redis加载了密钥: 18a27d70-6f69-48f5-977d-a8333c052ab3 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.688 [INFO] [KeyManagementService] 从Redis加载了密钥: f2cd89fd-c713-4da3-81b2-63fbf1d264c0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.688 [INFO] [KeyManagementService] 从Redis加载了密钥: 401b349c-c3f8-4fee-9ff3-3cfa252b56b7 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.688 [INFO] [KeyManagementService] 从Redis加载了4个密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.689 [INFO] [KeyManagementService] 尝试从Redis加载RSA密钥对... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.689 [INFO] [KeyManagementService] RSA密钥对已保存到Redis，ID: key-c1ba6e97, 有效期30天，指纹：c1ba6e97 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.689 [INFO] [KeyManagementService] 将密钥 key-c1ba6e97 状态从 active 更改为 deprecated {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.690 [INFO] [KeyManagementService] 设置活跃密钥ID: key-4ba5b012 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.690 [INFO] [KeyManagementService] 从Redis加载了402个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.690 [INFO] [KeyManagementService] 已从Redis加载402个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.691 [INFO] [KeyManagementService] 活跃RSA密钥对验证通过 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.691 [INFO] [KeyManagementService] RSA密钥对已初始化，共402个版本，活跃版本: key-4ba5b012 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.692 [INFO] [KeyManagementService] 密钥管理服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.693 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.693 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.693 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.694 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.694 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.694 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.694 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.695 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.695 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.695 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.697 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.699 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.702 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.704 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.705 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.705 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.705 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.706 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.706 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.707 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.707 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.707 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.708 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.708 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.709 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.709 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.710 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.710 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.710 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.711 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.711 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.711 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.712 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.713 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.713 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.713 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.713 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.714 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.714 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.714 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.714 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.715 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.715 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.716 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.716 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.716 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.717 [INFO] [NestApplication] Nest application successfully started {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:19:46.726 [INFO] [Startup] Application started successfully {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","url":"http://[::1]:8003","port":8003}
2025-07-30 11:20:00.006 [INFO] [EncryptionCleanupTask] 【定时任务】开始执行会话清理任务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.007 [INFO] [EncryptionService] 【会话清理】开始清理过期会话... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.008 [INFO] [EncryptionService] 【会话清理】已清理内存中的 0 个过期会话 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.014 [INFO] [EncryptionCleanupTask] 【定时任务】开始执行会话清理任务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.015 [INFO] [EncryptionService] 【会话清理】开始清理过期会话... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.016 [INFO] [EncryptionService] 【会话清理】已清理内存中的 0 个过期会话 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.021 [INFO] [EncryptionCleanupTask] 【定时任务】开始执行会话清理任务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.022 [INFO] [EncryptionService] 【会话清理】开始清理过期会话... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.022 [INFO] [EncryptionService] 【会话清理】已清理内存中的 0 个过期会话 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.023 [INFO] [EncryptionService] 【会话统计】当前会话状态: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.024 [INFO] [EncryptionService] 【会话统计】内存中会话总数: 0 (标准: 0, 安全: 0) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.024 [INFO] [EncryptionService] 【会话统计】Redis中会话总数: 0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.025 [INFO] [EncryptionService] 【会话统计】当前会话状态: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.025 [INFO] [EncryptionService] 【会话统计】内存中会话总数: 0 (标准: 0, 安全: 0) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.025 [INFO] [EncryptionService] 【会话统计】Redis中会话总数: 0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.026 [INFO] [EncryptionService] 【会话统计】当前会话状态: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.026 [INFO] [EncryptionService] 【会话统计】内存中会话总数: 0 (标准: 0, 安全: 0) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.026 [INFO] [EncryptionService] 【会话统计】Redis中会话总数: 0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.027 [INFO] [EncryptionCleanupTask] 【定时任务】会话清理任务完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.027 [INFO] [EncryptionCleanupTask] 【定时任务】会话清理任务完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:00.028 [INFO] [EncryptionCleanupTask] 【定时任务】会话清理任务完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:13.674 [INFO] [EncryptionService] 【密钥交换】客户端请求RSA公钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:13.696 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/encryption/publicKey","statusCode":200,"responseTime":"172ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.751 [WARN] [UserAuthController] 未找到用户5的学生信息: 用户ID 5 的学生记录未找到 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:13.757 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"100ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.775 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/publishedIdsByTarget","statusCode":304,"responseTime":"109ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.782 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/list","statusCode":201,"responseTime":"180ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.812 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web-carousel/active","statusCode":304,"responseTime":"104ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.815 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-auth/findAllBindByPhone?phone=15270736300","statusCode":304,"responseTime":"171ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.860 [INFO] [EncryptionService] 【密钥交换】客户端请求RSA公钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:13.863 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/encryption/publicKey","statusCode":304,"responseTime":"37ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.869 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":200,"responseTime":"208ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:13.983 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/list","statusCode":201,"responseTime":"159ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.133 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/teacher-task/list?studentId=5&roleId=2&page=1&size=50","statusCode":200,"responseTime":"338ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.155 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/publishedIdsByTarget","statusCode":304,"responseTime":"239ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.164 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web-carousel/active","statusCode":304,"responseTime":"87ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.193 [INFO] [EncryptionController] 创建会话密钥，会话ID: 1753845614193.ffff127001.u5hol33r7us {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.195 [INFO] [EncryptionService] 【会话创建】开始为会话 1753845614193.ffff127001.u5hol33r7us 创建标准密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.195 [INFO] [EncryptionService] 【会话创建】接收到加密的AES密钥数据，长度: 344, 密钥ID: key-4ba5b012 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.196 [INFO] [EncryptionService] 【会话创建】使用指定密钥ID key-4ba5b012 解密AES密钥... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.199 [INFO] [EncryptionService] 【会话创建】成功解析AES密钥数据 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.200 [INFO] [EncryptionService] 【会话创建】生成标准会话密钥，AES密钥指纹: e787f679, IV指纹: 732fd28b {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.201 [INFO] [EncryptionService] 【会话创建】会话将于 2025-07-30T03:50:14.199Z 过期 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.207 [INFO] [RedisSessionService] 会话 1753845614193.ffff127001.u5hol33r7us 已存储到Redis (类型: standard, TTL: 1800秒) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.208 [INFO] [EncryptionService] 【会话创建】标准会话 1753845614193.ffff127001.u5hol33r7us 创建成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.210 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/encryption/session","statusCode":201,"responseTime":"27ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.215 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"453ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.226 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"347ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.256 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=1&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"469ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.259 [INFO] [EncryptionController] 创建会话密钥，会话ID: 1753845614258.ffff127001.2l0eh5y1kq3 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.259 [INFO] [EncryptionService] 【会话创建】开始为会话 1753845614258.ffff127001.2l0eh5y1kq3 创建标准密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.260 [INFO] [EncryptionService] 【会话创建】接收到加密的AES密钥数据，长度: 344, 密钥ID: key-4ba5b012 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.260 [INFO] [EncryptionService] 【会话创建】使用指定密钥ID key-4ba5b012 解密AES密钥... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.264 [INFO] [EncryptionService] 【会话创建】成功解析AES密钥数据 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.265 [INFO] [EncryptionService] 【会话创建】生成标准会话密钥，AES密钥指纹: 4d2731d3, IV指纹: 21891885 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.265 [INFO] [EncryptionService] 【会话创建】会话将于 2025-07-30T03:50:14.264Z 过期 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.277 [INFO] [RedisSessionService] 会话 1753845614258.ffff127001.2l0eh5y1kq3 已存储到Redis (类型: standard, TTL: 1800秒) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.279 [INFO] [EncryptionService] 【会话创建】标准会话 1753845614258.ffff127001.2l0eh5y1kq3 创建成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.281 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/encryption/session","statusCode":201,"responseTime":"53ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.303 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"126ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.308 [WARN] [UserAuthController] 未找到用户5的学生信息: 用户ID 5 的学生记录未找到 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG"}
2025-07-30 11:20:14.309 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-auth/findAllBindByPhone?phone=15270736300","statusCode":304,"responseTime":"137ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.362 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"69ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.381 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":200,"responseTime":"63ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.417 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"131ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.459 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=1&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"146ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.473 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"81ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.490 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"126ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.529 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":200,"responseTime":"51ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.537 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"87ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.541 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/list","statusCode":201,"responseTime":"47ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.543 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/unread","statusCode":201,"responseTime":"307ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.564 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/publishedIdsByTarget","statusCode":304,"responseTime":"30ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.569 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/unread","statusCode":201,"responseTime":"322ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.587 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"42ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.616 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"134ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.641 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"40ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.644 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"95ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.704 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"53ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.742 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"72ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.744 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/publishedIdsByTarget","statusCode":304,"responseTime":"33ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.764 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/list","statusCode":201,"responseTime":"57ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.783 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"151ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.819 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"68ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.854 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"47ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.896 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/unread","statusCode":201,"responseTime":"248ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.919 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"28ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.939 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/unread","statusCode":201,"responseTime":"172ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:14.973 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":304,"responseTime":"43ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:15.859 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/ping","statusCode":200,"responseTime":"2ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:15.981 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":200,"responseTime":"42ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-30 11:20:16.240 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83196,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":200,"responseTime":"40ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
