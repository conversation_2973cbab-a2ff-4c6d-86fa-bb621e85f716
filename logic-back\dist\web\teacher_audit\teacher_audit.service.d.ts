import { Repository } from 'typeorm';
import { CreateTeacherAuditDto } from './dto/create-teacher_audit.dto';
import { UpdateTeacherAuditDto } from './dto/update-teacher_audit.dto';
import { TeacherAudit, AuditResult } from './entities/teacher_audit.entity';
import { UserInfo } from 'src/util/database/mysql/user_info/entities/user_info.entity';
export declare class TeacherAuditService {
    private teacherAuditRepository;
    private userInfoRepository;
    constructor(teacherAuditRepository: Repository<TeacherAudit>, userInfoRepository: Repository<UserInfo>);
    findByTeacherName(teacherName: string): Promise<TeacherAudit[]>;
    findByUserId(userId: number): Promise<TeacherAudit[]>;
    findByTeacherNameLike(namePattern: string): Promise<TeacherAudit[]>;
    findByTeacherNameAndStatus(teacherName: string, status: number): Promise<TeacherAudit[]>;
    create(createTeacherAuditDto: CreateTeacherAuditDto): Promise<TeacherAudit>;
    findAll(): Promise<TeacherAudit[]>;
    findPending(): Promise<TeacherAudit[]>;
    findByTeacherId(teacherId: number): Promise<TeacherAudit | null>;
    findOne(id: number): Promise<TeacherAudit>;
    update(id: number, updateTeacherAuditDto: UpdateTeacherAuditDto): Promise<TeacherAudit>;
    remove(id: number): Promise<void>;
    getTeacherAuditDetail(id: number): Promise<{
        teacher: {
            id: number;
            nickName: string;
            avatarUrl: string;
            phone: string;
        } | null;
        auditor: {
            id: number;
            nickName: string;
            avatarUrl: string;
            phone: string;
        } | null;
        id: number;
        teacherId: number;
        teacherName: string;
        auditorId: number;
        schoolInfo: string;
        auditorName: string;
        result: AuditResult;
        reason: string;
        beforeStatus: string;
        afterStatus: string;
        createTime: Date;
        updateTime: Date;
        operationIp: string;
        deviceInfo: string;
        isDelete: number;
    }>;
}
