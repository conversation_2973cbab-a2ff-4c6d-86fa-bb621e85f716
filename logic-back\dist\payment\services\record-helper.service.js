"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RecordHelperService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordHelperService = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const payment_record_service_1 = require("../../util/database/mysql/payment_record/payment-record.service");
const lock_manager_1 = require("../lock/lock.manager");
const payment_record_entity_1 = require("../../util/database/mysql/payment_record/entities/payment-record.entity");
const optimistic_lock_1 = require("../lock/optimistic.lock");
let RecordHelperService = RecordHelperService_1 = class RecordHelperService {
    moduleRef;
    lockManager;
    optimisticLock;
    paymentRecordService;
    logger = new common_1.Logger(RecordHelperService_1.name);
    constructor(moduleRef, lockManager, optimisticLock, paymentRecordService) {
        this.moduleRef = moduleRef;
        this.lockManager = lockManager;
        this.optimisticLock = optimisticLock;
        this.paymentRecordService = paymentRecordService;
    }
    isValidStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            'pending': ['success', 'failed', 'closed', 'expired'],
            'processing': ['success', 'failed', 'closed'],
            'success': ['refunded', 'partial_refund'],
            'failed': ['closed'],
            'closed': [],
            'refunded': [],
            'partial_refund': ['refunded'],
            'expired': ['closed'],
        };
        if (currentStatus === newStatus) {
            return true;
        }
        if (!validTransitions[currentStatus]) {
            this.logger.warn(`未知的当前状态: ${currentStatus}`);
            return false;
        }
        return validTransitions[currentStatus].includes(newStatus);
    }
    async createOrUpdatePaymentRecord(params) {
        try {
            this.logger.log(`开始处理支付记录: 订单号=${params.tradeNo}, 支付ID=${params.paymentId}, 状态=${params.status}`);
            return await this.lockManager?.withDistributedLock(`payment:record:${params.tradeNo}`, async () => {
                this.logger.log(`获取支付记录分布式锁成功: 订单号=${params.tradeNo}`);
                const existingRecords = await this.paymentRecordService?.findByOrderNo(params.tradeNo);
                if (existingRecords && existingRecords.length > 0) {
                    this.logger.log(`找到现有支付记录: 订单号=${params.tradeNo}, 状态=${existingRecords[0].status}`);
                    const existingRecord = existingRecords[0];
                    if (!this.isValidStatusTransition(existingRecord.status, params.status)) {
                        this.logger.warn(`状态转换无效: 从 ${existingRecord.status} 到 ${params.status}, 订单号=${params.tradeNo}`);
                        return false;
                    }
                    try {
                        this.logger.log(`使用乐观锁更新支付记录: 订单号=${params.tradeNo}, ID=${existingRecord.id}`);
                        if (!this.optimisticLock) {
                            throw new Error('乐观锁服务不可用');
                        }
                        await this.optimisticLock.updateWithOptimisticLock(payment_record_entity_1.PaymentRecord, existingRecord.id, async (record) => {
                            record.status = params.status;
                            record.paymentChannel = params.payMethod || record.paymentChannel;
                            record.paymentId = params.paymentId || record.paymentId;
                            record.amount = params.amount || record.amount;
                            if (params.paymentTime) {
                                record.paymentTime = params.paymentTime;
                            }
                            if (params.extraData) {
                                record.rawResponse = {
                                    ...(record.rawResponse || {}),
                                    ...params.extraData
                                };
                            }
                            this.logger.log(`支付记录更新内容: 状态=${record.status}, 支付方式=${record.paymentChannel}, 支付ID=${record.paymentId}`);
                            return record;
                        });
                        this.logger.log(`支付记录更新成功: 订单号=${params.tradeNo}`);
                        return true;
                    }
                    catch (error) {
                        this.logger.error(`支付记录更新失败: ${error.message}`, error.stack);
                        return false;
                    }
                }
                else {
                    this.logger.log(`未找到支付记录, 创建新记录: 订单号=${params.tradeNo}`);
                    try {
                        const newRecord = new payment_record_entity_1.PaymentRecord();
                        newRecord.orderNo = params.tradeNo;
                        newRecord.orderId = params.tradeNo;
                        newRecord.paymentId = params.paymentId || '';
                        newRecord.status = params.status;
                        newRecord.amount = params.amount || 0;
                        newRecord.userId = params.userId || '';
                        newRecord.paymentChannel = params.payMethod || '';
                        if (params.paymentTime) {
                            newRecord.paymentTime = params.paymentTime;
                        }
                        if (params.extraData) {
                            newRecord.rawResponse = params.extraData;
                        }
                        if (!this.paymentRecordService) {
                            throw new Error('支付记录服务不可用');
                        }
                        await this.paymentRecordService.create(newRecord);
                        this.logger.log(`新支付记录创建成功: 订单号=${params.tradeNo}`);
                        return true;
                    }
                    catch (error) {
                        this.logger.error(`新支付记录创建失败: ${error.message}`, error.stack);
                        return false;
                    }
                }
            }, 5000) || false;
        }
        catch (error) {
            this.logger.error(`处理支付记录过程中发生错误: ${error.message}`, error.stack);
            return false;
        }
    }
    async updateWithOptimisticLock(paymentRecordService, recordId, paymentData) {
        let retries = 0;
        const maxRetries = 3;
        while (retries < maxRetries) {
            try {
                const currentRecord = await paymentRecordService.findById(recordId);
                const updateData = {
                    status: paymentData.status,
                    paymentTime: paymentData.paymentTime,
                    paymentId: paymentData.paymentId,
                    rawResponse: {
                        ...(currentRecord.rawResponse || {}),
                        ...(paymentData.extraData || {}),
                    },
                    version: currentRecord.version,
                };
                await paymentRecordService.update(recordId, updateData);
                this.logger.log(`更新订单 ${paymentData.tradeNo} 的支付记录成功, ID: ${recordId}`);
                return true;
            }
            catch (error) {
                if (error.message.includes('乐观锁冲突') && retries < maxRetries - 1) {
                    retries++;
                    this.logger.warn(`乐观锁冲突，进行第${retries}次重试`);
                    await new Promise(resolve => setTimeout(resolve, 100 * Math.pow(2, retries)));
                    continue;
                }
                this.logger.error(`更新支付记录失败: ${error.message}`, error.stack);
                throw error;
            }
        }
        this.logger.error(`更新支付记录失败: 超过最大重试次数(${maxRetries})`);
        return false;
    }
    async executeSafely(fn, lockKey) {
        if (this.lockManager && lockKey) {
            return this.lockManager.withDistributedLock(`payment:record:${lockKey}`, fn, 10000);
        }
        return fn();
    }
};
exports.RecordHelperService = RecordHelperService;
exports.RecordHelperService = RecordHelperService = RecordHelperService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.ModuleRef,
        lock_manager_1.LockManager,
        optimistic_lock_1.OptimisticLock,
        payment_record_service_1.PaymentRecordService])
], RecordHelperService);
//# sourceMappingURL=record-helper.service.js.map