"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentRecordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePaymentRecordDto {
    orderId;
    orderNo;
    paymentChannel;
    paymentId;
    amount;
    status;
    userId;
    clientIp;
    paymentTime;
    rawRequest;
    rawResponse;
}
exports.CreatePaymentRecordDto = CreatePaymentRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '订单ID不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单编号' }),
    (0, class_validator_1.IsNotEmpty)({ message: '订单编号不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "orderNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付渠道', example: 'alipay' }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付渠道不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "paymentChannel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付平台交易号', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "paymentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付金额', example: 99.99 }),
    (0, class_validator_1.IsNotEmpty)({ message: '支付金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '支付金额必须为数字' }),
    __metadata("design:type", Number)
], CreatePaymentRecordDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付状态', example: 'pending', default: 'pending' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNotEmpty)({ message: '用户ID不能为空' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '客户端IP', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePaymentRecordDto.prototype, "clientIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支付完成时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreatePaymentRecordDto.prototype, "paymentTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始请求数据', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreatePaymentRecordDto.prototype, "rawRequest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '原始响应数据', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreatePaymentRecordDto.prototype, "rawResponse", void 0);
//# sourceMappingURL=create-payment-record.dto.js.map