"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordLoginLocationCommand = exports.LoginStatus = exports.LoginType = void 0;
const ip_address_vo_1 = require("../../domain/value-objects/ip-address.vo");
var LoginType;
(function (LoginType) {
    LoginType["PASSWORD"] = "PASSWORD";
    LoginType["SMS"] = "SMS";
    LoginType["QR_CODE"] = "QR_CODE";
    LoginType["OAUTH"] = "OAUTH";
})(LoginType || (exports.LoginType = LoginType = {}));
var LoginStatus;
(function (LoginStatus) {
    LoginStatus["SUCCESS"] = "SUCCESS";
    LoginStatus["FAILED"] = "FAILED";
    LoginStatus["BLOCKED"] = "BLOCKED";
})(LoginStatus || (exports.LoginStatus = LoginStatus = {}));
class RecordLoginLocationCommand {
    userId;
    ipAddress;
    location;
    riskScore;
    loginType;
    loginStatus;
    sessionId;
    userAgent;
    deviceInfo;
    failReason;
    timestamp;
    constructor(userId, ipAddress, location, riskScore, loginType, loginStatus, sessionId, userAgent, deviceInfo, failReason) {
        this.userId = userId;
        this.ipAddress = ipAddress;
        this.location = location;
        this.riskScore = riskScore;
        this.loginType = loginType;
        this.loginStatus = loginStatus;
        this.sessionId = sessionId;
        this.userAgent = userAgent;
        this.deviceInfo = deviceInfo;
        this.failReason = failReason;
        this.timestamp = new Date();
    }
    static createSuccessLogin(userId, ipAddressString, location, riskScore, loginType, sessionId, userAgent, deviceInfo) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new RecordLoginLocationCommand(userId, ipAddress, location, riskScore, loginType, LoginStatus.SUCCESS, sessionId, userAgent, deviceInfo);
    }
    static createFailedLogin(userId, ipAddressString, location, riskScore, loginType, failReason, userAgent, deviceInfo) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new RecordLoginLocationCommand(userId, ipAddress, location, riskScore, loginType, LoginStatus.FAILED, undefined, userAgent, deviceInfo, failReason);
    }
    static createBlockedLogin(userId, ipAddressString, location, riskScore, loginType, blockReason, userAgent, deviceInfo) {
        const ipAddress = ip_address_vo_1.IpAddress.create(ipAddressString);
        return new RecordLoginLocationCommand(userId, ipAddress, location, riskScore, loginType, LoginStatus.BLOCKED, undefined, userAgent, deviceInfo, blockReason);
    }
    validate() {
        const errors = [];
        if (this.userId <= 0) {
            errors.push('用户ID必须大于0');
        }
        if (this.loginStatus === LoginStatus.FAILED && !this.failReason) {
            errors.push('失败登录必须提供失败原因');
        }
        if (this.loginStatus === LoginStatus.BLOCKED && !this.failReason) {
            errors.push('被阻止登录必须提供阻止原因');
        }
        if (this.loginStatus === LoginStatus.SUCCESS && !this.sessionId) {
            errors.push('成功登录必须提供会话ID');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    get isSuccessLogin() {
        return this.loginStatus === LoginStatus.SUCCESS;
    }
    get isFailedLogin() {
        return this.loginStatus === LoginStatus.FAILED;
    }
    get isBlockedLogin() {
        return this.loginStatus === LoginStatus.BLOCKED;
    }
    get isHighRiskLogin() {
        return this.riskScore.isHighRisk;
    }
    get isForeignLogin() {
        return this.location.isForeign;
    }
    getSummary() {
        const status = this.loginStatus === LoginStatus.SUCCESS ? '成功' :
            this.loginStatus === LoginStatus.FAILED ? '失败' : '被阻止';
        return `用户${this.userId}${status}登录: ${this.location.displayName} (${this.ipAddress.masked}) - ${this.riskScore.levelDescription}`;
    }
    getSecurityInfo() {
        return {
            riskLevel: this.riskScore.level,
            riskFactors: this.riskScore.factors,
            needsVerification: this.riskScore.needsVerification,
            isForeign: this.location.isForeign,
            isNewLocation: this.riskScore.factors.includes('新位置')
        };
    }
    toJSON() {
        return {
            userId: this.userId,
            ipAddress: this.ipAddress.toJSON(),
            location: this.location.toJSON(),
            riskScore: this.riskScore.toJSON(),
            loginType: this.loginType,
            loginStatus: this.loginStatus,
            sessionId: this.sessionId,
            userAgent: this.userAgent,
            deviceInfo: this.deviceInfo,
            failReason: this.failReason,
            timestamp: this.timestamp.toISOString(),
            securityInfo: this.getSecurityInfo()
        };
    }
}
exports.RecordLoginLocationCommand = RecordLoginLocationCommand;
//# sourceMappingURL=record-login-location.command.js.map