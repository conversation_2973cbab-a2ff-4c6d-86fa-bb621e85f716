"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCommonLocation = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let UserCommonLocation = class UserCommonLocation {
    id;
    userId;
    country;
    province;
    city;
    isp;
    loginCount;
    firstLoginAt;
    lastLoginAt;
    isTrusted;
    trustScore;
    createdAt;
    updatedAt;
    getDisplayName() {
        const parts = [];
        if (this.country !== '中国')
            parts.push(this.country);
        if (this.province)
            parts.push(this.province);
        if (this.city && this.city !== this.province)
            parts.push(this.city);
        return parts.join(' ') || '未知位置';
    }
    isNewLocation(days = 7) {
        if (!this.firstLoginAt)
            return true;
        const daysDiff = (Date.now() - this.firstLoginAt.getTime()) / (1000 * 60 * 60 * 24);
        return daysDiff <= days;
    }
    isActiveLocation(days = 30) {
        if (!this.lastLoginAt)
            return false;
        const daysDiff = (Date.now() - this.lastLoginAt.getTime()) / (1000 * 60 * 60 * 24);
        return daysDiff <= days;
    }
    updateLoginStats(loginTime = new Date()) {
        this.loginCount += 1;
        this.lastLoginAt = loginTime;
        if (!this.firstLoginAt) {
            this.firstLoginAt = loginTime;
        }
        this.updateTrustScore();
    }
    updateTrustScore() {
        let baseScore = Math.min(this.loginCount * 2, 60);
        if (this.firstLoginAt) {
            const daysSinceFirst = (Date.now() - this.firstLoginAt.getTime()) / (1000 * 60 * 60 * 24);
            const timeBonus = Math.min(daysSinceFirst / 30 * 20, 20);
            baseScore += timeBonus;
        }
        if (this.lastLoginAt) {
            const daysSinceLast = (Date.now() - this.lastLoginAt.getTime()) / (1000 * 60 * 60 * 24);
            if (daysSinceLast <= 7) {
                baseScore += 10;
            }
            else if (daysSinceLast > 90) {
                baseScore -= 10;
            }
        }
        if (this.isTrusted) {
            baseScore += 20;
        }
        this.trustScore = Math.min(Math.max(baseScore, 0), 100);
    }
    setAsTrusted(reason) {
        this.isTrusted = true;
        this.updateTrustScore();
    }
    removeTrustedStatus() {
        this.isTrusted = false;
        this.updateTrustScore();
    }
};
exports.UserCommonLocation = UserCommonLocation;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ type: 'bigint', comment: '主键ID' }),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", Number)
], UserCommonLocation.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', comment: '用户ID' }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], UserCommonLocation.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        default: '中国',
        comment: '国家'
    }),
    (0, swagger_1.ApiProperty)({ description: '国家', default: '中国' }),
    __metadata("design:type", String)
], UserCommonLocation.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 30,
        comment: '省份'
    }),
    (0, swagger_1.ApiProperty)({ description: '省份' }),
    __metadata("design:type", String)
], UserCommonLocation.prototype, "province", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 30,
        comment: '城市'
    }),
    (0, swagger_1.ApiProperty)({ description: '城市' }),
    __metadata("design:type", String)
], UserCommonLocation.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: '主要运营商'
    }),
    (0, swagger_1.ApiProperty)({ description: '主要运营商', required: false }),
    __metadata("design:type", String)
], UserCommonLocation.prototype, "isp", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int',
        default: 1,
        comment: '登录次数'
    }),
    (0, swagger_1.ApiProperty)({ description: '登录次数', default: 1 }),
    __metadata("design:type", Number)
], UserCommonLocation.prototype, "loginCount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'datetime',
        nullable: true,
        comment: '首次登录时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '首次登录时间', required: false }),
    __metadata("design:type", Date)
], UserCommonLocation.prototype, "firstLoginAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'datetime',
        nullable: true,
        comment: '最后登录时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '最后登录时间', required: false }),
    __metadata("design:type", Date)
], UserCommonLocation.prototype, "lastLoginAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'boolean',
        default: false,
        comment: '是否为可信地区'
    }),
    (0, swagger_1.ApiProperty)({ description: '是否为可信地区', default: false }),
    __metadata("design:type", Boolean)
], UserCommonLocation.prototype, "isTrusted", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 0.00,
        comment: '信任评分(0-100)'
    }),
    (0, swagger_1.ApiProperty)({ description: '信任评分(0-100)', default: 0.00 }),
    __metadata("design:type", Number)
], UserCommonLocation.prototype, "trustScore", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        type: 'datetime',
        comment: '创建时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], UserCommonLocation.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        type: 'datetime',
        comment: '更新时间'
    }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], UserCommonLocation.prototype, "updatedAt", void 0);
exports.UserCommonLocation = UserCommonLocation = __decorate([
    (0, typeorm_1.Entity)('user_common_locations'),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['userId', 'isTrusted']),
    (0, typeorm_1.Index)(['loginCount']),
    (0, typeorm_1.Index)(['trustScore'])
], UserCommonLocation);
//# sourceMappingURL=user-common-location.entity.js.map