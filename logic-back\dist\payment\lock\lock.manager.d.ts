import { ConfigService } from '@nestjs/config';
import { EntityManager, ObjectLiteral } from 'typeorm';
import { DistributedLock } from './distributed.lock';
import { OptimisticLock } from './optimistic.lock';
import { PessimisticLock } from './pessimistic.lock';
export declare class LockManager {
    private readonly distributedLock;
    private readonly optimisticLock;
    private readonly pessimisticLock;
    private readonly configService;
    private readonly logger;
    constructor(distributedLock: DistributedLock, optimisticLock: OptimisticLock, pessimisticLock: PessimisticLock, configService: ConfigService);
    getDistributedLock(): DistributedLock;
    getOptimisticLock(): OptimisticLock;
    getPessimisticLock(): PessimisticLock;
    withDistributedLock<T>(key: string, callback: () => Promise<T>, ttl?: number): Promise<T>;
    withPessimisticRowLock<T>(tableName: string, condition: Record<string, any>, callback: (manager: EntityManager) => Promise<T>): Promise<T>;
    updateWithOptimisticLock<Entity extends ObjectLiteral>(entityClass: {
        new (): Entity;
    } & any, id: any, updateFn: (entity: Entity) => Promise<Entity> | Entity, maxRetries?: number): Promise<Entity>;
    withSmartLock<T>(key: string, callback: () => Promise<T>, options?: {
        preferredLockType?: 'distributed' | 'pessimistic' | 'optimistic';
        entity?: {
            new (): ObjectLiteral;
        } & any;
        entityId?: any;
        tableName?: string;
        condition?: Record<string, any>;
        ttl?: number;
        maxRetries?: number;
    }): Promise<T>;
    executeWithConcurrencyControl<T>(operations: Array<{
        key: string;
        callback: () => Promise<T>;
        lockType?: 'distributed' | 'pessimistic' | 'optimistic';
        options?: any;
    }>): Promise<T[]>;
}
