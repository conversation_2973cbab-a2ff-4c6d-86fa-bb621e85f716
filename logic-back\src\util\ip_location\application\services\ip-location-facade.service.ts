import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../../../common/logger/logger.service';

// CQRS服务
import { IpLocationCommandService } from './ip-location-command.service';
import { IpLocationQueryService } from './ip-location-query.service';

// 命令和查询对象
import { UpdateCommonLocationCommand } from '../commands/update-common-location.command';
import { SetTrustedLocationCommand } from '../commands/set-trusted-location.command';
import { RecordLoginLocationCommand, LoginType, LoginStatus } from '../commands/record-login-location.command';
import { GetLocationByIpQuery } from '../queries/get-location-by-ip.query';
import { GetUserLocationStatsQuery } from '../queries/get-user-location-stats.query';
import { AssessLoginRiskQuery } from '../queries/assess-login-risk.query';

// 领域服务
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { RiskAssessmentDomainService } from '../../domain/services/risk-assessment-domain.service';

// 值对象
import { IpAddress } from '../../domain/value-objects/ip-address.vo';
import { GeographicLocation } from '../../domain/value-objects/geographic-location.vo';
import { RiskScore } from '../../domain/value-objects/risk-score.vo';

// 异常处理
import { IpLocationDomainException } from '../../domain/exceptions/ip-location-domain.exception';
import { InvalidIpException } from '../../domain/exceptions/invalid-ip.exception';
import { LocationNotFoundException } from '../../domain/exceptions/location-not-found.exception';

/**
 * IP地理位置门面服务
 * 基于DDD架构提供统一的业务接口
 * 这是新的推荐使用方式，保持与原有应用服务的兼容
 */
@Injectable()
export class IpLocationFacadeService {
  constructor(
    private readonly commandService: IpLocationCommandService,
    private readonly queryService: IpLocationQueryService,
    private readonly domainService: IpLocationDomainService,
    private readonly riskAssessmentService: RiskAssessmentDomainService,
    private readonly logger: LoggerService,
  ) {}

  /**
   * 查询IP地理位置信息
   * @param ip IP地址字符串
   * @param includeRisk 是否包含风险评估
   * @returns 位置信息
   */
  async getLocationByIP(ip: string, includeRisk: boolean = false) {
    try {
      const query = GetLocationByIpQuery.create(ip, includeRisk);
      const result = await this.queryService.handleGetLocationByIp(query);
      
      if (!result.success) {
        throw new Error(result.errors?.join(', ') || '查询失败');
      }
      
      return {
        success: true,
        data: result.data,
        message: '查询成功',
        executionTime: result.executionTime,
        fromCache: result.fromCache
      };
    } catch (error) {
      this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 评估登录风险
   * @param userId 用户ID
   * @param ip IP地址
   * @param userAgent 用户代理
   * @param includeRecommendations 是否包含建议
   * @returns 风险评估结果
   */
  async assessLoginRisk(
    userId: number,
    ip: string,
    userAgent?: string,
    includeRecommendations: boolean = true
  ) {
    try {
      const query = AssessLoginRiskQuery.createBasic(userId, ip, userAgent);
      query.includeRecommendations = includeRecommendations;
      
      const result = await this.queryService.handleAssessLoginRisk(query);
      
      if (!result.success) {
        throw new Error(result.errors?.join(', ') || '风险评估失败');
      }
      
      return {
        success: true,
        data: result.data,
        message: '风险评估完成',
        executionTime: result.executionTime
      };
    } catch (error) {
      this.logger.error(`登录风险评估失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 获取用户位置统计
   * @param userId 用户ID
   * @param days 统计天数
   * @param includeRiskAnalysis 是否包含风险分析
   * @returns 位置统计
   */
  async getUserLocationStats(
    userId: number,
    days: number = 30,
    includeRiskAnalysis: boolean = false
  ) {
    try {
      const query = includeRiskAnalysis 
        ? GetUserLocationStatsQuery.createWithRiskAnalysis(userId, days)
        : GetUserLocationStatsQuery.createBasic(userId, days);
      
      const result = await this.queryService.handleGetUserLocationStats(query);
      
      if (!result.success) {
        throw new Error(result.errors?.join(', ') || '统计查询失败');
      }
      
      return {
        success: true,
        data: result.data,
        message: '统计查询成功',
        executionTime: result.executionTime,
        fromCache: result.fromCache
      };
    } catch (error) {
      this.logger.error(`用户位置统计失败: ${userId}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 记录登录位置
   * @param userId 用户ID
   * @param ip IP地址
   * @param loginType 登录类型
   * @param loginStatus 登录状态
   * @param sessionId 会话ID
   * @param userAgent 用户代理
   * @returns 记录结果
   */
  async recordLoginLocation(
    userId: number,
    ip: string,
    loginType: LoginType = LoginType.PASSWORD,
    loginStatus: LoginStatus = LoginStatus.SUCCESS,
    sessionId?: string,
    userAgent?: string
  ) {
    try {
      // 1. 解析IP地理位置
      const ipAddress = IpAddress.create(ip);
      const location = await this.domainService.resolveLocation(ipAddress);
      
      // 2. 评估风险（简化版，用于记录）
      const riskScore = RiskScore.createLow(['登录记录']);
      
      // 3. 创建记录命令
      let command;
      if (loginStatus === LoginStatus.SUCCESS) {
        command = RecordLoginLocationCommand.createSuccessLogin(
          userId, ip, location, riskScore, loginType, sessionId, userAgent
        );
      } else if (loginStatus === LoginStatus.FAILED) {
        command = RecordLoginLocationCommand.createFailedLogin(
          userId, ip, location, riskScore, loginType, '登录失败', userAgent
        );
      } else {
        command = RecordLoginLocationCommand.createBlockedLogin(
          userId, ip, location, riskScore, loginType, '登录被阻止', userAgent
        );
      }
      
      // 4. 执行命令
      const result = await this.commandService.handleRecordLoginLocation(command);
      
      if (!result.success) {
        throw new Error(result.errors?.join(', ') || '记录失败');
      }
      
      return {
        success: true,
        data: result.data,
        message: '登录位置记录成功',
        executionTime: result.executionTime
      };
    } catch (error) {
      this.logger.error(`登录位置记录失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 设置可信位置
   * @param userId 用户ID
   * @param province 省份
   * @param city 城市
   * @param reason 设置原因
   * @param setBy 设置者
   * @returns 设置结果
   */
  async setTrustedLocation(
    userId: number,
    province: string,
    city: string,
    reason: string = '用户主动设置',
    setBy: 'USER' | 'SYSTEM' | 'ADMIN' = 'USER'
  ) {
    try {
      const command = new SetTrustedLocationCommand(userId, province, city, reason, setBy);
      const result = await this.commandService.handleSetTrustedLocation(command);
      
      if (!result.success) {
        throw new Error(result.errors?.join(', ') || '设置失败');
      }
      
      return {
        success: true,
        data: result.data,
        message: '可信位置设置成功',
        executionTime: result.executionTime
      };
    } catch (error) {
      this.logger.error(`设置可信位置失败: ${userId}-${province}-${city}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 更新用户常用位置
   * @param userId 用户ID
   * @param ip IP地址
   * @param sessionId 会话ID
   * @param userAgent 用户代理
   * @returns 更新结果
   */
  async updateUserCommonLocation(
    userId: number,
    ip: string,
    sessionId?: string,
    userAgent?: string
  ) {
    try {
      // 1. 解析IP地理位置
      const ipAddress = IpAddress.create(ip);
      const location = await this.domainService.resolveLocation(ipAddress);
      
      // 2. 创建更新命令
      const command = new UpdateCommonLocationCommand(
        userId, ipAddress, location, sessionId, userAgent
      );
      
      // 3. 执行命令
      const result = await this.commandService.handleUpdateCommonLocation(command);
      
      if (!result.success) {
        throw new Error(result.errors?.join(', ') || '更新失败');
      }
      
      return {
        success: true,
        data: result.data,
        message: '常用位置更新成功',
        executionTime: result.executionTime
      };
    } catch (error) {
      this.logger.error(`更新常用位置失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 测试IP解析功能
   * @param testIp 测试IP
   * @returns 测试结果
   */
  async testIpResolution(testIp: string = '*******') {
    try {
      const result = await this.domainService.testResolution(testIp);
      return {
        success: result.success,
        data: result,
        message: result.success ? '测试成功' : '测试失败'
      };
    } catch (error) {
      this.logger.error(`IP解析测试失败: ${testIp}`, error, 'IpLocationFacadeService');
      
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }
}
