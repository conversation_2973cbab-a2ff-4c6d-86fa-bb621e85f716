import { TaskSelfAssessmentItemService } from 'src/util/database/mysql/task_self_assessment_item/task_self_assessment_item.service';
import { TaskSelfAssessmentItem } from 'src/util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
import { HttpResponse } from 'src/web/http_response_result/http-response.interface';
export declare class SelfAssessmentItemController {
    private readonly taskSelfAssessmentItemService;
    private readonly httpResponseResultService;
    constructor(taskSelfAssessmentItemService: TaskSelfAssessmentItemService, httpResponseResultService: HttpResponseResultService);
    getByTaskId(taskId: string): Promise<HttpResponse<TaskSelfAssessmentItem[]>>;
    searchItems(keyword: string): Promise<HttpResponse<TaskSelfAssessmentItem[]>>;
}
