"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentRecordService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentRecordService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const payment_record_entity_1 = require("./entities/payment-record.entity");
let PaymentRecordService = PaymentRecordService_1 = class PaymentRecordService {
    paymentRecordRepository;
    logger = new common_1.Logger(PaymentRecordService_1.name);
    constructor(paymentRecordRepository) {
        this.paymentRecordRepository = paymentRecordRepository;
    }
    async create(createPaymentRecordDto) {
        try {
            const paymentRecord = this.paymentRecordRepository.create({
                id: (0, uuid_1.v4)(),
                ...createPaymentRecordDto,
                status: createPaymentRecordDto.status || 'pending',
                version: 0,
            });
            return await this.paymentRecordRepository.save(paymentRecord);
        }
        catch (error) {
            this.logger.error(`创建支付记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async update(id, updatePaymentRecordDto) {
        try {
            const paymentRecord = await this.paymentRecordRepository.findOne({
                where: { id },
            });
            if (!paymentRecord) {
                throw new common_1.NotFoundException(`支付记录不存在: ${id}`);
            }
            if (updatePaymentRecordDto.version !== undefined) {
                const result = await this.paymentRecordRepository.update({ id, version: updatePaymentRecordDto.version }, {
                    ...updatePaymentRecordDto,
                    version: () => 'version + 1',
                });
                if (result.affected === 0) {
                    throw new Error('乐观锁冲突，更新失败');
                }
            }
            else {
                await this.paymentRecordRepository.update({ id }, {
                    ...updatePaymentRecordDto,
                    version: () => 'version + 1',
                });
            }
            const updatedRecord = await this.paymentRecordRepository.findOne({
                where: { id },
            });
            if (!updatedRecord) {
                throw new common_1.NotFoundException(`更新后的支付记录不存在: ${id}`);
            }
            return updatedRecord;
        }
        catch (error) {
            this.logger.error(`更新支付记录失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findById(id) {
        const paymentRecord = await this.paymentRecordRepository.findOne({
            where: { id },
        });
        if (!paymentRecord) {
            throw new common_1.NotFoundException(`支付记录不存在: ${id}`);
        }
        return paymentRecord;
    }
    async findByOrderNo(orderNo) {
        return this.paymentRecordRepository.find({
            where: { orderNo },
            order: { createdAt: 'DESC' },
        });
    }
    async findByPaymentId(paymentId, paymentChannel) {
        const where = { paymentId };
        if (paymentChannel) {
            where.paymentChannel = paymentChannel;
        }
        const paymentRecord = await this.paymentRecordRepository.findOne({
            where,
        });
        if (!paymentRecord) {
            throw new common_1.NotFoundException(`支付记录不存在: ${paymentId}`);
        }
        return paymentRecord;
    }
    async findAll(queryPaymentRecordDto, page = 1, limit = 10) {
        try {
            const where = {};
            if (queryPaymentRecordDto.orderId) {
                where.orderId = queryPaymentRecordDto.orderId;
            }
            if (queryPaymentRecordDto.orderNo) {
                where.orderNo = queryPaymentRecordDto.orderNo;
            }
            if (queryPaymentRecordDto.paymentChannel) {
                where.paymentChannel = queryPaymentRecordDto.paymentChannel;
            }
            if (queryPaymentRecordDto.paymentId) {
                where.paymentId = queryPaymentRecordDto.paymentId;
            }
            if (queryPaymentRecordDto.status) {
                where.status = queryPaymentRecordDto.status;
            }
            if (queryPaymentRecordDto.userId) {
                where.userId = queryPaymentRecordDto.userId;
            }
            if (queryPaymentRecordDto.startTime && queryPaymentRecordDto.endTime) {
                where.createdAt = (0, typeorm_2.Between)(queryPaymentRecordDto.startTime, queryPaymentRecordDto.endTime);
            }
            const [items, total] = await this.paymentRecordRepository.findAndCount({
                where,
                order: { createdAt: 'DESC' },
                skip: (page - 1) * limit,
                take: limit,
            });
            return { items, total };
        }
        catch (error) {
            this.logger.error(`查询支付记录列表失败: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateStatus(id, status, paymentId, paymentTime, rawResponse) {
        const updateDto = { status };
        if (paymentId) {
            updateDto.paymentId = paymentId;
        }
        if (paymentTime) {
            updateDto.paymentTime = paymentTime;
        }
        if (rawResponse) {
            updateDto.rawResponse = rawResponse;
        }
        return this.update(id, updateDto);
    }
};
exports.PaymentRecordService = PaymentRecordService;
exports.PaymentRecordService = PaymentRecordService = PaymentRecordService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_record_entity_1.PaymentRecord)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PaymentRecordService);
//# sourceMappingURL=payment-record.service.js.map