export declare class UserCommonLocation {
    id: number;
    userId: number;
    country: string;
    province: string;
    city: string;
    isp?: string;
    loginCount: number;
    firstLoginAt?: Date;
    lastLoginAt?: Date;
    isTrusted: boolean;
    trustScore: number;
    createdAt: Date;
    updatedAt: Date;
    getDisplayName(): string;
    isNewLocation(days?: number): boolean;
    isActiveLocation(days?: number): boolean;
    updateLoginStats(loginTime?: Date): void;
    private updateTrustScore;
    setAsTrusted(reason?: string): void;
    removeTrustedStatus(): void;
}
