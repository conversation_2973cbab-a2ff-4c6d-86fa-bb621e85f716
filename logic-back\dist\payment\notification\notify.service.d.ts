import { PaymentRecordService } from '../../util/database/mysql/payment_record/payment-record.service';
import { PaymentService } from '../services/payment.service';
import { PaymentTemplateService } from './template.service';
import { RecordHelperService } from '../services/record-helper.service';
import { NotificationRecordService } from '../../util/database/mysql/notification_record/notification-record.service';
import { PaymentLoggerService } from '../services/payment-logger.service';
import { PaymentOrderService } from '../../util/database/mysql/payment_order/payment-order.service';
import { WebSocketService } from '../../util/web_socket/web_socket.service';
export declare enum NotificationType {
    PAYMENT_SUCCESS = "payment_success",
    PAYMENT_FAIL = "payment_fail",
    REFUND_SUCCESS = "refund_success",
    REFUND_FAIL = "refund_fail"
}
export declare enum NotificationChannel {
    EMAIL = "email",
    SMS = "sms",
    WEBHOOK = "webhook",
    INTERNAL = "internal"
}
export declare class NotifyService {
    private readonly paymentRecordService;
    private readonly paymentService;
    private readonly templateService;
    private readonly recordHelper;
    private readonly notificationRecordService;
    private readonly paymentLogger;
    private readonly paymentOrderService;
    private readonly webSocketService;
    private readonly logger;
    constructor(paymentRecordService: PaymentRecordService, paymentService: PaymentService, templateService: PaymentTemplateService, recordHelper: RecordHelperService, notificationRecordService: NotificationRecordService, paymentLogger: PaymentLoggerService, paymentOrderService: PaymentOrderService, webSocketService: WebSocketService);
    handlePaymentSuccess(outTradeNo: string, paymentId: string, amount: number, channel: string, extraData?: any): Promise<boolean>;
    handlePaymentFail(outTradeNo: string, reason: string, channel: string, extraData?: any): Promise<boolean>;
    handleRefundSuccess(refundNo: string, refundId: string, amount: number, channel: string, extraData?: any): Promise<boolean>;
    handleRefundFail(refundNo: string, reason: string, channel: string, extraData?: any): Promise<boolean>;
    private sendNotification;
    private createNotificationRecord;
    private mapNotificationType;
    private handleInternalNotification;
    private handlePaymentSuccessBusinessLogic;
    private handleRefundSuccessBusinessLogic;
    private callPackageOrderCallback;
    private sendPackagePurchaseSuccessNotification;
}
