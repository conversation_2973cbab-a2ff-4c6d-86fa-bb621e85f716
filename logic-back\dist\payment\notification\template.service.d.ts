import { NotificationType } from './notify.service';
export declare class PaymentTemplateService {
    private readonly logger;
    private templates;
    private templateDir;
    constructor();
    private loadTemplates;
    private createDefaultTemplates;
    private registerHelpers;
    renderTemplate(type: NotificationType, data: any): Promise<string>;
    getTemplateNames(): string[];
    updateTemplate(name: string, content: string): Promise<boolean>;
}
