# IP地理位置解析模块

基于 ip2region 的离线IP地理位置解析功能，用于用户登录时的地理位置检查和风险评估。

## 功能特性

- 离线IP地理位置解析（基于ip2region v2.0）
- 登录地风险评估
- 用户常用登录地统计
- Redis缓存优化
- 完整的API接口

## 目录结构

```
ip_location/
├── application/          # 应用层
├── domain/              # 领域层
├── infrastructure/      # 基础设施层
├── controllers/         # 控制器层
├── utils/              # 工具类
├── docs/               # 文档
├── sql/                # SQL脚本
└── test/               # 测试文件
```

## 快速开始

1. 确保已安装 ip2region 依赖
2. 配置环境变量
3. 运行数据库迁移
4. 启动服务

## API 接口

- `GET /api/v1/ip-location/query` - IP地理位置查询
- `POST /api/v1/ip-location/check-risk` - 登录风险检查
- `GET /api/v1/ip-location/user/{userId}/stats` - 用户位置统计
