"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified";
exports.ids = ["vendor-chunks/unified"];
exports.modules = {

/***/ "(ssr)/./node_modules/unified/lib/callable-instance.js":
/*!*******************************************************!*\
  !*** ./node_modules/unified/lib/callable-instance.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CallableInstance: () => (/* binding */ CallableInstance)\n/* harmony export */ });\nconst CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/callable-instance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unified/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/unified/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Processor: () => (/* binding */ Processor),\n/* harmony export */   unified: () => (/* binding */ unified)\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/bail/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/* harmony import */ var _callable_instance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./callable-instance.js */ \"(ssr)/./node_modules/unified/lib/callable-instance.js\");\n/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\n\n\n\n\n\n\n\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nclass Processor extends _callable_instance_js__WEBPACK_IMPORTED_MODULE_2__.CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_5__.bail)(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_5__.bail)(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend__WEBPACK_IMPORTED_MODULE_0__(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(currentPrimary) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(primary)) {\n          primary = extend__WEBPACK_IMPORTED_MODULE_0__(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nconst unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_6__.VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/index.js\n");

/***/ })

};
;