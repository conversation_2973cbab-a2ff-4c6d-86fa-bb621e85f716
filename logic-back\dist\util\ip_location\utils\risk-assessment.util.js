"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessmentUtil = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../../common/logger/logger.service");
let RiskAssessmentUtil = class RiskAssessmentUtil {
    loggerService;
    riskConfig = {
        lowThreshold: 30,
        mediumThreshold: 70,
        highThreshold: 100,
        foreignLoginWeight: 60,
        crossProvinceWeight: 40,
        newLocationWeight: 15,
        ispChangeWeight: 5,
        dataQualityWeight: 20,
        newUserProtectionDays: 7,
        newUserRiskReduction: 15
    };
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    async assessLoginRisk(userId, currentLocation, userHistory) {
        const startTime = Date.now();
        try {
            const riskResult = this.calculateRiskScore(currentLocation, userHistory);
            const riskLevel = this.determineRiskLevel(riskResult.score);
            const risk = {
                level: riskLevel,
                score: riskResult.score,
                reason: this.generateRiskReason(riskLevel, riskResult.factors),
                needVerification: riskLevel === 'HIGH',
                factors: riskResult.factors
            };
            this.loggerService.logBusiness('RiskAssessmentUtil', 'assessLoginRisk', {
                userId,
                location: `${currentLocation.province}${currentLocation.city}`,
                riskLevel: risk.level,
                riskScore: risk.score,
                factors: risk.factors,
                responseTime: Date.now() - startTime
            });
            return risk;
        }
        catch (error) {
            this.loggerService.logBusiness('RiskAssessmentUtil', 'assessLoginRisk', {
                userId,
                responseTime: Date.now() - startTime
            }, error);
            return {
                level: 'LOW',
                score: 0,
                reason: '风险评估异常，默认低风险',
                needVerification: false,
                factors: ['评估异常']
            };
        }
    }
    calculateRiskScore(currentLocation, userHistory) {
        let score = 0;
        const riskFactors = [];
        if (currentLocation.country !== '中国') {
            score += this.riskConfig.foreignLoginWeight;
            riskFactors.push('境外登录');
        }
        if (currentLocation.province === '未知' || currentLocation.city === '未知') {
            score += this.riskConfig.dataQualityWeight;
            riskFactors.push('位置信息不完整');
        }
        const hasProvinceHistory = userHistory.commonLocations.some(loc => loc.province === currentLocation.province && currentLocation.province !== '未知');
        if (!hasProvinceHistory && currentLocation.country === '中国' && currentLocation.province !== '未知') {
            score += this.riskConfig.crossProvinceWeight;
            riskFactors.push(`跨省登录至${currentLocation.province}`);
        }
        if (hasProvinceHistory) {
            const isCityMatch = userHistory.commonLocations.some(loc => loc.province === currentLocation.province &&
                loc.city === currentLocation.city &&
                currentLocation.city !== '未知');
            if (!isCityMatch && currentLocation.city !== '未知') {
                score += this.riskConfig.newLocationWeight;
                riskFactors.push(`省内异地登录至${currentLocation.city}`);
            }
        }
        const hasISPHistory = userHistory.commonLocations.some(loc => loc.province === currentLocation.province &&
            currentLocation.isp !== '未知');
        if (hasProvinceHistory && !hasISPHistory && currentLocation.isp !== '未知') {
            score += this.riskConfig.ispChangeWeight;
            riskFactors.push('运营商变化');
        }
        if (userHistory.riskLoginCount > 5) {
            score += 10;
            riskFactors.push('频繁异地登录');
        }
        if (userHistory.totalLoginCount < 3) {
            score = Math.max(score - this.riskConfig.newUserRiskReduction, 0);
            riskFactors.push('新用户保护');
        }
        return {
            score: Math.min(Math.max(score, 0), 100),
            factors: riskFactors
        };
    }
    determineRiskLevel(score) {
        if (score >= this.riskConfig.highThreshold) {
            return 'HIGH';
        }
        else if (score >= this.riskConfig.mediumThreshold) {
            return 'MEDIUM';
        }
        else {
            return 'LOW';
        }
    }
    generateRiskReason(level, factors) {
        if (factors.length === 0) {
            return '常用登录地，风险较低';
        }
        const mainFactor = factors[0];
        switch (level) {
            case 'HIGH':
                return `高风险：${mainFactor}`;
            case 'MEDIUM':
                return `中风险：${mainFactor}`;
            case 'LOW':
            default:
                return `低风险：${mainFactor}`;
        }
    }
    needAdditionalVerification(risk, userHistory) {
        if (risk.level === 'HIGH') {
            return true;
        }
        if (risk.level === 'MEDIUM' && userHistory.totalLoginCount < 5) {
            return true;
        }
        if (risk.factors.includes('境外登录')) {
            return true;
        }
        return false;
    }
    getRecommendedVerificationMethods(risk) {
        const methods = [];
        if (risk.factors.includes('境外登录')) {
            methods.push('邮箱验证', '短信验证');
        }
        else if (risk.factors.includes('跨省登录')) {
            methods.push('短信验证');
        }
        else if (risk.level === 'MEDIUM') {
            methods.push('短信验证');
        }
        return methods.length > 0 ? methods : ['短信验证'];
    }
    updateRiskConfig(newConfig) {
        Object.assign(this.riskConfig, newConfig);
        this.loggerService.logBusiness('RiskAssessmentUtil', 'updateRiskConfig', {
            updatedFields: Object.keys(newConfig),
            newConfig: this.riskConfig
        });
    }
    getRiskConfig() {
        return { ...this.riskConfig };
    }
};
exports.RiskAssessmentUtil = RiskAssessmentUtil;
exports.RiskAssessmentUtil = RiskAssessmentUtil = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], RiskAssessmentUtil);
//# sourceMappingURL=risk-assessment.util.js.map